-- 安全的字符集修复方案 - 第三阶段
-- 修复剩余表，完成统一

-- 设置安全模式
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';

-- ========================================
-- 第三阶段：修复剩余表
-- ========================================

-- 检查前两阶段是否成功
SELECT 
    '前期阶段检查' as '检查项目',
    COUNT(*) as '核心表总数',
    SUM(CASE WHEN table_collation = 'utf8mb4_0900_ai_ci' THEN 1 ELSE 0 END) as '已修复表数',
    CASE 
        WHEN COUNT(*) = SUM(CASE WHEN table_collation = 'utf8mb4_0900_ai_ci' THEN 1 ELSE 0 END)
        THEN '✓ 前期阶段成功，可以继续'
        ELSE '✗ 前期阶段未完成，请先完成前期阶段'
    END as '状态'
FROM information_schema.tables 
WHERE table_schema = 'czerp_web' 
    AND table_name IN ('purchase_order', 'purchase_order_item', 'sys_role_permission', 'sys_user_role', 'sys_permission_backup', 'example_entity', 'paper_size_setting');

-- ========================================
-- 修复剩余的系统表
-- ========================================

-- 修复用户权限相关表
SELECT '修复系统表...' as '状态';
ALTER TABLE sys_department CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE sys_measurement_unit CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE sys_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE sys_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE sys_user CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- ========================================
-- 修复供应商相关表
-- ========================================

-- 修复供应商表
SELECT '修复供应商表...' as '状态';
ALTER TABLE pur_supplier_category CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE pur_supplier CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- ========================================
-- 修复销售订单相关表
-- ========================================

-- 修复销售订单工序和材料表
SELECT '修复销售订单相关表...' as '状态';
ALTER TABLE sales_order_item_process CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE sales_order_material_detail CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE sales_order_process_step CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- ========================================
-- 最终验证所有表
-- ========================================
SELECT 
    '最终验证' as '检查项目',
    COUNT(*) as '总表数',
    SUM(CASE WHEN TABLE_COLLATION = 'utf8mb4_0900_ai_ci' THEN 1 ELSE 0 END) as '正确字符集表数',
    SUM(CASE WHEN TABLE_COLLATION != 'utf8mb4_0900_ai_ci' THEN 1 ELSE 0 END) as '错误字符集表数',
    CASE 
        WHEN SUM(CASE WHEN TABLE_COLLATION != 'utf8mb4_0900_ai_ci' THEN 1 ELSE 0 END) = 0 
        THEN '✓ 全部修复成功' 
        ELSE '✗ 仍有问题' 
    END as '状态'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'czerp_web';

-- 列出仍有问题的表（如果有）
SELECT 
    '仍有问题的表' as '检查项目',
    TABLE_NAME as '表名', 
    TABLE_COLLATION as '当前字符集'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'czerp_web' 
    AND TABLE_COLLATION != 'utf8mb4_0900_ai_ci'
ORDER BY TABLE_NAME;

-- ========================================
-- 全面功能测试
-- ========================================

-- 测试采购单号筛选
SELECT 
    '采购单号筛选测试' as '测试项目',
    COUNT(DISTINCT po.purchase_order_no) as '采购单数量'
FROM purchase_order po
JOIN purchase_order_item poi ON po.id = poi.purchase_order_id
WHERE poi.source_sales_order_item_id IS NOT NULL;

-- 测试供应商筛选
SELECT 
    '供应商筛选测试' as '测试项目',
    COUNT(DISTINCT po.supplier_name) as '供应商数量'
FROM purchase_order po
JOIN purchase_order_item poi ON po.id = poi.purchase_order_id
WHERE poi.source_sales_order_item_id IS NOT NULL
    AND po.supplier_name IS NOT NULL;

-- 测试工序查询
SELECT 
    '工序查询测试' as '测试项目',
    COUNT(*) as '工序记录数'
FROM sales_order_item_process 
WHERE is_deleted = FALSE;

-- 恢复安全模式
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 完成报告
-- ========================================
SELECT '========== 字符集修复完成报告 ==========' as '报告';

SELECT 
    '修复时间' as '项目',
    NOW() as '值';

SELECT 
    '数据库' as '项目',
    'czerp_web' as '值';

SELECT 
    '目标字符集' as '项目',
    'utf8mb4_0900_ai_ci' as '值';

SELECT 
    '修复状态' as '项目',
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.TABLES 
              WHERE TABLE_SCHEMA = 'czerp_web' 
                AND TABLE_COLLATION != 'utf8mb4_0900_ai_ci') = 0 
        THEN '✓ 全部修复成功'
        ELSE '✗ 仍有未修复的表'
    END as '值';

-- ========================================
-- 第三阶段执行说明
-- ========================================
/*
第三阶段特点：
1. 修复所有剩余的表
2. 完成整个数据库的字符集统一
3. 进行全面的功能测试

执行后检查：
1. 最终验证应该显示 "✓ 全部修复成功"
2. 所有功能测试都应该正常执行
3. 重启应用程序进行完整测试

如果第三阶段成功：
- 所有表都使用统一的字符集
- 字符集冲突问题彻底解决
- 系统性能可能有所提升

后续步骤：
1. 重启应用程序
2. 进行完整的功能测试
3. 观察系统运行稳定性
4. 可以删除备份文件（建议保留一段时间）
*/
