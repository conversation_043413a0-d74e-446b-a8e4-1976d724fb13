package com.czerp.erpbackend.material.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 纸质资料权限初始化器
 * 负责初始化纸质资料模块的权限
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(15) // 在系统权限初始化之后执行
public class PaperMaterialPermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing paper material permissions...");

        // 初始化纸质资料管理权限
        createPaperMaterialPermissions();

        log.info("Paper material permissions initialized successfully");
    }

    /**
     * 创建纸质资料管理权限
     */
    private void createPaperMaterialPermissions() {
        log.info("Creating paper material permissions...");

        // 检查物料管理模块是否存在
        String materialModuleId = findPermissionIdByCode("material");
        if (materialModuleId == null) {
            log.info("Material module not found, creating it...");
            materialModuleId = createPermission("物料管理", "material", "menu", null, "/material", "material/index", "database", 50);
        }

        // 检查纸质资料模块是否已存在
        if (permissionRepository.existsByCode("material:paper-material")) {
            log.info("Paper material module already exists, skipping...");
            return;
        }

        // 创建纸质资料模块菜单
        String paperMaterialModuleId = createPermission("纸质资料管理", "material:paper-material", "menu", materialModuleId, "/material/paper-materials", "material/PaperMaterial", "file", 10);

        // 创建纸质资料模块按钮权限
        createPermission("纸质资料列表", "material:paper-material:list", "button", paperMaterialModuleId, null, null, null, 11);
        createPermission("纸质资料详情", "material:paper-material:read", "button", paperMaterialModuleId, null, null, null, 12);
        createPermission("创建纸质资料", "material:paper-material:create", "button", paperMaterialModuleId, null, null, null, 13);
        createPermission("更新纸质资料", "material:paper-material:update", "button", paperMaterialModuleId, null, null, null, 14);
        createPermission("删除纸质资料", "material:paper-material:delete", "button", paperMaterialModuleId, null, null, null, 15);
        createPermission("导入纸质资料", "material:paper-material:import", "button", paperMaterialModuleId, null, null, null, 16);

        log.info("Paper material module permissions created successfully");
    }

    /**
     * 根据编码查找权限ID
     * @param code 权限编码
     * @return 权限ID
     */
    private String findPermissionIdByCode(String code) {
        return permissionRepository.findByCode(code)
                .map(Permission::getId)
                .orElse(null);
    }

    /**
     * 创建权限
     * @param name 权限名称
     * @param code 权限编码
     * @param type 权限类型
     * @param parentId 父权限ID
     * @param path 路径
     * @param component 组件
     * @param icon 图标
     * @param sort 排序
     * @return 权限ID
     */
    private String createPermission(String name, String code, String type, String parentId, String path, String component, String icon, Integer sort) {
        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setName(name);
        permission.setCode(code);
        permission.setType(type);
        permission.setParentId(parentId);
        permission.setPath(path);
        permission.setComponent(component);
        permission.setIcon(icon);
        permission.setSort(sort);
        permission.setCreatedBy("system");
        permission.setCreatedTime(LocalDateTime.now());
        permission.setIsDeleted(false);
        permission = permissionRepository.save(permission);
        return permission.getId();
    }
}
