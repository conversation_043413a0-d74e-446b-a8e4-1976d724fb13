-- 动态计算已入库数量索引优化脚本
-- 用于提升采购订单明细已入库数量动态计算的查询性能

-- 1. 为入库单明细表添加复合索引
-- 优化查询：SELECT SUM(quantity) FROM stock_inbound_item WHERE purchase_order_item_id = ? AND is_deleted = false
CREATE INDEX idx_stock_inbound_item_calculation
ON stock_inbound_item(purchase_order_item_id, is_deleted, quantity);

-- 2. 为入库单明细表添加单独的采购订单明细ID索引（如果不存在）
-- 用于快速定位特定采购订单明细的入库记录
-- 注意：MySQL不支持条件索引创建，需要先检查索引是否存在

-- 检查索引是否存在的查询（手动执行检查）
-- SELECT COUNT(*) as index_exists FROM information_schema.statistics
-- WHERE table_schema = DATABASE()
-- AND table_name = 'stock_inbound_item'
-- AND column_name = 'purchase_order_item_id'
-- AND index_name != 'PRIMARY';

-- 如果上述查询返回0，则执行下面的索引创建语句
CREATE INDEX idx_stock_inbound_item_purchase_order_item_id
ON stock_inbound_item(purchase_order_item_id);

-- 3. 为入库单明细表添加软删除标记索引
-- 优化软删除过滤查询
CREATE INDEX idx_stock_inbound_item_is_deleted
ON stock_inbound_item(is_deleted);

-- 4. 为采购订单明细表添加数量字段索引
-- 优化可用性检查查询
CREATE INDEX idx_purchase_order_item_quantity
ON purchase_order_item(quantity);

-- 5. 创建覆盖索引用于批量查询优化
-- 注意：MySQL不支持INCLUDE语法，使用复合索引替代
-- 包含所有需要的字段，避免回表查询
CREATE INDEX idx_stock_inbound_item_batch_calculation
ON stock_inbound_item(purchase_order_item_id, is_deleted, quantity);

-- 6. 分析表统计信息，确保查询优化器使用最新的统计信息
ANALYZE TABLE stock_inbound_item;
ANALYZE TABLE purchase_order_item;

-- 7. 查看索引使用情况的查询语句（用于监控）
-- 可以定期执行以监控索引效果

-- 查看入库单明细表的索引使用情况
SELECT 
    table_name,
    index_name,
    column_name,
    cardinality,
    sub_part,
    packed,
    nullable,
    index_type
FROM information_schema.statistics 
WHERE table_name = 'stock_inbound_item' 
ORDER BY index_name, seq_in_index;

-- 查看采购订单明细表的索引使用情况
SELECT 
    table_name,
    index_name,
    column_name,
    cardinality,
    sub_part,
    packed,
    nullable,
    index_type
FROM information_schema.statistics 
WHERE table_name = 'purchase_order_item' 
ORDER BY index_name, seq_in_index;

-- 8. 性能测试查询
-- 用于验证索引优化效果

-- 测试单个采购订单明细的已入库数量计算
EXPLAIN SELECT COALESCE(SUM(sii.quantity), 0) 
FROM stock_inbound_item sii 
WHERE sii.purchase_order_item_id = 1 
AND sii.is_deleted = false;

-- 测试批量采购订单明细的已入库数量计算
EXPLAIN SELECT sii.purchase_order_item_id, COALESCE(SUM(sii.quantity), 0) 
FROM stock_inbound_item sii 
WHERE sii.purchase_order_item_id IN (1, 2, 3, 4, 5) 
AND sii.is_deleted = false 
GROUP BY sii.purchase_order_item_id;

-- 9. 索引维护建议
-- 定期执行以保持索引性能

-- 重建索引（如果发现性能下降）
-- ALTER TABLE stock_inbound_item REBUILD INDEX idx_stock_inbound_item_calculation;

-- 更新表统计信息（建议定期执行）
-- ANALYZE TABLE stock_inbound_item UPDATE HISTOGRAM ON purchase_order_item_id, quantity;

-- 10. 监控查询
-- 用于监控动态计算的性能

-- 查看慢查询日志中相关的查询
-- SELECT * FROM mysql.slow_log 
-- WHERE sql_text LIKE '%stock_inbound_item%' 
-- AND sql_text LIKE '%purchase_order_item_id%'
-- ORDER BY start_time DESC LIMIT 10;

-- 查看查询缓存命中率
-- SHOW STATUS LIKE 'Qcache%';

-- 注意事项：
-- 1. 在生产环境执行前，请先在测试环境验证
-- 2. 创建索引可能会锁表，建议在业务低峰期执行
-- 3. 定期监控索引使用情况，删除不必要的索引
-- 4. 根据实际数据量和查询模式调整索引策略
