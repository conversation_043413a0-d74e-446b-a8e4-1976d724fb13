package com.czerp.erpbackend.purchase.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 供应商权限初始化器
 * 负责初始化供应商模块的权限
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(6) // 在系统权限初始化之后执行
public class SupplierPermissionInitializer implements CommandLineRunner {
    
    private final PermissionRepository permissionRepository;
    
    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing supplier permissions...");
        
        // 初始化供应商管理权限
        createSupplierPermissions();
        
        // 初始化供应商分类管理权限
        createSupplierCategoryPermissions();
        
        log.info("Supplier permissions initialized successfully");
    }
    
    /**
     * 创建供应商管理权限
     */
    private void createSupplierPermissions() {
        String parentCode = "supplier";
        String parentName = "供应商管理";
        
        // 检查父权限是否已存在
        if (permissionRepository.existsByCode(parentCode)) {
            log.info("Supplier permissions already exist, skipping...");
            return;
        }
        
        // 创建父权限
        Permission parent = createPermission(null, parentCode, parentName, "menu");
        
        // 创建子权限
        List<Permission> permissions = new ArrayList<>();
        permissions.add(createPermission(parent.getId(), parentCode + ":list", "供应商列表", "button"));
        permissions.add(createPermission(parent.getId(), parentCode + ":read", "供应商详情", "button"));
        permissions.add(createPermission(parent.getId(), parentCode + ":create", "创建供应商", "button"));
        permissions.add(createPermission(parent.getId(), parentCode + ":update", "更新供应商", "button"));
        permissions.add(createPermission(parent.getId(), parentCode + ":delete", "删除供应商", "button"));
        
        // 保存权限
        permissionRepository.saveAll(permissions);
        log.info("Created {} supplier permissions", permissions.size() + 1);
    }
    
    /**
     * 创建供应商分类管理权限
     */
    private void createSupplierCategoryPermissions() {
        String parentCode = "supplier-category";
        String parentName = "供应商分类管理";
        
        // 检查父权限是否已存在
        if (permissionRepository.existsByCode(parentCode)) {
            log.info("Supplier category permissions already exist, skipping...");
            return;
        }
        
        // 创建父权限
        Permission parent = createPermission(null, parentCode, parentName, "menu");
        
        // 创建子权限
        List<Permission> permissions = new ArrayList<>();
        permissions.add(createPermission(parent.getId(), parentCode + ":list", "供应商分类列表", "button"));
        permissions.add(createPermission(parent.getId(), parentCode + ":read", "供应商分类详情", "button"));
        permissions.add(createPermission(parent.getId(), parentCode + ":create", "创建供应商分类", "button"));
        permissions.add(createPermission(parent.getId(), parentCode + ":update", "更新供应商分类", "button"));
        permissions.add(createPermission(parent.getId(), parentCode + ":delete", "删除供应商分类", "button"));
        
        // 保存权限
        permissionRepository.saveAll(permissions);
        log.info("Created {} supplier category permissions", permissions.size() + 1);
    }
    
    /**
     * 创建权限
     * @param parentId 父权限ID
     * @param code 权限编码
     * @param name 权限名称
     * @param type 权限类型
     * @return 权限
     */
    private Permission createPermission(String parentId, String code, String name, String type) {
        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setCode(code);
        permission.setName(name);
        permission.setType(type);
        permission.setParentId(parentId);
        permission.setStatus("active");
        permission.setIsDeleted(false);
        
        return permissionRepository.save(permission);
    }
}
