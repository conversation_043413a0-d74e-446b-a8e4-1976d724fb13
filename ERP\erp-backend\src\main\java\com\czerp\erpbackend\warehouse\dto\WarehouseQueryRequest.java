package com.czerp.erpbackend.warehouse.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 仓库查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseQueryRequest {

    /**
     * 关键词（仓库名称）
     */
    private String keyword;

    /**
     * 备料仓筛选（null-全部，true-备料仓，false-非备料仓）
     */
    private Boolean isMaterialWarehouse;

    /**
     * 页码（从1开始）
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 排序字段
     */
    private String sortBy = "createdTime";

    /**
     * 排序方向（asc-升序，desc-降序）
     */
    private String sortDirection = "desc";
}
