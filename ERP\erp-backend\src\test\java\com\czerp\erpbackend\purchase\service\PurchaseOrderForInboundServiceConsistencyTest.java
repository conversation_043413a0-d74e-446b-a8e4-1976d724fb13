package com.czerp.erpbackend.purchase.service;

import com.czerp.erpbackend.inventory.entity.StockInbound;
import com.czerp.erpbackend.inventory.entity.StockInboundItem;
import com.czerp.erpbackend.inventory.repository.StockInboundItemRepository;
import com.czerp.erpbackend.inventory.repository.StockInboundRepository;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemForInboundDTO;
import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderRepository;
import com.czerp.erpbackend.purchase.service.impl.PurchaseOrderForInboundServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 采购订单引用服务逻辑一致性测试
 * 验证三个接口的过滤逻辑是否一致
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class PurchaseOrderForInboundServiceConsistencyTest {

    @Autowired
    private PurchaseOrderForInboundService purchaseOrderForInboundService;

    @Autowired
    private PurchaseOrderRepository purchaseOrderRepository;

    @Autowired
    private PurchaseOrderItemRepository purchaseOrderItemRepository;

    @Autowired
    private StockInboundRepository stockInboundRepository;

    @Autowired
    private StockInboundItemRepository stockInboundItemRepository;

    private PurchaseOrder testPurchaseOrder;
    private PurchaseOrderItem availableItem; // 可用于入库的明细
    private PurchaseOrderItem fullyReceivedItem; // 已完全入库的明细
    private PurchaseOrderItem nullReceivedItem; // 已入库数为null的明细
    private StockInbound testStockInbound;

    @BeforeEach
    void setUp() {
        // 创建测试采购订单
        testPurchaseOrder = new PurchaseOrder();
        testPurchaseOrder.setPurchaseOrderNo("TEST-PO-001");
        testPurchaseOrder.setPurchaseDate(LocalDate.now());
        testPurchaseOrder.setSupplierName("测试供应商");
        testPurchaseOrder = purchaseOrderRepository.save(testPurchaseOrder);

        // 创建可用于入库的明细（部分入库）
        availableItem = new PurchaseOrderItem();
        availableItem.setPurchaseOrder(testPurchaseOrder);
        availableItem.setQuantity(100);
        availableItem.setPrice(BigDecimal.valueOf(10.0));
        availableItem.setAmount(BigDecimal.valueOf(1000.0));
        availableItem = purchaseOrderItemRepository.save(availableItem);

        // 创建已完全入库的明细
        fullyReceivedItem = new PurchaseOrderItem();
        fullyReceivedItem.setPurchaseOrder(testPurchaseOrder);
        fullyReceivedItem.setQuantity(200);
        fullyReceivedItem.setPrice(BigDecimal.valueOf(15.0));
        fullyReceivedItem.setAmount(BigDecimal.valueOf(3000.0));
        fullyReceivedItem = purchaseOrderItemRepository.save(fullyReceivedItem);

        // 创建已入库数为null的明细（从未入库）
        nullReceivedItem = new PurchaseOrderItem();
        nullReceivedItem.setPurchaseOrder(testPurchaseOrder);
        nullReceivedItem.setQuantity(150);
        nullReceivedItem.setPrice(BigDecimal.valueOf(12.0));
        nullReceivedItem.setAmount(BigDecimal.valueOf(1800.0));
        nullReceivedItem = purchaseOrderItemRepository.save(nullReceivedItem);

        // 创建测试入库单
        testStockInbound = new StockInbound();
        testStockInbound.setInboundNo("RK20240101001");
        testStockInbound.setInboundDate(LocalDate.now());
        testStockInbound.setSupplierName("测试供应商");
        testStockInbound.setIsDeleted(false);
        testStockInbound = stockInboundRepository.save(testStockInbound);

        // 创建入库单明细1（部分入库availableItem）
        StockInboundItem inboundItem1 = new StockInboundItem();
        inboundItem1.setStockInbound(testStockInbound);
        inboundItem1.setPurchaseOrderItem(availableItem);
        inboundItem1.setQuantity(50); // 入库50，还剩50未入库
        inboundItem1.setIsDeleted(false);
        stockInboundItemRepository.save(inboundItem1);

        // 创建入库单明细2（完全入库fullyReceivedItem）
        StockInboundItem inboundItem2 = new StockInboundItem();
        inboundItem2.setStockInbound(testStockInbound);
        inboundItem2.setPurchaseOrderItem(fullyReceivedItem);
        inboundItem2.setQuantity(200); // 完全入库200
        inboundItem2.setIsDeleted(false);
        stockInboundItemRepository.save(inboundItem2);

        // nullReceivedItem没有入库记录，动态计算结果为0
    }

    @Test
    @DisplayName("根据ID查询 - 可用于入库的明细应该返回数据")
    void testFindByIdAvailableItem() {
        PurchaseOrderItemForInboundDTO result = purchaseOrderForInboundService
                .findPurchaseOrderItemById(availableItem.getId());
        
        assertNotNull(result, "可用于入库的明细应该返回数据");
        assertEquals(availableItem.getId(), result.getId());
        assertEquals(Integer.valueOf(50), result.getUnreceivedQuantity()); // 100-50=50
    }

    @Test
    @DisplayName("根据ID查询 - 已完全入库的明细应该返回null")
    void testFindByIdFullyReceivedItem() {
        PurchaseOrderItemForInboundDTO result = purchaseOrderForInboundService
                .findPurchaseOrderItemById(fullyReceivedItem.getId());
        
        assertNull(result, "已完全入库的明细应该返回null");
    }

    @Test
    @DisplayName("根据ID查询 - 从未入库的明细应该返回数据")
    void testFindByIdNullReceivedItem() {
        PurchaseOrderItemForInboundDTO result = purchaseOrderForInboundService
                .findPurchaseOrderItemById(nullReceivedItem.getId());
        
        assertNotNull(result, "从未入库的明细应该返回数据");
        assertEquals(nullReceivedItem.getId(), result.getId());
        assertEquals(Integer.valueOf(150), result.getUnreceivedQuantity()); // 150-0=150
    }

    @Test
    @DisplayName("批量查询 - 应该过滤掉已完全入库的明细")
    void testFindByIdsFiltering() {
        List<Long> ids = Arrays.asList(
                availableItem.getId(),
                fullyReceivedItem.getId(),
                nullReceivedItem.getId()
        );
        
        List<PurchaseOrderItemForInboundDTO> results = purchaseOrderForInboundService
                .findPurchaseOrderItemsByIds(ids);
        
        assertEquals(2, results.size(), "应该返回2条记录（过滤掉已完全入库的）");
        
        // 验证返回的是可用的明细
        List<Long> resultIds = results.stream()
                .map(PurchaseOrderItemForInboundDTO::getId)
                .toList();
        
        assertTrue(resultIds.contains(availableItem.getId()), "应该包含部分入库的明细");
        assertTrue(resultIds.contains(nullReceivedItem.getId()), "应该包含从未入库的明细");
        assertFalse(resultIds.contains(fullyReceivedItem.getId()), "不应该包含已完全入库的明细");
    }

    @Test
    @DisplayName("逻辑一致性验证 - 三个接口的过滤逻辑应该一致")
    void testConsistencyBetweenInterfaces() {
        // 测试可用明细
        PurchaseOrderItemForInboundDTO singleResult1 = purchaseOrderForInboundService
                .findPurchaseOrderItemById(availableItem.getId());
        List<PurchaseOrderItemForInboundDTO> batchResult1 = purchaseOrderForInboundService
                .findPurchaseOrderItemsByIds(Arrays.asList(availableItem.getId()));
        
        assertNotNull(singleResult1, "单个查询应该返回可用明细");
        assertEquals(1, batchResult1.size(), "批量查询应该返回可用明细");
        assertEquals(singleResult1.getId(), batchResult1.get(0).getId(), "两个接口返回的数据应该一致");

        // 测试已完全入库明细
        PurchaseOrderItemForInboundDTO singleResult2 = purchaseOrderForInboundService
                .findPurchaseOrderItemById(fullyReceivedItem.getId());
        List<PurchaseOrderItemForInboundDTO> batchResult2 = purchaseOrderForInboundService
                .findPurchaseOrderItemsByIds(Arrays.asList(fullyReceivedItem.getId()));
        
        assertNull(singleResult2, "单个查询应该返回null（已完全入库）");
        assertEquals(0, batchResult2.size(), "批量查询应该过滤掉已完全入库的明细");
    }
}
