package com.czerp.erpbackend.purchase.service;

import java.util.List;
import java.util.Map;

/**
 * 采购订单明细计算服务接口
 * 提供动态计算已入库数量等相关功能
 */
public interface PurchaseOrderItemCalculationService {

    /**
     * 动态计算单个采购订单明细的已入库数量
     * @param purchaseOrderItemId 采购订单明细ID
     * @return 已入库数量，如果没有入库记录则返回0
     */
    Integer calculateReceivedQuantity(Long purchaseOrderItemId);

    /**
     * 批量动态计算多个采购订单明细的已入库数量
     * @param purchaseOrderItemIds 采购订单明细ID列表
     * @return 采购订单明细ID到已入库数量的映射
     */
    Map<Long, Integer> calculateReceivedQuantitiesBatch(List<Long> purchaseOrderItemIds);

    /**
     * 计算未入库数量
     * @param purchaseOrderItemId 采购订单明细ID
     * @param quantity 采购数量
     * @param returnedQuantity 已退货数量（可选，默认为0）
     * @return 未入库数量
     */
    Integer calculateUnreceivedQuantity(Long purchaseOrderItemId, Integer quantity, Integer returnedQuantity);

    /**
     * 检查采购订单明细是否可用于入库
     * @param purchaseOrderItemId 采购订单明细ID
     * @param quantity 采购数量
     * @return true表示可用于入库，false表示已完全入库
     */
    boolean isAvailableForInbound(Long purchaseOrderItemId, Integer quantity);

    /**
     * 批量检查采购订单明细是否可用于入库
     * @param purchaseOrderItemIds 采购订单明细ID列表
     * @param quantityMap 采购订单明细ID到采购数量的映射
     * @return 采购订单明细ID到是否可用的映射
     */
    Map<Long, Boolean> isAvailableForInboundBatch(List<Long> purchaseOrderItemIds, Map<Long, Integer> quantityMap);
}
