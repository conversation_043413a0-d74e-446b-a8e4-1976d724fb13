package com.czerp.erpbackend.sales.repository;

import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 销售订单明细Repository
 */
@Repository
public interface SalesOrderItemRepository extends JpaRepository<SalesOrderItem, String>, JpaSpecificationExecutor<SalesOrderItem> {

    /**
     * 根据订单ID查询订单明细
     * @param orderId 订单ID
     * @return 订单明细列表
     */
    List<SalesOrderItem> findByOrderId(String orderId);

    /**
     * 根据客方货号查询订单明细
     * @param customerProductCode 客方货号
     * @return 订单明细列表
     */
    List<SalesOrderItem> findByCustomerProductCode(String customerProductCode);

    /**
     * 根据订单ID统计订单明细数量
     * @param orderId 订单ID
     * @return 订单明细数量
     */
    long countByOrderId(String orderId);

    /**
     * 根据订单ID删除订单明细
     * @param orderId 订单ID
     */
    void deleteByOrderId(String orderId);

    /**
     * 根据产品名称模糊查询订单明细
     * @param productName 产品名称
     * @return 订单明细列表
     */
    @Query("SELECT i FROM SalesOrderItem i WHERE i.productName LIKE %:productName%")
    List<SalesOrderItem> findByProductNameLike(@Param("productName") String productName);

    /**
     * 根据前缀查询最大生产单号
     * @param prefix 前缀
     * @return 最大生产单号
     */
    @Query("SELECT MAX(i.productionOrderNo) FROM SalesOrderItem i WHERE i.productionOrderNo LIKE :prefix%")
    String findMaxProductionOrderNoByPrefix(@Param("prefix") String prefix);
}
