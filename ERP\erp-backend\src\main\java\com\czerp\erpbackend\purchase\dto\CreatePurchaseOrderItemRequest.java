package com.czerp.erpbackend.purchase.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 创建采购订单明细请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreatePurchaseOrderItemRequest {

    /**
     * 纸质
     */
    private String paperQuality;

    /**
     * 纸板类别
     */
    private String paperBoardCategory;

    /**
     * 楞别
     */
    private String corrugationType;

    /**
     * 纸度
     */
    private String paperWidth;

    /**
     * 纸长
     */
    private String paperLength;

    /**
     * 合订
     */
    private Boolean bindingMethod;

    /**
     * 合订规格
     */
    private String bindingSpecification;

    /**
     * 用料异动
     */
    private Boolean materialChange;

    /**
     * 报价特价
     */
    private String specialQuotation;

    /**
     * 纸质报价
     */
    private BigDecimal paperQuotation;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    private Integer quantity;

    /**
     * 价格
     */
    @NotNull(message = "价格不能为空")
    private BigDecimal price;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    /**
     * 压线尺寸(纸度)
     */
    private String creasingSize;

    /**
     * 压线方式
     */
    private String creasingMethod;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 折度规格
     */
    private String foldingSpecification;

    /**
     * 长度(米)
     */
    private BigDecimal lengthMeters;

    /**
     * 面积(平米)
     */
    private BigDecimal areaSquareMeters;

    /**
     * 体积(立方米)
     */
    private BigDecimal volumeCubicMeters;

    /**
     * 单重
     */
    private BigDecimal unitWeight;

    /**
     * 总重(KG)
     */
    private BigDecimal totalWeightKg;

    /**
     * 加工费
     */
    private BigDecimal processingFee;

    /**
     * 币种
     */
    private String currency;

    /**
     * 交期
     */
    private LocalDate deliveryDate;

    /**
     * 来源销售订单明细ID
     */
    private String sourceSalesOrderItemId;
}
