-- 计量单位表设计 (使用小写下划线命名)
CREATE TABLE measurement_unit (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    unit_name VARCHAR(50) NOT NULL UNIQUE COMMENT '单位名称',
    sort_order INT NOT NULL DEFAULT 999 COMMENT '排序',
    is_default_for_new_material BOOLEAN NOT NULL DEFAULT FALSE COMMENT '新建物料默认单位',
    is_dimension_unit BOOLEAN NOT NULL DEFAULT FALSE COMMENT '纸箱/纸板尺寸单位',
    is_default_dimension_unit BOOLEAN NOT NULL DEFAULT FALSE COMMENT '默认纸箱尺寸单位',
    is_default_thickness_unit BOOLEAN NOT NULL DEFAULT FALSE COMMENT '默认纸度单位',
    is_default_length_unit BOOLEAN NOT NULL DEFAULT FALSE COMMENT '默认纸长单位',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态(active-启用,inactive-停用)',

    -- 审计字段
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME COMMENT '更新时间',
    version INT DEFAULT 0 COMMENT '版本号',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计量单位表';

-- 创建索引
CREATE INDEX idx_measurement_unit_sort ON measurement_unit(sort_order);
CREATE INDEX idx_measurement_unit_status ON measurement_unit(status);

-- 创建触发器，确保只有一个默认物料单位
DELIMITER //
CREATE TRIGGER trg_check_default_material_unit BEFORE INSERT ON measurement_unit
FOR EACH ROW
BEGIN
    IF NEW.is_default_for_new_material = TRUE AND NEW.is_deleted = FALSE THEN
        IF EXISTS (SELECT 1 FROM measurement_unit WHERE is_default_for_new_material = TRUE AND is_deleted = FALSE AND id != NEW.id) THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Only one record can be set as default material unit';
        END IF;
    END IF;
END //

CREATE TRIGGER trg_check_default_material_unit_update BEFORE UPDATE ON measurement_unit
FOR EACH ROW
BEGIN
    IF NEW.is_default_for_new_material = TRUE AND NEW.is_deleted = FALSE THEN
        IF EXISTS (SELECT 1 FROM measurement_unit WHERE is_default_for_new_material = TRUE AND is_deleted = FALSE AND id != NEW.id) THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Only one record can be set as default material unit';
        END IF;
    END IF;
END //

-- 创建触发器，确保只有一个默认纸箱尺寸单位
CREATE TRIGGER trg_check_default_dimension_unit BEFORE INSERT ON measurement_unit
FOR EACH ROW
BEGIN
    IF NEW.is_default_dimension_unit = TRUE AND NEW.is_deleted = FALSE THEN
        IF EXISTS (SELECT 1 FROM measurement_unit WHERE is_default_dimension_unit = TRUE AND is_deleted = FALSE AND id != NEW.id) THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Only one record can be set as default dimension unit';
        END IF;
    END IF;
END //

CREATE TRIGGER trg_check_default_dimension_unit_update BEFORE UPDATE ON measurement_unit
FOR EACH ROW
BEGIN
    IF NEW.is_default_dimension_unit = TRUE AND NEW.is_deleted = FALSE THEN
        IF EXISTS (SELECT 1 FROM measurement_unit WHERE is_default_dimension_unit = TRUE AND is_deleted = FALSE AND id != NEW.id) THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Only one record can be set as default dimension unit';
        END IF;
    END IF;
END //

-- 创建触发器，确保只有一个默认纸度单位
CREATE TRIGGER trg_check_default_thickness_unit BEFORE INSERT ON measurement_unit
FOR EACH ROW
BEGIN
    IF NEW.is_default_thickness_unit = TRUE AND NEW.is_deleted = FALSE THEN
        IF EXISTS (SELECT 1 FROM measurement_unit WHERE is_default_thickness_unit = TRUE AND is_deleted = FALSE AND id != NEW.id) THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Only one record can be set as default thickness unit';
        END IF;
    END IF;
END //

CREATE TRIGGER trg_check_default_thickness_unit_update BEFORE UPDATE ON measurement_unit
FOR EACH ROW
BEGIN
    IF NEW.is_default_thickness_unit = TRUE AND NEW.is_deleted = FALSE THEN
        IF EXISTS (SELECT 1 FROM measurement_unit WHERE is_default_thickness_unit = TRUE AND is_deleted = FALSE AND id != NEW.id) THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Only one record can be set as default thickness unit';
        END IF;
    END IF;
END //

-- 创建触发器，确保只有一个默认纸长单位
CREATE TRIGGER trg_check_default_length_unit BEFORE INSERT ON measurement_unit
FOR EACH ROW
BEGIN
    IF NEW.is_default_length_unit = TRUE AND NEW.is_deleted = FALSE THEN
        IF EXISTS (SELECT 1 FROM measurement_unit WHERE is_default_length_unit = TRUE AND is_deleted = FALSE AND id != NEW.id) THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Only one record can be set as default length unit';
        END IF;
    END IF;
END //

CREATE TRIGGER trg_check_default_length_unit_update BEFORE UPDATE ON measurement_unit
FOR EACH ROW
BEGIN
    IF NEW.is_default_length_unit = TRUE AND NEW.is_deleted = FALSE THEN
        IF EXISTS (SELECT 1 FROM measurement_unit WHERE is_default_length_unit = TRUE AND is_deleted = FALSE AND id != NEW.id) THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Only one record can be set as default length unit';
        END IF;
    END IF;
END //
DELIMITER ;

-- 初始数据
INSERT INTO measurement_unit (
    unit_name,
    sort_order,
    is_default_for_new_material,
    is_dimension_unit,
    is_default_dimension_unit,
    is_default_thickness_unit,
    is_default_length_unit,
    status,
    created_by,
    created_time,
    is_deleted
) VALUES
('毫米', 10, TRUE, TRUE, TRUE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('厘米', 20, FALSE, TRUE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('英寸', 30, FALSE, TRUE, FALSE, TRUE, FALSE, 'active', 'system', NOW(), FALSE),
('米', 40, FALSE, TRUE, FALSE, FALSE, TRUE, 'active', 'system', NOW(), FALSE),
('千克', 50, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('克', 60, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('吨', 70, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('个', 80, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('箱', 90, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('包', 100, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE);

-- 注意：MySQL 8.0+ 支持部分索引（WHERE 子句），如果使用较低版本的 MySQL，
-- 可能需要使用触发器来实现唯一约束的功能。
