package com.czerp.erpbackend.product.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 创建货品请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateProductRequest {

    /**
     * 货品名称
     */
    @NotBlank(message = "货品名称不能为空")
    @Size(max = 100, message = "货品名称长度不能超过100个字符")
    private String name;

    /**
     * 货品编码
     */
    @NotBlank(message = "货品编码不能为空")
    @Size(max = 50, message = "货品编码长度不能超过50个字符")
    private String code;

    /**
     * 分类ID
     */
    @NotBlank(message = "分类ID不能为空")
    private String categoryId;

    /**
     * 规格ID
     */
    @NotBlank(message = "规格ID不能为空")
    private String specId;

    /**
     * 单位
     */
    @NotBlank(message = "单位不能为空")
    @Size(max = 20, message = "单位长度不能超过20个字符")
    private String unit;

    /**
     * 价格
     */
    @NotNull(message = "价格不能为空")
    private BigDecimal price;

    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    /**
     * 图片URL
     */
    @Size(max = 255, message = "图片URL长度不能超过255个字符")
    private String imageUrl;

    /**
     * 是否禁用
     */
    private Boolean disabled;
}
