| purchase_order_item | CREATE TABLE `purchase_order_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `purchase_order_id` bigint NOT NULL COMMENT '采购订单ID',
  `paper_quality` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '纸质',
  `paper_board_category` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '纸板类别',
  `corrugation_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '楞别',
  `paper_width` decimal(10,2) DEFAULT NULL COMMENT '纸度',
  `paper_length` decimal(10,2) DEFAULT NULL COMMENT '纸长',
  `binding_method` tinyint(1) DEFAULT NULL COMMENT '合订',
  `binding_specification` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '合订规格',
  `material_change` tinyint(1) DEFAULT NULL COMMENT '用料异动',
  `special_quotation` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '报价特价',
  `paper_quotation` decimal(10,2) DEFAULT NULL COMMENT '纸质报价',
  `discount` decimal(10,2) DEFAULT NULL COMMENT '折扣',
  `quantity` int DEFAULT NULL COMMENT '数量',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '金额',
  `creasing_size` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '压线尺寸(纸度)',
  `creasing_method` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '压线方式',
  `remarks` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `folding_specification` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '折度规格',
  `length_meters` decimal(10,2) DEFAULT NULL COMMENT '长度(米)',
  `area_square_meters` decimal(10,2) DEFAULT NULL COMMENT '面积(平米)',
  `volume_cubic_meters` decimal(10,2) DEFAULT NULL COMMENT '体积(立方米)',
  `unit_weight` decimal(10,2) DEFAULT NULL COMMENT '单重',
  `total_weight_kg` decimal(10,2) DEFAULT NULL COMMENT '总重(KG)',
  `received_quantity` int DEFAULT NULL COMMENT '已入库数',
  `processing_fee` decimal(10,2) DEFAULT NULL COMMENT '加工费',
  `currency` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '币种',
  `delivery_date` date DEFAULT NULL COMMENT '交期',
  `created_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `sales_order_item_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `is_deleted` bit(1) NOT NULL,
  `version` int DEFAULT NULL,
  `source_sales_order_item_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源销售订单明细ID',
  PRIMARY KEY (`id`),
  KEY `purchase_order_id` (`purchase_order_id`),
  KEY `idx_sales_order_item_id` (`sales_order_item_id`),
  CONSTRAINT `fk_poi_soi` FOREIGN KEY (`sales_order_item_id`) REFERENCES `sales_order_item` (`id`) ON DELETE CASCADE,
  CONSTRAINT `purchase_order_item_ibfk_1` FOREIGN KEY (`purchase_order_id`) REFERENCES `purchase_order` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采购订单行表' |