| sales_order_material | CREATE TABLE `sales_order_material` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `order_item_id` varchar(36) NOT NULL COMMENT '销售订单行项目ID',
  `serial_no` int DEFAULT NULL COMMENT '序号',
  `paper_quality` varchar(100) DEFAULT NULL COMMENT '纸质',
  `paper_width` decimal(10,2) DEFAULT NULL COMMENT '纸度',
  `paper_length` decimal(10,2) DEFAULT NULL COMMENT '纸长',
  `width_open` decimal(10,2) DEFAULT NULL COMMENT '度开',
  `length_open` decimal(10,2) DEFAULT NULL COMMENT '长开',
  `board_count` int DEFAULT NULL COMMENT '纸板数',
  `board_loss` decimal(10,2) DEFAULT NULL COMMENT '纸板损耗',
  `material_usage` decimal(10,2) DEFAULT NULL COMMENT '原料用量',
  `usage` decimal(10,2) DEFAULT NULL COMMENT '用量',
  `method` varchar(50) DEFAULT NULL COMMENT '方式',
  `press_size_width` varchar(100) DEFAULT NULL COMMENT '压线尺寸(纸度)',
  `press_method` varchar(50) DEFAULT NULL COMMENT '压线方式',
  `actual_material_width` decimal(10,4) DEFAULT NULL COMMENT '实际用料宽',
  `actual_material_length` decimal(10,4) DEFAULT NULL COMMENT '实际用料长',
  `die_model` varchar(100) DEFAULT NULL COMMENT '啤模',
  `die_model_no` varchar(50) DEFAULT NULL COMMENT '啤模编号',
  `die_open_count` int DEFAULT NULL COMMENT '模开数',
  `die_model_position` varchar(100) DEFAULT NULL COMMENT '啤模位置',
  `die_model_checked` tinyint(1) DEFAULT '0' COMMENT '啤模选中状态',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `current_inventory` int DEFAULT NULL COMMENT '当前库存',
  `available_inventory` int DEFAULT NULL COMMENT '可用库存',
  `use_inventory_count` int DEFAULT NULL COMMENT '使用库存数',
  `actual_material_length_converted` decimal(10,4) DEFAULT NULL COMMENT '实际用料长(转换)',
  `actual_material_width_converted` decimal(10,4) DEFAULT NULL COMMENT '实际用料宽(转换)',
  `supplier` varchar(100) DEFAULT NULL COMMENT '供应商',
  `material_remark` varchar(500) DEFAULT NULL COMMENT '材料备注',
  `purchased_count` int DEFAULT NULL COMMENT '已采购数',
  `die_press_line` varchar(100) DEFAULT NULL COMMENT '啤模压线',
  `press_size_length` decimal(10,2) DEFAULT NULL COMMENT '压线尺寸(纸长)',
  `inbound_count` int DEFAULT NULL COMMENT '已入库数',
  `materials_received_count` int DEFAULT NULL COMMENT '已领料数',
  `created_by` varchar(50) NOT NULL,
  `created_time` datetime(6) NOT NULL,
  `is_deleted` bit(1) NOT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `version` int DEFAULT NULL,
  `created_by_name` varchar(50) DEFAULT NULL,
  `updated_by_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order_item_id` (`order_item_id`),
  KEY `idx_paper_quality` (`paper_quality`),
  KEY `idx_die_model_no` (`die_model_no`),
  KEY `idx_supplier` (`supplier`),
  CONSTRAINT `sales_order_material_ibfk_1` FOREIGN KEY (`order_item_id`) REFERENCES `sales_order_item` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售订单材料信息表' |