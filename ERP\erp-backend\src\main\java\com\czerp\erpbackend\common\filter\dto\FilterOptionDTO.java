package com.czerp.erpbackend.common.filter.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用筛选选项DTO
 * 用于返回筛选下拉框的选项数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterOptionDTO {
    
    /**
     * 显示文本
     */
    private String label;
    
    /**
     * 筛选值
     */
    private String value;
    
    /**
     * 该选项对应的记录数（已废弃，保留用于向后兼容）
     */
    private Long count;
    
    /**
     * 创建筛选选项（推荐使用）
     * @param value 值
     * @return 筛选选项
     */
    public static FilterOptionDTO of(String value) {
        return FilterOptionDTO.builder()
                .label(value)
                .value(value)
                .count(null) // 不再使用计数
                .build();
    }

    /**
     * 创建筛选选项（已废弃，保留用于向后兼容）
     * @param value 值
     * @param count 记录数（已废弃）
     * @return 筛选选项
     */
    @Deprecated
    public static FilterOptionDTO of(String value, Long count) {
        return FilterOptionDTO.builder()
                .label(value)
                .value(value)
                .count(null) // 忽略计数参数
                .build();
    }
}
