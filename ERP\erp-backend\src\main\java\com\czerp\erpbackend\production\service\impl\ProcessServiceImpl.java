package com.czerp.erpbackend.production.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.production.dto.CreateProcessRequest;
import com.czerp.erpbackend.production.dto.ProcessDTO;
import com.czerp.erpbackend.production.dto.ProcessQueryRequest;
import com.czerp.erpbackend.production.dto.UpdateProcessRequest;
import com.czerp.erpbackend.production.entity.Process;
import com.czerp.erpbackend.production.mapper.ProcessMapper;
import com.czerp.erpbackend.production.repository.ProcessRepository;
import com.czerp.erpbackend.production.service.ProcessService;
import com.czerp.erpbackend.system.entity.User;
import com.czerp.erpbackend.system.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 工序服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProcessServiceImpl implements ProcessService {
    
    private final ProcessRepository processRepository;
    private final ProcessMapper processMapper;
    private final UserRepository userRepository;
    
    /**
     * 分页查询工序列表
     * @param request 查询请求
     * @return 工序分页列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<ProcessDTO> findProcesses(ProcessQueryRequest request) {
        log.debug("Finding processes with request: {}", request);
        
        // 构建分页参数
        int page = request.getPage() != null ? request.getPage() - 1 : 0;
        int size = request.getSize() != null ? request.getSize() : 10;
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdTime"));
        
        // 查询工序
        Page<Process> processPage = processRepository.search(
                request.getKeyword(),
                request.getStartDateTime(),
                request.getEndDateTime(),
                pageable);
        
        // 转换为DTO
        List<ProcessDTO> processDTOs = processMapper.toDtoList(processPage.getContent());
        
        // 构建分页响应
        return PageResponse.<ProcessDTO>builder()
                .content(processDTOs)
                .page(page + 1)
                .size(size)
                .totalElements(processPage.getTotalElements())
                .totalPages(processPage.getTotalPages())
                .build();
    }
    
    /**
     * 查询所有工序
     * @return 工序列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<ProcessDTO> findAllProcesses() {
        log.debug("Finding all processes");
        
        // 查询所有工序
        List<Process> processes = processRepository.findAll(Sort.by(Sort.Direction.ASC, "id"));
        
        // 转换为DTO
        return processMapper.toDtoList(processes);
    }
    
    /**
     * 根据ID查询工序
     * @param id 工序ID
     * @return 工序信息
     */
    @Override
    @Transactional(readOnly = true)
    public ProcessDTO findProcessById(Long id) {
        log.debug("Finding process by id: {}", id);
        
        // 查询工序
        Process process = processRepository.findById(id)
                .orElseThrow(() -> new BusinessException("工序不存在"));
        
        // 转换为DTO
        return processMapper.toDto(process);
    }
    
    /**
     * 创建工序
     * @param request 创建请求
     * @return 工序信息
     */
    @Override
    @Transactional
    public ProcessDTO createProcess(CreateProcessRequest request) {
        log.debug("Creating process with request: {}", request);
        
        // 检查工序名称是否已存在
        if (processRepository.existsByProcess(request.getProcess())) {
            throw new BusinessException("工序名称已存在");
        }
        
        // 创建工序
        Process process = processMapper.toEntity(request);
        
        // 设置创建人信息
        setCreatedInfo(process);
        
        // 保存工序
        process = processRepository.save(process);
        
        // 转换为DTO
        return processMapper.toDto(process);
    }
    
    /**
     * 更新工序
     * @param id 工序ID
     * @param request 更新请求
     * @return 工序信息
     */
    @Override
    @Transactional
    public ProcessDTO updateProcess(Long id, UpdateProcessRequest request) {
        log.debug("Updating process with id: {} and request: {}", id, request);
        
        // 查询工序
        Process process = processRepository.findById(id)
                .orElseThrow(() -> new BusinessException("工序不存在"));
        
        // 检查工序名称是否已存在（排除自身）
        if (request.getProcess() != null && !request.getProcess().equals(process.getProcess()) &&
                processRepository.existsByProcessAndIdNot(request.getProcess(), id)) {
            throw new BusinessException("工序名称已存在");
        }
        
        // 更新工序
        processMapper.updateEntity(request, process);
        
        // 设置更新人信息
        setUpdatedInfo(process);
        
        // 保存工序
        process = processRepository.save(process);
        
        // 转换为DTO
        return processMapper.toDto(process);
    }
    
    /**
     * 删除工序
     * @param id 工序ID
     */
    @Override
    @Transactional
    public void deleteProcess(Long id) {
        log.debug("Deleting process with id: {}", id);
        
        // 查询工序
        Process process = processRepository.findById(id)
                .orElseThrow(() -> new BusinessException("工序不存在"));
        
        // 逻辑删除
        process.setIsDeleted(true);
        process.setUpdatedTime(LocalDateTime.now());
        
        // 设置更新人信息
        setUpdatedInfo(process);
        
        // 保存工序
        processRepository.save(process);
    }
    
    /**
     * 批量删除工序
     * @param ids 工序ID列表
     */
    @Override
    @Transactional
    public void batchDeleteProcesses(List<Long> ids) {
        log.debug("Batch deleting processes with ids: {}", ids);
        
        // 查询工序列表
        List<Process> processes = processRepository.findAllById(ids);
        
        // 逻辑删除
        for (Process process : processes) {
            process.setIsDeleted(true);
            process.setUpdatedTime(LocalDateTime.now());
            
            // 设置更新人信息
            setUpdatedInfo(process);
        }
        
        // 保存工序
        processRepository.saveAll(processes);
    }
    
    /**
     * 设置创建人信息
     * @param process 工序
     */
    private void setCreatedInfo(Process process) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            String username = authentication.getName();
            process.setCreatedBy(username);
            process.setUpdatedBy(username);
            
            // 设置创建人姓名
            Optional<User> userOptional = userRepository.findByUsername(username);
            if (userOptional.isPresent()) {
                User user = userOptional.get();
                process.setCreatedByName(user.getNickname());
                process.setUpdatedByName(user.getNickname());
            }
        }
        
        LocalDateTime now = LocalDateTime.now();
        process.setCreatedTime(now);
        process.setUpdatedTime(now);
    }
    
    /**
     * 设置更新人信息
     * @param process 工序
     */
    private void setUpdatedInfo(Process process) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            String username = authentication.getName();
            process.setUpdatedBy(username);
            
            // 设置更新人姓名
            Optional<User> userOptional = userRepository.findByUsername(username);
            if (userOptional.isPresent()) {
                User user = userOptional.get();
                process.setUpdatedByName(user.getNickname());
            }
        }
        
        process.setUpdatedTime(LocalDateTime.now());
    }
}
