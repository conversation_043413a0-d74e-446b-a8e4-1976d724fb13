package com.czerp.erpbackend.purchase.service;

import com.czerp.erpbackend.inventory.entity.StockInbound;
import com.czerp.erpbackend.inventory.entity.StockInboundItem;
import com.czerp.erpbackend.inventory.repository.StockInboundItemRepository;
import com.czerp.erpbackend.inventory.repository.StockInboundRepository;
import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 采购订单明细动态计算服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class PurchaseOrderItemCalculationServiceTest {

    @Autowired
    private PurchaseOrderItemCalculationService calculationService;

    @Autowired
    private PurchaseOrderRepository purchaseOrderRepository;

    @Autowired
    private PurchaseOrderItemRepository purchaseOrderItemRepository;

    @Autowired
    private StockInboundRepository stockInboundRepository;

    @Autowired
    private StockInboundItemRepository stockInboundItemRepository;

    private PurchaseOrder testPurchaseOrder;
    private PurchaseOrderItem testItem1; // 部分入库
    private PurchaseOrderItem testItem2; // 完全入库
    private PurchaseOrderItem testItem3; // 从未入库
    private StockInbound testStockInbound;

    @BeforeEach
    void setUp() {
        // 创建测试采购订单
        testPurchaseOrder = new PurchaseOrder();
        testPurchaseOrder.setPurchaseOrderNo("TEST-CALC-001");
        testPurchaseOrder.setPurchaseDate(LocalDate.now());
        testPurchaseOrder.setSupplierName("测试供应商");
        testPurchaseOrder = purchaseOrderRepository.save(testPurchaseOrder);

        // 创建测试采购订单明细1（部分入库）
        testItem1 = new PurchaseOrderItem();
        testItem1.setPurchaseOrder(testPurchaseOrder);
        testItem1.setQuantity(100);
        testItem1.setPrice(BigDecimal.valueOf(10.0));
        testItem1.setAmount(BigDecimal.valueOf(1000.0));
        testItem1 = purchaseOrderItemRepository.save(testItem1);

        // 创建测试采购订单明细2（完全入库）
        testItem2 = new PurchaseOrderItem();
        testItem2.setPurchaseOrder(testPurchaseOrder);
        testItem2.setQuantity(200);
        testItem2.setPrice(BigDecimal.valueOf(15.0));
        testItem2.setAmount(BigDecimal.valueOf(3000.0));
        testItem2 = purchaseOrderItemRepository.save(testItem2);

        // 创建测试采购订单明细3（从未入库）
        testItem3 = new PurchaseOrderItem();
        testItem3.setPurchaseOrder(testPurchaseOrder);
        testItem3.setQuantity(150);
        testItem3.setPrice(BigDecimal.valueOf(12.0));
        testItem3.setAmount(BigDecimal.valueOf(1800.0));
        testItem3 = purchaseOrderItemRepository.save(testItem3);

        // 创建测试入库单
        testStockInbound = new StockInbound();
        testStockInbound.setInboundNo("RK20240101001");
        testStockInbound.setInboundDate(LocalDate.now());
        testStockInbound.setSupplierName("测试供应商");
        testStockInbound.setIsDeleted(false);
        testStockInbound = stockInboundRepository.save(testStockInbound);

        // 创建入库单明细1（部分入库testItem1）
        StockInboundItem inboundItem1 = new StockInboundItem();
        inboundItem1.setStockInbound(testStockInbound);
        inboundItem1.setPurchaseOrderItem(testItem1);
        inboundItem1.setQuantity(60); // 入库60，还剩40未入库
        inboundItem1.setIsDeleted(false);
        stockInboundItemRepository.save(inboundItem1);

        // 创建入库单明细2（完全入库testItem2）
        StockInboundItem inboundItem2a = new StockInboundItem();
        inboundItem2a.setStockInbound(testStockInbound);
        inboundItem2a.setPurchaseOrderItem(testItem2);
        inboundItem2a.setQuantity(120); // 第一次入库120
        inboundItem2a.setIsDeleted(false);
        stockInboundItemRepository.save(inboundItem2a);

        StockInboundItem inboundItem2b = new StockInboundItem();
        inboundItem2b.setStockInbound(testStockInbound);
        inboundItem2b.setPurchaseOrderItem(testItem2);
        inboundItem2b.setQuantity(80); // 第二次入库80，总计200，完全入库
        inboundItem2b.setIsDeleted(false);
        stockInboundItemRepository.save(inboundItem2b);

        // testItem3没有入库记录
    }

    @Test
    @DisplayName("单个计算 - 部分入库的明细")
    void testCalculateReceivedQuantityPartial() {
        Integer receivedQty = calculationService.calculateReceivedQuantity(testItem1.getId());
        assertEquals(Integer.valueOf(60), receivedQty, "部分入库明细的已入库数应为60");
    }

    @Test
    @DisplayName("单个计算 - 完全入库的明细")
    void testCalculateReceivedQuantityFull() {
        Integer receivedQty = calculationService.calculateReceivedQuantity(testItem2.getId());
        assertEquals(Integer.valueOf(200), receivedQty, "完全入库明细的已入库数应为200");
    }

    @Test
    @DisplayName("单个计算 - 从未入库的明细")
    void testCalculateReceivedQuantityNever() {
        Integer receivedQty = calculationService.calculateReceivedQuantity(testItem3.getId());
        assertEquals(Integer.valueOf(0), receivedQty, "从未入库明细的已入库数应为0");
    }

    @Test
    @DisplayName("批量计算 - 多个明细的已入库数量")
    void testCalculateReceivedQuantitiesBatch() {
        List<Long> itemIds = Arrays.asList(testItem1.getId(), testItem2.getId(), testItem3.getId());
        Map<Long, Integer> receivedQuantityMap = calculationService.calculateReceivedQuantitiesBatch(itemIds);

        assertEquals(3, receivedQuantityMap.size(), "应该返回3个明细的计算结果");
        assertEquals(Integer.valueOf(60), receivedQuantityMap.get(testItem1.getId()), "明细1已入库数应为60");
        assertEquals(Integer.valueOf(200), receivedQuantityMap.get(testItem2.getId()), "明细2已入库数应为200");
        assertEquals(Integer.valueOf(0), receivedQuantityMap.get(testItem3.getId()), "明细3已入库数应为0");
    }

    @Test
    @DisplayName("未入库数量计算")
    void testCalculateUnreceivedQuantity() {
        Integer unreceived1 = calculationService.calculateUnreceivedQuantity(testItem1.getId(), testItem1.getQuantity(), 0);
        Integer unreceived2 = calculationService.calculateUnreceivedQuantity(testItem2.getId(), testItem2.getQuantity(), 0);
        Integer unreceived3 = calculationService.calculateUnreceivedQuantity(testItem3.getId(), testItem3.getQuantity(), 0);

        assertEquals(Integer.valueOf(40), unreceived1, "明细1未入库数应为40");
        assertEquals(Integer.valueOf(0), unreceived2, "明细2未入库数应为0");
        assertEquals(Integer.valueOf(150), unreceived3, "明细3未入库数应为150");
    }

    @Test
    @DisplayName("可用性检查 - 单个明细")
    void testIsAvailableForInbound() {
        boolean available1 = calculationService.isAvailableForInbound(testItem1.getId(), testItem1.getQuantity());
        boolean available2 = calculationService.isAvailableForInbound(testItem2.getId(), testItem2.getQuantity());
        boolean available3 = calculationService.isAvailableForInbound(testItem3.getId(), testItem3.getQuantity());

        assertTrue(available1, "部分入库的明细应该可用于入库");
        assertFalse(available2, "完全入库的明细不应该可用于入库");
        assertTrue(available3, "从未入库的明细应该可用于入库");
    }

    @Test
    @DisplayName("可用性检查 - 批量明细")
    void testIsAvailableForInboundBatch() {
        List<Long> itemIds = Arrays.asList(testItem1.getId(), testItem2.getId(), testItem3.getId());
        Map<Long, Integer> quantityMap = Map.of(
                testItem1.getId(), testItem1.getQuantity(),
                testItem2.getId(), testItem2.getQuantity(),
                testItem3.getId(), testItem3.getQuantity()
        );

        Map<Long, Boolean> availabilityMap = calculationService.isAvailableForInboundBatch(itemIds, quantityMap);

        assertEquals(3, availabilityMap.size(), "应该返回3个明细的可用性结果");
        assertTrue(availabilityMap.get(testItem1.getId()), "明细1应该可用于入库");
        assertFalse(availabilityMap.get(testItem2.getId()), "明细2不应该可用于入库");
        assertTrue(availabilityMap.get(testItem3.getId()), "明细3应该可用于入库");
    }

    @Test
    @DisplayName("边界情况 - 空ID列表")
    void testEmptyIdList() {
        Map<Long, Integer> result = calculationService.calculateReceivedQuantitiesBatch(Arrays.asList());
        assertTrue(result.isEmpty(), "空ID列表应该返回空结果");
    }

    @Test
    @DisplayName("边界情况 - null参数")
    void testNullParameters() {
        Integer result = calculationService.calculateReceivedQuantity(null);
        assertEquals(Integer.valueOf(0), result, "null ID应该返回0");

        Map<Long, Integer> batchResult = calculationService.calculateReceivedQuantitiesBatch(null);
        assertTrue(batchResult.isEmpty(), "null ID列表应该返回空结果");
    }
}
