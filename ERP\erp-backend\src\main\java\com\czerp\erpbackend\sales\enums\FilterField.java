package com.czerp.erpbackend.sales.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 筛选字段枚举
 * 定义销售订单查询中支持筛选的字段及其元数据
 */
@Getter
@RequiredArgsConstructor
public enum FilterField {
    
    // ========== 动态查询字段（从数据库查询） ==========
    
    /**
     * 客户名称 - 从销售订单表查询
     */
    CUSTOMER_NAME("customerName", "customerName", JoinSource.ROOT, FilterType.DYNAMIC_QUERY, null),
    
    /**
     * 产品名称 - 从销售订单明细表查询
     */
    PRODUCT_NAME("productName", "productName", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),
    
    /**
     * 销售员 - 从销售订单表查询
     */
    SALES_PERSON("salesPerson", "salesPerson", JoinSource.ROOT, FilterType.DYNAMIC_QUERY, null),
    
    /**
     * 生产单号 - 从销售订单明细表查询
     */
    PRODUCTION_ORDER_NO("productionOrderNo", "productionOrderNo", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),
    
    /**
     * 销售单号 - 从销售订单表查询
     */
    ORDER_NO("orderNo", "orderNo", JoinSource.ROOT, FilterType.DYNAMIC_QUERY, null),
    
    /**
     * 客户订单号 - 从销售订单明细表查询
     */
    CUSTOMER_ORDER_NO("customerOrderNo", "customerOrderNo", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),
    
    /**
     * 客方货号 - 从销售订单明细表查询
     */
    CUSTOMER_PRODUCT_CODE("customerProductCode", "customerProductCode", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),
    
    /**
     * 纸质 - 从销售订单明细表查询
     */
    PAPER_TYPE("paperType", "paperType", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),

    /**
     * 生产纸质 - 从销售订单明细表查询
     */
    PRODUCTION_PAPER_TYPE("productionPaperType", "productionPaperType", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),

    /**
     * 工艺要求 - 从销售订单明细表查询
     */
    PROCESS_REQUIREMENTS("processRequirements", "processRequirements", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),

    /**
     * 盒式 - 从销售订单明细表查询（存储的是盒式名称，不是编码）
     */
    BOX_TYPE("boxType", "boxType", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),

    /**
     * 楞别 - 从销售订单明细表查询（存储的是实际楞别名称）
     */
    CORRUGATION_TYPE("corrugationType", "corrugationType", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),

    /**
     * 连接方式 - 从销售订单明细表查询（存储的是实际连接方式）
     */
    CONNECTION_METHOD("connectionMethod", "connectionMethod", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),

    /**
     * 钉位 - 从销售订单明细表查询（存储的是实际钉位信息）
     */
    STAPLE_POSITION("staplePosition", "staplePosition", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),

    /**
     * 单位 - 从销售订单明细表查询（存储的是实际单位）
     */
    UNIT("unit", "unit", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),

    /**
     * 币种 - 从销售订单明细表查询（存储的是实际币种）
     */
    CURRENCY("currency", "currency", JoinSource.ITEM, FilterType.DYNAMIC_QUERY, null),

    /**
     * 采购供应商 - 通过采购订单表子查询获取（需要特殊处理）
     */
    SUPPLIER_NAME("supplierName", "supplierName", JoinSource.SUBQUERY, FilterType.DYNAMIC_QUERY, null),

    /**
     * 纸板类别 - 通过采购订单明细表子查询获取（需要特殊处理）
     */
    PAPER_TYPE_NAME("paperTypeName", "paperBoardCategory", JoinSource.SUBQUERY, FilterType.DYNAMIC_QUERY, null),

    /**
     * 采购单号 - 通过采购订单表子查询获取（需要特殊处理）
     */
    PURCHASE_ORDER_NO("purchaseOrderNo", "purchaseOrderNo", JoinSource.SUBQUERY, FilterType.DYNAMIC_QUERY, null),

    /**
     * 合订规格 - 通过采购订单明细表子查询获取（需要特殊处理）
     */
    BINDING_SPECIFICATION("bindingSpecification", "bindingSpecification", JoinSource.SUBQUERY, FilterType.DYNAMIC_QUERY, null),

    /**
     * 压线尺寸 - 通过销售订单用料表子查询获取（需要特殊处理）
     */
    PRESS_SIZE_WIDTH("pressSizeWidth", "pressSizeWidth", JoinSource.SUBQUERY, FilterType.DYNAMIC_QUERY, null),

    /**
     * 工序 - 通过销售订单工序表子查询获取（需要特殊处理）
     */
    PROCESSES("processes", "processName", JoinSource.SUBQUERY, FilterType.DYNAMIC_QUERY, null),

    /**
     * 规格 - 动态构建字段（长×宽×高 单位格式）
     */
    SPECIFICATION("specification", "specification", JoinSource.SUBQUERY, FilterType.DYNAMIC_QUERY, null),

    /**
     * 生产规格 - 动态构建字段（生产长×生产宽×生产高 单位格式）
     */
    PRODUCTION_SPECIFICATION("productionSpecification", "productionSpecification", JoinSource.SUBQUERY, FilterType.DYNAMIC_QUERY, null);

    /**
     * 字段名称（前端传递的字段名）
     */
    private final String fieldName;
    
    /**
     * 实体字段名称（数据库字段名）
     */
    private final String entityFieldName;
    
    /**
     * 连接源（ROOT表示主表，ITEM表示明细表）
     */
    private final JoinSource joinSource;
    
    /**
     * 筛选类型
     */
    private final FilterType filterType;
    
    /**
     * 字典编码（仅静态字典字段使用）
     */
    private final String dictionaryCode;
    
    /**
     * 根据字段名称查找对应的筛选字段枚举
     * @param fieldName 字段名称
     * @return 筛选字段枚举，如果未找到则返回null
     */
    public static FilterField findByFieldName(String fieldName) {
        for (FilterField field : values()) {
            if (field.getFieldName().equals(fieldName)) {
                return field;
            }
        }
        return null;
    }
    
    /**
     * 检查字段是否为动态查询类型
     * @return 是否为动态查询类型
     */
    public boolean isDynamicQuery() {
        return FilterType.DYNAMIC_QUERY.equals(this.filterType);
    }
    
    /**
     * 检查字段是否为静态字典类型
     * @return 是否为静态字典类型
     */
    public boolean isStaticDictionary() {
        return FilterType.STATIC_DICTIONARY.equals(this.filterType);
    }
    
    /**
     * 连接源枚举
     */
    public enum JoinSource {
        /**
         * 主表（sales_order）
         */
        ROOT,

        /**
         * 明细表（sales_order_item）
         */
        ITEM,

        /**
         * 子查询（需要通过其他表关联查询）
         */
        SUBQUERY
    }
    
    /**
     * 筛选类型枚举
     */
    public enum FilterType {
        /**
         * 动态查询 - 从数据库查询获取选项
         */
        DYNAMIC_QUERY,
        
        /**
         * 静态字典 - 从字典服务获取选项
         */
        STATIC_DICTIONARY
    }
}
