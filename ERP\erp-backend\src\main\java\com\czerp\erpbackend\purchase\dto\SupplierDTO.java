package com.czerp.erpbackend.purchase.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 供应商DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierDTO {
    
    /**
     * 供应商ID
     */
    private Long id;
    
    /**
     * 供应商编码
     */
    private String supplierCode;
    
    /**
     * 供应商名称
     */
    private String supplierName;
    
    /**
     * 供应商全称
     */
    private String fullName;
    
    /**
     * 供应商分类ID
     */
    private String categoryId;
    
    /**
     * 供应商分类名称
     */
    private String categoryName;
    
    /**
     * 行业
     */
    private String industry;
    
    /**
     * 电话
     */
    private String phone;
    
    /**
     * 传真
     */
    private String fax;
    
    /**
     * 联系人
     */
    private String contactPerson;
    
    /**
     * 手机
     */
    private String mobile;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 地区
     */
    private String region;
    
    /**
     * 最近下单日期
     */
    private LocalDate lastOrderDate;
    
    /**
     * 最近收货日期
     */
    private LocalDate lastReceiveDate;
    
    /**
     * 单价小数位
     */
    private Integer priceDecimalPlaces;
    
    /**
     * 金额小数位
     */
    private Integer amountDecimalPlaces;
    
    /**
     * 单重小数位
     */
    private Integer unitWeightDecimalPlaces;
    
    /**
     * 总重小数位
     */
    private Integer totalWeightDecimalPlaces;
    
    /**
     * 未下单天数
     */
    private Integer daysWithoutOrder;
    
    /**
     * 未收货天数
     */
    private Integer daysWithoutReceive;
    
    /**
     * 税号
     */
    private String taxNumber;
    
    /**
     * 开户行
     */
    private String bankName;
    
    /**
     * 银行账号
     */
    private String bankAccount;
    
    /**
     * 付款条件
     */
    private String paymentTerms;
    
    /**
     * 状态(active-启用,inactive-停用)
     */
    private String status;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
