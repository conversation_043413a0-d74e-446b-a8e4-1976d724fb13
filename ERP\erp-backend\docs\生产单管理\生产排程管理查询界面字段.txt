排程单号    production_schedule表中schedule_no获取	
排程日期	production_schedule表中schedule_date获取
急单	复选框
已打印	复选框
最近打印时间	
生产单号	sales_order_item表的production_order_no字段获取
订单日期	sales_order表的order_date字段获取
交期	sales_order表的order_date字段获取
客户分类	统一为“订单客户"
客户名称	sales_order表中获取
客户订单号	sales_order表中获取
客方货号	sales_order表中获取
[工艺要求]	sales_order表中获取
品名	sales_order表中获取
盒式	sales_order表中获取
纸质	sales_order表中获取
生产纸质	sales_order表中获取
规格	sales_order表中获取
订单数	sales_order表中获取
备品数	sales_order表中获取
排程数量	production_schedule_item表的schedule_quantity字段获取
采购数量	purchase_order_item表的quantity获取
到料数量	stock_inbound_item表的quantity获取
到料日期	stock_inbound表的inbound_date获取
计划完成日期	sales_order_item表的delivery_date字段获取
单价	sales_order表中获取
总金额	sales_order表中获取
样品	复选框
单重	sales_order表中获取(如果有的话)
总重	sales_order表中获取(如果有的话)
产品面积	sales_order表中获取(如果有的话)
总面积(平米)	sales_order表中获取(如果有的话)
产品体积	sales_order表中获取(如果有的话)
总体积(立方米)	sales_order表中获取(如果有的话)
纸度	sales_order_material表中paper_width获取
纸长	sales_order_material表中paper_length获取
度开	sales_order_material表中width_open获取
长开	sales_order_material表中length_open获取
备注	sales_order_item表中remark获取
生产备注	sales_order_item表中production_remark获取
销售单号	sales_order表中order_no获取
订单类型	统一为“销售订单"
工序	特殊字段需要参考“销售订单管理查询接口”的这个字段"processes": 返回格式："分纸→开槽→粘箱→包装"
创建人	production_schedule表中created_by获取
创建时间	production_schedule表中schedule_date获取
