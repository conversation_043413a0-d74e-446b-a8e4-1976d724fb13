package com.czerp.erpbackend.inventory.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.inventory.dto.CreateStockInboundRequest;
import com.czerp.erpbackend.inventory.dto.StockInboundDTO;
import com.czerp.erpbackend.inventory.dto.StockInboundItemDTO;
import com.czerp.erpbackend.inventory.dto.StockInboundQueryRequest;
import com.czerp.erpbackend.inventory.dto.UpdateStockInboundRequest;

/**
 * 入库单服务接口
 */
public interface StockInboundService {

    /**
     * 生成入库单号
     * @return 入库单号
     */
    String generateInboundNo();

    /**
     * 创建入库单
     * @param request 创建请求
     * @return 入库单DTO
     */
    StockInboundDTO createStockInbound(CreateStockInboundRequest request);

    /**
     * 更新入库单
     * @param id 入库单ID
     * @param request 更新请求
     * @return 入库单DTO
     */
    StockInboundDTO updateStockInbound(Long id, UpdateStockInboundRequest request);

    /**
     * 根据ID查询入库单
     * @param id 入库单ID
     * @return 入库单DTO
     */
    StockInboundDTO getStockInboundById(Long id);

    /**
     * 根据入库单号查询入库单
     * @param inboundNo 入库单号
     * @return 入库单DTO
     */
    StockInboundDTO getStockInboundByNo(String inboundNo);

    /**
     * 删除入库单
     * @param id 入库单ID
     */
    void deleteStockInbound(Long id);

    /**
     * 分页查询入库单（按订单级别分页）
     * @param request 查询请求
     * @return 入库单分页列表
     */
    PageResponse<StockInboundDTO> findStockInbounds(StockInboundQueryRequest request);

    /**
     * 分页查询入库单明细（按明细行级别分页）
     * @param request 查询请求
     * @return 入库单明细分页列表
     */
    PageResponse<StockInboundItemDTO> findStockInboundItems(StockInboundQueryRequest request);

    /**
     * 优化的入库明细查询方法：使用JOIN查询避免N+1问题
     * @param request 查询请求
     * @return 分页结果
     */
    PageResponse<StockInboundItemDTO> findStockInboundItemsOptimized(StockInboundQueryRequest request);

    /**
     * 原始查询方法（保留作为备用和性能对比）
     * @param request 查询请求
     * @return 分页结果
     */
    PageResponse<StockInboundItemDTO> findStockInboundItemsOriginal(StockInboundQueryRequest request);
}
