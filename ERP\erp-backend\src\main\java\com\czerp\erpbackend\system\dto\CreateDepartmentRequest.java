package com.czerp.erpbackend.system.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 创建部门请求
 */
@Data
public class CreateDepartmentRequest {
    
    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 50, message = "部门名称长度不能超过50个字符")
    private String name;
    
    /**
     * 部门编码
     */
    @NotBlank(message = "部门编码不能为空")
    @Size(max = 50, message = "部门编码长度不能超过50个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "部门编码只能包含字母、数字和下划线")
    private String code;
    
    /**
     * 父部门ID
     */
    private String parentId;
    
    /**
     * 部门负责人ID
     */
    private String managerId;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 状态
     */
    @Pattern(regexp = "^(active|inactive)?$", message = "状态值不正确，可选值：active, inactive")
    private String status;
} 