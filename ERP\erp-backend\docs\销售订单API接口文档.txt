销售订单API接口文档
创建销售订单
接口信息
URL: /api/sales/orders
方法: POST
描述: 创建新的销售订单
权限要求: 需要 sales:order:create 权限
认证方式: Bearer Token
请求参数
请求体为JSON格式，包含完整的销售订单信息（SalesOrderDetailDTO）：
{
  "orderNo": "SO2023060100001",  // 订单编号，可通过 /api/sales/orders/generate-order-no 接口获取
  "orderDate": "2023-06-01",     // 订单日期
  "paymentMethod": "转账",        // 付款方式
  "customerCode": "C001",        // 客户编码
  "customerName": "测试客户",      // 客户名称
  "salesPerson": "销售员",        // 销售员
  "customerPurchaser": "采购员",   // 客户采购员
  "receivingUnit": "收货单位",     // 收货单位
  "receiver": "收货人",           // 收货人
  "receiverPhone": "13800138000", // 收货人电话
  "receivingAddress": "收货地址",  // 收货地址
  "remark": "订单备注",           // 备注
  "orderType": "常规订单",        // 订单类型
  "items": [                     // 订单明细列表
    {
      "customerOrderNo": "CO001",       // 客户订单号
      "customerProductCode": "CP001",   // 客方货号
      "productName": "产品名称",         // 品名
      "processRequirements": "工艺要求",  // 工艺要求
      "isTaxed": true,                  // 是否含税
      "boxType": "盒式",                // 盒式
      "paperType": "纸质",              // 纸质
      "corrugationType": "楞别",        // 楞别
      "productionPaperType": "生产纸质",  // 生产纸质
      "length": 100,                    // 长
      "width": 200,                     // 宽
      "height": 300,                    // 高
      "sizeUnit": "mm",                 // 尺寸单位
      "quantity": 1000,                 // 数量
      "spareQuantity": 100,             // 备品数
      "price": 10.5,                    // 单价
      "amount": 10500,                  // 金额
      "deliveryDate": "2023-06-30",     // 送货日期
      "isSpecialPrice": false,          // 特价
      "paperQuotation": 8.5,            // 纸质报价
      "connectionMethod": "连接方式",     // 连接方式
      "staplePosition": "钉口",          // 钉口
      "packagingCount": 50,             // 包装数
      "productionRemark": "生产备注",     // 生产备注
      "remark": "明细备注",              // 备注
      "productionLength": 110,          // 生产长
      "productionWidth": 210,           // 生产宽
      "productionHeight": 310,          // 生产高
      "spareRatio": 0.1,                // 成套比例
      "isSpecialSpecification": false,  // 特殊规格
      "unitWeight": 0.5,                // 单重
      "totalWeight": 500,               // 总重(KG)
      "productArea": 2,                 // 产品面积
      "totalArea": 2000,                // 总面积(平米)
      "productVolume": 0.006,           // 产品体积
      "totalVolume": 6,                 // 总体积(立方米)
      "component": "部件",               // 部件
      "isSample": false,                // 样品
      "unit": "个",                     // 单位
      "currency": "CNY",                // 币种
      "taxRate": 0.13                   // 税率
    }
  ]
}

响应结果
响应为统一的 ApiResponse<SalesOrderDetailDTO> 格式：
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": "order-id-123",
    "orderNo": "SO2023060100001",
    "orderDate": "2023-06-01",
    "paymentMethod": "转账",
    "customerCode": "C001",
    "customerName": "测试客户",
    "salesPerson": "销售员",
    "customerPurchaser": "采购员",
    "receivingUnit": "收货单位",
    "receiver": "收货人",
    "receiverPhone": "13800138000",
    "receivingAddress": "收货地址",
    "remark": "订单备注",
    "orderType": "常规订单",
    "createdBy": "admin",
    "createdByName": "管理员",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": null,
    "updatedByName": null,
    "updatedTime": null,
    "items": [
      {
        "id": "item-id-123",
        "orderId": "order-id-123",
        "productionOrderNo": "PO2023060100001",  // 系统自动生成的生产单号
        "customerOrderNo": "CO001",
        "customerProductCode": "CP001",
        "productName": "产品名称",
        "processRequirements": "工艺要求",
        "isTaxed": true,
        "boxType": "盒式",
        "paperType": "纸质",
        "corrugationType": "楞别",
        "productionPaperType": "生产纸质",
        "length": 100,
        "width": 200,
        "height": 300,
        "sizeUnit": "mm",
        "quantity": 1000,
        "spareQuantity": 100,
        "price": 10.5,
        "amount": 10500,
        "deliveryDate": "2023-06-30",
        "isSpecialPrice": false,
        "paperQuotation": 8.5,
        "connectionMethod": "连接方式",
        "staplePosition": "钉口",
        "packagingCount": 50,
        "productionRemark": "生产备注",
        "remark": "明细备注",
        "productionLength": 110,
        "productionWidth": 210,
        "productionHeight": 310,
        "spareRatio": 0.1,
        "spareQuantityTotal": 100,
        "currentInventory": 0,
        "availableInventory": 0,
        "useInventory": 0,
        "isSpecialSpecification": false,
        "unitWeight": 0.5,
        "totalWeight": 500,
        "productArea": 2,
        "totalArea": 2000,
        "productVolume": 0.006,
        "totalVolume": 6,
        "component": "部件",
        "isSample": false,
        "deliveredQuantity": 0,
        "deliveredSpareQuantity": 0,
        "returnedQuantity": 0,
        "safetyStock": 0,
        "reconciliationQuantity": 0,
        "unit": "个",
        "currency": "CNY",
        "taxRate": 0.13,
        "createdBy": "admin",
        "createdTime": "2023-06-01T10:00:00",
        "updatedBy": null,
        "updatedTime": null
      }
    ]
  }
}

错误码
错误码	描述
400	请求参数错误
401	未授权
403	权限不足
500	服务器内部错误
注意事项
创建订单时，系统会自动为每个订单明细生成生产单号
客户编码必须存在于系统中，否则会返回错误
订单号可以通过 /api/sales/orders/generate-order-no 接口获取，也可以手动指定
创建订单时，不需要提供 id、createdBy、createdTime 等字段，这些字段会由系统自动填充
生成订单号
接口信息
URL: /api/sales/orders/generate-order-no
方法: GET
描述: 生成新的销售订单号
权限要求: 需要 sales:order:create 权限
认证方式: Bearer Token
请求参数
无
响应结果:
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": "SO2023060100001"  // 生成的订单号
}

订单号格式
订单号格式为：SO + 年月日 + 5位序号，例如：SO2023060100001
SO: 固定前缀，表示销售订单
20230601: 当前日期，格式为yyyyMMdd
00001: 5位序号，从00001开始，每天重新计数
查询销售订单
接口信息
URL: /api/sales/orders
方法: GET
描述: 分页查询销售订单列表
权限要求: 需要 sales:order:read 权限
认证方式: Bearer Token
请求参数
参数名	类型	必填	描述
keyword	string	否	关键字搜索（订单号、客户名称等）
orderNo	string	否	销售单号
productionOrderNo	string	否	生产单号
customerName	string	否	客户名称
customerOrderNo	string	否	客户订单号
customerProductCode	string	否	客方货号
productName	string	否	品名
startDate	string	否	开始日期（格式：yyyy-MM-dd）
endDate	string	否	结束日期（格式：yyyy-MM-dd）
salesPerson	string	否	销售员
page	integer	否	页码（默认：1）
pageSize	integer	否	每页大小（默认：10）
sortField	string	否	排序字段
sortOrder	string	否	排序方向（asc/desc）
响应结果
响应为统一的 ApiResponse<PageResponse<SalesOrderDTO>> 格式：
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": "order-id-123",
        "orderNo": "SO2023060100001",
        "orderDate": "2023-06-01",
        "paymentMethod": "转账",
        "customerCode": "C001",
        "customerName": "测试客户",
        "salesPerson": "销售员",
        "customerPurchaser": "采购员",
        "receivingUnit": "收货单位",
        "receiver": "收货人",
        "receiverPhone": "13800138000",
        "receivingAddress": "收货地址",
        "remark": "订单备注",
        "orderType": "常规订单",
        "createdBy": "admin",
        "createdByName": "管理员",
        "createdTime": "2023-06-01T10:00:00",
        "updatedBy": null,
        "updatedByName": null,
        "updatedTime": null
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "page": 1,
    "size": 10,
    "first": true,
    "last": true,
    "empty": false
  }
}

获取销售订单详情
接口信息
URL: /api/sales/orders/{id}
方法: GET
描述: 根据ID获取销售订单详情
权限要求: 需要 sales:order:read 权限
认证方式: Bearer Token
请求参数
参数名	类型	必填	描述
id	string	是	订单ID
响应结果
响应为统一的 ApiResponse<SalesOrderDetailDTO> 格式，内容与创建订单的响应相同。