package com.czerp.erpbackend.purchase.service;

import com.czerp.erpbackend.purchase.dto.CreatePurchaseOrderItemRequest;
import com.czerp.erpbackend.purchase.dto.CreatePurchaseOrderRequest;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderDTO;
import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import com.czerp.erpbackend.purchase.mapper.PurchaseOrderItemMapper;
import com.czerp.erpbackend.purchase.mapper.PurchaseOrderMapper;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderRepository;
import com.czerp.erpbackend.purchase.service.impl.PurchaseOrderServiceImpl;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PurchaseOrderServiceTest {

    @Mock
    private PurchaseOrderRepository purchaseOrderRepository;

    @Mock
    private PurchaseOrderItemRepository purchaseOrderItemRepository;

    @Mock
    private PurchaseOrderMapper purchaseOrderMapper;

    @Mock
    private PurchaseOrderItemMapper purchaseOrderItemMapper;

    @Mock
    private SalesOrderItemRepository salesOrderItemRepository;

    @InjectMocks
    private PurchaseOrderServiceImpl purchaseOrderService;

    private CreatePurchaseOrderRequest createRequest;
    private PurchaseOrder purchaseOrder;
    private PurchaseOrderDTO purchaseOrderDTO;
    private SalesOrderItem salesOrderItem;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        createRequest = new CreatePurchaseOrderRequest();
        createRequest.setPurchaseDate(LocalDate.now());
        createRequest.setSupplierCode("SUP001");
        createRequest.setSupplierName("测试供应商");
        createRequest.setPurchaser("测试采购员");

        List<CreatePurchaseOrderItemRequest> items = new ArrayList<>();
        CreatePurchaseOrderItemRequest itemRequest = new CreatePurchaseOrderItemRequest();
        itemRequest.setPaperQuality("A级纸");
        itemRequest.setCorrugationType("三层");
        itemRequest.setQuantity(100);
        itemRequest.setPrice(new BigDecimal("10.00"));
        itemRequest.setAmount(new BigDecimal("1000.00"));
        itemRequest.setSourceSalesOrderItemId("sales-order-item-001");
        items.add(itemRequest);
        createRequest.setItems(items);

        // 模拟采购订单
        purchaseOrder = new PurchaseOrder();
        purchaseOrder.setId(1L);
        purchaseOrder.setPurchaseOrderNo("CG2023060100001");
        purchaseOrder.setPurchaseDate(LocalDate.now());
        purchaseOrder.setSupplierCode("SUP001");
        purchaseOrder.setSupplierName("测试供应商");
        purchaseOrder.setPurchaser("测试采购员");

        // 模拟采购订单DTO
        purchaseOrderDTO = new PurchaseOrderDTO();
        purchaseOrderDTO.setId(1L);
        purchaseOrderDTO.setPurchaseOrderNo("CG2023060100001");
        purchaseOrderDTO.setPurchaseDate(LocalDate.now());
        purchaseOrderDTO.setSupplierCode("SUP001");
        purchaseOrderDTO.setSupplierName("测试供应商");
        purchaseOrderDTO.setPurchaser("测试采购员");

        // 模拟销售订单明细
        SalesOrder order = new SalesOrder();
        order.setId("sales-order-001");
        order.setOrderNo("SO2023060100001");

        salesOrderItem = new SalesOrderItem();
        salesOrderItem.setId("sales-order-item-001");
        salesOrderItem.setOrder(order);
        salesOrderItem.setProductionOrderNo("PO2023060100001");
        salesOrderItem.setProductName("测试产品");
        salesOrderItem.setQuantity(100);
        salesOrderItem.setPurchasedQuantity(0);
        salesOrderItem.setPurchaseStatus("NOT_PURCHASED");
    }

    @Test
    void testCreatePurchaseOrder() {
        // 模拟行为
        when(purchaseOrderMapper.toEntity(any(CreatePurchaseOrderRequest.class))).thenReturn(purchaseOrder);
        when(purchaseOrderRepository.save(any(PurchaseOrder.class))).thenReturn(purchaseOrder);
        when(purchaseOrderMapper.toDto(any(PurchaseOrder.class))).thenReturn(purchaseOrderDTO);
        when(purchaseOrderRepository.findMaxPurchaseOrderNoByPrefix(anyString())).thenReturn(null);

        PurchaseOrderItem item = new PurchaseOrderItem();
        item.setQuantity(100);
        when(purchaseOrderItemMapper.toEntity(any(CreatePurchaseOrderItemRequest.class))).thenReturn(item);

        when(salesOrderItemRepository.findById("sales-order-item-001")).thenReturn(Optional.of(salesOrderItem));

        // 执行测试
        PurchaseOrderDTO result = purchaseOrderService.createPurchaseOrder(createRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals("CG2023060100001", result.getPurchaseOrderNo());

        // 验证销售订单明细的采购状态和已采购数量是否被更新
        verify(salesOrderItemRepository, times(1)).findById("sales-order-item-001");
        verify(salesOrderItemRepository, times(1)).save(salesOrderItem);

        // 验证销售订单明细的采购状态和已采购数量是否正确
        assertEquals(100, salesOrderItem.getPurchasedQuantity());
        assertEquals("FULLY_PURCHASED", salesOrderItem.getPurchaseStatus());
    }
}
