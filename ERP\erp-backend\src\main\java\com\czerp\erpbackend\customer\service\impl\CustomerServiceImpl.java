package com.czerp.erpbackend.customer.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.common.util.SecurityUtils;
import com.czerp.erpbackend.customer.dto.CreateCustomerRequest;
import com.czerp.erpbackend.customer.dto.CustomerDTO;
import com.czerp.erpbackend.customer.dto.CustomerQueryRequest;
import com.czerp.erpbackend.customer.dto.UpdateCustomerRequest;
import com.czerp.erpbackend.customer.entity.Customer;
import com.czerp.erpbackend.customer.mapper.CustomerMapper;
import com.czerp.erpbackend.customer.repository.CustomerCategoryRepository;
import com.czerp.erpbackend.customer.repository.CustomerRepository;
import com.czerp.erpbackend.customer.service.CustomerCategoryService;
import com.czerp.erpbackend.customer.service.CustomerService;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 客户服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerServiceImpl implements CustomerService {

    private final CustomerRepository customerRepository;
    private final CustomerCategoryRepository categoryRepository;
    private final CustomerMapper customerMapper;
    private final CustomerCategoryService categoryService;

    /**
     * 分页查询客户列表
     * @param request 查询请求
     * @return 客户分页列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<CustomerDTO> findCustomers(CustomerQueryRequest request) {
        log.debug("Finding customers with request: {}", request);

        // 构建排序
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        if (StringUtils.hasText(request.getSortField())) {
            Sort.Direction direction = Sort.Direction.ASC;
            if (request.getSortDirection() != null && request.getSortDirection().equalsIgnoreCase("desc")) {
                direction = Sort.Direction.DESC;
            }
            sort = Sort.by(direction, request.getSortField());
        }

        // 构建分页
        int pageIndex = Math.max(0, request.getPage() - 1); // 确保页码不小于0
        Pageable pageable = PageRequest.of(pageIndex, request.getSize(), sort);

        // 构建查询条件
        Specification<Customer> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 添加未删除条件
            predicates.add(cb.equal(root.get("isDeleted"), false));

            // 添加客户编码条件
            if (StringUtils.hasText(request.getCustomerCode())) {
                predicates.add(cb.like(root.get("customerCode"), "%" + request.getCustomerCode() + "%"));
            }

            // 添加客户名称条件
            if (StringUtils.hasText(request.getCustomerName())) {
                predicates.add(cb.like(root.get("customerName"), "%" + request.getCustomerName() + "%"));
            }

            // 添加客户分类条件
            if (StringUtils.hasText(request.getCategoryId())) {
                predicates.add(cb.equal(root.get("categoryId"), request.getCategoryId()));
            }

            // 添加联系人条件
            if (StringUtils.hasText(request.getContactPerson())) {
                predicates.add(cb.like(root.get("contactPerson"), "%" + request.getContactPerson() + "%"));
            }

            // 添加手机条件
            if (StringUtils.hasText(request.getMobile())) {
                predicates.add(cb.like(root.get("mobile"), "%" + request.getMobile() + "%"));
            }

            // 添加地区条件
            if (StringUtils.hasText(request.getRegion())) {
                predicates.add(cb.like(root.get("region"), "%" + request.getRegion() + "%"));
            }

            // 添加销售员条件
            if (StringUtils.hasText(request.getSalesPerson())) {
                predicates.add(cb.like(root.get("salesPerson"), "%" + request.getSalesPerson() + "%"));
            }

            // 添加行业条件
            if (StringUtils.hasText(request.getIndustry())) {
                predicates.add(cb.like(root.get("industry"), "%" + request.getIndustry() + "%"));
            }

            // 添加停用条件
            if (request.getDisabled() != null) {
                predicates.add(cb.equal(root.get("disabled"), request.getDisabled()));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 查询数据
        Page<Customer> resultPage = customerRepository.findAll(spec, pageable);

        // 转换为DTO
        List<CustomerDTO> content = resultPage.getContent().stream()
                .map(customerMapper::toDto)
                .toList();

        return new PageResponse<>(
                content,
                resultPage.getTotalElements(),
                resultPage.getTotalPages(),
                request.getPage(),
                request.getSize()
        );
    }

    /**
     * 根据ID查询客户
     * @param id 客户ID
     * @return 客户信息
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "customers", key = "#id")
    public CustomerDTO findCustomerById(String id) {
        log.debug("Finding customer by id: {}", id);

        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> new BusinessException("客户不存在"));

        return customerMapper.toDto(customer);
    }

    /**
     * 根据客户编码查询客户
     * @param customerCode 客户编码
     * @return 客户信息
     */
    @Override
    @Transactional(readOnly = true)
    public CustomerDTO findCustomerByCode(String customerCode) {
        log.debug("Finding customer by code: {}", customerCode);

        Customer customer = customerRepository.findByCustomerCode(customerCode)
                .orElseThrow(() -> new BusinessException("客户不存在"));

        return customerMapper.toDto(customer);
    }

    /**
     * 创建客户
     * @param request 创建请求
     * @return 客户信息
     */
    @Override
    @Transactional
    @CacheEvict(value = "customers", allEntries = true)
    public CustomerDTO createCustomer(CreateCustomerRequest request) {
        log.debug("Creating customer with request: {}", request);

        // 检查客户编码是否已存在
        if (customerRepository.existsByCustomerCode(request.getCustomerCode())) {
            throw new BusinessException("客户编码已存在");
        }

        // 检查分类是否存在
        if (StringUtils.hasText(request.getCategoryId())) {
            categoryRepository.findById(request.getCategoryId())
                    .orElseThrow(() -> new BusinessException("客户分类不存在"));
        } else {
            // 如果未指定分类，使用默认分类
            try {
                request.setCategoryId(categoryService.getDefaultCategory().getId());
            } catch (Exception e) {
                log.warn("No default customer category found: {}", e.getMessage());
            }
        }

        // 创建客户
        Customer customer = customerMapper.toEntity(request);

        // 设置ID
        customer.setId(UUID.randomUUID().toString());

        // 设置默认值
        if (customer.getPriceDecimalPlaces() == null) {
            customer.setPriceDecimalPlaces(2);
        }
        if (customer.getAmountDecimalPlaces() == null) {
            customer.setAmountDecimalPlaces(2);
        }
        if (customer.getUnitWeightDecimalPlaces() == null) {
            customer.setUnitWeightDecimalPlaces(3);
        }
        if (customer.getTotalWeightDecimalPlaces() == null) {
            customer.setTotalWeightDecimalPlaces(3);
        }
        if (customer.getUnitAreaDecimalPlaces() == null) {
            customer.setUnitAreaDecimalPlaces(3);
        }
        if (customer.getTotalAreaDecimalPlaces() == null) {
            customer.setTotalAreaDecimalPlaces(3);
        }
        if (customer.getDisabled() == null) {
            customer.setDisabled(false);
        }

        // 设置审计字段
        String currentUser = SecurityUtils.getCurrentUsername();
        customer.setCreatedBy(currentUser);
        customer.setCreatedTime(LocalDateTime.now());

        // 保存客户
        customer = customerRepository.save(customer);

        return customerMapper.toDto(customer);
    }

    /**
     * 更新客户
     * @param id 客户ID
     * @param request 更新请求
     * @return 客户信息
     */
    @Override
    @Transactional
    @CacheEvict(value = "customers", key = "#id")
    public CustomerDTO updateCustomer(String id, UpdateCustomerRequest request) {
        log.debug("Updating customer with id: {} and request: {}", id, request);

        // 查询客户
        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> new BusinessException("客户不存在"));

        // 检查分类是否存在
        if (StringUtils.hasText(request.getCategoryId()) && !request.getCategoryId().equals(customer.getCategoryId())) {
            categoryRepository.findById(request.getCategoryId())
                    .orElseThrow(() -> new BusinessException("客户分类不存在"));
        }

        // 更新客户
        customerMapper.updateEntity(request, customer);

        // 设置审计字段
        String currentUser = SecurityUtils.getCurrentUsername();
        customer.setUpdatedBy(currentUser);
        customer.setUpdatedTime(LocalDateTime.now());

        // 保存客户
        customer = customerRepository.save(customer);

        return customerMapper.toDto(customer);
    }

    /**
     * 删除客户
     * @param id 客户ID
     */
    @Override
    @Transactional
    @CacheEvict(value = "customers", key = "#id")
    public void deleteCustomer(String id) {
        log.debug("Deleting customer with id: {}", id);

        // 查询客户
        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> new BusinessException("客户不存在"));

        // 软删除
        customer.setIsDeleted(true);
        customer.setUpdatedBy(SecurityUtils.getCurrentUsername());
        customer.setUpdatedTime(LocalDateTime.now());

        // 保存客户
        customerRepository.save(customer);
    }

    /**
     * 批量删除客户
     * @param ids 客户ID列表
     */
    @Override
    @Transactional
    @CacheEvict(value = "customers", allEntries = true)
    public void batchDeleteCustomers(List<String> ids) {
        log.debug("Batch deleting customers with ids: {}", ids);

        // 查询客户列表
        List<Customer> customers = customerRepository.findAllById(ids);

        if (customers.isEmpty()) {
            return;
        }

        // 软删除
        String currentUser = SecurityUtils.getCurrentUsername();
        LocalDateTime now = LocalDateTime.now();

        customers.forEach(customer -> {
            customer.setIsDeleted(true);
            customer.setUpdatedBy(currentUser);
            customer.setUpdatedTime(now);
        });

        // 保存客户
        customerRepository.saveAll(customers);
    }
}
