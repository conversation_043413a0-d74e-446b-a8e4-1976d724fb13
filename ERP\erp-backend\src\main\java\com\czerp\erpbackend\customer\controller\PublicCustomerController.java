package com.czerp.erpbackend.customer.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.customer.dto.CustomerCategoryDTO;
import com.czerp.erpbackend.customer.service.CustomerCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共客户接口控制器
 */
@RestController
@RequestMapping("/public/customers")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Public Customer API", description = "公共客户接口")
public class PublicCustomerController {
    
    private final CustomerCategoryService categoryService;
    
    /**
     * 获取所有客户分类
     * @return 客户分类列表
     */
    @GetMapping("/categories")
    @Operation(summary = "获取所有客户分类", description = "获取系统中所有客户分类列表")
    public ResponseEntity<ApiResponse<List<CustomerCategoryDTO>>> getAllCategories() {
        log.info("Getting all categories (public)");
        List<CustomerCategoryDTO> categories = categoryService.findAllCategories();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
}
