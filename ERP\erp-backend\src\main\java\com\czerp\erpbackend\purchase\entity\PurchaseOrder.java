package com.czerp.erpbackend.purchase.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 采购订单实体
 */
@Entity
@Table(name = "purchase_order")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrder extends BaseEntity {

    /**
     * 采购订单ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 采购单号
     */
    @Column(name = "purchase_order_no", length = 50, nullable = false, unique = true)
    private String purchaseOrderNo;

    /**
     * 采购日期
     */
    @Column(name = "purchase_date")
    private LocalDate purchaseDate;

    /**
     * 采购员
     */
    @Column(name = "purchaser", length = 50)
    private String purchaser;

    /**
     * 付款方式
     */
    @Column(name = "payment_method", length = 50)
    private String paymentMethod;

    /**
     * 采购类型
     */
    @Column(name = "purchase_type", length = 50)
    private String purchaseType;

    /**
     * 交易单位
     */
    @Column(name = "trading_unit", length = 100)
    private String tradingUnit;

    /**
     * 供应商编码
     */
    @Column(name = "supplier_code", length = 50)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Column(name = "supplier_name", length = 100)
    private String supplierName;

    /**
     * 地址
     */
    @Column(name = "address", length = 255)
    private String address;

    /**
     * 电话
     */
    @Column(name = "phone", length = 20)
    private String phone;

    /**
     * 联系人
     */
    @Column(name = "contact_person", length = 50)
    private String contactPerson;

    /**
     * 手机
     */
    @Column(name = "mobile", length = 20)
    private String mobile;

    /**
     * 邮箱
     */
    @Column(name = "email", length = 100)
    private String email;

    /**
     * 备注
     */
    @Column(name = "remarks", length = 500)
    private String remarks;

    /**
     * 订单明细
     */
    @OneToMany(mappedBy = "purchaseOrder", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PurchaseOrderItem> items = new ArrayList<>();

    /**
     * 添加订单明细
     * @param item 订单明细
     */
    public void addItem(PurchaseOrderItem item) {
        items.add(item);
        item.setPurchaseOrder(this);
    }

    /**
     * 移除订单明细
     * @param item 订单明细
     */
    public void removeItem(PurchaseOrderItem item) {
        items.remove(item);
        item.setPurchaseOrder(null);
    }
}
