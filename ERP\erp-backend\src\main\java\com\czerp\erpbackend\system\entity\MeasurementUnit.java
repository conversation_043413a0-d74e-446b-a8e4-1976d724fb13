package com.czerp.erpbackend.system.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 计量单位实体
 */
@Entity
@Table(name = "measurement_unit")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MeasurementUnit extends BaseEntity {

    /**
     * 单位ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 单位名称
     */
    @Column(name = "unit_name", length = 50, nullable = false, unique = true)
    private String unitName;

    /**
     * 排序
     */
    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 999;

    /**
     * 新建物料时默认此单位
     */
    @Column(name = "is_default_for_new_material", nullable = false)
    private Boolean isDefaultForNewMaterial = false;

    /**
     * 纸箱/纸板尺寸单位
     */
    @Column(name = "is_dimension_unit", nullable = false)
    private Boolean isDimensionUnit = false;

    /**
     * 默认纸箱尺寸单位
     */
    @Column(name = "is_default_dimension_unit", nullable = false)
    private Boolean isDefaultDimensionUnit = false;

    /**
     * 默认纸度单位
     */
    @Column(name = "is_default_thickness_unit", nullable = false)
    private Boolean isDefaultThicknessUnit = false;

    /**
     * 默认纸长单位
     */
    @Column(name = "is_default_length_unit", nullable = false)
    private Boolean isDefaultLengthUnit = false;

    /**
     * 状态(active-启用,inactive-停用)
     */
    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";
}
