客户档案后端返回状态码和数据结构

1. 客户存在时返回的状态码和数据结构
当客户存在时，后端返回：
HTTP状态码：200 OK
响应数据结构：
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": "客户ID",
    "customerCode": "客户编码",
    "customerName": "客户名称",
    "categoryId": "分类ID",
    "categoryName": "分类名称",
    "fullName": "客户全称",
    "phone": "电话",
    "fax": "传真",
    "contactPerson": "联系人",
    "mobile": "手机",
    "address": "地址",
    "shippingAddress": "收货地址",
    // ... 其他客户字段
    "createdBy": "创建人",
    "createdTime": "创建时间",
    "updatedBy": "更新人",
    "updatedTime": "更新时间"
  }
}


2. 客户不存在时返回的状态码和错误消息
当客户不存在时，后端返回：
HTTP状态码：400 Bad Request
响应数据结构：
{
  "success": false,
  "code": "400",
  "message": "客户不存在",
  "data": null
}


详细说明
客户存在的情况：
在CustomerController中，当调用findCustomerById或findCustomerByCode方法时，如果客户存在，会返回包含客户信息的成功响应。
响应使用ApiResponse.success(customer)构建，其中success为true，code为"200"，message为"操作成功"。
客户不存在的情况：
在CustomerServiceImpl中，当调用findCustomerById或findCustomerByCode方法时，如果客户不存在，会抛出BusinessException异常，错误消息为"客户不存在"。
这个异常会被GlobalExceptionHandler中的handleBusinessException方法捕获，并返回错误响应。
错误响应使用ApiResponse.error(e.getCode(), e.getMessage())构建，其中success为false，code为"400"（来自BusinessException的默认构造函数），message为异常消息"客户不存在"。
响应处理：
全局异常处理器GlobalExceptionHandler会为BusinessException设置HTTP状态码为400 Bad Request。
对于NotFoundException（资源不存在异常的特殊类型），会设置HTTP状态码为404 Not Found，但客户服务使用的是普通的BusinessException而不是NotFoundException。
这种设计确保了API响应的一致性，无论是成功还是失败，都会返回统一格式的响应，只是success字段、code字段、message字段和HTTP状态码会有所不同。