package com.czerp.erpbackend.auth.service.impl;

import com.czerp.erpbackend.auth.service.TokenBlacklistService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 令牌黑名单服务实现
 *
 * 注意：这是一个简单的内存实现，生产环境应该使用Redis等分布式缓存
 */
@Service
@Slf4j
public class TokenBlacklistServiceImpl implements TokenBlacklistService {

    @Value("${jwt.blacklist.cleanup-interval:3600000}")
    private long cleanupInterval;

    /**
     * 令牌黑名单，键为令牌，值为过期时间（毫秒）
     */
    private final Map<String, Long> tokenBlacklist = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        log.debug("Token blacklist service initialized");
    }

    @Override
    public void addToBlacklist(String token, long expirationTime) {
        tokenBlacklist.put(token, expirationTime);
        log.debug("Token added to blacklist, expiration: {}", expirationTime);
    }

    @Override
    public boolean isBlacklisted(String token) {
        if (token == null) {
            return false;
        }

        Long expirationTime = tokenBlacklist.get(token);
        if (expirationTime == null) {
            return false;
        }

        // 检查是否过期
        if (System.currentTimeMillis() > expirationTime) {
            // 已过期，从黑名单中移除
            tokenBlacklist.remove(token);
            return false;
        }

        return true;
    }

    @Override
    public void removeFromBlacklist(String token) {
        tokenBlacklist.remove(token);
        log.debug("Token removed from blacklist");
    }

    @Override
    @Scheduled(fixedRateString = "${jwt.blacklist.cleanup-interval:3600000}") // 根据配置执行清理
    public void cleanupExpiredTokens() {
        long currentTime = System.currentTimeMillis();
        int count = 0;

        for (Map.Entry<String, Long> entry : tokenBlacklist.entrySet()) {
            if (currentTime > entry.getValue()) {
                tokenBlacklist.remove(entry.getKey());
                count++;
            }
        }

        if (count > 0) {
            log.debug("Cleaned up {} expired tokens from blacklist", count);
        }
    }
}
