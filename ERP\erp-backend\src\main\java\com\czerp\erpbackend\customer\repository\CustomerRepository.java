package com.czerp.erpbackend.customer.repository;

import com.czerp.erpbackend.customer.entity.Customer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 客户存储库
 */
@Repository
public interface CustomerRepository extends JpaRepository<Customer, String>, JpaSpecificationExecutor<Customer> {
    
    /**
     * 根据客户编码查找客户
     * @param customerCode 客户编码
     * @return 客户
     */
    Optional<Customer> findByCustomerCode(String customerCode);
    
    /**
     * 判断客户编码是否存在
     * @param customerCode 客户编码
     * @return 是否存在
     */
    boolean existsByCustomerCode(String customerCode);
    
    /**
     * 根据客户分类ID查询客户列表
     * @param categoryId 客户分类ID
     * @param pageable 分页参数
     * @return 客户列表
     */
    Page<Customer> findByCategoryId(String categoryId, Pageable pageable);
    
    /**
     * 根据客户名称模糊查询客户列表
     * @param customerName 客户名称
     * @param pageable 分页参数
     * @return 客户列表
     */
    Page<Customer> findByCustomerNameContaining(String customerName, Pageable pageable);
    
    /**
     * 根据客户编码模糊查询客户列表
     * @param customerCode 客户编码
     * @param pageable 分页参数
     * @return 客户列表
     */
    Page<Customer> findByCustomerCodeContaining(String customerCode, Pageable pageable);
    
    /**
     * 根据联系人模糊查询客户列表
     * @param contactPerson 联系人
     * @param pageable 分页参数
     * @return 客户列表
     */
    Page<Customer> findByContactPersonContaining(String contactPerson, Pageable pageable);
    
    /**
     * 根据手机模糊查询客户列表
     * @param mobile 手机
     * @param pageable 分页参数
     * @return 客户列表
     */
    Page<Customer> findByMobileContaining(String mobile, Pageable pageable);
    
    /**
     * 根据地区模糊查询客户列表
     * @param region 地区
     * @param pageable 分页参数
     * @return 客户列表
     */
    Page<Customer> findByRegionContaining(String region, Pageable pageable);
    
    /**
     * 根据销售员模糊查询客户列表
     * @param salesPerson 销售员
     * @param pageable 分页参数
     * @return 客户列表
     */
    Page<Customer> findBySalesPersonContaining(String salesPerson, Pageable pageable);
    
    /**
     * 根据行业模糊查询客户列表
     * @param industry 行业
     * @param pageable 分页参数
     * @return 客户列表
     */
    Page<Customer> findByIndustryContaining(String industry, Pageable pageable);
    
    /**
     * 根据停用状态查询客户列表
     * @param disabled 停用状态
     * @param pageable 分页参数
     * @return 客户列表
     */
    Page<Customer> findByDisabled(Boolean disabled, Pageable pageable);
}
