# 销售订单用料数据表格 API 接口文档

本文档详细说明销售订单用料数据表格功能的API接口，包括所有新增的API端点、请求参数、响应格式以及与现有销售订单API的集成方式。

## 目录

- [概述](#概述)
- [数据结构](#数据结构)
- [API端点](#api端点)
  - [创建/更新销售订单（包含用料数据）](#创建更新销售订单包含用料数据)
  - [查询销售订单详情（包含用料数据）](#查询销售订单详情包含用料数据)
  - [根据订单行项目ID查询材料信息](#根据订单行项目id查询材料信息)
  - [根据订单ID查询所有行项目的材料信息](#根据订单id查询所有行项目的材料信息)
  - [保存单个材料信息](#保存单个材料信息)
  - [批量保存材料信息](#批量保存材料信息)
  - [更新材料信息](#更新材料信息)
  - [删除材料信息](#删除材料信息)
  - [根据订单行项目ID删除材料信息](#根据订单行项目id删除材料信息)

## 概述

销售订单用料数据表格功能允许用户在创建或更新销售订单时，同时管理与订单行项目相关的材料信息。这些材料信息包括纸质、纸度、纸长、度开、长开、纸板数等详细参数。

本功能主要通过以下两种方式提供：

1. 集成到现有销售订单API中，允许在创建/更新订单时一并处理材料信息
2. 提供独立的材料信息管理API，用于单独操作材料信息

## 数据结构

### SalesOrderMaterialDTO

销售订单材料信息DTO，用于前后端数据传输。

```json
{
  "id": "string",                           // 主键ID
  "orderItemId": "string",                  // 销售订单行项目ID
  "serialNo": 1,                            // 序号
  "paperQuality": "string",                 // 纸质
  "paperWidth": 100.00,                     // 纸度
  "paperLength": 200.00,                    // 纸长
  "widthOpen": 50.00,                       // 度开
  "lengthOpen": 100.00,                     // 长开
  "boardCount": 100,                        // 纸板数
  "boardLoss": 5.00,                        // 纸板损耗
  "materialUsage": 105.00,                  // 原料用量
  "materialUsageCount": 105.00,            // 用量
  "method": "string",                       // 方式
  "pressSizeWidth": "string",               // 压线尺寸(纸度)
  "pressMethod": "string",                  // 压线方式
  "actualMaterialWidth": 102.5000,          // 实际用料宽
  "actualMaterialLength": 205.0000,         // 实际用料长
  "dieModel": "string",                     // 啤模
  "dieModelNo": "string",                   // 啤模编号
  "dieOpenCount": 10,                       // 模开数
  "dieModelPosition": "string",             // 啤模位置
  "dieModelChecked": true,                  // 啤模选中状态
  "unit": "string",                         // 单位
  "currentInventory": 500,                  // 当前库存
  "availableInventory": 300,                // 可用库存
  "useInventoryCount": 100,                 // 使用库存数
  "actualMaterialLengthConverted": 205.0000, // 实际用料长(转换)
  "actualMaterialWidthConverted": 102.5000,  // 实际用料宽(转换)
  "supplier": "string",                     // 供应商
  "materialRemark": "string",               // 材料备注
  "purchasedCount": 0,                      // 已采购数
  "diePressLine": "string",                 // 啤模压线
  "pressSizeLength": 200.00,                // 压线尺寸(纸长)
  "inboundCount": 0,                        // 已入库数
  "materialsReceivedCount": 0,              // 已领料数
  "createdBy": "string",                    // 创建人
  "createdByName": "string",                // 创建人姓名
  "createdTime": "2023-06-01T12:00:00",     // 创建时间
  "updatedBy": "string",                    // 更新人
  "updatedByName": "string",                // 更新人姓名
  "updatedTime": "2023-06-01T13:00:00"      // 更新时间
}
```

### 与销售订单的集成

在销售订单DTO中，新增了以下字段：

1. 在`SalesOrderDetailDTO`中：
```json
{
  // 其他字段...
  "materials": {
    "orderItemId1": [SalesOrderMaterialDTO数组],
    "orderItemId2": [SalesOrderMaterialDTO数组]
  }
}
```

2. 在`SalesOrderItemDTO`中：
```json
{
  // 其他字段...
  "materials": [SalesOrderMaterialDTO数组]
}
```

## API端点

### 创建/更新销售订单（包含用料数据）

#### 创建销售订单

**请求**

```
POST /sales/orders
```

请求体示例：
```json
{
  "orderNo": "SO2023060100001",
  "orderDate": "2023-06-01",
  "customerCode": "C001",
  "customerName": "测试客户",
  "items": [
    {
      "productName": "测试产品",
      "quantity": 100,
      "price": 10.5,
      "materials": [
        {
          "serialNo": 1,
          "paperQuality": "A级牛卡",
          "paperWidth": 100.00,
          "paperLength": 200.00,
          "widthOpen": 50.00,
          "lengthOpen": 100.00,
          "boardCount": 100,
          "boardLoss": 5.00,
          "materialUsage": 105.00
        }
      ]
    }
  ]
}
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "order123",
    "orderNo": "SO2023060100001",
    "orderDate": "2023-06-01",
    "customerCode": "C001",
    "customerName": "测试客户",
    "items": [
      {
        "id": "item123",
        "productName": "测试产品",
        "quantity": 100,
        "price": 10.5,
        "materials": [
          {
            "id": "material123",
            "orderItemId": "item123",
            "serialNo": 1,
            "paperQuality": "A级牛卡",
            "paperWidth": 100.00,
            "paperLength": 200.00,
            "widthOpen": 50.00,
            "lengthOpen": 100.00,
            "boardCount": 100,
            "boardLoss": 5.00,
            "materialUsage": 105.00
          }
        ]
      }
    ],
    "materials": {
      "item123": [
        {
          "id": "material123",
          "orderItemId": "item123",
          "serialNo": 1,
          "paperQuality": "A级牛卡",
          "paperWidth": 100.00,
          "paperLength": 200.00,
          "widthOpen": 50.00,
          "lengthOpen": 100.00,
          "boardCount": 100,
          "boardLoss": 5.00,
          "materialUsage": 105.00
        }
      ]
    }
  }
}
```

#### 更新销售订单

**请求**

```
PUT /sales/orders/{id}
```

请求体格式与创建销售订单相同。

**响应**

响应格式与创建销售订单相同。

### 查询销售订单详情（包含用料数据）

**请求**

```
GET /sales/orders/{id}
```

**响应**

响应格式与创建销售订单相同，包含完整的订单信息、订单行项目信息和材料信息。

### 根据订单行项目ID查询材料信息

**请求**

```
GET /sales/orders/materials/by-order-item/{orderItemId}
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "material123",
      "orderItemId": "item123",
      "serialNo": 1,
      "paperQuality": "A级牛卡",
      "paperWidth": 100.00,
      "paperLength": 200.00,
      "widthOpen": 50.00,
      "lengthOpen": 100.00,
      "boardCount": 100,
      "boardLoss": 5.00,
      "materialUsage": 105.00
    }
  ]
}
```

### 根据订单ID查询所有行项目的材料信息

**请求**

```
GET /sales/orders/materials/by-order/{orderId}
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "material123",
      "orderItemId": "item123",
      "serialNo": 1,
      "paperQuality": "A级牛卡",
      "paperWidth": 100.00,
      "paperLength": 200.00,
      "widthOpen": 50.00,
      "lengthOpen": 100.00,
      "boardCount": 100,
      "boardLoss": 5.00,
      "materialUsage": 105.00
    },
    {
      "id": "material124",
      "orderItemId": "item124",
      "serialNo": 1,
      "paperQuality": "B级牛卡",
      "paperWidth": 150.00,
      "paperLength": 250.00,
      "widthOpen": 75.00,
      "lengthOpen": 125.00,
      "boardCount": 200,
      "boardLoss": 10.00,
      "materialUsage": 210.00
    }
  ]
}
```

### 保存单个材料信息

**请求**

```
POST /sales/orders/materials
```

请求体示例：
```json
{
  "orderItemId": "item123",
  "serialNo": 1,
  "paperQuality": "A级牛卡",
  "paperWidth": 100.00,
  "paperLength": 200.00,
  "widthOpen": 50.00,
  "lengthOpen": 100.00,
  "boardCount": 100,
  "boardLoss": 5.00,
  "materialUsage": 105.00
}
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "material123",
    "orderItemId": "item123",
    "serialNo": 1,
    "paperQuality": "A级牛卡",
    "paperWidth": 100.00,
    "paperLength": 200.00,
    "widthOpen": 50.00,
    "lengthOpen": 100.00,
    "boardCount": 100,
    "boardLoss": 5.00,
    "materialUsage": 105.00,
    "createdBy": "user1",
    "createdByName": "用户1",
    "createdTime": "2023-06-01T12:00:00"
  }
}
```

### 批量保存材料信息

**请求**

```
POST /sales/orders/materials/batch
```

请求体示例：
```json
[
  {
    "orderItemId": "item123",
    "serialNo": 1,
    "paperQuality": "A级牛卡",
    "paperWidth": 100.00,
    "paperLength": 200.00,
    "widthOpen": 50.00,
    "lengthOpen": 100.00,
    "boardCount": 100,
    "boardLoss": 5.00,
    "materialUsage": 105.00
  },
  {
    "orderItemId": "item123",
    "serialNo": 2,
    "paperQuality": "B级牛卡",
    "paperWidth": 150.00,
    "paperLength": 250.00,
    "widthOpen": 75.00,
    "lengthOpen": 125.00,
    "boardCount": 200,
    "boardLoss": 10.00,
    "materialUsage": 210.00
  }
]
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "material123",
      "orderItemId": "item123",
      "serialNo": 1,
      "paperQuality": "A级牛卡",
      "paperWidth": 100.00,
      "paperLength": 200.00,
      "widthOpen": 50.00,
      "lengthOpen": 100.00,
      "boardCount": 100,
      "boardLoss": 5.00,
      "materialUsage": 105.00,
      "createdBy": "user1",
      "createdByName": "用户1",
      "createdTime": "2023-06-01T12:00:00"
    },
    {
      "id": "material124",
      "orderItemId": "item123",
      "serialNo": 2,
      "paperQuality": "B级牛卡",
      "paperWidth": 150.00,
      "paperLength": 250.00,
      "widthOpen": 75.00,
      "lengthOpen": 125.00,
      "boardCount": 200,
      "boardLoss": 10.00,
      "materialUsage": 210.00,
      "createdBy": "user1",
      "createdByName": "用户1",
      "createdTime": "2023-06-01T12:00:00"
    }
  ]
}
```

### 更新材料信息

**请求**

```
PUT /sales/orders/materials/{id}
```

请求体格式与保存单个材料信息相同。

**响应**

响应格式与保存单个材料信息相同。

### 删除材料信息

**请求**

```
DELETE /sales/orders/materials/{id}
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 根据订单行项目ID删除材料信息

**请求**

```
DELETE /sales/orders/materials/by-order-item/{orderItemId}
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```


## 注意事项

1. 在创建或更新销售订单时，如果不需要修改用料数据，可以不传递materials字段
2. 在删除订单行项目时，系统会自动删除与该行项目关联的所有用料数据
3. 用料数据的审计字段（创建人、创建时间等）会由系统自动填充，前端无需传递
