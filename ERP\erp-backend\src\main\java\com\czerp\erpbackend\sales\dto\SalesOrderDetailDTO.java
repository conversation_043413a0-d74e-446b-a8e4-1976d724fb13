package com.czerp.erpbackend.sales.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售订单详情DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SalesOrderDetailDTO {

    /**
     * 订单ID
     */
    private String id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单日期
     */
    private LocalDate orderDate;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 销售员
     */
    private String salesPerson;

    /**
     * 客户采购员
     */
    private String customerPurchaser;

    /**
     * 收货单位
     */
    private String receivingUnit;

    /**
     * 收货人
     */
    private String receiver;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 收货地址
     */
    private String receivingAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新人姓名
     */
    private String updatedByName;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 订单明细
     */
    private List<SalesOrderItemDTO> items = new ArrayList<>();

    /**
     * 订单工序
     */
    private List<SalesOrderProcessDTO> processes = new ArrayList<>();

    /**
     * 订单材料信息
     * 键为订单行项目ID，值为该行项目的材料信息列表
     */
    private Map<String, List<SalesOrderMaterialDTO>> materials = new HashMap<>();
}
