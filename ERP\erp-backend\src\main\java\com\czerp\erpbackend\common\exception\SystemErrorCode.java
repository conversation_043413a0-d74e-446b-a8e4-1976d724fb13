package com.czerp.erpbackend.common.exception;

import lombok.Getter;

/**
 * 系统错误码枚举
 */
@Getter
public enum SystemErrorCode implements ErrorCode {
    
    // 系统级别错误码
    SYSTEM_ERROR("SYS-00001", "系统错误"),
    PARAM_ERROR("SYS-00002", "参数错误"),
    UNAUTHORIZED("SYS-00003", "未授权"),
    FORBIDDEN("SYS-00004", "禁止访问"),
    NOT_FOUND("SYS-00005", "资源不存在"),
    METHOD_NOT_ALLOWED("SYS-00006", "方法不允许"),
    CONFLICT("SYS-00007", "资源冲突"),
    TOO_MANY_REQUESTS("SYS-00008", "请求过多"),
    
    // 业务级别错误码
    BUSINESS_ERROR("BIZ-00001", "业务错误"),
    DATA_NOT_FOUND("BIZ-00002", "数据不存在"),
    DATA_ALREADY_EXISTS("BIZ-00003", "数据已存在"),
    DATA_VALIDATION_ERROR("BIZ-00004", "数据验证错误"),
    OPERATION_NOT_ALLOWED("BIZ-00005", "操作不允许"),
    OPTIMISTIC_LOCK_ERROR("BIZ-00006", "数据已被修改，请刷新后重试");
    
    private final String code;
    private final String message;
    
    SystemErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
