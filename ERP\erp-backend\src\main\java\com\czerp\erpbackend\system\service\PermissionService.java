package com.czerp.erpbackend.system.service;

import com.czerp.erpbackend.system.dto.PermissionDTO;
import com.czerp.erpbackend.system.entity.Permission;

import java.util.List;
import java.util.Optional;

/**
 * 权限服务接口
 */
public interface PermissionService {

    /**
     * 获取权限树
     * @return 权限树
     */
    List<PermissionDTO> findPermissionTree();

    /**
     * 查询所有权限
     * @return 权限列表
     */
    List<PermissionDTO> findAllPermissions();

    /**
     * 根据角色ID查询权限列表
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<PermissionDTO> findPermissionsByRoleId(String roleId);

    /**
     * 根据ID查询权限
     * @param id 权限ID
     * @return 权限信息
     */
    PermissionDTO findPermissionById(String id);

    /**
     * 根据角色ID查询权限ID列表
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<String> findPermissionIdsByRoleId(String roleId);

    /**
     * 根据用户ID查询权限编码列表
     * @param userId 用户ID
     * @return 权限编码列表
     */
    List<String> findPermissionCodesByUserId(String userId);

    /**
     * 根据角色ID列表查询权限编码列表
     * @param roleIds 角色ID列表
     * @return 权限编码列表
     */
    List<String> findPermissionCodesByRoleIds(List<String> roleIds);

    /**
     * 根据编码查询权限
     * @param code 权限编码
     * @return 权限
     */
    Optional<Permission> findByCode(String code);

    /**
     * 根据编码列表查询权限列表
     * @param codes 编码列表
     * @return 权限列表
     */
    List<Permission> findByCodes(List<String> codes);

    /**
     * 保存权限，如果不存在
     * @param permission 权限
     * @return 权限
     */
    Permission saveIfNotExists(Permission permission);

    /**
     * 批量保存权限，如果不存在
     * @param permissions 权限列表
     * @return 权限列表
     */
    List<Permission> saveAllIfNotExist(List<Permission> permissions);
}