package com.czerp.erpbackend.system.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 修改状态请求
 */
@Data
public class ChangeStatusRequest {
    
    /**
     * 状态(active-正常,inactive-停用,locked-锁定)
     */
    @NotBlank(message = "状态不能为空")
    @Pattern(regexp = "^(active|inactive|locked)$", message = "状态值不正确，可选值：active, inactive, locked")
    private String status;
} 