package com.czerp.erpbackend.customer.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.customer.dto.CreateCustomerBoxRelationRequest;
import com.czerp.erpbackend.customer.dto.CustomerBoxRelationDTO;
import com.czerp.erpbackend.customer.service.CustomerBoxRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户盒式关联控制器
 */
@RestController
@RequestMapping("/customers/{customerId}/box-infos")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Customer Box Relation Management", description = "客户盒式关联管理相关接口")
public class CustomerBoxRelationController {

    private final CustomerBoxRelationService customerBoxRelationService;

    /**
     * 查询客户关联的盒式信息列表
     * @param customerId 客户ID
     * @return 盒式信息列表
     */
    @GetMapping
    @Operation(summary = "查询客户关联的盒式信息列表", description = "查询客户关联的盒式信息列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:read')")
    public ResponseEntity<ApiResponse<List<CustomerBoxRelationDTO>>> findCustomerBoxRelations(@PathVariable String customerId) {
        log.info("Finding customer box relations for customer id: {}", customerId);
        List<CustomerBoxRelationDTO> response = customerBoxRelationService.findCustomerBoxRelations(customerId);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 创建客户盒式关联
     * @param customerId 客户ID
     * @param request 创建请求
     * @return 关联信息
     */
    @PostMapping
    @Operation(summary = "创建客户盒式关联", description = "创建客户盒式关联")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:update')")
    public ResponseEntity<ApiResponse<CustomerBoxRelationDTO>> createCustomerBoxRelation(
            @PathVariable String customerId,
            @Valid @RequestBody CreateCustomerBoxRelationRequest request) {
        log.info("Creating customer box relation for customer id: {} and request: {}", customerId, request);
        CustomerBoxRelationDTO response = customerBoxRelationService.createCustomerBoxRelation(customerId, request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 批量创建客户盒式关联
     * @param customerId 客户ID
     * @param requests 创建请求列表
     * @return 关联信息列表
     */
    @PostMapping("/batch")
    @Operation(summary = "批量创建客户盒式关联", description = "批量创建客户盒式关联")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:update')")
    public ResponseEntity<ApiResponse<List<CustomerBoxRelationDTO>>> batchCreateCustomerBoxRelations(
            @PathVariable String customerId,
            @Valid @RequestBody List<CreateCustomerBoxRelationRequest> requests) {
        log.info("Batch creating customer box relations for customer id: {} and requests: {}", customerId, requests);
        List<CustomerBoxRelationDTO> response = customerBoxRelationService.batchCreateCustomerBoxRelations(customerId, requests);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 删除客户盒式关联
     * @param customerId 客户ID
     * @param boxInfoId 盒式信息ID
     * @return 无内容
     */
    @DeleteMapping("/{boxInfoId}")
    @Operation(summary = "删除客户盒式关联", description = "删除客户盒式关联")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:update')")
    public ResponseEntity<ApiResponse<Void>> deleteCustomerBoxRelation(
            @PathVariable String customerId,
            @PathVariable String boxInfoId) {
        log.info("Deleting customer box relation for customer id: {} and box info id: {}", customerId, boxInfoId);
        customerBoxRelationService.deleteCustomerBoxRelation(customerId, boxInfoId);
        return ResponseEntity.ok(ApiResponse.success());
    }

    /**
     * 批量删除客户盒式关联
     * @param customerId 客户ID
     * @param boxInfoIds 盒式信息ID列表
     * @return 无内容
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除客户盒式关联", description = "批量删除客户盒式关联")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:update')")
    public ResponseEntity<ApiResponse<Void>> batchDeleteCustomerBoxRelations(
            @PathVariable String customerId,
            @RequestBody List<String> boxInfoIds) {
        log.info("Batch deleting customer box relations for customer id: {} and box info ids: {}", customerId, boxInfoIds);
        customerBoxRelationService.batchDeleteCustomerBoxRelations(customerId, boxInfoIds);
        return ResponseEntity.ok(ApiResponse.success());
    }

    /**
     * 删除客户的所有盒式关联
     * @param customerId 客户ID
     * @return 无内容
     */
    @DeleteMapping
    @Operation(summary = "删除客户的所有盒式关联", description = "删除客户的所有盒式关联")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:update')")
    public ResponseEntity<ApiResponse<Void>> deleteAllCustomerBoxRelations(@PathVariable String customerId) {
        log.info("Deleting all customer box relations for customer id: {}", customerId);
        customerBoxRelationService.deleteAllCustomerBoxRelations(customerId);
        return ResponseEntity.ok(ApiResponse.success());
    }
}
