package com.czerp.erpbackend.system.dto;

import com.czerp.erpbackend.common.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserQueryRequest extends PageRequest {
    
    /**
     * 关键字（用户名/姓名/邮箱）
     */
    private String keyword;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 部门ID
     */
    private String department;
} 