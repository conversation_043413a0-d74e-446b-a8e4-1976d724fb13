package com.czerp.erpbackend.common.filter.builder;

import com.czerp.erpbackend.common.filter.metadata.FilterFieldMetadata;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 级联筛选构建器
 * 负责处理字段间的级联筛选关系
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CascadeFilterBuilder {
    
    private final FilterQueryBuilder filterQueryBuilder;
    
    /**
     * 构建级联筛选条件
     * @param cb CriteriaBuilder
     * @param root 根实体
     * @param currentFilters 当前筛选条件
     * @param excludeField 排除的字段
     * @param moduleFieldsMap 模块字段映射
     * @param entityManager EntityManager
     * @return 查询条件列表
     */
    public List<Predicate> buildCascadePredicates(CriteriaBuilder cb, Root<?> root,
                                                 Map<String, Object> currentFilters,
                                                 String excludeField,
                                                 Map<String, FilterFieldMetadata> moduleFieldsMap,
                                                 EntityManager entityManager) {
        List<Predicate> predicates = new ArrayList<>();
        
        if (currentFilters == null || currentFilters.isEmpty()) {
            return predicates;
        }
        
        // 按字段类型分组处理
        List<Predicate> rootPredicates = new ArrayList<>();
        List<Predicate> joinPredicates = new ArrayList<>();
        List<Predicate> subqueryPredicates = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : currentFilters.entrySet()) {
            String fieldName = entry.getKey();
            Object filterValue = entry.getValue();
            
            // 跳过当前字段和空值
            if (fieldName.equals(excludeField) || filterValue == null) {
                continue;
            }
            
            // 获取字段元数据
            FilterFieldMetadata metadata = moduleFieldsMap.get(fieldName);
            if (metadata == null) {
                log.warn("No metadata found for field: {}", fieldName);
                continue;
            }
            
            // 根据连接源类型分类处理
            switch (metadata.getJoinSource()) {
                case ROOT:
                    addRootPredicate(cb, root, metadata, filterValue, rootPredicates);
                    break;
                case JOIN:
                    addJoinPredicate(cb, root, metadata, filterValue, joinPredicates);
                    break;
                case SUBQUERY:
                    addSubqueryPredicate(cb, root, metadata, filterValue, subqueryPredicates, entityManager);
                    break;
                case DYNAMIC:
                    addDynamicPredicate(cb, root, metadata, filterValue, predicates);
                    break;
                default:
                    log.warn("Unsupported join source: {} for field: {}", 
                            metadata.getJoinSource(), fieldName);
            }
        }
        
        // 合并所有条件
        predicates.addAll(rootPredicates);
        predicates.addAll(joinPredicates);
        predicates.addAll(subqueryPredicates);
        
        return predicates;
    }
    
    /**
     * 添加根表筛选条件
     */
    private void addRootPredicate(CriteriaBuilder cb, Root<?> root, 
                                 FilterFieldMetadata metadata, Object filterValue,
                                 List<Predicate> predicates) {
        try {
            Path<String> fieldPath = root.get(metadata.getEntityFieldName());
            Predicate predicate = buildValuePredicate(cb, fieldPath, filterValue);
            if (predicate != null) {
                predicates.add(predicate);
            }
        } catch (Exception e) {
            log.warn("Failed to add root predicate for field: {}, error: {}", 
                    metadata.getFieldName(), e.getMessage());
        }
    }
    
    /**
     * 添加关联表筛选条件
     */
    private void addJoinPredicate(CriteriaBuilder cb, Root<?> root,
                                 FilterFieldMetadata metadata, Object filterValue,
                                 List<Predicate> predicates) {
        try {
            // 🔥 修复：正确处理JOIN类型的字段路径
            String fieldPath = metadata.getFieldPath();
            if (fieldPath == null) {
                fieldPath = metadata.getEntityFieldName();
            }

            // 🔥 修复：处理JOIN路径，如 "items.productName"
            Path<String> path;
            if (fieldPath.contains(".")) {
                // 分解路径：items.productName -> join("items").get("productName")
                String[] parts = fieldPath.split("\\.");
                Join<?, ?> join = root.join(parts[0], JoinType.INNER);

                // 构建剩余路径
                Path<String> finalPath = join.get(parts[1]);
                for (int i = 2; i < parts.length; i++) {
                    finalPath = finalPath.get(parts[i]);
                }
                path = finalPath;
            } else {
                // 简单字段路径
                path = root.get(fieldPath);
            }

            Predicate predicate = buildValuePredicate(cb, path, filterValue);
            if (predicate != null) {
                predicates.add(predicate);
            }
        } catch (Exception e) {
            log.warn("Failed to add join predicate for field: {}, error: {}",
                    metadata.getFieldName(), e.getMessage());
        }
    }
    
    /**
     * 添加子查询筛选条件
     */
    private void addSubqueryPredicate(CriteriaBuilder cb, Root<?> root,
                                     FilterFieldMetadata metadata, Object filterValue,
                                     List<Predicate> predicates, EntityManager entityManager) {
        try {
            // 子查询需要特殊处理，这里提供基础框架
            // 具体实现需要根据业务逻辑定制
            log.debug("Subquery predicate for field: {} not implemented in base builder", 
                     metadata.getFieldName());
        } catch (Exception e) {
            log.warn("Failed to add subquery predicate for field: {}, error: {}", 
                    metadata.getFieldName(), e.getMessage());
        }
    }
    
    /**
     * 添加动态字段筛选条件
     */
    private void addDynamicPredicate(CriteriaBuilder cb, Root<?> root,
                                    FilterFieldMetadata metadata, Object filterValue,
                                    List<Predicate> predicates) {
        try {
            // 动态字段需要特殊处理，这里提供基础框架
            // 具体实现需要根据业务逻辑定制
            log.debug("Dynamic predicate for field: {} not implemented in base builder", 
                     metadata.getFieldName());
        } catch (Exception e) {
            log.warn("Failed to add dynamic predicate for field: {}, error: {}", 
                    metadata.getFieldName(), e.getMessage());
        }
    }
    
    /**
     * 构建值筛选条件
     */
    private Predicate buildValuePredicate(CriteriaBuilder cb, Path<String> fieldPath, Object filterValue) {
        if (filterValue instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> values = (List<String>) filterValue;
            if (!values.isEmpty()) {
                return fieldPath.in(values);
            }
        } else if (filterValue instanceof String) {
            String value = (String) filterValue;
            if (value != null && !value.trim().isEmpty()) {
                return cb.equal(fieldPath, value);
            }
        }
        return null;
    }
    
    /**
     * 获取嵌套路径
     */
    private Path<String> getNestedPath(Root<?> root, String fieldPath) {
        if (fieldPath.contains(".")) {
            String[] parts = fieldPath.split("\\.");
            Path<String> path = root.get(parts[0]);
            for (int i = 1; i < parts.length; i++) {
                path = path.get(parts[i]);
            }
            return path;
        } else {
            return root.get(fieldPath);
        }
    }
}
