package com.czerp.erpbackend.material.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 纸质类别实体
 */
@Entity
@Table(name = "paper_type")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PaperType extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /**
     * 纸板类别名称
     */
    @Column(name = "paper_type_name", length = 50, nullable = false)
    private String paperTypeName;

    /**
     * 打钉钉口(inch)
     */
    @Column(name = "stapling_flap_inch", precision = 10, scale = 2)
    private BigDecimal staplingFlapInch;

    /**
     * 打钉钉口(cm)
     */
    @Column(name = "stapling_flap_cm", precision = 10, scale = 2)
    private BigDecimal staplingFlapCm;

    /**
     * 粘箱钉口(inch)
     */
    @Column(name = "gluing_flap_inch", precision = 10, scale = 2)
    private BigDecimal gluingFlapInch;

    /**
     * 粘箱钉口(cm)
     */
    @Column(name = "gluing_flap_cm", precision = 10, scale = 2)
    private BigDecimal gluingFlapCm;

    /**
     * 加分(mm)
     */
    @Column(name = "add_margin_mm", precision = 10, scale = 2)
    private BigDecimal addMarginMm;

    /**
     * 缩分(mm)
     */
    @Column(name = "reduce_margin_mm", precision = 10, scale = 2)
    private BigDecimal reduceMarginMm;

    /**
     * 厚度(mm)
     */
    @Column(name = "thickness_mm", precision = 10, scale = 2)
    private BigDecimal thicknessMm;

    /**
     * 层数
     */
    @Column(name = "layer_count")
    private Integer layerCount;

    /**
     * 每板数
     */
    @Column(name = "sheets_per_board")
    private Integer sheetsPerBoard;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @Column(name = "is_deleted")
    private Boolean isDeleted = false;
}
