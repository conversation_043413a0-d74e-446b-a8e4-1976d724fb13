package com.czerp.erpbackend.sales.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售订单材料信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SalesOrderMaterialDTO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 销售订单行项目ID
     */
    private String orderItemId;

    /**
     * 序号
     */
    private Integer serialNo;

    /**
     * 纸质
     */
    private String paperQuality;

    /**
     * 纸度
     */
    private BigDecimal paperWidth;

    /**
     * 纸长
     */
    private BigDecimal paperLength;

    /**
     * 度开
     */
    private BigDecimal widthOpen;

    /**
     * 长开
     */
    private BigDecimal lengthOpen;

    /**
     * 纸板数
     */
    private Integer boardCount;

    /**
     * 纸板损耗
     */
    private BigDecimal boardLoss;

    /**
     * 原料用量
     */
    private BigDecimal materialUsage;

    /**
     * 用量
     */
    private BigDecimal usage;

    /**
     * 方式
     */
    private String method;

    /**
     * 压线尺寸(纸度)
     */
    private String pressSizeWidth;

    /**
     * 压线方式
     */
    private String pressMethod;

    /**
     * 实际用料宽
     */
    private BigDecimal actualMaterialWidth;

    /**
     * 实际用料长
     */
    private BigDecimal actualMaterialLength;

    /**
     * 啤模
     */
    private String dieModel;

    /**
     * 啤模编号
     */
    private String dieModelNo;

    /**
     * 模开数
     */
    private Integer dieOpenCount;

    /**
     * 啤模位置
     */
    private String dieModelPosition;

    /**
     * 啤模选中状态
     */
    private Boolean dieModelChecked;

    /**
     * 单位
     */
    private String unit;

    /**
     * 当前库存
     */
    private Integer currentInventory;

    /**
     * 可用库存
     */
    private Integer availableInventory;

    /**
     * 使用库存数
     */
    private Integer useInventoryCount;

    /**
     * 实际用料长(转换)
     */
    private BigDecimal actualMaterialLengthConverted;

    /**
     * 实际用料宽(转换)
     */
    private BigDecimal actualMaterialWidthConverted;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 材料备注
     */
    private String materialRemark;

    /**
     * 已采购数
     */
    private Integer purchasedCount;

    /**
     * 啤模压线
     */
    private String diePressLine;

    /**
     * 压线尺寸(纸长)
     */
    private BigDecimal pressSizeLength;

    /**
     * 已入库数
     */
    private Integer inboundCount;

    /**
     * 已领料数
     */
    private Integer materialsReceivedCount;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新人姓名
     */
    private String updatedByName;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
