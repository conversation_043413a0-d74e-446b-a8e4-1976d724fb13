package com.czerp.erpbackend.material.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.material.dto.PaperMaterialDTO;
import com.czerp.erpbackend.material.entity.PaperType;
import com.czerp.erpbackend.material.mapper.PaperMaterialMapper;
import com.czerp.erpbackend.material.repository.PaperMaterialRepository;
import com.czerp.erpbackend.material.repository.PaperTypeRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 纸质类别测试控制器
 * 用于测试纸质类别与纸质资料的关联关系
 */
@RestController
@RequestMapping("/test/paper-type")
@Tag(name = "纸质类别测试", description = "纸质类别测试接口")
@RequiredArgsConstructor
@Slf4j
public class PaperTypeTestController {

    private final PaperMaterialRepository paperMaterialRepository;
    private final PaperTypeRepository paperTypeRepository;
    private final PaperMaterialMapper paperMaterialMapper;

    /**
     * 根据纸类ID查询纸质资料
     * @param id 纸类ID
     * @return 纸质资料列表
     */
    @GetMapping("/materials/by-id/{id}")
    @Operation(summary = "根据纸类ID查询纸质资料", description = "根据纸类ID查询纸质资料")
    public ResponseEntity<ApiResponse<List<PaperMaterialDTO>>> findMaterialsByPaperTypeId(@PathVariable Integer id) {
        log.debug("Finding materials by paper type id: {}", id);
        List<PaperMaterialDTO> materials = paperMaterialMapper.toDtoList(
                paperMaterialRepository.findByPaperType_Id(id));
        return ResponseEntity.ok(ApiResponse.success(materials));
    }

    /**
     * 根据纸类名称查询纸质资料
     * @param name 纸类名称
     * @return 纸质资料列表
     */
    @GetMapping("/materials/by-name/{name}")
    @Operation(summary = "根据纸类名称查询纸质资料", description = "根据纸类名称查询纸质资料")
    public ResponseEntity<ApiResponse<List<PaperMaterialDTO>>> findMaterialsByPaperTypeName(@PathVariable String name) {
        log.debug("Finding materials by paper type name: {}", name);
        List<PaperMaterialDTO> materials = paperMaterialMapper.toDtoList(
                paperMaterialRepository.findByPaperType_PaperTypeName(name));
        return ResponseEntity.ok(ApiResponse.success(materials));
    }

    /**
     * 根据纸类对象查询纸质资料
     * @param id 纸类ID
     * @return 纸质资料列表
     */
    @GetMapping("/materials/by-object/{id}")
    @Operation(summary = "根据纸类对象查询纸质资料", description = "根据纸类对象查询纸质资料")
    public ResponseEntity<ApiResponse<List<PaperMaterialDTO>>> findMaterialsByPaperTypeObject(@PathVariable Integer id) {
        log.debug("Finding materials by paper type object with id: {}", id);
        PaperType paperType = paperTypeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("纸质类别不存在"));
        List<PaperMaterialDTO> materials = paperMaterialMapper.toDtoList(
                paperMaterialRepository.findByPaperType(paperType));
        return ResponseEntity.ok(ApiResponse.success(materials));
    }
}
