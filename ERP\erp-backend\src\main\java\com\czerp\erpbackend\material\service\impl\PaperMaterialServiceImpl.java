package com.czerp.erpbackend.material.service.impl;

import com.czerp.erpbackend.common.dto.ImportResult;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.common.util.ExcelUtil;
import com.czerp.erpbackend.material.dto.CreatePaperMaterialRequest;
import com.czerp.erpbackend.material.dto.PaperMaterialDTO;
import com.czerp.erpbackend.material.dto.PaperMaterialQueryRequest;
import com.czerp.erpbackend.material.dto.UpdatePaperMaterialRequest;
import com.czerp.erpbackend.material.entity.PaperMaterial;
import com.czerp.erpbackend.material.entity.PaperType;
import com.czerp.erpbackend.material.mapper.PaperMaterialMapper;
import com.czerp.erpbackend.material.mapper.PaperMaterialMapperHelper;
import com.czerp.erpbackend.material.repository.PaperMaterialRepository;
import com.czerp.erpbackend.material.repository.PaperTypeRepository;
import com.czerp.erpbackend.material.service.PaperMaterialService;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 纸质资料服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaperMaterialServiceImpl implements PaperMaterialService {

    private final PaperMaterialRepository paperMaterialRepository;
    private final PaperTypeRepository paperTypeRepository;
    private final PaperMaterialMapper paperMaterialMapper;
    private final PaperMaterialMapperHelper paperMaterialMapperHelper;

    /**
     * 分页查询纸质资料列表
     * @param request 查询请求
     * @return 纸质资料分页列表
     */
    @Override
    public PageResponse<PaperMaterialDTO> findPaperMaterials(PaperMaterialQueryRequest request) {
        log.debug("Finding paper materials with request: {}", request);

        // 创建分页对象 - 根据请求动态设置排序
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime"); // 默认排序
        if (StringUtils.hasText(request.getSortField())) {
            Sort.Direction direction = Sort.Direction.ASC;
            if ("desc".equalsIgnoreCase(request.getSortDirection())) {
                direction = Sort.Direction.DESC;
            }
            sort = Sort.by(direction, request.getSortField());
        }

        // 页码处理：前端从1开始，后端PageRequest从0开始
        int pageNumber = request.getPage() != null && request.getPage() > 0 ? request.getPage() - 1 : 0;
        int pageSize = request.getSize() != null && request.getSize() > 0 ? request.getSize() : 10;

        Pageable pageable = PageRequest.of(pageNumber, pageSize, sort);

        // 创建查询条件
        Specification<PaperMaterial> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 纸质编码
            if (StringUtils.hasText(request.getPaperCode())) {
                predicates.add(cb.like(root.get("paperCode"), "%" + request.getPaperCode() + "%"));
            }

            // 纸质
            if (StringUtils.hasText(request.getPaperName())) {
                predicates.add(cb.like(root.get("paperName"), "%" + request.getPaperName() + "%"));
            }

            // 纸类
            if (StringUtils.hasText(request.getPaperType())) {
                predicates.add(cb.like(root.get("paperType").get("paperTypeName"), "%" + request.getPaperType() + "%"));
            }

            // 楞别
            if (StringUtils.hasText(request.getFluteType())) {
                predicates.add(cb.like(root.get("fluteType"), "%" + request.getFluteType() + "%"));
            }

            // 标准纸质
            if (request.getIsStandard() != null) {
                predicates.add(cb.equal(root.get("isStandard"), request.getIsStandard()));
            }

            // 停用
            if (request.getIsDisabled() != null) {
                predicates.add(cb.equal(root.get("isDisabled"), request.getIsDisabled()));
            }

            // 未删除
            predicates.add(cb.equal(root.get("isDeleted"), false));

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 执行查询
        Page<PaperMaterial> page = paperMaterialRepository.findAll(spec, pageable);

        // 转换为DTO - 使用辅助类确保paperType属性中的addMarginMm字段被正确映射
        List<PaperMaterialDTO> dtoList = paperMaterialMapperHelper.manualToDtoList(page.getContent());

        // 返回分页结果 - 页码+1返回给前端
        return PageResponse.<PaperMaterialDTO>builder()
                .content(dtoList)
                .page(page.getNumber() + 1) // 页码+1
                .size(page.getSize())
                .totalElements(page.getTotalElements())
                .totalPages(page.getTotalPages())
                .build();
    }

    /**
     * 查询所有纸质资料
     * @return 纸质资料列表
     */
    @Override
    public List<PaperMaterialDTO> findAllPaperMaterials() {
        log.debug("Finding all paper materials");
        List<PaperMaterial> materials = paperMaterialRepository.findAll();
        return paperMaterialMapperHelper.manualToDtoList(materials);
    }

    /**
     * 查询所有未停用的纸质资料
     * @return 纸质资料列表
     */
    @Override
    public List<PaperMaterialDTO> findActivePaperMaterials() {
        log.debug("Finding all active paper materials");
        List<PaperMaterial> materials = paperMaterialRepository.findByIsDisabledFalse();
        return paperMaterialMapperHelper.manualToDtoList(materials);
    }

    /**
     * 查询所有标准纸质资料
     * @return 纸质资料列表
     */
    @Override
    public List<PaperMaterialDTO> findStandardPaperMaterials() {
        log.debug("Finding all standard paper materials");
        List<PaperMaterial> materials = paperMaterialRepository.findByIsStandardTrueAndIsDisabledFalse();
        return paperMaterialMapperHelper.manualToDtoList(materials);
    }

    /**
     * 根据ID查询纸质资料
     * @param id 纸质资料ID
     * @return 纸质资料信息
     */
    @Override
    public PaperMaterialDTO findPaperMaterialById(Long id) {
        log.debug("Finding paper material by id: {}", id);
        PaperMaterial material = paperMaterialRepository.findById(id)
                .orElseThrow(() -> new BusinessException("纸质资料不存在"));
        return paperMaterialMapperHelper.manualToDto(material);
    }

    /**
     * 创建纸质资料
     * @param request 创建请求
     * @return 纸质资料信息
     */
    @Override
    @Transactional
    public PaperMaterialDTO createPaperMaterial(CreatePaperMaterialRequest request) {
        log.debug("Creating paper material with request: {}", request);

        // 检查纸质编码是否已存在
        if (paperMaterialRepository.existsByPaperCode(request.getPaperCode())) {
            throw new BusinessException("纸质编码已存在");
        }

        // 检查纸质名称是否已存在
        if (paperMaterialRepository.existsByPaperName(request.getPaperName())) {
            throw new BusinessException("纸质名称已存在");
        }

        // 创建纸质资料
        PaperMaterial material = paperMaterialMapper.toEntity(request);

        // 设置纸类
        if (request.getPaperTypeId() != null) {
            PaperType paperType = paperTypeRepository.findById(request.getPaperTypeId())
                    .orElseThrow(() -> new BusinessException("纸质类别不存在"));
            material.setPaperType(paperType);
        }

        // 设置默认值
        if (material.getIsStandard() == null) {
            material.setIsStandard(false);
        }
        if (material.getIsDisabled() == null) {
            material.setIsDisabled(false);
        }

        // 保存纸质资料
        material = paperMaterialRepository.save(material);

        return paperMaterialMapperHelper.manualToDto(material);
    }

    /**
     * 更新纸质资料
     * @param id 纸质资料ID
     * @param request 更新请求
     * @return 纸质资料信息
     */
    @Override
    @Transactional
    public PaperMaterialDTO updatePaperMaterial(Long id, UpdatePaperMaterialRequest request) {
        log.debug("Updating paper material with id: {} and request: {}", id, request);

        // 查询纸质资料
        PaperMaterial material = paperMaterialRepository.findById(id)
                .orElseThrow(() -> new BusinessException("纸质资料不存在"));

        // 检查纸质编码是否已存在
        if (StringUtils.hasText(request.getPaperCode()) &&
                !request.getPaperCode().equals(material.getPaperCode()) &&
                paperMaterialRepository.existsByPaperCodeAndIdNot(request.getPaperCode(), id)) {
            throw new BusinessException("纸质编码已存在");
        }

        // 检查纸质名称是否已存在
        if (StringUtils.hasText(request.getPaperName()) &&
                !request.getPaperName().equals(material.getPaperName()) &&
                paperMaterialRepository.existsByPaperNameAndIdNot(request.getPaperName(), id)) {
            throw new BusinessException("纸质名称已存在");
        }

        // 更新纸质资料
        paperMaterialMapper.updateEntity(request, material);

        // 更新纸类
        if (request.getPaperTypeId() != null) {
            PaperType paperType = paperTypeRepository.findById(request.getPaperTypeId())
                    .orElseThrow(() -> new BusinessException("纸质类别不存在"));
            material.setPaperType(paperType);
        }

        // 保存纸质资料
        material = paperMaterialRepository.save(material);

        return paperMaterialMapperHelper.manualToDto(material);
    }

    /**
     * 删除纸质资料
     * @param id 纸质资料ID
     */
    @Override
    @Transactional
    public void deletePaperMaterial(Long id) {
        log.debug("Deleting paper material with id: {}", id);

        // 查询纸质资料
        PaperMaterial material = paperMaterialRepository.findById(id)
                .orElseThrow(() -> new BusinessException("纸质资料不存在"));

        // 删除纸质资料（逻辑删除）
        material.setIsDeleted(true);
        paperMaterialRepository.save(material);
    }

    /**
     * 批量删除纸质资料
     * @param ids 纸质资料ID列表
     */
    @Override
    @Transactional
    public void batchDeletePaperMaterials(List<Long> ids) {
        log.debug("Batch deleting paper materials with ids: {}", ids);

        // 查询纸质资料
        List<PaperMaterial> materials = paperMaterialRepository.findAllById(ids);

        // 删除纸质资料（逻辑删除）
        materials.forEach(material -> material.setIsDeleted(true));
        paperMaterialRepository.saveAll(materials);
    }

    /**
     * 导入纸质资料
     * @param file Excel文件
     * @return 导入结果
     */
    @Override
    @Transactional
    public ImportResult importPaperMaterials(MultipartFile file) {
        log.debug("Importing paper materials from Excel file: {}", file.getOriginalFilename());

        ImportResult result = ImportResult.builder()
                .success(0)
                .fail(0)
                .errors(new ArrayList<>())
                .build();

        try {
            List<CreatePaperMaterialRequest> requests = ExcelUtil.readExcel(file, this::mapRowToPaperMaterial);

            for (CreatePaperMaterialRequest request : requests) {
                try {
                    // 验证必填字段
                    if (!StringUtils.hasText(request.getPaperCode())) {
                        throw new BusinessException("纸质编码不能为空");
                    }
                    if (!StringUtils.hasText(request.getPaperName())) {
                        throw new BusinessException("纸质名称不能为空");
                    }

                    // 检查纸质编码是否已存在
                    if (paperMaterialRepository.existsByPaperCode(request.getPaperCode())) {
                        throw new BusinessException("纸质编码已存在: " + request.getPaperCode());
                    }

                    // 检查纸质名称是否已存在
                    if (paperMaterialRepository.existsByPaperName(request.getPaperName())) {
                        throw new BusinessException("纸质名称已存在: " + request.getPaperName());
                    }

                    // 创建纸质资料
                    createPaperMaterial(request);
                    result.incrementSuccess();
                } catch (Exception e) {
                    log.error("Error importing paper material: {}", e.getMessage(), e);
                    result.incrementFail();
                    result.addError("导入失败: " + request.getPaperCode() + " - " + request.getPaperName() + ", 原因: " + e.getMessage());
                }
            }

            log.info("Imported paper materials: {} success, {} fail", result.getSuccess(), result.getFail());
            return result;
        } catch (IOException e) {
            log.error("Error reading Excel file: {}", e.getMessage(), e);
            result.incrementFail();
            result.addError("读取Excel文件失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 将Excel行映射为纸质资料创建请求
     * @param row Excel行
     * @return 纸质资料创建请求
     */
    private CreatePaperMaterialRequest mapRowToPaperMaterial(Row row) {
        // 如果前两列都为空，则认为是空行
        String paperCode = ExcelUtil.getStringCellValue(row, 0);
        String paperName = ExcelUtil.getStringCellValue(row, 1);
        if (!StringUtils.hasText(paperCode) && !StringUtils.hasText(paperName)) {
            return null;
        }

        return CreatePaperMaterialRequest.builder()
                .paperCode(paperCode)
                .paperName(paperName)
                // 纸类名称转换为纸类ID
                .paperTypeId(getPaperTypeIdByName(ExcelUtil.getStringCellValue(row, 2)))
                .fluteType(ExcelUtil.getStringCellValue(row, 3))
                .isStandard(ExcelUtil.getBooleanCellValue(row, 4))
                .productionCode(ExcelUtil.getStringCellValue(row, 5))
                .facePaper(ExcelUtil.getStringCellValue(row, 6))
                .corePaper1(ExcelUtil.getStringCellValue(row, 7))
                .middlePartition1(ExcelUtil.getStringCellValue(row, 8))
                .corePaper2(ExcelUtil.getStringCellValue(row, 9))
                .middlePartition2(ExcelUtil.getStringCellValue(row, 10))
                .corePaper3(ExcelUtil.getStringCellValue(row, 11))
                .linerPaper(ExcelUtil.getStringCellValue(row, 12))
                .layerCount(ExcelUtil.getIntegerCellValue(row, 13))
                .weightKgPerKsi(ExcelUtil.getBigDecimalCellValue(row, 14))
                .weightKgPerSqm(ExcelUtil.getBigDecimalCellValue(row, 15))
                .edgeCrushStrength(ExcelUtil.getStringCellValue(row, 16))
                .burstingStrength(ExcelUtil.getStringCellValue(row, 17))
                .correspondingStandard(ExcelUtil.getStringCellValue(row, 18))
                .defaultSupplier(ExcelUtil.getStringCellValue(row, 19))
                .remarks(ExcelUtil.getStringCellValue(row, 20))
                .isDisabled(ExcelUtil.getBooleanCellValue(row, 21))
                .build();
    }

    /**
     * 根据纸类名称获取纸类ID
     * @param paperTypeName 纸类名称
     * @return 纸类ID
     */
    private Integer getPaperTypeIdByName(String paperTypeName) {
        if (!StringUtils.hasText(paperTypeName)) {
            return null;
        }

        return paperTypeRepository.findByPaperTypeName(paperTypeName)
                .map(PaperType::getId)
                .orElse(null);
    }
}
