# 入库管理前端开发字段映射指南

## 📋 概述

本文档为前端开发人员提供入库管理查询接口的完整字段映射指南，包含所有37个新增字段的详细说明和使用示例。

## 🎯 接口使用

### 推荐接口
```javascript
// 生产环境推荐使用（默认优化查询）
GET /api/stock-inbounds/items?page=0&size=20&keyword=搜索关键字
```

### 性能对比接口
```javascript
// 优化查询（带性能监控）
GET /api/stock-inbounds/items/optimized?page=0&size=20

// 原始查询（性能对比用）
GET /api/stock-inbounds/items/original?page=0&size=20
```

## 📊 完整字段映射表

### 基础字段（原有）
| 前端显示名称 | API字段名 | 数据类型 | 示例值 | 说明 |
|-------------|-----------|----------|--------|------|
| 明细ID | id | Long | 1 | 入库明细唯一标识 |
| 入库单号 | inboundId | String | "RK20241201001" | 入库单编号 |
| 采购明细ID | purchaseOrderItemId | Long | 1 | 关联的采购订单明细ID |
| 入库数量 | quantity | Integer | 1000 | 实际入库数量 |
| 收备品数 | spareQuantity | Integer | 50 | 备品数量 |
| 单价 | price | BigDecimal | 12.50 | 入库单价 |
| 面积(平米) | areaSquareMeters | BigDecimal | 100.00 | 面积 |
| 单重 | unitWeight | BigDecimal | 0.5 | 单位重量 |
| 重量(KG) | weightKg | BigDecimal | 500.00 | 总重量 |
| 每板数 | quantityPerBoard | Integer | 100 | 每板数量 |
| 税率% | taxRate | BigDecimal | 13.00 | 税率 |
| 币别 | currency | String | "CNY" | 货币类型 |
| 供应商送货单号 | supplierDeliveryNo | String | "DN001" | 送货单号 |
| 折度规格 | foldingSpecification | String | "1200*800" | 折度规格 |
| 折算数量 | conversionQuantity | BigDecimal | 1000.00 | 折算后数量 |
| 折算单价 | conversionPrice | BigDecimal | 12.50 | 折算后单价 |
| 折算金额 | conversionAmount | BigDecimal | 12500.00 | 折算后金额 |
| 备注 | remark | String | "质量良好" | 备注信息 |
| 创建人 | createdBy | String | "admin" | 创建用户 |
| 创建时间 | createdTime | LocalDateTime | "2024-12-01T10:00:00" | 创建时间 |
| 更新人 | updatedBy | String | "admin" | 更新用户 |
| 更新时间 | updatedTime | LocalDateTime | "2024-12-01T10:00:00" | 更新时间 |
| 版本号 | version | Integer | 0 | 乐观锁版本 |

### 采购单相关字段（新增）
| 前端显示名称 | API字段名 | 数据类型 | 示例值 | 说明 |
|-------------|-----------|----------|--------|------|
| 采购单号 | purchaseOrderNo | String | "CG20241201001" | 采购单编号 |
| 采购日期 | purchaseDate | LocalDate | "2024-12-01" | 采购单日期 |
| 交易单位 | tradingUnit | String | "供应商A" | 交易对象 |
| 采购订单数量 | purchaseOrderQuantity | Integer | 1000 | 采购订单中的数量 |
| 纸质 | paperQuality | String | "250g白卡纸" | 纸张质量规格 |
| 纸板类别 | paperBoardCategory | String | "白卡纸" | 纸板分类 |
| 楞别 | corrugationType | String | "E楞" | 瓦楞纸楞型 |
| 规格 | specification | String | "1200×800" | 纸张规格(纸度×纸长) |
| 纸质报价 | paperQuotation | BigDecimal | 11.80 | 纸质报价 |
| 折扣 | discount | BigDecimal | 0.95 | 折扣率 |

### 销售单相关字段（新增）
| 前端显示名称 | API字段名 | 数据类型 | 示例值 | 说明 |
|-------------|-----------|----------|--------|------|
| 生产单号 | productionOrderNo | String | "000001" | 生产订单编号 |
| 销售单号 | salesOrderNo | String | "SO20241201001" | 销售订单编号 |
| 销售日期 | salesOrderDate | LocalDate | "2024-11-30" | 销售订单日期 |
| 客户名称 | customerName | String | "客户A" | 客户名称 |
| 客户订单号 | customerOrderNo | String | "CUS001" | 客户的订单编号 |
| 客方货号 | customerProductCode | String | "CP001" | 客户产品编码 |
| 产品 | product | String | "盒式 250g白卡纸" | 产品描述(盒式+纸质) |
| 产品规格 | productSpecification | String | "300×200×100mm" | 产品尺寸规格 |
| 品名 | productName | String | "包装盒" | 产品名称 |
| 工艺要求 | processRequirements | String | "分纸→开槽→粘箱→包装" | 生产工艺流程 |
| 订单数 | orderQuantity | Integer | 1000 | 销售订单数量 |
| 产品单价 | productPrice | BigDecimal | 15.00 | 销售单价 |
| 产品金额 | productAmount | BigDecimal | 15000.00 | 销售金额 |
| 销售备注 | salesRemark | String | "客户要求加急" | 销售备注 |
| 销售员 | salesPerson | String | "张三" | 负责销售员 |

### 计算字段（新增）
| 前端显示名称 | API字段名 | 数据类型 | 示例值 | 说明 |
|-------------|-----------|----------|--------|------|
| 金额 | amount | BigDecimal | 12500.00 | 入库金额(单价×数量) |

## 🎨 前端表格配置示例

### Vue.js + Element Plus 示例
```vue
<template>
  <el-table :data="tableData" stripe>
    <!-- 基础信息列 -->
    <el-table-column prop="id" label="明细ID" width="80" />
    <el-table-column prop="inboundId" label="入库单号" width="140" />
    <el-table-column prop="quantity" label="入库数量" width="100" />
    <el-table-column prop="price" label="单价" width="100">
      <template #default="{ row }">
        ¥{{ row.price?.toFixed(2) || '-' }}
      </template>
    </el-table-column>
    <el-table-column prop="amount" label="金额" width="120">
      <template #default="{ row }">
        ¥{{ row.amount?.toFixed(2) || '-' }}
      </template>
    </el-table-column>
    
    <!-- 采购单信息列 -->
    <el-table-column prop="purchaseOrderNo" label="采购单号" width="140" />
    <el-table-column prop="paperQuality" label="纸质" width="120" />
    <el-table-column prop="specification" label="规格" width="100" />
    
    <!-- 销售单信息列 -->
    <el-table-column prop="salesOrderNo" label="销售单号" width="140" />
    <el-table-column prop="customerName" label="客户名称" width="120" />
    <el-table-column prop="productionOrderNo" label="生产单号" width="100" />
    <el-table-column prop="processRequirements" label="工艺要求" width="200" />
    
    <!-- 操作列 -->
    <el-table-column label="操作" width="120" fixed="right">
      <template #default="{ row }">
        <el-button size="small" @click="viewDetail(row)">详情</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const tableData = ref([])

const fetchData = async () => {
  try {
    const response = await fetch('/api/stock-inbounds/items?page=0&size=20')
    const result = await response.json()
    if (result.success) {
      tableData.value = result.data.content
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

const viewDetail = (row) => {
  console.log('查看详情:', row)
}

onMounted(() => {
  fetchData()
})
</script>
```

### React + Ant Design 示例
```jsx
import React, { useState, useEffect } from 'react';
import { Table, message } from 'antd';

const StockInboundItemTable = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  const columns = [
    {
      title: '明细ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '入库单号',
      dataIndex: 'inboundId',
      key: 'inboundId',
      width: 140,
    },
    {
      title: '入库数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price) => price ? `¥${price.toFixed(2)}` : '-',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount) => amount ? `¥${amount.toFixed(2)}` : '-',
    },
    {
      title: '采购单号',
      dataIndex: 'purchaseOrderNo',
      key: 'purchaseOrderNo',
      width: 140,
    },
    {
      title: '纸质',
      dataIndex: 'paperQuality',
      key: 'paperQuality',
      width: 120,
    },
    {
      title: '销售单号',
      dataIndex: 'salesOrderNo',
      key: 'salesOrderNo',
      width: 140,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 120,
    },
    {
      title: '生产单号',
      dataIndex: 'productionOrderNo',
      key: 'productionOrderNo',
      width: 100,
    },
    {
      title: '工艺要求',
      dataIndex: 'processRequirements',
      key: 'processRequirements',
      width: 200,
      ellipsis: true,
    },
  ];

  const fetchData = async (page = 1, size = 20) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/stock-inbounds/items?page=${page - 1}&size=${size}`);
      const result = await response.json();
      
      if (result.success) {
        setData(result.data.content);
        setPagination({
          current: result.data.page + 1,
          pageSize: result.data.size,
          total: result.data.totalElements,
        });
      } else {
        message.error('获取数据失败');
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('网络错误');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleTableChange = (pagination) => {
    fetchData(pagination.current, pagination.pageSize);
  };

  return (
    <Table
      columns={columns}
      dataSource={data}
      loading={loading}
      pagination={pagination}
      onChange={handleTableChange}
      rowKey="id"
      scroll={{ x: 1500 }}
    />
  );
};

export default StockInboundItemTable;
```

## 🔍 搜索功能使用

### 关键字搜索
```javascript
// 支持多字段模糊搜索
const searchParams = {
  keyword: '测试',  // 搜索：入库单号、供应商名称、仓库、送货单号、采购单号、销售单号、客户名称
  page: 0,
  size: 20
};

fetch(`/api/stock-inbounds/items?${new URLSearchParams(searchParams)}`);
```

### 高级筛选
```javascript
// 组合条件查询
const filterParams = {
  inboundDateStart: '2024-01-01',
  inboundDateEnd: '2024-12-31',
  supplierName: '供应商A',
  customerName: '客户B',
  page: 0,
  size: 20
};

fetch(`/api/stock-inbounds/items?${new URLSearchParams(filterParams)}`);
```

## ⚠️ 注意事项

### 数据处理
1. **空值处理**：关联数据可能为null，前端需要做空值判断
2. **数字格式化**：金额字段建议保留2位小数显示
3. **日期格式化**：日期字段需要根据需要格式化显示

### 性能优化
1. **分页设置**：建议每页20-50条，避免一次加载过多数据
2. **字段选择**：根据业务需要选择显示的字段，避免表格过宽
3. **缓存策略**：可以对查询结果进行适当缓存

### 错误处理
```javascript
const handleApiError = (error, response) => {
  if (response?.status === 401) {
    // 未授权，跳转登录
    window.location.href = '/login';
  } else if (response?.status === 403) {
    // 权限不足
    message.error('权限不足');
  } else {
    // 其他错误
    message.error('操作失败，请稍后重试');
  }
};
```

## 📈 性能监控

### 前端性能监控
```javascript
const performanceTest = async () => {
  const start = performance.now();
  
  const response = await fetch('/api/stock-inbounds/items/optimized?page=0&size=100');
  const result = await response.json();
  
  const end = performance.now();
  console.log(`API调用耗时: ${(end - start).toFixed(2)}ms`);
  console.log(`返回数据量: ${result.data?.content?.length || 0}条`);
};
```

这个字段映射指南为前端开发提供了完整的API使用说明，包含所有37个新增字段的详细信息和实际使用示例。
