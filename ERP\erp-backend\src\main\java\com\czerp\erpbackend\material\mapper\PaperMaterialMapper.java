package com.czerp.erpbackend.material.mapper;

import com.czerp.erpbackend.material.dto.CreatePaperMaterialRequest;
import com.czerp.erpbackend.material.dto.PaperMaterialDTO;
import com.czerp.erpbackend.material.dto.UpdatePaperMaterialRequest;
import com.czerp.erpbackend.material.entity.PaperMaterial;
import com.czerp.erpbackend.material.entity.PaperType;
import org.mapstruct.*;

import java.util.List;

/**
 * 纸质资料Mapper
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PaperMaterialMapper {

    /**
     * 实体转DTO
     * @param entity 实体
     * @return DTO
     */
    @Mapping(target = "paperTypeName", source = "paperType.paperTypeName")
    PaperMaterialDTO toDto(PaperMaterial entity);

    /**
     * 实体列表转DTO列表
     * @param entities 实体列表
     * @return DTO列表
     */
    List<PaperMaterialDTO> toDtoList(List<PaperMaterial> entities);

    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "paperType", ignore = true)
    PaperMaterial toEntity(CreatePaperMaterialRequest request);

    /**
     * 更新实体
     * @param request 更新请求
     * @param entity 实体
     */
    @Mapping(target = "paperType", ignore = true)
    void updateEntity(UpdatePaperMaterialRequest request, @MappingTarget PaperMaterial entity);

    /**
     * 将纸类ID转换为纸类实体
     * @param id 纸类ID
     * @return 纸类实体
     */
    default PaperType map(Integer id) {
        if (id == null) {
            return null;
        }
        PaperType paperType = new PaperType();
        paperType.setId(id);
        return paperType;
    }
}
