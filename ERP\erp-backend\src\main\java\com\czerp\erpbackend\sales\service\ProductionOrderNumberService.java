package com.czerp.erpbackend.sales.service;

import com.czerp.erpbackend.sales.entity.SalesOrderItem;

import java.util.List;
import java.util.Map;

/**
 * 生产单号生成服务接口
 */
public interface ProductionOrderNumberService {
    
    /**
     * 为销售订单项生成生产单号
     * @param orderItem 销售订单项
     * @return 生成的生产单号
     */
    String generateProductionOrderNumber(SalesOrderItem orderItem);
    
    /**
     * 批量为销售订单项生成生产单号
     * @param orderItems 销售订单项列表
     * @return 订单项ID与生产单号的映射
     */
    Map<String, String> batchGenerateProductionOrderNumbers(List<SalesOrderItem> orderItems);
}
