package com.czerp.erpbackend.customer.dto;

import com.czerp.erpbackend.common.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户分类查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerCategoryQueryRequest extends PageRequest {
    
    /**
     * 分类编码
     */
    private String categoryCode;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 状态(active-启用,inactive-停用)
     */
    private String status;
    
    /**
     * 是否默认
     */
    private Boolean isDefault;
}
