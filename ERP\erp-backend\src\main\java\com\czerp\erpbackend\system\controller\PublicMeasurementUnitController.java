package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.system.dto.MeasurementUnitDTO;
import com.czerp.erpbackend.system.service.MeasurementUnitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共计量单位接口控制器
 */
@RestController
@RequestMapping("/public/measurement-units")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Public Measurement Unit API", description = "公共计量单位接口")
public class PublicMeasurementUnitController {
    
    private final MeasurementUnitService measurementUnitService;
    
    @GetMapping
    @Operation(summary = "获取所有启用的计量单位", description = "获取所有启用的计量单位列表")
    public ResponseEntity<ApiResponse<List<MeasurementUnitDTO>>> getAllActiveMeasurementUnits() {
        log.info("Getting all active measurement units (public)");
        List<MeasurementUnitDTO> units = measurementUnitService.findAllActiveMeasurementUnits();
        return ResponseEntity.ok(ApiResponse.success(units));
    }
    
    @GetMapping("/dimension")
    @Operation(summary = "获取所有尺寸单位", description = "获取所有尺寸单位列表")
    public ResponseEntity<ApiResponse<List<MeasurementUnitDTO>>> getAllDimensionUnits() {
        log.info("Getting all dimension units (public)");
        List<MeasurementUnitDTO> units = measurementUnitService.findAllDimensionUnits();
        return ResponseEntity.ok(ApiResponse.success(units));
    }
}
