-- 索引重复性分析脚本
-- 用于分析stock_inbound_item表的索引重复情况

-- 1. 查看所有索引的详细信息
SELECT 
    index_name,
    column_name,
    seq_in_index,
    cardinality,
    sub_part,
    packed,
    nullable,
    index_type,
    CASE 
        WHEN non_unique = 0 THEN 'UNIQUE'
        ELSE 'NON-UNIQUE'
    END as uniqueness
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name = 'stock_inbound_item'
ORDER BY index_name, seq_in_index;

-- 2. 分析重复索引
-- 检查idx_purchase_order_item_id和idx_stock_inbound_item_purchase_order_item_id的差异
SELECT 
    'idx_purchase_order_item_id' as index_name,
    GROUP_CONCAT(column_name ORDER BY seq_in_index) as columns
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name = 'stock_inbound_item'
AND index_name = 'idx_purchase_order_item_id'

UNION ALL

SELECT 
    'idx_stock_inbound_item_purchase_order_item_id' as index_name,
    GROUP_CONCAT(column_name ORDER BY seq_in_index) as columns
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name = 'stock_inbound_item'
AND index_name = 'idx_stock_inbound_item_purchase_order_item_id';

-- 3. 检查我们需要的复合索引是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS'
        ELSE 'NOT EXISTS'
    END as status,
    'idx_stock_inbound_item_calculation' as required_index,
    'purchase_order_item_id, is_deleted, quantity' as required_columns
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name = 'stock_inbound_item'
AND index_name = 'idx_stock_inbound_item_calculation';

-- 4. 分析索引使用情况（需要开启performance_schema）
-- 注意：这个查询需要MySQL 5.6+且开启performance_schema
SELECT 
    object_name as table_name,
    index_name,
    count_star as total_access,
    count_read as read_access,
    count_write as write_access,
    count_fetch as fetch_count,
    count_insert as insert_count,
    count_update as update_count,
    count_delete as delete_count
FROM performance_schema.table_io_waits_summary_by_index_usage 
WHERE object_schema = DATABASE()
AND object_name = 'stock_inbound_item'
AND index_name IS NOT NULL
ORDER BY count_star DESC;

-- 5. 查看索引大小
SELECT 
    table_name,
    index_name,
    ROUND(stat_value * @@innodb_page_size / 1024 / 1024, 2) AS size_mb
FROM mysql.innodb_index_stats 
WHERE database_name = DATABASE()
AND table_name = 'stock_inbound_item'
AND stat_name = 'size'
ORDER BY size_mb DESC;
