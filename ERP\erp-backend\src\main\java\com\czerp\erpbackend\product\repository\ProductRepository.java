package com.czerp.erpbackend.product.repository;

import com.czerp.erpbackend.product.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 货品存储库
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, String> {

    /**
     * 根据编码查找货品
     * @param code 编码
     * @return 货品
     */
    Optional<Product> findByCode(String code);

    /**
     * 判断编码是否存在
     * @param code 编码
     * @return 是否存在
     */
    boolean existsByCode(String code);

    /**
     * 根据分类ID查询货品列表
     * @param categoryId 分类ID
     * @return 货品列表
     */
    List<Product> findByCategoryId(String categoryId);

    /**
     * 根据规格ID查询货品列表
     * @param specId 规格ID
     * @return 货品列表
     */
    List<Product> findBySpecId(String specId);

    /**
     * 查询所有启用的货品
     * @return 货品列表
     */
    @Query("SELECT p FROM Product p WHERE p.isDeleted = false AND p.disabled = false ORDER BY p.code ASC")
    List<Product> findAllActive();

    /**
     * 多条件查询货品
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @param disabled 是否禁用
     * @param startDateTime 开始时间
     * @param endDateTime 结束时间
     * @param pageable 分页参数
     * @return 货品分页列表
     */
    @Query("SELECT p FROM Product p WHERE p.isDeleted = false AND " +
           "(:keyword IS NULL OR :keyword = '' OR p.name LIKE %:keyword% OR p.code LIKE %:keyword%) AND " +
           "(:categoryId IS NULL OR :categoryId = '' OR p.categoryId = :categoryId) AND " +
           "(:disabled IS NULL OR p.disabled = :disabled) AND " +
           "(:startDateTime IS NULL OR p.createdTime >= :startDateTime) AND " +
           "(:endDateTime IS NULL OR p.createdTime <= :endDateTime)")
    Page<Product> search(
            @Param("keyword") String keyword,
            @Param("categoryId") String categoryId,
            @Param("disabled") Boolean disabled,
            @Param("startDateTime") LocalDateTime startDateTime,
            @Param("endDateTime") LocalDateTime endDateTime,
            Pageable pageable
    );
}
