# 数据库字符集冲突修复指南

## 问题描述

系统出现字符集冲突错误：
```
Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_unicode_ci,IMPLICIT) for operation '='
```

**根本原因：**
- `sales_order_item` 表使用 `utf8mb4_0900_ai_ci` 字符集
- `purchase_order_item` 表使用 `utf8mb4_unicode_ci` 字符集
- 当进行 JOIN 或 IN 查询时，MySQL 无法处理不同字符集之间的比较

## 解决方案

采用最佳实践：**统一所有表的字符集为 `utf8mb4_0900_ai_ci`**

## 执行步骤

### 1. 执行前准备

#### 1.1 停止应用程序
```bash
# 停止后端服务
cd D:\CZERP-JAVA\ERP\erp-backend
# 如果使用 IDE，直接停止运行
# 如果使用命令行，按 Ctrl+C 停止
```

#### 1.2 备份数据库
```bash
# 创建备份目录
mkdir -p D:\CZERP-JAVA\ERP\erp-backend\backup

# 备份数据库
mysqldump -u root -p czerp_web > D:\CZERP-JAVA\ERP\erp-backend\backup\backup_before_collation_fix_$(date +%Y%m%d_%H%M%S).sql
```

#### 1.3 检查当前状态
```sql
-- 在 MySQL 客户端中执行
source D:\CZERP-JAVA\ERP\erp-backend\scripts\check_collation_status.sql
```

### 2. 执行修复

#### 2.1 执行修复脚本
```sql
-- 在 MySQL 客户端中执行
source D:\CZERP-JAVA\ERP\erp-backend\scripts\fix_collation_conflicts.sql
```

**预计执行时间：** 2-5 分钟（取决于数据量）

#### 2.2 验证修复结果
```sql
-- 在 MySQL 客户端中执行
source D:\CZERP-JAVA\ERP\erp-backend\scripts\verify_collation_fix.sql
```

### 3. 执行后验证

#### 3.1 重启应用程序
```bash
cd D:\CZERP-JAVA\ERP\erp-backend
# 重新启动后端服务
```

#### 3.2 测试功能
1. 访问销售订单查询页面
2. 使用采购单号筛选功能
3. 确认不再出现字符集冲突错误

## 文件说明

| 文件名 | 用途 | 执行时机 |
|--------|------|----------|
| `check_collation_status.sql` | 检查当前字符集状态 | 执行修复前 |
| `fix_collation_conflicts.sql` | 执行字符集修复 | 主要修复脚本 |
| `verify_collation_fix.sql` | 验证修复结果 | 执行修复后 |
| `README_字符集修复指南.md` | 执行指南 | 参考文档 |

## 风险评估

### 低风险
- ✅ 只修改字符集，不修改数据结构
- ✅ 不删除任何数据
- ✅ 可以完全回滚

### 注意事项
- ⚠️ 执行期间数据库会被锁定，应用程序必须停止
- ⚠️ 大表的字符集转换可能需要较长时间
- ⚠️ 建议在维护窗口期间执行

## 回滚方案

如果修复后出现问题，可以恢复备份：

```bash
# 停止应用程序
# 恢复数据库
mysql -u root -p czerp_web < D:\CZERP-JAVA\ERP\erp-backend\backup\backup_before_collation_fix_YYYYMMDD_HHMMSS.sql
# 重启应用程序
```

## 预期结果

修复成功后：
1. 所有表使用统一的 `utf8mb4_0900_ai_ci` 字符集
2. 销售订单查询中的采购单号筛选功能正常工作
3. 不再出现字符集冲突错误
4. 查询性能可能有轻微提升（utf8mb4_0900_ai_ci 性能更好）

## 技术细节

### 字符集对比

| 字符集 | 特点 | 性能 | 兼容性 |
|--------|------|------|--------|
| `utf8mb4_unicode_ci` | 旧版默认，严格 Unicode 标准 | 较慢 | 更好 |
| `utf8mb4_0900_ai_ci` | MySQL 8.0 默认，优化性能 | 更快 | 良好 |

### 修复的表列表

**需要修复的表（从 utf8mb4_unicode_ci 改为 utf8mb4_0900_ai_ci）：**
- purchase_order
- purchase_order_item
- pur_supplier
- pur_supplier_category
- sales_order_item_process
- sales_order_material_detail
- sales_order_process_step
- sys_* 系列表
- example_entity
- paper_size_setting

**已经正确的表（保持 utf8mb4_0900_ai_ci）：**
- sales_order
- sales_order_item
- sales_order_material
- sales_order_query
- 其他大部分业务表

## 联系支持

如果在执行过程中遇到问题，请：
1. 保留错误信息截图
2. 记录执行到哪一步出现问题
3. 不要继续执行，等待技术支持

---

**重要提醒：** 
- 执行前务必备份数据库
- 在维护窗口期间执行
- 确保应用程序完全停止
- 验证修复结果后再重启应用程序
