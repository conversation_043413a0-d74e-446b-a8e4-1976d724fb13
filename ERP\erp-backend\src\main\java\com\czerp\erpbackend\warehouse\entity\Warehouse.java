package com.czerp.erpbackend.warehouse.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 仓库实体类
 */
@Entity
@Table(name = "warehouse")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Warehouse extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 仓库名称
     */
    @Column(name = "warehouse_name", length = 100, nullable = false)
    private String warehouseName;

    /**
     * 备料仓（0-否，1-是）
     */
    @Column(name = "is_material_warehouse", nullable = false)
    private Boolean isMaterialWarehouse = false;
}
