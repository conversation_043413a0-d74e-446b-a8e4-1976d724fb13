package com.czerp.erpbackend.system.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 纸度设置权限初始化器
 * 负责初始化纸度设置模块的权限并分配给管理员角色
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(10) // 在系统权限初始化之后执行
public class PaperSizeSettingPermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing paper size setting permissions...");

        // 初始化纸度设置模块权限
        initPaperSizeSettingPermissions();

        // 为管理员角色分配纸度设置模块权限
        assignPermissionsToAdminRole();

        log.info("Paper size setting permissions initialized successfully");
    }

    /**
     * 初始化纸度设置模块权限
     */
    private void initPaperSizeSettingPermissions() {
        log.info("Initializing paper size setting module permissions...");

        // 检查系统模块是否存在
        String systemModuleId = findPermissionIdByCode("system");
        if (systemModuleId == null) {
            log.warn("System module not found, creating it...");
            systemModuleId = createPermission("系统管理", "system", "MENU", null, "/system", "system/index", "setting", 1);
        }

        // 检查纸度设置模块是否已存在
        if (permissionRepository.existsByCode("system:paper-size-setting")) {
            log.info("Paper size setting module already exists, skipping...");
            return;
        }

        // 创建纸度设置模块菜单
        String paperSizeSettingModuleId = createPermission("纸度设置", "system:paper-size-setting", "MENU", systemModuleId,
                "/system/paper-size-setting", "system/PaperSizeSetting", "ruler", 60);

        // 创建纸度设置模块按钮权限
        createPermission("纸度设置列表", "system:paper-size-setting:list", "BUTTON", paperSizeSettingModuleId, null, null, null, 61);
        createPermission("纸度设置详情", "system:paper-size-setting:read", "BUTTON", paperSizeSettingModuleId, null, null, null, 62);
        createPermission("创建纸度设置", "system:paper-size-setting:create", "BUTTON", paperSizeSettingModuleId, null, null, null, 63);
        createPermission("更新纸度设置", "system:paper-size-setting:update", "BUTTON", paperSizeSettingModuleId, null, null, null, 64);
        createPermission("删除纸度设置", "system:paper-size-setting:delete", "BUTTON", paperSizeSettingModuleId, null, null, null, 65);

        log.info("Paper size setting module permissions created successfully");
    }

    /**
     * 为管理员角色分配纸度设置模块权限
     */
    private void assignPermissionsToAdminRole() {
        log.info("Assigning paper size setting permissions to admin role...");

        // 查找管理员角色
        Role adminRole = roleRepository.findByCode("admin")
                .orElseThrow(() -> new RuntimeException("Admin role not found"));

        // 查找纸度设置模块权限
        List<String> permissionCodes = Arrays.asList(
                "system:paper-size-setting",
                "system:paper-size-setting:list",
                "system:paper-size-setting:read",
                "system:paper-size-setting:create",
                "system:paper-size-setting:update",
                "system:paper-size-setting:delete"
        );
        List<Permission> permissions = permissionRepository.findByCodeIn(permissionCodes);
        if (permissions.isEmpty()) {
            log.warn("No paper size setting permissions found, skipping assignment");
            return;
        }

        // 检查权限是否已分配
        List<String> permissionIds = permissions.stream()
                .map(Permission::getId)
                .toList();
        List<RolePermission> existingRolePermissions = rolePermissionRepository.findByRoleIdAndPermissionIdIn(adminRole.getId(), permissionIds);
        if (existingRolePermissions.size() == permissions.size()) {
            log.info("Paper size setting permissions already assigned to admin role, skipping...");
            return;
        }

        // 已分配的权限ID
        Set<String> assignedPermissionIds = existingRolePermissions.stream()
                .map(RolePermission::getPermissionId)
                .collect(java.util.stream.Collectors.toSet());

        // 创建角色权限关联
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : permissions) {
            if (assignedPermissionIds.contains(permission.getId())) {
                continue;
            }
            RolePermission rolePermission = new RolePermission();
            rolePermission.setId(UUID.randomUUID().toString());
            rolePermission.setRoleId(adminRole.getId());
            rolePermission.setPermissionId(permission.getId());
            rolePermission.setCreateBy("system");
            rolePermission.setCreateTime(LocalDateTime.now());
            rolePermissions.add(rolePermission);
        }

        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} paper size setting permissions to admin role", rolePermissions.size());
        }
    }

    /**
     * 根据编码查找权限ID
     * @param code 权限编码
     * @return 权限ID
     */
    private String findPermissionIdByCode(String code) {
        return permissionRepository.findByCode(code)
                .map(Permission::getId)
                .orElse(null);
    }

    /**
     * 创建权限
     * @param name 权限名称
     * @param code 权限编码
     * @param type 权限类型
     * @param parentId 父权限ID
     * @param path 路径
     * @param component 组件
     * @param icon 图标
     * @param sort 排序
     * @return 权限ID
     */
    private String createPermission(String name, String code, String type, String parentId, String path, String component, String icon, Integer sort) {
        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setName(name);
        permission.setCode(code);
        permission.setType(type);
        permission.setParentId(parentId);
        permission.setPath(path);
        permission.setComponent(component);
        permission.setIcon(icon);
        permission.setSort(sort);
        permission.setCreatedBy("system");
        permission.setCreatedTime(LocalDateTime.now());
        permission.setStatus("active");
        permission.setIsDeleted(false);
        permission = permissionRepository.save(permission);
        return permission.getId();
    }
}
