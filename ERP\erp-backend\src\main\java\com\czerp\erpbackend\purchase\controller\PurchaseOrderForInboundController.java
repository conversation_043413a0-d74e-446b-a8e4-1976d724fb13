package com.czerp.erpbackend.purchase.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemForInboundDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemForInboundQueryRequest;
import com.czerp.erpbackend.purchase.service.PurchaseOrderForInboundService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购订单引用控制器（用于入库管理）
 */
@RestController
@RequestMapping("/purchase-orders-for-inbound")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Purchase Order Reference for Inbound", description = "采购订单引用相关接口（用于入库管理）")
public class PurchaseOrderForInboundController {

    private final PurchaseOrderForInboundService purchaseOrderForInboundService;

    /**
     * 分页查询可用于入库的采购订单明细
     * @param request 查询请求
     * @return 采购订单明细分页列表
     */
    @GetMapping("/items")
    @Operation(summary = "分页查询可用于入库的采购订单明细", description = "分页查询可用于入库的采购订单明细")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:create')")
    public ResponseEntity<ApiResponse<PageResponse<PurchaseOrderItemForInboundDTO>>> findPurchaseOrderItemsForInbound(PurchaseOrderItemForInboundQueryRequest request) {
        log.info("Finding purchase order items for inbound with request: {}", request);
        PageResponse<PurchaseOrderItemForInboundDTO> response = purchaseOrderForInboundService.findPurchaseOrderItemsForInbound(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据ID查询采购订单明细
     * @param id 采购订单明细ID
     * @return 采购订单明细
     */
    @GetMapping("/items/{id}")
    @Operation(summary = "根据ID查询采购订单明细", description = "根据ID查询采购订单明细")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:read')")
    public ResponseEntity<ApiResponse<PurchaseOrderItemForInboundDTO>> findPurchaseOrderItemById(@PathVariable Long id) {
        log.info("Finding purchase order item by id: {}", id);
        PurchaseOrderItemForInboundDTO response = purchaseOrderForInboundService.findPurchaseOrderItemById(id);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据多个ID查询采购订单明细
     * @param ids 采购订单明细ID列表
     * @return 采购订单明细列表
     */
    @GetMapping("/items/batch")
    @Operation(summary = "根据多个ID查询采购订单明细", description = "根据多个ID查询采购订单明细")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:read')")
    public ResponseEntity<ApiResponse<List<PurchaseOrderItemForInboundDTO>>> findPurchaseOrderItemsByIds(@RequestParam List<Long> ids) {
        log.info("Finding purchase order items by ids: {}", ids);
        List<PurchaseOrderItemForInboundDTO> response = purchaseOrderForInboundService.findPurchaseOrderItemsByIds(ids);
        return ResponseEntity.ok(ApiResponse.success(response));
    }
}
