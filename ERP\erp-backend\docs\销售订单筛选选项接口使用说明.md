# 销售订单筛选选项接口详细使用说明

## 接口概述

**接口路径**: `GET /api/sales/orders/query/filter-options`

**用途**: 获取指定字段的筛选选项，专门用于表格列头筛选功能，支持级联筛选

**权限要求**: `sales:order:read`

## 核心特性

### ✅ 完全独立使用
- 不依赖任何其他查询条件
- 可以单独调用，无需配合其他接口
- 专为列头筛选设计，与搜索表单分离

### ✅ 高性能优化
- 使用 JPA Criteria API 构建高效查询
- 自动限制返回选项数量（最多50个）
- 按记录数降序排列，优先显示常用选项
- 支持搜索文本过滤，减少网络传输

### ✅ 智能数据处理
- 自动过滤空值和空字符串
- 去重处理，确保选项唯一性
- 包含记录数统计，便于用户了解数据分布
- **统计级别一致性**：所有字段均按销售订单明细级别统计，与主查询结果保持一致

### ✅ 级联筛选支持
- **动态关联筛选**：根据当前已有的筛选条件，动态计算其他字段的可用选项
- **智能排除逻辑**：自动排除当前字段的筛选条件，避免循环依赖
- **实时更新**：筛选条件变化时，其他字段的选项会实时更新
- **多字段联动**：支持多个字段同时筛选，选项会基于所有条件的交集计算

## 请求参数

### 必填参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `fieldName` | string | 字段名称 | `customerName` |

### 可选参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `searchText` | string | 搜索文本，支持模糊匹配 | `华为` |

### 级联筛选参数（可选）

支持传递当前其他筛选条件，用于实现级联筛选功能：

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `filterCustomerNames` | string[] | 客户名称筛选（多选） | `["华为技术有限公司", "小米科技有限公司"]` |
| `filterProductNames` | string[] | 产品名称筛选（多选） | `["产品A", "产品B"]` |
| `filterSalesPersons` | string[] | 销售员筛选（多选） | `["张三", "李四"]` |
| `filterOrderNo` | string | 销售单号筛选 | `SO202501001` |
| `filterProductionOrderNo` | string | 生产单号筛选 | `PO000001` |
| `filterCustomerOrderNo` | string | 客户订单号筛选 | `CUS001` |
| `filterCustomerProductCode` | string | 客方货号筛选 | `CODE001` |
| `filterOrderDateStart` | date | 订单日期开始 | `2025-01-01` |
| `filterOrderDateEnd` | date | 订单日期结束 | `2025-01-31` |
| `filterDeliveryDateStart` | date | 交期开始 | `2025-02-01` |
| `filterDeliveryDateEnd` | date | 交期结束 | `2025-02-28` |
| `filterQuantityMin` | number | 数量最小值 | `100` |
| `filterQuantityMax` | number | 数量最大值 | `1000` |
| `filterTotalAmountMin` | number | 金额最小值 | `10000` |
| `filterTotalAmountMax` | number | 金额最大值 | `100000` |

**注意**：当前字段的筛选条件会被自动排除，避免循环依赖。

### 🚨 **重要：数组参数格式要求**

**后端期望的数组参数格式**：
- ✅ **正确格式**：`filterCustomerNames=华为&filterCustomerNames=小米`
- ❌ **错误格式**：`filterCustomerNames[]=华为&filterCustomerNames[]=小米`

**Spring Boot参数绑定说明**：
- 后端使用`@ModelAttribute`自动绑定URL参数到`List<String>`字段
- Spring Boot支持重复参数名的数组绑定，**不需要**参数名带`[]`后缀
- 参数名带`[]`后缀会导致绑定失败，筛选条件无效

**前端实现要求**：
```typescript
// ✅ 正确的参数构建方式
const params = new URLSearchParams();
filterCustomerNames.forEach(name => {
  params.append('filterCustomerNames', name); // 重复参数名
});

// ❌ 错误的参数构建方式
const params = {
  'filterCustomerNames[]': filterCustomerNames // 带[]后缀
};
```

**Axios配置建议**：
```typescript
// 配置Axios不自动添加[]后缀
axios.defaults.paramsSerializer = {
  indexes: null // 不使用索引格式
};

// 或者使用arrayFormat配置
import qs from 'qs';
axios.defaults.paramsSerializer = (params) => {
  return qs.stringify(params, { arrayFormat: 'repeat' });
};
```

## 支持的字段

### 1. customerName - 客户名称
- **数据来源**: `sales_order.customer_name`
- **查询逻辑**: 按客户名称分组统计
- **排序规则**: 按记录数降序
- **使用场景**: 客户名称列的筛选下拉框

### 2. productName - 产品名称  
- **数据来源**: `sales_order_item.product_name`
- **查询逻辑**: 关联订单明细表，按产品名称分组统计
- **排序规则**: 按记录数降序
- **使用场景**: 产品名称列的筛选下拉框

### 3. salesPerson - 销售员
- **数据来源**: `sales_order.sales_person`
- **查询逻辑**: 按销售员分组统计
- **排序规则**: 按记录数降序
- **使用场景**: 销售员列的筛选下拉框

## 响应数据结构

```typescript
interface FilterOptionDTO {
  label: string;  // 显示文本
  value: string;  // 筛选值（与label相同）
  count: number;  // 该选项对应的记录数
}

type FilterOptionsResponse = FilterOptionDTO[];
```

### 响应示例

```json
{
  "success": true,
  "data": [
    {
      "label": "华为技术有限公司",
      "value": "华为技术有限公司", 
      "count": 156
    },
    {
      "label": "小米科技有限公司",
      "value": "小米科技有限公司",
      "count": 89
    },
    {
      "label": "腾讯科技有限公司", 
      "value": "腾讯科技有限公司",
      "count": 67
    }
  ],
  "message": "操作成功",
  "timestamp": "2025-01-27T10:30:00"
}
```

## 使用示例

### 基础调用

```typescript
// 获取客户名称筛选选项
const getCustomerOptions = async () => {
  const response = await get('/api/sales/orders/query/filter-options', {
    fieldName: 'customerName'
  });
  return response.data; // FilterOptionDTO[]
};

// 获取产品名称筛选选项
const getProductOptions = async () => {
  const response = await get('/api/sales/orders/query/filter-options', {
    fieldName: 'productName'
  });
  return response.data;
};

// 获取销售员筛选选项
const getSalesPersonOptions = async () => {
  const response = await get('/api/sales/orders/query/filter-options', {
    fieldName: 'salesPerson'
  });
  return response.data;
};
```

### 带搜索的调用

```typescript
// 搜索包含"华为"的客户
const searchCustomers = async (searchText: string) => {
  const response = await get('/api/sales/orders/query/filter-options', {
    fieldName: 'customerName',
    searchText: searchText
  });
  return response.data;
};

// 使用示例
const huaweiCustomers = await searchCustomers('华为');
```

### 级联筛选调用

```typescript
// 场景1：单列筛选 - 客户名称筛选"华为"后，获取该客户的产品选项
const getProductOptionsForCustomer = async () => {
  // 🚨 注意：确保数组参数不带[]后缀
  const response = await get('/api/sales/orders/query/filter-options', {
    fieldName: 'productName',
    filterCustomerNames: ['华为技术有限公司'] // 正确：不带[]后缀
  });
  return response.data; // 只返回华为客户的产品选项
};

// 场景2：多列筛选 - 客户="华为" + 品名="产品A"后，获取销售员选项
const getSalesPersonOptionsForFilters = async () => {
  const response = await get('/api/sales/orders/query/filter-options', {
    fieldName: 'salesPerson',
    filterCustomerNames: ['华为技术有限公司'], // 正确：不带[]后缀
    filterProductNames: ['产品A'] // 正确：不带[]后缀
  });
  return response.data; // 只返回同时满足客户和产品条件的销售员选项
};

// 场景3：复杂筛选 - 多个条件组合
const getFilteredOptions = async () => {
  const response = await get('/api/sales/orders/query/filter-options', {
    fieldName: 'customerName',
    filterSalesPersons: ['张三'],
    filterOrderDateStart: '2025-01-01',
    filterOrderDateEnd: '2025-01-31',
    filterQuantityMin: 100
  });
  return response.data; // 基于多个条件的交集计算客户选项
};

// 场景4：前端级联筛选实现
const cascadeFilterExample = async (currentFilters: Record<string, any>, targetField: string) => {
  // 构建级联筛选参数
  const cascadeParams: any = { fieldName: targetField };

  // 添加当前筛选条件（排除目标字段）
  Object.keys(currentFilters).forEach(key => {
    if (key !== targetField && currentFilters[key] && currentFilters[key].length > 0) {
      cascadeParams[key] = currentFilters[key];
    }
  });

  const response = await get('/api/sales/orders/query/filter-options', cascadeParams);
  return response.data;
};
```

### 前端表格集成

```typescript
// Ant Design Vue 表格列配置
const columns = [
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    filters: [], // 动态加载
    onFilter: (value: string, record: any) => record.customerName === value,
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => {
      // 自定义筛选下拉框
      return h(FilterDropdown, {
        fieldName: 'customerName',
        selectedKeys,
        setSelectedKeys,
        confirm,
        clearFilters
      });
    }
  }
];

// 动态加载筛选选项
const loadFilterOptions = async (column: any) => {
  if (column.key === 'customerName') {
    const options = await getCustomerOptions();
    column.filters = options.map(opt => ({
      text: `${opt.label} (${opt.count})`,
      value: opt.value
    }));
  }
};
```

## 后端实现逻辑

### 统计级别说明

**重要：所有筛选选项均按销售订单明细级别统计**

为确保筛选选项的数量与主查询结果保持一致，所有字段的筛选选项都按销售订单明细级别进行统计：

- **customerName**: 统计该客户在所有销售订单明细中出现的次数
- **productName**: 统计该产品在所有销售订单明细中出现的次数
- **salesPerson**: 统计该销售员在所有销售订单明细中出现的次数

这样确保了筛选选项显示的数量与用户在查询结果中看到的行数完全对应。

### 查询构建流程

1. **参数验证**: 检查fieldName是否支持
2. **查询构建**: 使用JPA Criteria API构建查询，JOIN销售订单明细表
3. **条件添加**:
   - 添加搜索文本条件（如果提供）
   - 过滤空值和空字符串
4. **分组统计**: 按字段值分组，统计明细记录数
5. **排序限制**: 按记录数降序，限制50个结果
6. **数据转换**: 转换为FilterOptionDTO格式

### 核心代码逻辑

```java
@Override
@Transactional(readOnly = true)
public List<FilterOptionDTO> getFilterOptions(String fieldName, String searchText, SalesOrderQueryParamDTO currentFilters) {
    // 根据字段名称生成相应的筛选选项
    return switch (fieldName) {
        case "customerName" -> getCustomerNameOptions(searchText, currentFilters);
        case "productName" -> getProductNameOptions(searchText, currentFilters);
        case "salesPerson" -> getSalesPersonOptions(searchText, currentFilters);
        default -> {
            log.warn("Unsupported filter field: {}", fieldName);
            yield List.of();
        }
    };
}
```

### 客户名称选项查询

```java
private List<FilterOptionDTO> getCustomerNameOptions(String searchText, SalesOrderQueryParamDTO currentFilters) {
    CriteriaBuilder cb = entityManager.getCriteriaBuilder();
    CriteriaQuery<Tuple> query = cb.createTupleQuery();
    Root<SalesOrder> orderRoot = query.from(SalesOrder.class);
    Join<SalesOrder, SalesOrderItem> itemJoin = orderRoot.join("items", JoinType.INNER);

    // 选择客户名称和记录数（按明细级别统计，与主查询保持一致）
    query.multiselect(
        orderRoot.get("customerName").alias("customerName"),
        cb.count(itemJoin).alias("count")
    );

    List<Predicate> predicates = new ArrayList<>();

    // 添加搜索文本条件
    if (StringUtils.hasText(searchText)) {
        predicates.add(cb.like(orderRoot.get("customerName"), "%" + searchText + "%"));
    }

    // 添加其他筛选条件（排除当前字段）
    addOtherFilterConditions(cb, orderRoot, itemJoin, currentFilters, predicates, "customerName");

    if (!predicates.isEmpty()) {
        query.where(cb.and(predicates.toArray(new Predicate[0])));
    }

    query.groupBy(orderRoot.get("customerName"))
         .orderBy(cb.desc(cb.count(itemJoin)));

    List<Tuple> results = entityManager.createQuery(query)
            .setMaxResults(50) // 限制选项数量
            .getResultList();

    return results.stream()
            .filter(tuple -> StringUtils.hasText(tuple.get("customerName", String.class)))
            .map(tuple -> FilterOptionDTO.of(
                tuple.get("customerName", String.class),
                tuple.get("count", Long.class)
            ))
            .collect(Collectors.toList());
}
```

## 性能优化

### 1. 查询优化
- 使用索引字段进行查询
- 限制返回结果数量（50个）
- 使用COUNT聚合减少数据传输

### 2. 缓存策略（建议）
```typescript
// 前端缓存实现示例
const filterOptionsCache = new Map<string, { data: FilterOptionDTO[], timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟

const getCachedFilterOptions = async (fieldName: string, searchText?: string) => {
  const cacheKey = `${fieldName}_${searchText || ''}`;
  const cached = filterOptionsCache.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  
  const data = await getFilterOptions(fieldName, searchText);
  filterOptionsCache.set(cacheKey, { data, timestamp: Date.now() });
  return data;
};
```

### 3. 防抖处理
```typescript
// 搜索防抖实现
import { debounce } from 'lodash-es';

const debouncedSearch = debounce(async (fieldName: string, searchText: string) => {
  const options = await getFilterOptions(fieldName, searchText);
  updateFilterOptions(options);
}, 300);
```

## 错误处理

### 常见错误

| 错误类型 | 错误信息 | 解决方案 |
|----------|----------|----------|
| 不支持的字段 | `Unsupported filter field: xxx` | 检查fieldName参数是否正确 |
| 权限不足 | `Access Denied` | 确保用户有`sales:order:read`权限 |
| 参数缺失 | `Required parameter 'fieldName' is missing` | 提供必填的fieldName参数 |
| 数组参数格式错误 | 筛选条件无效，后端接收到null | 检查数组参数是否带了`[]`后缀，应使用重复参数名格式 |
| 级联筛选失效 | 筛选选项未按预期更新 | 确认数组参数格式正确，检查网络请求中的参数格式 |

### 错误处理示例

```typescript
const getFilterOptionsWithErrorHandling = async (fieldName: string, searchText?: string) => {
  try {
    const response = await get('/api/sales/orders/query/filter-options', {
      fieldName,
      searchText
    });

    if (response.success) {
      return response.data;
    } else {
      console.error('获取筛选选项失败:', response.message);
      message.error(response.message);
      return [];
    }
  } catch (error) {
    console.error('网络请求失败:', error);
    message.error('网络请求失败，请检查网络连接');
    return [];
  }
};
```

## 最佳实践

### 1. 正确的参数格式配置
```typescript
// ✅ 推荐：配置Axios正确处理数组参数
import qs from 'qs';

// 全局配置
axios.defaults.paramsSerializer = (params) => {
  return qs.stringify(params, {
    arrayFormat: 'repeat', // 使用重复参数名格式
    skipNulls: true // 跳过null值
  });
};

// 或者针对特定请求配置
const getFilterOptionsWithCorrectFormat = async (fieldName: string, filters: any) => {
  const response = await axios.get('/api/sales/orders/query/filter-options', {
    params: { fieldName, ...filters },
    paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' })
  });
  return response.data;
};
```

### 2. 独立使用模式
```typescript
// ✅ 推荐：独立使用筛选选项接口
const tableConfig = {
  // 主数据查询使用传统接口
  dataSource: () => getSalesOrders(queryParams),

  // 列头筛选使用筛选选项接口
  columnFilters: {
    customerName: () => getFilterOptions('customerName'),
    productName: () => getFilterOptions('productName'),
    salesPerson: () => getFilterOptions('salesPerson')
  }
};
```

### 3. 按需加载
```typescript
// ✅ 推荐：按需加载筛选选项
const loadFilterOptionsOnDemand = async (columnKey: string) => {
  if (!filterOptionsCache.has(columnKey)) {
    const options = await getFilterOptions(columnKey);
    filterOptionsCache.set(columnKey, options);
  }
  return filterOptionsCache.get(columnKey);
};
```

### 4. 用户体验优化
```typescript
// ✅ 推荐：显示记录数，帮助用户了解数据分布
const formatFilterOption = (option: FilterOptionDTO) => ({
  text: `${option.label} (${option.count})`,
  value: option.value
});
```

## 实际应用场景

### 场景1：表格列头筛选
```typescript
// 在表格列头添加筛选功能
const customerNameColumn = {
  title: '客户名称',
  dataIndex: 'customerName',
  key: 'customerName',
  filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => {
    return h(Select, {
      mode: 'multiple',
      placeholder: '选择客户',
      style: { width: '200px' },
      value: selectedKeys,
      onChange: (values) => {
        setSelectedKeys(values);
      },
      onDropdownVisibleChange: async (open) => {
        if (open) {
          // 动态加载筛选选项
          const options = await getFilterOptions('customerName');
          // 更新选项...
        }
      }
    });
  },
  onFilter: (value, record) => record.customerName === value
};
```

### 场景2：搜索建议
```typescript
// 为搜索框提供自动完成建议
const CustomerSearchInput = () => {
  const [options, setOptions] = useState<FilterOptionDTO[]>([]);

  const handleSearch = debounce(async (searchText: string) => {
    if (searchText.length >= 2) {
      const results = await getFilterOptions('customerName', searchText);
      setOptions(results);
    }
  }, 300);

  return (
    <AutoComplete
      options={options.map(opt => ({
        value: opt.value,
        label: `${opt.label} (${opt.count}条记录)`
      }))}
      onSearch={handleSearch}
      placeholder="输入客户名称"
    />
  );
};
```

### 场景3：数据统计展示
```typescript
// 显示数据分布统计
const DataDistribution = () => {
  const [customerStats, setCustomerStats] = useState<FilterOptionDTO[]>([]);

  useEffect(() => {
    const loadStats = async () => {
      const stats = await getFilterOptions('customerName');
      setCustomerStats(stats.slice(0, 10)); // 显示前10名
    };
    loadStats();
  }, []);

  return (
    <Card title="客户订单分布">
      {customerStats.map(stat => (
        <div key={stat.value}>
          {stat.label}: {stat.count}单
        </div>
      ))}
    </Card>
  );
};
```

## 技术细节

### 数据库查询优化

1. **索引使用**
   - `sales_order.customer_name` 字段建议添加索引
   - `sales_order_item.product_name` 字段建议添加索引
   - `sales_order.sales_person` 字段建议添加索引

2. **查询计划**
   ```sql
   -- 客户名称查询的等效SQL
   SELECT customer_name, COUNT(*) as count
   FROM sales_order
   WHERE customer_name LIKE '%搜索文本%'
   GROUP BY customer_name
   ORDER BY count DESC
   LIMIT 50;
   ```

3. **性能监控**
   - 监控查询执行时间
   - 关注慢查询日志
   - 定期分析查询计划

### 内存使用优化

1. **结果集限制**: 最多返回50个选项，避免内存溢出
2. **流式处理**: 使用Stream API进行数据转换
3. **及时释放**: 查询完成后及时释放资源

### 并发处理

1. **只读事务**: 使用`@Transactional(readOnly = true)`
2. **连接池**: 合理配置数据库连接池
3. **超时设置**: 设置合理的查询超时时间

## 总结

销售订单筛选选项接口是一个**完全独立**的接口，专门为表格列头筛选功能设计。它具有以下优势：

### ✅ 核心优势

1. **独立性强**: 不依赖其他查询条件，可单独使用
2. **性能优化**: 限制结果数量，按使用频率排序
3. **用户友好**: 包含记录数统计，支持搜索过滤
4. **架构清晰**: 与搜索表单分离，职责明确
5. **扩展性好**: 易于添加新的筛选字段支持

### 🎯 推荐架构

```
前端表格系统
├── 搜索工具栏 ──→ 传统查询接口 (/api/sales/orders/query)
└── 列头筛选 ──→ 筛选选项接口 (/api/sales/orders/query/filter-options)
```

### 📋 实施建议

1. **前端继续使用传统查询接口**进行主数据查询
2. **列头筛选单独调用筛选选项接口**获取选项数据
3. **两个系统保持独立**，实现最佳的用户体验和系统性能
4. **按需加载筛选选项**，避免不必要的网络请求
5. **实施适当的缓存策略**，提升响应速度
6. **🚨 重要：确保数组参数格式正确**，使用重复参数名而非带`[]`后缀的格式

这种设计既保持了系统的独立性和灵活性，又能充分利用后端提供的筛选选项功能，为用户提供优秀的交互体验。
