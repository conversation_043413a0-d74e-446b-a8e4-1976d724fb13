package com.czerp.erpbackend.common.filter.config;

import com.czerp.erpbackend.common.filter.metadata.FilterFieldMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 筛选字段注册中心
 * 管理各模块的筛选字段配置
 */
@Component
@Slf4j
public class FilterFieldRegistry {
    
    /**
     * 模块字段配置映射
     * key: moduleCode
     * value: Map<fieldName, FilterFieldMetadata>
     */
    private final Map<String, Map<String, FilterFieldMetadata>> moduleFieldsMap = new ConcurrentHashMap<>();
    
    /**
     * 注册模块的筛选字段配置
     * @param moduleCode 模块编码
     * @param fieldConfigs 字段配置列表
     */
    public void registerModuleFields(String moduleCode, List<FilterFieldMetadata> fieldConfigs) {
        if (fieldConfigs == null || fieldConfigs.isEmpty()) {
            log.warn("No field configurations provided for module: {}", moduleCode);
            return;
        }
        
        Map<String, FilterFieldMetadata> fieldsMap = new HashMap<>();
        for (FilterFieldMetadata config : fieldConfigs) {
            if (config.getFieldName() == null) {
                log.warn("Field name is null in configuration for module: {}", moduleCode);
                continue;
            }
            fieldsMap.put(config.getFieldName(), config);
        }
        
        moduleFieldsMap.put(moduleCode, fieldsMap);
        log.info("Registered {} filter fields for module: {}", fieldsMap.size(), moduleCode);
    }
    
    /**
     * 获取模块的字段配置
     * @param moduleCode 模块编码
     * @param fieldName 字段名称
     * @return 字段配置，如果不存在则返回null
     */
    public FilterFieldMetadata getFieldMetadata(String moduleCode, String fieldName) {
        Map<String, FilterFieldMetadata> fieldsMap = moduleFieldsMap.get(moduleCode);
        if (fieldsMap == null) {
            return null;
        }
        return fieldsMap.get(fieldName);
    }
    
    /**
     * 检查模块是否支持指定字段
     * @param moduleCode 模块编码
     * @param fieldName 字段名称
     * @return 是否支持
     */
    public boolean isFieldSupported(String moduleCode, String fieldName) {
        return getFieldMetadata(moduleCode, fieldName) != null;
    }
    
    /**
     * 获取模块支持的所有字段名称
     * @param moduleCode 模块编码
     * @return 字段名称列表
     */
    public List<String> getSupportedFields(String moduleCode) {
        Map<String, FilterFieldMetadata> fieldsMap = moduleFieldsMap.get(moduleCode);
        if (fieldsMap == null) {
            return Collections.emptyList();
        }
        return new ArrayList<>(fieldsMap.keySet());
    }
    
    /**
     * 获取模块的所有字段配置
     * @param moduleCode 模块编码
     * @return 字段配置映射
     */
    public Map<String, FilterFieldMetadata> getModuleFields(String moduleCode) {
        Map<String, FilterFieldMetadata> fieldsMap = moduleFieldsMap.get(moduleCode);
        return fieldsMap != null ? new HashMap<>(fieldsMap) : Collections.emptyMap();
    }
    
    /**
     * 获取所有已注册的模块编码
     * @return 模块编码列表
     */
    public List<String> getRegisteredModules() {
        return new ArrayList<>(moduleFieldsMap.keySet());
    }
    
    /**
     * 清除模块的字段配置
     * @param moduleCode 模块编码
     */
    public void clearModuleFields(String moduleCode) {
        moduleFieldsMap.remove(moduleCode);
        log.info("Cleared filter fields for module: {}", moduleCode);
    }
    
    /**
     * 清除所有字段配置
     */
    public void clearAllFields() {
        moduleFieldsMap.clear();
        log.info("Cleared all filter field configurations");
    }
}
