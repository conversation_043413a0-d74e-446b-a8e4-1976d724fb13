package com.czerp.erpbackend.sales.filter;

import com.czerp.erpbackend.common.filter.dto.FilterOptionDTO;
import com.czerp.erpbackend.common.filter.dto.FilterRequest;
import com.czerp.erpbackend.common.filter.handler.FilterHandler;
import com.czerp.erpbackend.common.filter.metadata.FilterFieldMetadata;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售订单动态字段筛选处理器
 * 处理动态构建的筛选字段：specification, productionSpecification
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SalesOrderDynamicFieldHandler implements FilterHandler {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    public List<FilterOptionDTO> getFilterOptions(FilterRequest request, FilterFieldMetadata metadata) {
        log.debug("Getting dynamic field filter options for field: {}", request.getFieldName());
        
        try {
            switch (request.getFieldName()) {
                case "specification":
                    return getSpecificationOptions(request);
                case "productionSpecification":
                    return getProductionSpecificationOptions(request);
                default:
                    log.warn("Unsupported dynamic field: {}", request.getFieldName());
                    return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Failed to get dynamic field filter options for field: {}, error: {}", 
                     request.getFieldName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public boolean supports(String fieldName) {
        return List.of("specification", "productionSpecification").contains(fieldName);
    }
    
    /**
     * 获取规格筛选选项（动态构建字段）
     */
    private List<FilterOptionDTO> getSpecificationOptions(FilterRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<SalesOrder> orderRoot = query.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> itemJoin = orderRoot.join("items", JoinType.INNER);

        // 动态构建规格字段：长×宽×高 单位
        Expression<String> specificationExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
            cb.coalesce(itemJoin.get("length"), cb.literal(BigDecimal.ZERO)).as(String.class),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("width"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("height"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal(" ")),
            cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

        query.select(specificationExpr).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(request.getSearchText())) {
            predicates.add(cb.like(specificationExpr, "%" + request.getSearchText() + "%"));
        }

        // 过滤空值和零值
        predicates.add(cb.isNotNull(itemJoin.get("length")));
        predicates.add(cb.isNotNull(itemJoin.get("width")));
        predicates.add(cb.isNotNull(itemJoin.get("height")));
        predicates.add(cb.or(
            cb.notEqual(itemJoin.get("length"), BigDecimal.ZERO),
            cb.notEqual(itemJoin.get("width"), BigDecimal.ZERO),
            cb.notEqual(itemJoin.get("height"), BigDecimal.ZERO)
        ));

        // 添加级联筛选条件（排除当前字段）
        if (Boolean.TRUE.equals(request.getEnableCascade())) {
            addDynamicFieldCascadeFilterConditions(cb, orderRoot, itemJoin, request, predicates, "specification");
        }

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(specificationExpr));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(request.getMaxResults() != null ? request.getMaxResults() : 50)
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .filter(spec -> !spec.trim().equals("0×0×0") && !spec.trim().equals("0×0×0 "))
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取生产规格筛选选项（动态构建字段）
     * 实现与查询结果一致的回退机制逻辑
     */
    private List<FilterOptionDTO> getProductionSpecificationOptions(FilterRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<SalesOrder> orderRoot = query.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> itemJoin = orderRoot.join("items", JoinType.INNER);

        // 构建生产规格表达式：优先使用生产尺寸，为空时使用产品尺寸
        Expression<String> productionSpecExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
            cb.coalesce(itemJoin.get("productionLength"), cb.literal(BigDecimal.ZERO)).as(String.class),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("productionWidth"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("productionHeight"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal(" ")),
            cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

        // 构建产品规格表达式（回退选项）
        Expression<String> productSpecExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
            cb.coalesce(itemJoin.get("length"), cb.literal(BigDecimal.ZERO)).as(String.class),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("width"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("height"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal(" ")),
            cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

        // 实现回退机制 - 优先使用生产规格，为空时使用产品规格
        // 判断生产规格是否有效（排除各种无效格式）
        Predicate hasValidProductionSpec = cb.and(
            cb.isNotNull(itemJoin.get("productionLength")),
            cb.isNotNull(itemJoin.get("productionWidth")),
            cb.isNotNull(itemJoin.get("productionHeight")),
            cb.or(
                cb.notEqual(itemJoin.get("productionLength"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("productionWidth"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("productionHeight"), BigDecimal.ZERO)
            )
        );

        // 使用CASE WHEN实现回退逻辑
        Expression<String> finalSpecExpr = cb.<String>selectCase()
            .when(hasValidProductionSpec, productionSpecExpr)
            .otherwise(productSpecExpr);

        query.select(finalSpecExpr).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 搜索文本条件应该基于最终的规格表达式
        if (StringUtils.hasText(request.getSearchText())) {
            predicates.add(cb.like(finalSpecExpr, "%" + request.getSearchText() + "%"));
        }

        // 优化过滤条件，实现与查询结果一致的逻辑
        // 至少有生产尺寸或产品尺寸其中之一有效
        Predicate hasValidProductionDimensions = cb.and(
            cb.isNotNull(itemJoin.get("productionLength")),
            cb.isNotNull(itemJoin.get("productionWidth")),
            cb.isNotNull(itemJoin.get("productionHeight")),
            cb.or(
                cb.notEqual(itemJoin.get("productionLength"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("productionWidth"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("productionHeight"), BigDecimal.ZERO)
            )
        );

        Predicate hasValidProductDimensions = cb.and(
            cb.isNotNull(itemJoin.get("length")),
            cb.isNotNull(itemJoin.get("width")),
            cb.isNotNull(itemJoin.get("height")),
            cb.or(
                cb.notEqual(itemJoin.get("length"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("width"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("height"), BigDecimal.ZERO)
            )
        );

        // 至少有生产尺寸或产品尺寸其中之一有效
        predicates.add(cb.or(hasValidProductionDimensions, hasValidProductDimensions));

        // 添加级联筛选条件（排除当前字段）
        if (Boolean.TRUE.equals(request.getEnableCascade())) {
            addDynamicFieldCascadeFilterConditions(cb, orderRoot, itemJoin, request, predicates, "productionSpecification");
        }

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按最终规格表达式排序
        query.orderBy(cb.asc(finalSpecExpr));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(request.getMaxResults() != null ? request.getMaxResults() : 50)
                .getResultList();

        // 过滤逻辑，排除无效的规格值（包括各种零值格式）
        return results.stream()
                .filter(StringUtils::hasText)
                .filter(spec -> !spec.trim().isEmpty()) // 排除空字符串
                .filter(spec -> {
                    // 排除各种零值格式：0×0×0, 0.0×0.0×0.0, 0.00×0.00×0.00 等
                    String trimmed = spec.trim();
                    return !trimmed.matches("^0(\\.0+)?×0(\\.0+)?×0(\\.0+)?( .*)?$");
                })
                .distinct() // 确保去重
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }
    
    /**
     * 为动态字段添加级联筛选条件
     */
    private void addDynamicFieldCascadeFilterConditions(CriteriaBuilder cb, Root<SalesOrder> orderRoot,
                                                       Join<SalesOrder, SalesOrderItem> itemJoin,
                                                       FilterRequest request, List<Predicate> predicates, String excludeField) {
        if (request.getCurrentFilters() == null || request.getCurrentFilters().isEmpty()) {
            return;
        }

        // TODO: 实现级联筛选逻辑
        // 这里需要根据当前筛选条件构建子查询，与原有的CascadeFilterManager逻辑保持一致
        log.debug("Cascade filtering for dynamic field: {} not fully implemented yet", excludeField);
    }
}
