package com.czerp.erpbackend.customer.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.customer.dto.CreateCustomerRequest;
import com.czerp.erpbackend.customer.dto.CustomerDTO;
import com.czerp.erpbackend.customer.dto.CustomerQueryRequest;
import com.czerp.erpbackend.customer.dto.UpdateCustomerRequest;

import java.util.List;

/**
 * 客户服务接口
 */
public interface CustomerService {
    
    /**
     * 分页查询客户列表
     * @param request 查询请求
     * @return 客户分页列表
     */
    PageResponse<CustomerDTO> findCustomers(CustomerQueryRequest request);
    
    /**
     * 根据ID查询客户
     * @param id 客户ID
     * @return 客户信息
     */
    CustomerDTO findCustomerById(String id);
    
    /**
     * 根据客户编码查询客户
     * @param customerCode 客户编码
     * @return 客户信息
     */
    CustomerDTO findCustomerByCode(String customerCode);
    
    /**
     * 创建客户
     * @param request 创建请求
     * @return 客户信息
     */
    CustomerDTO createCustomer(CreateCustomerRequest request);
    
    /**
     * 更新客户
     * @param id 客户ID
     * @param request 更新请求
     * @return 客户信息
     */
    CustomerDTO updateCustomer(String id, UpdateCustomerRequest request);
    
    /**
     * 删除客户
     * @param id 客户ID
     */
    void deleteCustomer(String id);
    
    /**
     * 批量删除客户
     * @param ids 客户ID列表
     */
    void batchDeleteCustomers(List<String> ids);
}
