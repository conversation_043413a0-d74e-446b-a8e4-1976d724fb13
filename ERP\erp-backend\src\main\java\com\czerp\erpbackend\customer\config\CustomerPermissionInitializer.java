package com.czerp.erpbackend.customer.config;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import com.czerp.erpbackend.system.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 客户模块权限初始化
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(3)
public class CustomerPermissionInitializer implements CommandLineRunner {

    private final PermissionService permissionService;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing customer module permissions...");

        try {
            // 获取系统模块权限
            Permission systemModule = permissionService.findByCode("system")
                    .orElseThrow(() -> new RuntimeException("System module permission not found"));

            // 创建客户模块权限
            Permission customerModule = createModulePermission(systemModule.getId());

            // 创建客户管理权限
            Permission customerManagement = createCustomerManagementPermission(customerModule.getId());

            // 创建客户档案权限
            createCustomerPermissions(customerManagement.getId());

            // 创建客户分类权限
            createCustomerCategoryPermissions(customerManagement.getId());

            // 为管理员角色分配客户模块权限
            assignPermissionsToAdminRole();

            log.info("Customer module permissions initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize customer module permissions", e);
        }
    }

    /**
     * 创建模块权限
     * @param parentId 父权限ID
     * @return 模块权限
     */
    private Permission createModulePermission(String parentId) {
        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setName("客户模块");
        permission.setCode("customer");
        permission.setType("module");
        permission.setParentId(parentId);
        permission.setPath(null);
        permission.setComponent(null);
        permission.setIcon("team");
        permission.setSort(30);
        permission.setStatus("active");

        return permissionService.saveIfNotExists(permission);
    }

    /**
     * 创建客户管理权限
     * @param parentId 父权限ID
     * @return 客户管理权限
     */
    private Permission createCustomerManagementPermission(String parentId) {
        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setName("客户管理");
        permission.setCode("customer:management");
        permission.setType("menu");
        permission.setParentId(parentId);
        permission.setPath("/customer");
        permission.setComponent("RouteView");
        permission.setIcon("team");
        permission.setSort(10);
        permission.setStatus("active");

        return permissionService.saveIfNotExists(permission);
    }

    /**
     * 创建客户档案权限
     * @param parentId 父权限ID
     */
    private void createCustomerPermissions(String parentId) {
        // 客户档案菜单
        Permission customerMenu = new Permission();
        customerMenu.setId(UUID.randomUUID().toString());
        customerMenu.setName("客户档案");
        customerMenu.setCode("customer:list");
        customerMenu.setType("menu");
        customerMenu.setParentId(parentId);
        customerMenu.setPath("/customer/list");
        customerMenu.setComponent("customer/CustomerList");
        customerMenu.setIcon("profile");
        customerMenu.setSort(10);
        customerMenu.setStatus("active");

        Permission savedMenu = permissionService.saveIfNotExists(customerMenu);

        // 客户档案按钮权限
        List<Permission> permissions = new ArrayList<>();

        // 查看权限
        Permission viewPermission = new Permission();
        viewPermission.setId(UUID.randomUUID().toString());
        viewPermission.setName("查看客户");
        viewPermission.setCode("customer:read");
        viewPermission.setType("button");
        viewPermission.setParentId(savedMenu.getId());
        viewPermission.setSort(10);
        permissions.add(viewPermission);

        // 创建权限
        Permission createPermission = new Permission();
        createPermission.setId(UUID.randomUUID().toString());
        createPermission.setName("创建客户");
        createPermission.setCode("customer:create");
        createPermission.setType("button");
        createPermission.setParentId(savedMenu.getId());
        createPermission.setSort(20);
        permissions.add(createPermission);

        // 更新权限
        Permission updatePermission = new Permission();
        updatePermission.setId(UUID.randomUUID().toString());
        updatePermission.setName("更新客户");
        updatePermission.setCode("customer:update");
        updatePermission.setType("button");
        updatePermission.setParentId(savedMenu.getId());
        updatePermission.setSort(30);
        permissions.add(updatePermission);

        // 删除权限
        Permission deletePermission = new Permission();
        deletePermission.setId(UUID.randomUUID().toString());
        deletePermission.setName("删除客户");
        deletePermission.setCode("customer:delete");
        deletePermission.setType("button");
        deletePermission.setParentId(savedMenu.getId());
        deletePermission.setSort(40);
        permissions.add(deletePermission);

        // 保存权限
        permissionService.saveAllIfNotExist(permissions);
    }

    /**
     * 创建客户分类权限
     * @param parentId 父权限ID
     */
    private void createCustomerCategoryPermissions(String parentId) {
        // 客户分类菜单
        Permission categoryMenu = new Permission();
        categoryMenu.setId(UUID.randomUUID().toString());
        categoryMenu.setName("客户分类");
        categoryMenu.setCode("customer:category:list");
        categoryMenu.setType("menu");
        categoryMenu.setParentId(parentId);
        categoryMenu.setPath("/customer/category");
        categoryMenu.setComponent("customer/CustomerCategoryList");
        categoryMenu.setIcon("apartment");
        categoryMenu.setSort(20);
        categoryMenu.setStatus("active");

        permissionService.saveIfNotExists(categoryMenu);

        // 注意：客户分类使用与客户档案相同的权限代码，因此不需要创建额外的按钮权限
    }

    /**
     * 为管理员角色分配客户模块权限
     */
    private void assignPermissionsToAdminRole() {
        log.info("Assigning customer permissions to admin role...");

        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping permission assignment");
            return;
        }

        // 获取所有客户相关权限
        List<String> permissionCodes = Arrays.asList(
                "customer",
                "customer:management",
                "customer:list",
                "customer:read",
                "customer:create",
                "customer:update",
                "customer:delete",
                "customer:category:list"
        );

        List<Permission> permissions = permissionService.findByCodes(permissionCodes);

        // 为管理员角色分配权限
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : permissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString());
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermission.setCreateBy("system");
                rolePermission.setCreateTime(LocalDateTime.now());
                rolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }

        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} customer permissions to admin role", rolePermissions.size());
        } else {
            log.info("All customer permissions already assigned to admin role, skipping");
        }
    }
}
