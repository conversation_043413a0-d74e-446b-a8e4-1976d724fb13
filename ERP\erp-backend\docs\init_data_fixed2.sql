-- 修复版初始化数据脚本 (第二版)
-- 确保所有数据都符合数据库表的列长度限制

-- 设置当前时间
SET @now = NOW();
SET @admin_id = (SELECT id FROM sys_user WHERE username = 'admin' LIMIT 1);

-- 初始化部门数据
INSERT INTO sys_department (id, created_by, created_time, is_deleted, code, level, manager_id, name, parent_id, sort, status) VALUES
('d001', @admin_id, @now, 0, 'HQ', 1, @admin_id, '总部', NULL, 1, 'active'),
('d002', @admin_id, @now, 0, 'RD', 2, @admin_id, '研发部', 'd001', 1, 'active'),
('d003', @admin_id, @now, 0, 'SALES', 2, @admin_id, '销售部', 'd001', 2, 'active'),
('d004', @admin_id, @now, 0, 'HR', 2, @admin_id, '人力资源', 'd001', 3, 'active'),
('d005', @admin_id, @now, 0, 'FINANCE', 2, @admin_id, '财务部', 'd001', 4, 'active');

-- 初始化权限数据
-- 用户管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, code, name, type, status) VALUES
('p001', @admin_id, @now, 0, 'user', '用户管理', 'menu', 'active'),
('p002', @admin_id, @now, 0, 'user:list', '用户列表', 'button', 'active'),
('p003', @admin_id, @now, 0, 'user:read', '用户详情', 'button', 'active'),
('p004', @admin_id, @now, 0, 'user:create', '创建用户', 'button', 'active'),
('p005', @admin_id, @now, 0, 'user:update', '更新用户', 'button', 'active'),
('p006', @admin_id, @now, 0, 'user:delete', '删除用户', 'button', 'active');

-- 角色管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, code, name, type, status) VALUES
('p007', @admin_id, @now, 0, 'role', '角色管理', 'menu', 'active'),
('p008', @admin_id, @now, 0, 'role:list', '角色列表', 'button', 'active'),
('p009', @admin_id, @now, 0, 'role:read', '角色详情', 'button', 'active'),
('p010', @admin_id, @now, 0, 'role:create', '创建角色', 'button', 'active'),
('p011', @admin_id, @now, 0, 'role:update', '更新角色', 'button', 'active'),
('p012', @admin_id, @now, 0, 'role:delete', '删除角色', 'button', 'active');

-- 权限管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, code, name, type, status) VALUES
('p013', @admin_id, @now, 0, 'permission', '权限管理', 'menu', 'active'),
('p014', @admin_id, @now, 0, 'permission:list', '权限列表', 'button', 'active'),
('p015', @admin_id, @now, 0, 'permission:read', '权限详情', 'button', 'active');

-- 部门管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, code, name, type, status) VALUES
('p016', @admin_id, @now, 0, 'department', '部门管理', 'menu', 'active'),
('p017', @admin_id, @now, 0, 'department:list', '部门列表', 'button', 'active'),
('p018', @admin_id, @now, 0, 'department:read', '部门详情', 'button', 'active'),
('p019', @admin_id, @now, 0, 'department:create', '创建部门', 'button', 'active'),
('p020', @admin_id, @now, 0, 'department:update', '更新部门', 'button', 'active'),
('p021', @admin_id, @now, 0, 'department:delete', '删除部门', 'button', 'active');

-- 初始化角色数据
INSERT INTO sys_role (id, created_by, created_time, is_deleted, code, name, status) VALUES
('r001', @admin_id, @now, 0, 'admin', '管理员', 'active'),
('r002', @admin_id, @now, 0, 'user', '普通用户', 'active'),
('r003', @admin_id, @now, 0, 'hr', '人力资源', 'active'),
('r004', @admin_id, @now, 0, 'finance', '财务人员', 'active');

-- 为管理员角色分配所有权限
INSERT INTO sys_role_permission (id, create_by, create_time, permission_id, role_id) VALUES
('rp001', @admin_id, @now, 'p001', 'r001'),
('rp002', @admin_id, @now, 'p002', 'r001'),
('rp003', @admin_id, @now, 'p003', 'r001'),
('rp004', @admin_id, @now, 'p004', 'r001'),
('rp005', @admin_id, @now, 'p005', 'r001'),
('rp006', @admin_id, @now, 'p006', 'r001'),
('rp007', @admin_id, @now, 'p007', 'r001'),
('rp008', @admin_id, @now, 'p008', 'r001'),
('rp009', @admin_id, @now, 'p009', 'r001'),
('rp010', @admin_id, @now, 'p010', 'r001'),
('rp011', @admin_id, @now, 'p011', 'r001'),
('rp012', @admin_id, @now, 'p012', 'r001'),
('rp013', @admin_id, @now, 'p013', 'r001'),
('rp014', @admin_id, @now, 'p014', 'r001'),
('rp015', @admin_id, @now, 'p015', 'r001'),
('rp016', @admin_id, @now, 'p016', 'r001'),
('rp017', @admin_id, @now, 'p017', 'r001'),
('rp018', @admin_id, @now, 'p018', 'r001'),
('rp019', @admin_id, @now, 'p019', 'r001'),
('rp020', @admin_id, @now, 'p020', 'r001'),
('rp021', @admin_id, @now, 'p021', 'r001');

-- 为普通用户角色分配基本权限
INSERT INTO sys_role_permission (id, create_by, create_time, permission_id, role_id) VALUES
('rp022', @admin_id, @now, 'p002', 'r002'), -- 用户列表
('rp023', @admin_id, @now, 'p003', 'r002'), -- 用户详情
('rp024', @admin_id, @now, 'p008', 'r002'), -- 角色列表
('rp025', @admin_id, @now, 'p009', 'r002'), -- 角色详情
('rp026', @admin_id, @now, 'p017', 'r002'); -- 部门列表

-- 为人力资源角色分配权限
INSERT INTO sys_role_permission (id, create_by, create_time, permission_id, role_id) VALUES
('rp027', @admin_id, @now, 'p001', 'r003'), -- 用户管理
('rp028', @admin_id, @now, 'p002', 'r003'), -- 用户列表
('rp029', @admin_id, @now, 'p003', 'r003'), -- 用户详情
('rp030', @admin_id, @now, 'p004', 'r003'), -- 创建用户
('rp031', @admin_id, @now, 'p005', 'r003'), -- 更新用户
('rp032', @admin_id, @now, 'p016', 'r003'), -- 部门管理
('rp033', @admin_id, @now, 'p017', 'r003'), -- 部门列表
('rp034', @admin_id, @now, 'p018', 'r003'); -- 部门详情

-- 为财务人员角色分配权限
INSERT INTO sys_role_permission (id, create_by, create_time, permission_id, role_id) VALUES
('rp035', @admin_id, @now, 'p002', 'r004'), -- 用户列表
('rp036', @admin_id, @now, 'p003', 'r004'), -- 用户详情
('rp037', @admin_id, @now, 'p017', 'r004'); -- 部门列表

-- 为管理员用户分配管理员角色
INSERT INTO sys_user_role (id, create_by, create_time, role_id, user_id) VALUES
('ur001', @admin_id, @now, 'r001', @admin_id);
