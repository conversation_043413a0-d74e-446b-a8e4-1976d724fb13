package com.czerp.erpbackend.purchase.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.purchase.dto.CreateSupplierCategoryRequest;
import com.czerp.erpbackend.purchase.dto.SupplierCategoryDTO;
import com.czerp.erpbackend.purchase.dto.UpdateSupplierCategoryRequest;
import com.czerp.erpbackend.purchase.service.SupplierCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


import java.util.List;

/**
 * 供应商分类控制器
 */
@RestController
@RequestMapping("/supplier-categories")
@Tag(name = "供应商分类管理", description = "供应商分类管理相关接口")
@RequiredArgsConstructor
@Slf4j
public class SupplierCategoryController {

    private final SupplierCategoryService categoryService;

    /**
     * 查询所有供应商分类
     * @return 供应商分类列表
     */
    @GetMapping
    @Operation(summary = "查询所有供应商分类", description = "查询所有供应商分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:read')")
    public ResponseEntity<ApiResponse<List<SupplierCategoryDTO>>> findAllCategories() {
        log.info("Finding all supplier categories");
        List<SupplierCategoryDTO> categories = categoryService.findAllCategories();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }

    /**
     * 查询所有启用的供应商分类
     * @return 供应商分类列表
     */
    @GetMapping("/active")
    @Operation(summary = "查询所有启用的供应商分类", description = "查询所有启用的供应商分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:read')")
    public ResponseEntity<ApiResponse<List<SupplierCategoryDTO>>> findAllActiveCategories() {
        log.info("Finding all active supplier categories");
        List<SupplierCategoryDTO> categories = categoryService.findAllActiveCategories();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }

    /**
     * 根据ID查询供应商分类
     * @param id 供应商分类ID
     * @return 供应商分类信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询供应商分类", description = "根据ID查询供应商分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:read')")
    public ResponseEntity<ApiResponse<SupplierCategoryDTO>> findCategoryById(@PathVariable String id) {
        log.info("Finding supplier category by id: {}", id);
        SupplierCategoryDTO category = categoryService.findCategoryById(id);
        return ResponseEntity.ok(ApiResponse.success(category));
    }

    /**
     * 创建供应商分类
     * @param request 创建请求
     * @return 供应商分类信息
     */
    @PostMapping
    @Operation(summary = "创建供应商分类", description = "创建供应商分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:create')")
    public ResponseEntity<ApiResponse<SupplierCategoryDTO>> createCategory(@Valid @RequestBody CreateSupplierCategoryRequest request) {
        log.info("Creating supplier category with request: {}", request);
        SupplierCategoryDTO category = categoryService.createCategory(request);
        return ResponseEntity.ok(ApiResponse.success(category));
    }

    /**
     * 更新供应商分类
     * @param id 供应商分类ID
     * @param request 更新请求
     * @return 供应商分类信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新供应商分类", description = "更新供应商分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:update')")
    public ResponseEntity<ApiResponse<SupplierCategoryDTO>> updateCategory(
            @PathVariable String id,
            @Valid @RequestBody UpdateSupplierCategoryRequest request) {
        log.info("Updating supplier category with id: {} and request: {}", id, request);
        SupplierCategoryDTO category = categoryService.updateCategory(id, request);
        return ResponseEntity.ok(ApiResponse.success(category));
    }

    /**
     * 删除供应商分类
     * @param id 供应商分类ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除供应商分类", description = "删除供应商分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteCategory(@PathVariable String id) {
        log.info("Deleting supplier category with id: {}", id);
        categoryService.deleteCategory(id);
        return ResponseEntity.ok(ApiResponse.success());
    }
}
