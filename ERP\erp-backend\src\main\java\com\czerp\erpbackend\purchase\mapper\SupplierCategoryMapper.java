package com.czerp.erpbackend.purchase.mapper;

import com.czerp.erpbackend.purchase.dto.CreateSupplierCategoryRequest;
import com.czerp.erpbackend.purchase.dto.SupplierCategoryDTO;
import com.czerp.erpbackend.purchase.dto.UpdateSupplierCategoryRequest;
import com.czerp.erpbackend.purchase.entity.SupplierCategory;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 供应商分类Mapper
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SupplierCategoryMapper {

    /**
     * 实体转DTO
     * @param category 实体
     * @return DTO
     */
    @Mapping(source = "createdTime", target = "createTime")
    @Mapping(source = "updatedTime", target = "updateTime")
    SupplierCategoryDTO toDto(SupplierCategory category);

    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    SupplierCategory toEntity(CreateSupplierCategoryRequest request);

    /**
     * 更新实体
     * @param request 更新请求
     * @param category 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "categoryCode", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateSupplierCategoryRequest request, @MappingTarget SupplierCategory category);
}
