-- 验证字符集修复结果
-- 执行此脚本来确认修复是否成功

-- ========================================
-- 1. 验证所有表都使用正确的字符集
-- ========================================
SELECT 
    '所有表字符集检查' as '检查项目',
    COUNT(*) as '总表数',
    SUM(CASE WHEN TABLE_COLLATION = 'utf8mb4_0900_ai_ci' THEN 1 ELSE 0 END) as '正确字符集表数',
    SUM(CASE WHEN TABLE_COLLATION != 'utf8mb4_0900_ai_ci' THEN 1 ELSE 0 END) as '错误字符集表数',
    CASE 
        WHEN SUM(CASE WHEN TABLE_COLLATION != 'utf8mb4_0900_ai_ci' THEN 1 ELSE 0 END) = 0 
        THEN '✓ 全部正确' 
        ELSE '✗ 仍有问题' 
    END as '状态'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'czerp_web';

-- ========================================
-- 2. 列出仍有问题的表（如果有）
-- ========================================
SELECT 
    TABLE_NAME as '问题表名', 
    TABLE_COLLATION as '当前字符集',
    '应该是 utf8mb4_0900_ai_ci' as '期望字符集'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'czerp_web' 
    AND TABLE_COLLATION != 'utf8mb4_0900_ai_ci'
ORDER BY TABLE_NAME;

-- ========================================
-- 3. 验证关键字段的字符集
-- ========================================
SELECT 
    '关键字段检查' as '检查项目',
    TABLE_NAME as '表名',
    COLUMN_NAME as '字段名',
    COLLATION_NAME as '当前字符集',
    CASE 
        WHEN COLLATION_NAME = 'utf8mb4_0900_ai_ci' THEN '✓ 正确'
        ELSE '✗ 错误'
    END as '状态'
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'czerp_web' 
    AND TABLE_NAME IN ('sales_order_item', 'purchase_order_item', 'purchase_order')
    AND COLUMN_NAME IN ('id', 'source_sales_order_item_id', 'purchase_order_no')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- ========================================
-- 4. 测试之前失败的查询
-- ========================================
-- 这个查询之前会因为字符集冲突而失败，现在应该能正常执行
SELECT 
    '问题查询测试' as '测试项目',
    COUNT(*) as '查询结果',
    '如果能看到数字说明修复成功' as '说明'
FROM sales_order so1_0 
JOIN sales_order_item i1_0 ON so1_0.id = i1_0.order_id 
WHERE i1_0.id IN (
    SELECT poi1_0.source_sales_order_item_id 
    FROM purchase_order_item poi1_0 
    JOIN purchase_order po1_0 ON po1_0.id = poi1_0.purchase_order_id 
    WHERE po1_0.purchase_order_no IN ('CG2025051900003')
);

-- ========================================
-- 5. 测试其他可能的字符集相关查询
-- ========================================

-- 测试采购单号筛选
SELECT 
    '采购单号筛选测试' as '测试项目',
    COUNT(DISTINCT po.purchase_order_no) as '采购单数量',
    '能正常执行说明修复成功' as '说明'
FROM purchase_order po
JOIN purchase_order_item poi ON po.id = poi.purchase_order_id
WHERE poi.source_sales_order_item_id IS NOT NULL;

-- 测试供应商名称筛选
SELECT 
    '供应商筛选测试' as '测试项目',
    COUNT(DISTINCT po.supplier_name) as '供应商数量',
    '能正常执行说明修复成功' as '说明'
FROM purchase_order po
JOIN purchase_order_item poi ON po.id = poi.purchase_order_id
WHERE poi.source_sales_order_item_id IS NOT NULL
    AND po.supplier_name IS NOT NULL;

-- ========================================
-- 6. 性能测试（可选）
-- ========================================
-- 测试字符集统一后的查询性能
EXPLAIN SELECT 
    soi.id,
    soi.production_order_no,
    po.purchase_order_no,
    po.supplier_name
FROM sales_order_item soi
JOIN purchase_order_item poi ON soi.id = poi.source_sales_order_item_id
JOIN purchase_order po ON poi.purchase_order_id = po.id
WHERE po.purchase_order_no LIKE 'CG%'
LIMIT 10;

-- ========================================
-- 7. 最终验证报告
-- ========================================
SELECT 
    '=== 修复验证报告 ===' as '报告',
    '' as '详情';

SELECT 
    '数据库名' as '项目',
    'czerp_web' as '值';

SELECT 
    '修复时间' as '项目',
    NOW() as '值';

SELECT 
    '总表数' as '项目',
    COUNT(*) as '值'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'czerp_web';

SELECT 
    '正确字符集表数' as '项目',
    COUNT(*) as '值'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'czerp_web' 
    AND TABLE_COLLATION = 'utf8mb4_0900_ai_ci';

SELECT 
    '错误字符集表数' as '项目',
    COUNT(*) as '值'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'czerp_web' 
    AND TABLE_COLLATION != 'utf8mb4_0900_ai_ci';

SELECT 
    '修复状态' as '项目',
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.TABLES 
              WHERE TABLE_SCHEMA = 'czerp_web' 
                AND TABLE_COLLATION != 'utf8mb4_0900_ai_ci') = 0 
        THEN '✓ 修复成功，所有表字符集已统一'
        ELSE '✗ 修复未完成，仍有表使用错误字符集'
    END as '值';

-- ========================================
-- 执行说明
-- ========================================
/*
执行此脚本后，请检查：

1. 所有表字符集检查：
   - 错误字符集表数应该为 0
   - 状态应该显示 "✓ 全部正确"

2. 关键字段检查：
   - 所有字段状态都应该显示 "✓ 正确"

3. 问题查询测试：
   - 应该能正常执行并返回数字结果
   - 不应该出现字符集冲突错误

4. 最终验证报告：
   - 修复状态应该显示 "✓ 修复成功，所有表字符集已统一"

如果所有检查都通过，说明字符集冲突问题已经完全解决。
如果仍有问题，请检查 fix_collation_conflicts.sql 脚本是否完全执行成功。
*/
