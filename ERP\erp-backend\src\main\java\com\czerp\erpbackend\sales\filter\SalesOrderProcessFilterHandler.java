package com.czerp.erpbackend.sales.filter;

import com.czerp.erpbackend.common.filter.dto.FilterOptionDTO;
import com.czerp.erpbackend.common.filter.dto.FilterRequest;
import com.czerp.erpbackend.common.filter.handler.FilterHandler;
import com.czerp.erpbackend.common.filter.metadata.FilterFieldMetadata;
import com.czerp.erpbackend.sales.entity.SalesOrderProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售订单工序筛选处理器
 * 处理工序相关的筛选字段：processes
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SalesOrderProcessFilterHandler implements FilterHandler {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    public List<FilterOptionDTO> getFilterOptions(FilterRequest request, FilterFieldMetadata metadata) {
        log.debug("Getting process filter options for field: {}", request.getFieldName());
        
        try {
            switch (request.getFieldName()) {
                case "processes":
                    return getProcessesOptions(request);
                default:
                    log.warn("Unsupported process field: {}", request.getFieldName());
                    return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Failed to get process filter options for field: {}, error: {}", 
                     request.getFieldName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public boolean supports(String fieldName) {
        return "processes".equals(fieldName);
    }
    
    /**
     * 获取工序筛选选项
     */
    private List<FilterOptionDTO> getProcessesOptions(FilterRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<SalesOrderProcess> processRoot = query.from(SalesOrderProcess.class);

        // 选择工序名称，去重
        query.select(processRoot.get("processName")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(request.getSearchText())) {
            predicates.add(cb.like(processRoot.get("processName"), "%" + request.getSearchText() + "%"));
        }

        // 过滤空值和已删除的记录
        predicates.add(cb.isNotNull(processRoot.get("processName")));
        predicates.add(cb.notEqual(processRoot.get("processName"), ""));
        predicates.add(cb.equal(processRoot.get("isDeleted"), false));

        // 只查询有关联销售订单明细的工序
        predicates.add(cb.isNotNull(processRoot.get("orderItemId")));

        // 添加级联筛选条件（排除当前字段）
        if (Boolean.TRUE.equals(request.getEnableCascade())) {
            addProcessCascadeFilterConditions(cb, processRoot, request, predicates, "processes");
        }

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(processRoot.get("processName")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(request.getMaxResults() != null ? request.getMaxResults() : 50)
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }
    
    /**
     * 为工序表子查询添加级联筛选条件
     */
    private void addProcessCascadeFilterConditions(CriteriaBuilder cb, Root<SalesOrderProcess> processRoot,
                                                  FilterRequest request, List<Predicate> predicates, String excludeField) {
        if (request.getCurrentFilters() == null || request.getCurrentFilters().isEmpty()) {
            return;
        }

        // TODO: 实现级联筛选逻辑
        // 这里需要根据当前筛选条件构建子查询，与原有的CascadeFilterManager逻辑保持一致
        log.debug("Cascade filtering for process field: {} not fully implemented yet", excludeField);
    }
}
