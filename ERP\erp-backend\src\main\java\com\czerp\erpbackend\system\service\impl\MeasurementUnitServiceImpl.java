package com.czerp.erpbackend.system.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.system.dto.CreateMeasurementUnitRequest;
import com.czerp.erpbackend.system.dto.MeasurementUnitDTO;
import com.czerp.erpbackend.system.dto.MeasurementUnitQueryRequest;
import com.czerp.erpbackend.system.dto.UpdateMeasurementUnitRequest;
import com.czerp.erpbackend.system.entity.MeasurementUnit;
import com.czerp.erpbackend.system.mapper.MeasurementUnitMapper;
import com.czerp.erpbackend.system.repository.MeasurementUnitRepository;
import com.czerp.erpbackend.system.service.MeasurementUnitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 计量单位服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MeasurementUnitServiceImpl implements MeasurementUnitService {

    private final MeasurementUnitRepository measurementUnitRepository;
    private final MeasurementUnitMapper measurementUnitMapper;

    /**
     * 分页查询计量单位列表
     * @param request 查询请求
     * @return 计量单位分页列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<MeasurementUnitDTO> findMeasurementUnits(MeasurementUnitQueryRequest request) {
        log.debug("Finding measurement units with request: {}", request);

        // 构建分页参数
        int page = request.getPage() != null ? request.getPage() - 1 : 0;
        int size = request.getSize() != null ? request.getSize() : 10;

        // 构建排序参数
        Sort sort = Sort.by(Sort.Direction.ASC, "sortOrder");
        if (StringUtils.hasText(request.getSortField())) {
            Sort.Direction direction = "desc".equalsIgnoreCase(request.getSortDirection())
                    ? Sort.Direction.DESC : Sort.Direction.ASC;
            sort = Sort.by(direction, request.getSortField());
        }

        Pageable pageable = PageRequest.of(page, size, sort);

        // 执行查询
        Page<MeasurementUnit> unitPage = measurementUnitRepository.search(
                request.getKeyword(),
                request.getStatus(),
                request.getIsDimensionUnit(),
                pageable
        );

        // 查询结果日志记录在DEBUG级别
        log.debug("Query result - total elements: {}, total pages: {}, content size: {}",
                unitPage.getTotalElements(), unitPage.getTotalPages(), unitPage.getContent().size());

        // 记录查询结果
        log.debug("Query result - content size: {}, totalElements: {}, totalPages: {}",
                unitPage.getContent().size(), unitPage.getTotalElements(), unitPage.getTotalPages());

        // 转换为DTO
        List<MeasurementUnitDTO> unitDTOs = unitPage.getContent().stream()
                .map(measurementUnitMapper::toDto)
                .collect(Collectors.toList());

        // 转换后的DTO数量记录在DEBUG级别
        log.debug("Converted DTOs size: {}", unitDTOs.size());

        // 构建分页响应
        int pageNumber = request.getPage() != null ? request.getPage() : 1;

        return new PageResponse<>(
                unitDTOs,
                unitPage.getTotalElements(),
                unitPage.getTotalPages(),
                pageNumber,
                size
        );
    }

    /**
     * 查询所有计量单位
     * @return 计量单位列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<MeasurementUnitDTO> findAllMeasurementUnits() {
        log.debug("Finding all measurement units");

        List<MeasurementUnit> units = measurementUnitRepository.findByOrderBySortOrderAsc();

        return units.stream()
                .map(measurementUnitMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 查询所有启用的计量单位
     * @return 计量单位列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<MeasurementUnitDTO> findAllActiveMeasurementUnits() {
        log.debug("Finding all active measurement units");

        List<MeasurementUnit> units = measurementUnitRepository.findByStatusEqualsOrderBySortOrderAsc("active");

        return units.stream()
                .map(measurementUnitMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 查询所有尺寸单位
     * @return 计量单位列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<MeasurementUnitDTO> findAllDimensionUnits() {
        log.debug("Finding all dimension units");

        List<MeasurementUnit> units = measurementUnitRepository.findByIsDimensionUnitTrueOrderBySortOrderAsc();

        return units.stream()
                .map(measurementUnitMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID查询计量单位
     * @param id 计量单位ID
     * @return 计量单位信息
     */
    @Override
    @Transactional(readOnly = true)
    public MeasurementUnitDTO findMeasurementUnitById(Long id) {
        log.debug("Finding measurement unit by id: {}", id);

        MeasurementUnit unit = measurementUnitRepository.findById(id)
                .orElseThrow(() -> new BusinessException("计量单位不存在"));

        return measurementUnitMapper.toDto(unit);
    }

    /**
     * 创建计量单位
     * @param request 创建请求
     * @return 计量单位信息
     */
    @Override
    @Transactional
    public MeasurementUnitDTO createMeasurementUnit(CreateMeasurementUnitRequest request) {
        log.debug("Creating measurement unit with request: {}", request);

        // 检查单位名称是否已存在
        if (measurementUnitRepository.existsByUnitName(request.getUnitName())) {
            throw new BusinessException("计量单位名称已存在");
        }

        // 创建计量单位
        MeasurementUnit unit = measurementUnitMapper.toEntity(request);

        // 处理默认单位设置
        handleDefaultSettings(unit);

        // 保存计量单位
        unit = measurementUnitRepository.save(unit);

        return measurementUnitMapper.toDto(unit);
    }

    /**
     * 更新计量单位
     * @param id 计量单位ID
     * @param request 更新请求
     * @return 计量单位信息
     */
    @Override
    @Transactional
    public MeasurementUnitDTO updateMeasurementUnit(Long id, UpdateMeasurementUnitRequest request) {
        log.debug("Updating measurement unit with id: {} and request: {}", id, request);

        // 查询计量单位
        MeasurementUnit unit = measurementUnitRepository.findById(id)
                .orElseThrow(() -> new BusinessException("计量单位不存在"));

        // 检查单位名称是否已存在（排除自身）
        if (!unit.getUnitName().equals(request.getUnitName()) &&
                measurementUnitRepository.existsByUnitName(request.getUnitName())) {
            throw new BusinessException("计量单位名称已存在");
        }

        // 更新计量单位
        measurementUnitMapper.updateEntity(request, unit);

        // 处理默认单位设置
        handleDefaultSettings(unit);

        // 保存计量单位
        unit = measurementUnitRepository.save(unit);

        return measurementUnitMapper.toDto(unit);
    }

    /**
     * 删除计量单位
     * @param id 计量单位ID
     */
    @Override
    @Transactional
    public void deleteMeasurementUnit(Long id) {
        log.debug("Deleting measurement unit with id: {}", id);

        // 查询计量单位
        MeasurementUnit unit = measurementUnitRepository.findById(id)
                .orElseThrow(() -> new BusinessException("计量单位不存在"));

        // 删除计量单位（物理删除）
        measurementUnitRepository.delete(unit);
    }

    /**
     * 批量删除计量单位
     * @param ids 计量单位ID列表
     */
    @Override
    @Transactional
    public void batchDeleteMeasurementUnits(List<Long> ids) {
        log.debug("Batch deleting measurement units with ids: {}", ids);

        // 查询计量单位列表
        List<MeasurementUnit> units = measurementUnitRepository.findAllById(ids);

        if (units.isEmpty()) {
            return;
        }

        // 删除计量单位（物理删除）
        measurementUnitRepository.deleteAll(units);
    }

    /**
     * 切换计量单位状态
     * @param id 计量单位ID
     * @param isActive 是否启用
     * @return 计量单位信息
     */
    @Override
    @Transactional
    public MeasurementUnitDTO toggleMeasurementUnitStatus(Long id, boolean isActive) {
        log.debug("Toggling measurement unit status with id: {} and isActive: {}", id, isActive);

        // 查询计量单位
        MeasurementUnit unit = measurementUnitRepository.findById(id)
                .orElseThrow(() -> new BusinessException("计量单位不存在"));

        // 更新状态
        unit.setStatus(isActive ? "active" : "inactive");
        unit = measurementUnitRepository.save(unit);

        return measurementUnitMapper.toDto(unit);
    }

    /**
     * 设置为默认物料单位
     * @param id 计量单位ID
     * @return 计量单位信息
     */
    @Override
    @Transactional
    public MeasurementUnitDTO setAsDefaultForNewMaterial(Long id) {
        log.debug("Setting measurement unit as default for new material with id: {}", id);

        // 查询计量单位
        MeasurementUnit unit = measurementUnitRepository.findById(id)
                .orElseThrow(() -> new BusinessException("计量单位不存在"));

        // 清除其他默认物料单位
        Optional<MeasurementUnit> existingDefault = measurementUnitRepository.findByIsDefaultForNewMaterialTrue();
        existingDefault.ifPresent(u -> {
            if (!u.getId().equals(id)) {
                u.setIsDefaultForNewMaterial(false);
                measurementUnitRepository.save(u);
            }
        });

        // 设置为默认物料单位
        unit.setIsDefaultForNewMaterial(true);
        unit = measurementUnitRepository.save(unit);

        return measurementUnitMapper.toDto(unit);
    }

    /**
     * 设置为默认纸箱尺寸单位
     * @param id 计量单位ID
     * @return 计量单位信息
     */
    @Override
    @Transactional
    public MeasurementUnitDTO setAsDefaultDimensionUnit(Long id) {
        log.debug("Setting measurement unit as default dimension unit with id: {}", id);

        // 查询计量单位
        MeasurementUnit unit = measurementUnitRepository.findById(id)
                .orElseThrow(() -> new BusinessException("计量单位不存在"));

        // 确保是尺寸单位
        if (!unit.getIsDimensionUnit()) {
            throw new BusinessException("只有尺寸单位才能设置为默认纸箱尺寸单位");
        }

        // 清除其他默认纸箱尺寸单位
        Optional<MeasurementUnit> existingDefault = measurementUnitRepository.findByIsDefaultDimensionUnitTrue();
        existingDefault.ifPresent(u -> {
            if (!u.getId().equals(id)) {
                u.setIsDefaultDimensionUnit(false);
                measurementUnitRepository.save(u);
            }
        });

        // 设置为默认纸箱尺寸单位
        unit.setIsDefaultDimensionUnit(true);
        unit = measurementUnitRepository.save(unit);

        return measurementUnitMapper.toDto(unit);
    }

    /**
     * 设置为默认纸度单位
     * @param id 计量单位ID
     * @return 计量单位信息
     */
    @Override
    @Transactional
    public MeasurementUnitDTO setAsDefaultThicknessUnit(Long id) {
        log.debug("Setting measurement unit as default thickness unit with id: {}", id);

        // 查询计量单位
        MeasurementUnit unit = measurementUnitRepository.findById(id)
                .orElseThrow(() -> new BusinessException("计量单位不存在"));

        // 清除其他默认纸度单位
        Optional<MeasurementUnit> existingDefault = measurementUnitRepository.findByIsDefaultThicknessUnitTrue();
        existingDefault.ifPresent(u -> {
            if (!u.getId().equals(id)) {
                u.setIsDefaultThicknessUnit(false);
                measurementUnitRepository.save(u);
            }
        });

        // 设置为默认纸度单位
        unit.setIsDefaultThicknessUnit(true);
        unit = measurementUnitRepository.save(unit);

        return measurementUnitMapper.toDto(unit);
    }

    /**
     * 设置为默认纸长单位
     * @param id 计量单位ID
     * @return 计量单位信息
     */
    @Override
    @Transactional
    public MeasurementUnitDTO setAsDefaultLengthUnit(Long id) {
        log.debug("Setting measurement unit as default length unit with id: {}", id);

        // 查询计量单位
        MeasurementUnit unit = measurementUnitRepository.findById(id)
                .orElseThrow(() -> new BusinessException("计量单位不存在"));

        // 清除其他默认纸长单位
        Optional<MeasurementUnit> existingDefault = measurementUnitRepository.findByIsDefaultLengthUnitTrue();
        existingDefault.ifPresent(u -> {
            if (!u.getId().equals(id)) {
                u.setIsDefaultLengthUnit(false);
                measurementUnitRepository.save(u);
            }
        });

        // 设置为默认纸长单位
        unit.setIsDefaultLengthUnit(true);
        unit = measurementUnitRepository.save(unit);

        return measurementUnitMapper.toDto(unit);
    }

    /**
     * 处理默认单位设置
     * @param unit 计量单位
     */
    private void handleDefaultSettings(MeasurementUnit unit) {
        // 如果设置为默认物料单位，清除其他默认物料单位
        if (Boolean.TRUE.equals(unit.getIsDefaultForNewMaterial())) {
            Optional<MeasurementUnit> existingDefault = measurementUnitRepository.findByIsDefaultForNewMaterialTrue();
            existingDefault.ifPresent(u -> {
                if (unit.getId() == null || !u.getId().equals(unit.getId())) {
                    u.setIsDefaultForNewMaterial(false);
                    measurementUnitRepository.save(u);
                }
            });
        }

        // 如果设置为默认纸箱尺寸单位，清除其他默认纸箱尺寸单位
        if (Boolean.TRUE.equals(unit.getIsDefaultDimensionUnit())) {
            // 确保是尺寸单位
            if (!Boolean.TRUE.equals(unit.getIsDimensionUnit())) {
                throw new BusinessException("只有尺寸单位才能设置为默认纸箱尺寸单位");
            }

            Optional<MeasurementUnit> existingDefault = measurementUnitRepository.findByIsDefaultDimensionUnitTrue();
            existingDefault.ifPresent(u -> {
                if (unit.getId() == null || !u.getId().equals(unit.getId())) {
                    u.setIsDefaultDimensionUnit(false);
                    measurementUnitRepository.save(u);
                }
            });
        }

        // 如果设置为默认纸度单位，清除其他默认纸度单位
        if (Boolean.TRUE.equals(unit.getIsDefaultThicknessUnit())) {
            Optional<MeasurementUnit> existingDefault = measurementUnitRepository.findByIsDefaultThicknessUnitTrue();
            existingDefault.ifPresent(u -> {
                if (unit.getId() == null || !u.getId().equals(unit.getId())) {
                    u.setIsDefaultThicknessUnit(false);
                    measurementUnitRepository.save(u);
                }
            });
        }

        // 如果设置为默认纸长单位，清除其他默认纸长单位
        if (Boolean.TRUE.equals(unit.getIsDefaultLengthUnit())) {
            Optional<MeasurementUnit> existingDefault = measurementUnitRepository.findByIsDefaultLengthUnitTrue();
            existingDefault.ifPresent(u -> {
                if (unit.getId() == null || !u.getId().equals(unit.getId())) {
                    u.setIsDefaultLengthUnit(false);
                    measurementUnitRepository.save(u);
                }
            });
        }
    }
}
