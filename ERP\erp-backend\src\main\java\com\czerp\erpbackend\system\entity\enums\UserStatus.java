package com.czerp.erpbackend.system.entity.enums;

import lombok.Getter;

/**
 * 用户状态枚举
 */
@Getter
public enum UserStatus {
    
    /**
     * 正常
     */
    ACTIVE("active", "正常"),
    
    /**
     * 停用
     */
    INACTIVE("inactive", "停用"),
    
    /**
     * 锁定
     */
    LOCKED("locked", "锁定");
    
    private final String code;
    private final String description;
    
    UserStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据编码获取用户状态
     * @param code 编码
     * @return 用户状态
     */
    public static UserStatus fromCode(String code) {
        for (UserStatus status : UserStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 