package com.czerp.erpbackend.system.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 纸度匹配请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaperSizeMatchingRequest {

    /**
     * 纸盒宽度
     */
    @NotNull(message = "纸盒宽度不能为空")
    @DecimalMin(value = "0.01", message = "纸盒宽度必须大于0")
    private BigDecimal width;

    /**
     * 纸盒高度
     */
    @NotNull(message = "纸盒高度不能为空")
    @DecimalMin(value = "0.01", message = "纸盒高度必须大于0")
    private BigDecimal height;

    /**
     * 长度单位，默认为厘米(CM)
     */
    private LengthUnit unit = LengthUnit.CM;
}
