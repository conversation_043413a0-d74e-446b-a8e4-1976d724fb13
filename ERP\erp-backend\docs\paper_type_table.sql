-- 创建纸质类别表
CREATE TABLE paper_type (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    paper_type_name VARCHAR(50) NOT NULL COMMENT '纸板类别名称',
    stapling_flap_inch DECIMAL(10, 2) COMMENT '打钉钉口(inch)',
    stapling_flap_cm DECIMAL(10, 2) COMMENT '打钉钉口(cm)',
    gluing_flap_inch DECIMAL(10, 2) COMMENT '粘箱钉口(inch)',
    gluing_flap_cm DECIMAL(10, 2) COMMENT '粘箱钉口(cm)',
    add_margin_mm DECIMAL(10, 2) COMMENT '加分(mm)',
    reduce_margin_mm DECIMAL(10, 2) COMMENT '缩分(mm)',
    thickness_mm DECIMAL(10, 2) COMMENT '厚度(mm)',
    layer_count INT COMMENT '层数',
    sheets_per_board INT COMMENT '每板数',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='纸质类别表';

-- 插入示例数据
INSERT INTO paper_type (paper_type_name, stapling_flap_inch, stapling_flap_cm, gluing_flap_inch, gluing_flap_cm, add_margin_mm, thickness_mm, layer_count) 
VALUES 
('三坑', 1.5, 3.81, 1.5, 3.81, 3, 9, 7),
('双坑', 1.18, 3, 1.18, 3, 2, 6, 5),
('单坑', 1.18, 3, 1.18, 3, 1, 3, 3),
('E坑', 1, 2.54, 1, 2.54, 1, 2, 3);
