package com.czerp.erpbackend.sales.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.sales.dto.FilterOptionDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryParamDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryResponseDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryResultDTO;

import java.util.List;

/**
 * 销售订单查询Service接口
 */
public interface SalesOrderQueryService {

    /**
     * 分页查询销售订单（传统方式，保持向后兼容）
     * @param queryParam 查询参数
     * @return 销售订单分页列表
     */
    PageResponse<SalesOrderQueryResultDTO> querySalesOrders(SalesOrderQueryParamDTO queryParam);

    /**
     * 分页查询销售订单（增强版，支持筛选选项）
     * @param queryParam 查询参数
     * @return 销售订单查询响应（包含分页数据和筛选选项）
     */
    SalesOrderQueryResponseDTO querySalesOrdersEnhanced(SalesOrderQueryParamDTO queryParam);

    /**
     * 获取指定字段的筛选选项
     * @param fieldName 字段名称
     * @param searchText 搜索文本（可选）
     * @param currentFilters 当前其他筛选条件（可选）
     * @return 筛选选项列表
     */
    List<FilterOptionDTO> getFilterOptions(String fieldName, String searchText, SalesOrderQueryParamDTO currentFilters);
}
