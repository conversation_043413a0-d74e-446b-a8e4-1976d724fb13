package com.czerp.erpbackend.sales.service;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.sales.dto.SalesOrderMaterialDTO;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderMaterialRepository;
import com.czerp.erpbackend.sales.service.impl.SalesOrderMaterialServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SalesOrderMaterialServiceTest {

    @Mock
    private SalesOrderMaterialRepository salesOrderMaterialRepository;

    @Mock
    private SalesOrderItemRepository salesOrderItemRepository;

    @InjectMocks
    private SalesOrderMaterialServiceImpl salesOrderMaterialService;

    private SalesOrderItem testOrderItem;
    private SalesOrderMaterial testMaterial;
    private SalesOrderMaterialDTO testMaterialDTO;
    private List<SalesOrderMaterial> testMaterials;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testOrderItem = new SalesOrderItem();
        testOrderItem.setId("item1");

        testMaterial = new SalesOrderMaterial();
        testMaterial.setId("material1");
        testMaterial.setOrderItem(testOrderItem);
        testMaterial.setPaperQuality("测试纸质");
        testMaterial.setPaperWidth(new BigDecimal("100.00"));
        testMaterial.setPaperLength(new BigDecimal("200.00"));
        testMaterial.setSerialNo(1);
        testMaterial.setDieModelChecked(true);

        testMaterialDTO = new SalesOrderMaterialDTO();
        testMaterialDTO.setId("material1");
        testMaterialDTO.setOrderItemId("item1");
        testMaterialDTO.setPaperQuality("测试纸质");
        testMaterialDTO.setPaperWidth(new BigDecimal("100.00"));
        testMaterialDTO.setPaperLength(new BigDecimal("200.00"));
        testMaterialDTO.setSerialNo(1);
        testMaterialDTO.setDieModelChecked(true);

        testMaterials = new ArrayList<>();
        testMaterials.add(testMaterial);
    }

    @Test
    void testGetMaterialsByOrderItemId() {
        // 配置Mock行为
        when(salesOrderMaterialRepository.findByOrderItemIdOrderBySerialNoAsc(anyString())).thenReturn(testMaterials);

        // 执行测试
        List<SalesOrderMaterialDTO> result = salesOrderMaterialService.getMaterialsByOrderItemId("item1");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("测试纸质", result.get(0).getPaperQuality());
        assertEquals(new BigDecimal("100.00"), result.get(0).getPaperWidth());
        assertEquals(new BigDecimal("200.00"), result.get(0).getPaperLength());
        assertEquals(1, result.get(0).getSerialNo());
        assertTrue(result.get(0).getDieModelChecked());

        // 验证调用
        verify(salesOrderMaterialRepository).findByOrderItemIdOrderBySerialNoAsc("item1");
    }

    @Test
    void testSaveMaterial() {
        // 配置Mock行为
        when(salesOrderItemRepository.findById(anyString())).thenReturn(Optional.of(testOrderItem));
        when(salesOrderMaterialRepository.save(any(SalesOrderMaterial.class))).thenReturn(testMaterial);

        // 执行测试
        SalesOrderMaterialDTO result = salesOrderMaterialService.saveMaterial(testMaterialDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试纸质", result.getPaperQuality());
        assertEquals(new BigDecimal("100.00"), result.getPaperWidth());
        assertEquals(new BigDecimal("200.00"), result.getPaperLength());
        assertEquals(1, result.getSerialNo());
        assertTrue(result.getDieModelChecked());

        // 验证调用
        verify(salesOrderItemRepository).findById("item1");
        verify(salesOrderMaterialRepository).save(any(SalesOrderMaterial.class));
    }

    @Test
    void testSaveMaterialWithInvalidOrderItemId() {
        // 配置Mock行为
        when(salesOrderItemRepository.findById(anyString())).thenReturn(Optional.empty());

        // 执行测试并验证异常
        assertThrows(BusinessException.class, () -> salesOrderMaterialService.saveMaterial(testMaterialDTO));

        // 验证调用
        verify(salesOrderItemRepository).findById("item1");
        verify(salesOrderMaterialRepository, never()).save(any(SalesOrderMaterial.class));
    }

    @Test
    void testUpdateMaterial() {
        // 配置Mock行为
        when(salesOrderMaterialRepository.findById(anyString())).thenReturn(Optional.of(testMaterial));
        when(salesOrderMaterialRepository.save(any(SalesOrderMaterial.class))).thenReturn(testMaterial);

        // 修改DTO
        testMaterialDTO.setPaperQuality("更新的纸质");
        testMaterialDTO.setPaperWidth(new BigDecimal("150.00"));
        testMaterialDTO.setDieModelChecked(false);

        // 执行测试
        SalesOrderMaterialDTO result = salesOrderMaterialService.updateMaterial("material1", testMaterialDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals("更新的纸质", result.getPaperQuality());
        assertEquals(new BigDecimal("150.00"), result.getPaperWidth());
        assertFalse(result.getDieModelChecked());

        // 验证调用
        verify(salesOrderMaterialRepository).findById("material1");
        verify(salesOrderMaterialRepository).save(any(SalesOrderMaterial.class));
    }

    @Test
    void testDeleteMaterial() {
        // 执行测试
        salesOrderMaterialService.deleteMaterial("material1");

        // 验证调用
        verify(salesOrderMaterialRepository).deleteById("material1");
    }

    @Test
    void testDeleteMaterialsByOrderItemId() {
        // 执行测试
        salesOrderMaterialService.deleteMaterialsByOrderItemId("item1");

        // 验证调用
        verify(salesOrderMaterialRepository).deleteByOrderItemId("item1");
    }

    @Test
    void testDieModelCheckedField() {
        // 配置Mock行为
        when(salesOrderItemRepository.findById(anyString())).thenReturn(Optional.of(testOrderItem));

        // 设置dieModelChecked字段
        testMaterial.setDieModelChecked(true);
        when(salesOrderMaterialRepository.save(any(SalesOrderMaterial.class))).thenReturn(testMaterial);

        // 执行测试 - 保存时设置为true
        testMaterialDTO.setDieModelChecked(true);
        SalesOrderMaterialDTO result1 = salesOrderMaterialService.saveMaterial(testMaterialDTO);

        // 验证结果
        assertTrue(result1.getDieModelChecked());

        // 执行测试 - 更新时设置为false
        testMaterial.setDieModelChecked(false);
        when(salesOrderMaterialRepository.findById(anyString())).thenReturn(Optional.of(testMaterial));
        when(salesOrderMaterialRepository.save(any(SalesOrderMaterial.class))).thenReturn(testMaterial);

        testMaterialDTO.setDieModelChecked(false);
        SalesOrderMaterialDTO result2 = salesOrderMaterialService.updateMaterial("material1", testMaterialDTO);

        // 验证结果
        assertFalse(result2.getDieModelChecked());
    }
}
