# 销售订单工序管理模块设计说明（简化版）

## 1. 设计背景

销售订单工序管理是生产管理的重要环节，需要记录每个销售订单明细项的工序信息，包括工序名称、工艺要求、印版编号、水墨编号、水墨名称和颜色数等信息。通过工序管理，可以清晰地了解每个订单的生产流程和进度，提高生产效率和质量控制。

## 2. 数据库设计

### 2.1 核心表结构

#### 2.1.1 销售订单工序表 (sales_order_item_process)

此表用于存储销售订单明细项的工序信息，与销售订单明细表(sales_order_item)关联。

主要字段：
- `id`: 主键
- `order_item_id`: 销售订单明细ID，关联sales_order_item表
- `sequence`: 工序顺序，表示工序的执行顺序
- `process_name`: 工序名称，如分纸、印刷、打订、包装等
- `process_requirements`: 工艺要求，详细描述工序的具体要求
- `plate_number`: 印版编号
- `ink_number`: 水墨编号
- `ink_name`: 水墨名称
- `color_count`: 颜色数
- `status`: 工序状态，如待处理、处理中、已完成等
- 其他审计字段（创建人、创建时间等）

### 2.2 表关系

`sales_order_item_process` 与 `sales_order_item` 是多对一关系，一个销售订单明细项可以有多个工序。

## 3. 设计考虑

### 3.1 数据库命名规范

- 表名和字段名采用小写下划线命名法(snake_case)，符合项目规范。
- 表名清晰表达表的用途，如 `sales_order_item_process`。
- 字段名明确表达字段的含义，如 `process_name`、`ink_number` 等。

### 3.2 数据完整性

- 使用外键约束确保数据的引用完整性，如 `sales_order_item_process` 表中的 `order_item_id` 字段引用 `sales_order_item` 表的 `id` 字段。
- 为重要字段添加索引，提高查询效率。

### 3.3 设计简洁性

- 采用简洁的设计方案，只使用一个表来存储工序信息，避免复杂的表关系。
- 直接在工序表中存储所有相关信息，不需要额外的关联表。

### 3.4 性能考虑

- 为常用查询条件添加索引，如 `order_item_id`、`process_name` 等。
- 合理设计字段类型和长度，避免浪费存储空间。
- 使用 `is_deleted` 字段实现逻辑删除，避免物理删除带来的性能问题。

## 4. 业务流程

### 4.1 工序管理流程

1. 创建销售订单及明细项
2. 为销售订单明细项添加工序信息
   - 手动添加工序，设置工序名称、顺序、工艺要求等信息
   - 对于印刷工序，可以添加印版编号、水墨信息等
3. 维护工序信息，包括工艺要求、印版编号、水墨信息等
4. 跟踪工序状态，记录工序完成情况

## 5. 前端界面设计

前端界面应包含以下功能：

1. 工序列表展示
   - 显示工序名称、工艺要求、印版编号、水墨编号、水墨名称、颜色数等信息
   - 支持按工序顺序排序
   - 支持工序状态的显示和更新

2. 工序添加/编辑功能
   - 提供工序添加按钮
   - 支持工序信息的编辑
   - 支持工序顺序的调整

3. 工序删除功能
   - 提供工序删除按钮
   - 删除前进行确认

## 6. 后续扩展建议

1. 如果未来需要更复杂的工序管理，可以考虑添加工序模板功能，方便快速应用常用工序组合。
2. 如果需要更详细的水墨和印版管理，可以考虑添加独立的水墨信息表和印版信息表。
3. 如果需要更详细的工序执行记录，可以考虑添加工序执行记录表。

## 7. 结论

本设计方案采用简洁直接的方式，通过一个销售订单工序表(sales_order_item_process)来存储与销售订单明细项关联的工序信息。这种设计简单易实现，满足当前的业务需求，同时也为未来可能的扩展留有空间。
