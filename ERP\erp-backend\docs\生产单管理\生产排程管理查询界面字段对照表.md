# 生产排程管理查询界面字段对照表

## 字段实现状态总览

| 序号 | 字段名称 | 数据来源 | 实现状态 | DTO字段名 | 备注 |
|------|----------|----------|----------|-----------|------|
| 1 | 排程单号 | production_schedule.schedule_no | ✅ 已实现 | scheduleNo | 基础字段 |
| 2 | 最近打印时间 | production_schedule.last_print_time | ✅ 已实现 | lastPrintTime | 新增字段 |
| 3 | 销售订单号 | sales_order.order_no | ✅ 已实现 | salesOrderNo | 关联查询 |
| 4 | 生产单号 | sales_order_item.production_order_no | ✅ 已实现 | productionOrderNo | 关联查询 |
| 5 | 订单日期 | sales_order.order_date | ✅ 已实现 | orderDate | 新增字段 |
| 6 | 客户分类 | 固定值："订单客户" | ✅ 已实现 | customerCategory | 固定值 |
| 7 | 客户名称 | sales_order.customer_name | ✅ 已实现 | customerName | 关联查询 |
| 8 | 产品名称 | sales_order_item.product_name | ✅ 已实现 | productName | 关联查询 |
| 9 | 规格 | 计算字段：length×width×height | ✅ 已实现 | specification | 计算字段 |
| 10 | 生产规格 | 计算字段：production_length×production_width×production_height | ✅ 已实现 | productionSpecification | 计算字段 |
| 11 | 单价 | sales_order_item.price | ✅ 已实现 | unitPrice | 新增字段 |
| 12 | 销售数量 | sales_order_item.quantity | ✅ 已实现 | salesQuantity | 关联查询 |
| 13 | 总金额 | sales_order_item.amount | ✅ 已实现 | totalAmount | 新增字段 |
| 14 | 样品 | sales_order_item.is_sample | ✅ 已实现 | isSample | 新增字段 |
| 15 | 订单类型 | 固定值："销售订单" | ✅ 已实现 | orderType | 固定值 |
| 16 | 交期 | sales_order_item.delivery_date | ✅ 已实现 | deliveryDate | 关联查询 |
| 17 | 工艺要求 | sales_order_item.process_requirements | ✅ 已实现 | processRequirements | 关联查询 |
| 18 | 纸度 | sales_order_material.paper_width | ✅ 已实现 | materialPaperWidth | 新增字段 |
| 19 | 纸长 | sales_order_material.paper_length | ✅ 已实现 | materialPaperLength | 新增字段 |
| 20 | 度开 | sales_order_material.width_open | ✅ 已实现 | widthOpen | 新增字段 |
| 21 | 长开 | sales_order_material.length_open | ✅ 已实现 | lengthOpen | 新增字段 |
| 22 | 采购数量 | purchase_order_item.quantity汇总 | ✅ 已实现 | purchaseQuantity | 新增字段 |
| 23 | 到料数量 | stock_inbound_item.quantity汇总 | ✅ 已实现 | arrivedMaterialQuantity | 新增字段 |
| 24 | 到料日期 | stock_inbound.inbound_date最新 | ✅ 已实现 | materialArrivalDate | 新增字段 |
| 25 | 排程数量 | production_schedule_item.schedule_quantity | ✅ 已实现 | scheduleQuantity | 基础字段 |
| 26 | 包装数 | production_schedule_item.package_count | ✅ 已实现 | packageCount | 基础字段 |
| 27 | 急单 | production_schedule_item.is_urgent | ✅ 已实现 | isUrgent | 基础字段 |
| 28 | 急单序号 | production_schedule_item.urgent_sequence | ✅ 已实现 | urgentSequence | 基础字段 |
| 29 | 已打印 | production_schedule_item.is_printed | ✅ 已实现 | isPrinted | 基础字段 |
| 30 | 计划完成日期 | production_schedule_item.planned_completion_date | ✅ 已实现 | plannedCompletionDate | 基础字段 |
| 31 | 备注 | production_schedule_item.remark | ✅ 已实现 | remark | 基础字段 |

## 字段分类说明

### 基础字段（直接来源于主表）
- 来源：`production_schedule_item` 表
- 特点：无需额外查询，性能最优
- 字段：id, scheduleId, isUrgent, urgentSequence, isPrinted, plannedCompletionDate, scheduleQuantity, packageCount, remark

### 关联字段（需要JOIN查询）
- 来源：关联表查询
- 特点：通过外键关联获取
- 主要表：`production_schedule`, `sales_order`, `sales_order_item`

### 扩展字段（需要额外查询）
- 来源：材料、采购、入库等扩展表
- 特点：通过批量查询获取，有异常隔离机制
- 主要表：`sales_order_material`, `purchase_order_item`, `stock_inbound_item`

### 计算字段
- 来源：基于其他字段计算得出
- 特点：实时计算，无存储
- 示例：specification（规格）、productionSpecification（生产规格）

### 固定值字段
- 来源：业务规则定义的固定值
- 特点：不依赖数据库，直接赋值
- 字段：customerCategory（"订单客户"）、orderType（"销售订单"）

## 数据获取流程

### 第一步：主查询
```sql
SELECT * FROM production_schedule_item 
WHERE is_deleted = false 
  AND [动态条件]
ORDER BY [排序字段]
LIMIT [分页参数]
```

### 第二步：关联数据查询
```sql
-- 排程单信息
SELECT * FROM production_schedule WHERE id IN (...)

-- 销售订单明细信息
SELECT * FROM sales_order_item WHERE id IN (...)

-- 销售订单信息
SELECT * FROM sales_order WHERE id IN (...)
```

### 第三步：扩展数据查询
```sql
-- 材料信息
SELECT * FROM sales_order_material WHERE order_item_id IN (...)

-- 采购信息
SELECT * FROM purchase_order_item WHERE source_sales_order_item_id IN (...)

-- 入库信息
SELECT * FROM stock_inbound_item WHERE purchase_order_item_id IN (...)
```

### 第四步：数据组装
- 构建Map映射关系
- 填充DTO对象
- 计算衍生字段
- 设置固定值字段

## 性能优化要点

### 1. 批量查询策略
- 使用`IN`查询避免N+1问题
- 一次性获取所有需要的关联数据
- 通过Map构建关联关系

### 2. 异常隔离机制
- 每个扩展数据模块独立异常处理
- 核心数据获取失败不影响扩展数据
- 扩展数据获取失败不影响核心功能

### 3. 内存优化
- 使用懒加载减少内存占用
- 及时释放不需要的对象引用
- 合理设置分页大小

### 4. 数据库优化建议
```sql
-- 建议添加的索引
CREATE INDEX idx_production_schedule_item_sales_order_item_id 
ON production_schedule_item(sales_order_item_id);

CREATE INDEX idx_sales_order_material_order_item_id 
ON sales_order_material(order_item_id);

CREATE INDEX idx_purchase_order_item_source_sales_order_item_id 
ON purchase_order_item(source_sales_order_item_id);

CREATE INDEX idx_stock_inbound_item_purchase_order_item_id 
ON stock_inbound_item(purchase_order_item_id);
```

## 字段映射关系图

```
ProductionScheduleItemDTO
├── 基础字段 (production_schedule_item)
│   ├── id, scheduleId, isUrgent, urgentSequence
│   ├── isPrinted, plannedCompletionDate
│   ├── scheduleQuantity, packageCount, remark
│   └── salesOrderItemId
├── 排程单字段 (production_schedule)
│   ├── scheduleNo
│   └── lastPrintTime ⭐新增
├── 销售订单字段 (sales_order + sales_order_item)
│   ├── salesOrderNo, productionOrderNo
│   ├── customerName, productName
│   ├── salesQuantity, deliveryDate
│   ├── processRequirements, specification
│   ├── productionSpecification
│   ├── orderDate ⭐新增, unitPrice ⭐新增
│   ├── totalAmount ⭐新增, isSample ⭐新增
│   └── customerCategory ⭐固定值, orderType ⭐固定值
├── 材料字段 (sales_order_material) ⭐新增模块
│   ├── materialPaperWidth, materialPaperLength
│   └── widthOpen, lengthOpen
├── 采购字段 (purchase_order_item) ⭐新增模块
│   └── purchaseQuantity
└── 入库字段 (stock_inbound_item) ⭐新增模块
    ├── arrivedMaterialQuantity
    └── materialArrivalDate
```

## 维护说明

### 新增字段流程
1. 更新 `ProductionScheduleItemDTO`
2. 更新 `ProductionScheduleMapper`
3. 更新 `ProductionScheduleServiceImpl` 数据丰富化逻辑
4. 更新接口文档和字段对照表
5. 添加相应的数据库索引（如需要）

### 字段修改注意事项
- 修改字段名称需要同步更新所有相关代码
- 修改数据来源需要验证数据一致性
- 删除字段需要评估对前端的影响
- 新增计算字段需要考虑性能影响

### 测试验证要点
- 验证所有字段数据来源正确
- 验证批量查询性能
- 验证异常情况处理
- 验证分页和排序功能
- 验证数据一致性
