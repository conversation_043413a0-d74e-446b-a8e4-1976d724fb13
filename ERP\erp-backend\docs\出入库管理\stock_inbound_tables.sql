-- 入库单数据库表结构
-- 严格按照文档字段设计，使用小写下划线命名规范

-- 入库单主表
CREATE TABLE stock_inbound (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    inbound_no VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL UNIQUE COMMENT '入库单号',
    inbound_date DATE NOT NULL COMMENT '入库日期',
    warehouse VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '仓库',
    remark VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
    supplier_code VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '供应商编码',
    supplier_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '供应商名称',
    supplier_delivery_no VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '供应商送货单号',
    delivery_date DATE COMMENT '送货单日期',

    -- 审计字段
    created_by VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',

    -- 索引
    INDEX idx_inbound_no (inbound_no),
    INDEX idx_inbound_date (inbound_date),
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_delivery_date (delivery_date),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入库单主表';

-- 入库单明细表
CREATE TABLE stock_inbound_item (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    inbound_id BIGINT NOT NULL COMMENT '入库单ID',
    purchase_order_item_id BIGINT COMMENT '来源采购订单明细ID',
    quantity INT COMMENT '数量',
    spare_quantity INT COMMENT '收备品数',
    price DECIMAL(18,4) COMMENT '价格',
    area_square_meters DECIMAL(18,4) COMMENT '面积(平米)',
    unit_weight DECIMAL(18,4) COMMENT '单重',
    weight_kg DECIMAL(18,2) COMMENT '重量(KG)',
    quantity_per_board INT COMMENT '每板数',
    tax_rate DECIMAL(5,2) COMMENT '税率%',
    currency VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '币别',
    supplier_delivery_no VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '供应商送货单号',
    folding_specification VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '折度规格',
    conversion_quantity DECIMAL(18,4) COMMENT '折算数量',
    conversion_price DECIMAL(18,4) COMMENT '折算单价',
    conversion_amount DECIMAL(18,2) COMMENT '折算金额',
    remark VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',

    -- 审计字段
    created_by VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',

    -- 索引
    INDEX idx_inbound_id (inbound_id),
    INDEX idx_purchase_order_item_id (purchase_order_item_id),
    INDEX idx_supplier_delivery_no (supplier_delivery_no),
    INDEX idx_created_time (created_time),

    -- 外键约束
    FOREIGN KEY (inbound_id) REFERENCES stock_inbound(id) ON DELETE CASCADE,
    FOREIGN KEY (purchase_order_item_id) REFERENCES purchase_order_item(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入库单明细表';
