package com.czerp.erpbackend.material.dto;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 更新纸质资料请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePaperMaterialRequest {

    /**
     * 纸质编码
     */
    @Size(max = 50, message = "纸质编码长度不能超过50个字符")
    private String paperCode;

    /**
     * 纸质
     */
    @Size(max = 50, message = "纸质长度不能超过50个字符")
    private String paperName;

    /**
     * 纸类ID
     */
    private Integer paperTypeId;

    /**
     * 楞别
     */
    @Size(max = 50, message = "楞别长度不能超过50个字符")
    private String fluteType;

    /**
     * 标准纸质
     */
    private Boolean isStandard;

    /**
     * 生产代号
     */
    @Size(max = 50, message = "生产代号长度不能超过50个字符")
    private String productionCode;

    /**
     * 面纸
     */
    @Size(max = 100, message = "面纸长度不能超过100个字符")
    private String facePaper;

    /**
     * 芯纸1
     */
    @Size(max = 100, message = "芯纸1长度不能超过100个字符")
    private String corePaper1;

    /**
     * 中隔1
     */
    @Size(max = 100, message = "中隔1长度不能超过100个字符")
    private String middlePartition1;

    /**
     * 芯纸2
     */
    @Size(max = 100, message = "芯纸2长度不能超过100个字符")
    private String corePaper2;

    /**
     * 中隔2
     */
    @Size(max = 100, message = "中隔2长度不能超过100个字符")
    private String middlePartition2;

    /**
     * 芯纸3
     */
    @Size(max = 100, message = "芯纸3长度不能超过100个字符")
    private String corePaper3;

    /**
     * 里纸
     */
    @Size(max = 100, message = "里纸长度不能超过100个字符")
    private String linerPaper;

    /**
     * 层数
     */
    private Integer layerCount;

    /**
     * 重量(千克/千平方英寸)
     */
    private BigDecimal weightKgPerKsi;

    /**
     * 重量(千克/平方米)
     */
    private BigDecimal weightKgPerSqm;

    /**
     * 边压强度
     */
    @Size(max = 50, message = "边压强度长度不能超过50个字符")
    private String edgeCrushStrength;

    /**
     * 纸板耐破度
     */
    @Size(max = 50, message = "纸板耐破度长度不能超过50个字符")
    private String burstingStrength;

    /**
     * 对应标准纸质
     */
    @Size(max = 100, message = "对应标准纸质长度不能超过100个字符")
    private String correspondingStandard;

    /**
     * 默认供应商名称
     */
    @Size(max = 100, message = "默认供应商名称长度不能超过100个字符")
    private String defaultSupplier;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remarks;

    /**
     * 停用
     */
    private Boolean isDisabled;
}
