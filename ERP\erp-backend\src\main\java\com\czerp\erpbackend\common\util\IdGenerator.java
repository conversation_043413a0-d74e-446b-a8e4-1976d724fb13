package com.czerp.erpbackend.common.util;

import java.util.UUID;

/**
 * ID生成器工具类
 */
public class IdGenerator {
    
    private IdGenerator() {
        throw new IllegalStateException("Utility class");
    }
    
    /**
     * 生成UUID
     * @return UUID字符串（无连字符）
     */
    public static String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成带前缀的UUID
     * @param prefix 前缀
     * @return 带前缀的UUID字符串
     */
    public static String generateId(String prefix) {
        return prefix + "_" + generateId();
    }
} 