package com.czerp.erpbackend.system.mapper;

import com.czerp.erpbackend.system.dto.CreateRoleRequest;
import com.czerp.erpbackend.system.dto.RoleDTO;
import com.czerp.erpbackend.system.dto.UpdateRoleRequest;
import com.czerp.erpbackend.system.entity.Role;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * 角色映射器
 */
@Mapper(componentModel = "spring", 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        uses = {PermissionMapper.class})
public interface RoleMapper {
    
    /**
     * 角色实体转DTO
     * @param role 角色实体
     * @return 角色DTO
     */
    @Mapping(target = "createTime", source = "createdTime")
    @Mapping(target = "updateTime", source = "updatedTime")
    @Mapping(target = "permissions", ignore = true)
    RoleDTO toDto(Role role);
    
    /**
     * 创建角色请求转角色实体
     * @param request 创建角色请求
     * @return 角色实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    Role toEntity(CreateRoleRequest request);
    
    /**
     * 更新角色
     * @param request 更新角色请求
     * @param role 角色实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateRoleRequest request, @MappingTarget Role role);
    
    /**
     * 角色ID列表转角色对象列表
     * @param ids 角色ID列表
     * @return 角色对象列表
     */
    default List<Role> toRoles(List<String> ids) {
        if (ids == null) {
            return null;
        }
        return ids.stream()
                .map(this::toRole)
                .toList();
    }
    
    /**
     * 角色ID转角色对象
     * @param id 角色ID
     * @return 角色对象
     */
    default Role toRole(String id) {
        if (id == null) {
            return null;
        }
        Role role = new Role();
        role.setId(id);
        return role;
    }
} 