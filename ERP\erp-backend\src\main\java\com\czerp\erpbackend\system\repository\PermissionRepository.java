package com.czerp.erpbackend.system.repository;

import com.czerp.erpbackend.system.entity.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 权限存储库
 */
@Repository
public interface PermissionRepository extends JpaRepository<Permission, String> {
    
    /**
     * 根据编码查找权限
     * @param code 编码
     * @return 权限
     */
    Optional<Permission> findByCode(String code);
    
    /**
     * 根据编码列表查找权限列表
     * @param codes 编码列表
     * @return 权限列表
     */
    List<Permission> findByCodeIn(List<String> codes);
    
    /**
     * 判断编码是否存在
     * @param code 编码
     * @return 是否存在
     */
    boolean existsByCode(String code);
    
    /**
     * 查询所有未删除的权限
     * @return 权限列表
     */
    List<Permission> findByIsDeletedFalseOrderBySortAsc();
    
    /**
     * 查询根权限列表
     * @return 根权限列表
     */
    List<Permission> findByParentIdIsNullAndIsDeletedFalseOrderBySortAsc();
    
    /**
     * 根据父权限ID查询子权限列表
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    List<Permission> findByParentIdAndIsDeletedFalseOrderBySortAsc(String parentId);
    
    /**
     * 根据权限ID列表查询权限列表
     * @param ids 权限ID列表
     * @return 权限列表
     */
    List<Permission> findByIdInAndIsDeletedFalse(List<String> ids);
    
    /**
     * 查询角色关联的权限编码
     * @param roleIds 角色ID列表
     * @return 权限编码列表
     */
    @Query("SELECT DISTINCT p.code FROM Permission p " +
            "JOIN RolePermission rp ON p.id = rp.permissionId " +
            "WHERE rp.roleId IN ?1 AND p.isDeleted = false")
    List<String> findPermissionCodesByRoleIds(List<String> roleIds);
} 