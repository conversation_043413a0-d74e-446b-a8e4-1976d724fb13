package com.czerp.erpbackend.common.filter.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 通用筛选请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterRequest {
    
    /**
     * 模块编码（如：sales-order, purchase-order）
     */
    private String moduleCode;
    
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 搜索文本（可选）
     */
    private String searchText;
    
    /**
     * 当前筛选条件（可选）
     * key: 字段名称
     * value: 筛选值（可以是单个值或值列表）
     */
    private Map<String, Object> currentFilters;
    
    /**
     * 最大返回选项数量（可选，默认50）
     */
    private Integer maxResults;
    
    /**
     * 是否启用级联筛选（默认true）
     */
    private Boolean enableCascade;
}
