package com.czerp.erpbackend.production.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

/**
 * 生产排程单明细表实体
 */
@Entity
@Table(name = "production_schedule_item")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProductionScheduleItem extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 排程单ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "schedule_id", nullable = false)
    private ProductionSchedule schedule;

    /**
     * 急单
     */
    @Column(name = "is_urgent")
    private Boolean isUrgent = false;

    /**
     * 急单序号
     */
    @Column(name = "urgent_sequence")
    private Integer urgentSequence;

    /**
     * 已打印
     */
    @Column(name = "is_printed")
    private Boolean isPrinted = false;

    /**
     * 计划完成日期
     */
    @Column(name = "planned_completion_date")
    private LocalDate plannedCompletionDate;

    /**
     * 排程数量
     */
    @Column(name = "schedule_quantity")
    private Integer scheduleQuantity;

    /**
     * 包装数
     */
    @Column(name = "package_count")
    private Integer packageCount;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 销售订单明细ID
     */
    @Column(name = "sales_order_item_id", length = 36)
    private String salesOrderItemId;

    /**
     * 销售订单明细（关联查询用）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sales_order_item_id", insertable = false, updatable = false)
    private SalesOrderItem salesOrderItem;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;
}
