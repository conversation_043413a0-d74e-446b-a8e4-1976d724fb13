package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.system.dto.PaperSizeSettingDTO;
import com.czerp.erpbackend.system.entity.PaperSizeSetting;
import com.czerp.erpbackend.system.mapper.PaperSizeSettingMapper;
import com.czerp.erpbackend.system.repository.PaperSizeSettingRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 公共纸度设置接口控制器
 */
@RestController
@RequestMapping("/public/paper-size-settings")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Public Paper Size Setting API", description = "公共纸度设置接口")
public class PublicPaperSizeSettingController {
    
    private final PaperSizeSettingRepository paperSizeSettingRepository;
    private final PaperSizeSettingMapper paperSizeSettingMapper;
    
    @GetMapping
    @Operation(summary = "获取所有纸度设置", description = "获取所有未删除的纸度设置列表")
    public ResponseEntity<ApiResponse<List<PaperSizeSettingDTO>>> getAllPaperSizeSettings() {
        log.info("Getting all paper size settings (public)");
        
        List<PaperSizeSetting> settings = paperSizeSettingRepository.findByIsDeletedFalseOrderByPaperSizeInchAsc();
        List<PaperSizeSettingDTO> dtos = settings.stream()
                .map(paperSizeSettingMapper::toDto)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(ApiResponse.success(dtos));
    }
}
