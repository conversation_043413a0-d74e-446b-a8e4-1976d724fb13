package com.czerp.erpbackend.customer.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 更新客户分类请求
 */
@Data
public class UpdateCustomerCategoryRequest {

    /**
     * 分类名称
     */
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    @JsonAlias("name")
    private String categoryName;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;

    /**
     * 状态(active-启用,inactive-停用)
     */
    private String status;

    /**
     * 新建客户时默认属于此分类
     */
    private Boolean isDefault;
}
