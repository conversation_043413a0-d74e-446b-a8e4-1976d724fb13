-- 修复版初始化数据脚本
-- 确保所有数据都符合数据库表的列长度限制

-- 设置当前时间
SET @now = NOW();
SET @admin_id = (SELECT id FROM sys_user WHERE username = 'admin' LIMIT 1);

-- 初始化权限数据
-- 用户管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, code, name, type, status) VALUES
('p001', @admin_id, @now, 0, 'user', '用户管理', 'menu', 'active'),
('p002', @admin_id, @now, 0, 'user:list', '用户列表', 'button', 'active'),
('p003', @admin_id, @now, 0, 'user:read', '用户详情', 'button', 'active'),
('p004', @admin_id, @now, 0, 'user:create', '创建用户', 'button', 'active'),
('p005', @admin_id, @now, 0, 'user:update', '更新用户', 'button', 'active'),
('p006', @admin_id, @now, 0, 'user:delete', '删除用户', 'button', 'active');

-- 角色管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, code, name, type, status) VALUES
('p007', @admin_id, @now, 0, 'role', '角色管理', 'menu', 'active'),
('p008', @admin_id, @now, 0, 'role:list', '角色列表', 'button', 'active'),
('p009', @admin_id, @now, 0, 'role:read', '角色详情', 'button', 'active');

-- 初始化角色数据
INSERT INTO sys_role (id, created_by, created_time, is_deleted, code, name, status) VALUES
('r001', @admin_id, @now, 0, 'admin', '管理员', 'active');

-- 为管理员角色分配权限
INSERT INTO sys_role_permission (id, create_by, create_time, permission_id, role_id) VALUES
('rp001', @admin_id, @now, 'p001', 'r001'),
('rp002', @admin_id, @now, 'p002', 'r001'),
('rp003', @admin_id, @now, 'p003', 'r001'),
('rp004', @admin_id, @now, 'p004', 'r001'),
('rp005', @admin_id, @now, 'p005', 'r001'),
('rp006', @admin_id, @now, 'p006', 'r001'),
('rp007', @admin_id, @now, 'p007', 'r001'),
('rp008', @admin_id, @now, 'p008', 'r001'),
('rp009', @admin_id, @now, 'p009', 'r001');

-- 为管理员用户分配管理员角色
INSERT INTO sys_user_role (id, create_by, create_time, role_id, user_id) VALUES
('ur001', @admin_id, @now, 'r001', @admin_id);
