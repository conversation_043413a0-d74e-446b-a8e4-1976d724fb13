package com.czerp.erpbackend.purchase.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.purchase.dto.SalesOrderItemForPurchaseDTO;
import com.czerp.erpbackend.purchase.dto.SalesOrderItemForPurchaseQueryRequest;

import java.util.List;

/**
 * 用于采购订单引用销售订单的服务接口
 */
public interface SalesOrderForPurchaseService {
    
    /**
     * 分页查询可用于采购的销售订单明细
     * @param request 查询请求
     * @return 销售订单明细分页列表
     */
    PageResponse<SalesOrderItemForPurchaseDTO> findSalesOrderItemsForPurchase(SalesOrderItemForPurchaseQueryRequest request);
    
    /**
     * 根据ID查询销售订单明细
     * @param orderItemId 销售订单明细ID
     * @return 销售订单明细
     */
    SalesOrderItemForPurchaseDTO findSalesOrderItemById(String orderItemId);
    
    /**
     * 根据多个ID查询销售订单明细
     * @param orderItemIds 销售订单明细ID列表
     * @return 销售订单明细列表
     */
    List<SalesOrderItemForPurchaseDTO> findSalesOrderItemsByIds(List<String> orderItemIds);
}
