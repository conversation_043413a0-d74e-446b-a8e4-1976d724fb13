package com.czerp.erpbackend.customer.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.customer.dto.CustomerBoxInfoDTO;
import com.czerp.erpbackend.customer.dto.CustomerBoxInfoQueryRequest;

import java.util.List;

/**
 * 客户盒式信息服务接口
 */
public interface CustomerBoxInfoService {

    /**
     * 分页查询客户盒式信息列表
     * @param request 查询请求
     * @return 客户盒式信息分页列表
     */
    PageResponse<CustomerBoxInfoDTO> findCustomerBoxInfos(CustomerBoxInfoQueryRequest request);

    /**
     * 根据ID查询客户盒式信息
     * @param id 客户盒式信息ID
     * @return 客户盒式信息
     */
    CustomerBoxInfoDTO findCustomerBoxInfoById(String id);

    /**
     * 根据盒式编码查询客户盒式信息
     * @param boxCode 盒式编码
     * @return 客户盒式信息
     */
    CustomerBoxInfoDTO findCustomerBoxInfoByCode(String boxCode);

    /**
     * 查询所有启用的客户盒式信息
     * @return 客户盒式信息列表
     */
    List<CustomerBoxInfoDTO> findActiveCustomerBoxInfos();

    /**
     * 查询默认盒式
     * @return 默认盒式
     */
    CustomerBoxInfoDTO findDefaultCustomerBoxInfo();
}
