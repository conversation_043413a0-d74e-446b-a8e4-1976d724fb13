package com.czerp.erpbackend.sales.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.sales.dto.SalesOrderDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderDetailDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderItemDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryDTO;
import com.czerp.erpbackend.sales.service.SalesOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售订单Controller
 */
@RestController
@RequestMapping("/sales/orders")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "销售订单管理", description = "销售订单管理相关接口")
public class SalesOrderController {

    private final SalesOrderService salesOrderService;

    /**
     * 创建销售订单
     * @param orderDTO 销售订单DTO
     * @return 销售订单DTO
     */
    @PostMapping
    @Operation(summary = "创建销售订单", description = "创建销售订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:create')")
    public ResponseEntity<ApiResponse<SalesOrderDetailDTO>> createOrder(@Valid @RequestBody SalesOrderDetailDTO orderDTO) {
        log.info("Creating sales order: {}", orderDTO);
        SalesOrderDetailDTO createdOrder = salesOrderService.createOrder(orderDTO);
        return ResponseEntity.ok(ApiResponse.success(createdOrder));
    }

    /**
     * 更新销售订单
     * @param id 订单ID
     * @param orderDTO 销售订单DTO
     * @return 销售订单DTO
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新销售订单", description = "更新销售订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<SalesOrderDetailDTO>> updateOrder(@PathVariable String id, @Valid @RequestBody SalesOrderDetailDTO orderDTO) {
        log.info("Updating sales order with id: {}, data: {}", id, orderDTO);
        SalesOrderDetailDTO updatedOrder = salesOrderService.updateOrder(id, orderDTO);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    /**
     * 根据ID查询销售订单
     * @param id 订单ID
     * @return 销售订单DTO
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询销售订单", description = "根据ID查询销售订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<SalesOrderDetailDTO>> getOrderById(@PathVariable String id) {
        log.info("Getting sales order by id: {}", id);
        SalesOrderDetailDTO order = salesOrderService.getOrderById(id);
        return ResponseEntity.ok(ApiResponse.success(order));
    }

    /**
     * 根据订单号查询销售订单
     * @param orderNo 订单号
     * @return 销售订单DTO
     */
    @GetMapping("/by-order-no/{orderNo}")
    @Operation(summary = "根据订单号查询销售订单", description = "根据订单号查询销售订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<SalesOrderDetailDTO>> getOrderByOrderNo(@PathVariable String orderNo) {
        log.info("Getting sales order by order no: {}", orderNo);
        SalesOrderDetailDTO order = salesOrderService.getOrderByOrderNo(orderNo);
        return ResponseEntity.ok(ApiResponse.success(order));
    }

    /**
     * 分页查询销售订单
     * @param queryDTO 查询条件
     * @return 销售订单分页列表
     */
    @GetMapping
    @Operation(summary = "分页查询销售订单", description = "分页查询销售订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<PageResponse<SalesOrderDTO>>> getOrders(SalesOrderQueryDTO queryDTO) {
        log.info("Getting sales orders with query: {}", queryDTO);
        PageResponse<SalesOrderDTO> orders = salesOrderService.getOrders(queryDTO);
        return ResponseEntity.ok(ApiResponse.success(orders));
    }

    /**
     * 删除销售订单
     * @param id 订单ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除销售订单", description = "删除销售订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteOrder(@PathVariable String id) {
        log.info("Deleting sales order with id: {}", id);
        salesOrderService.deleteOrder(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 根据客户编码查询销售订单
     * @param customerCode 客户编码
     * @return 销售订单列表
     */
    @GetMapping("/by-customer-code/{customerCode}")
    @Operation(summary = "根据客户编码查询销售订单", description = "根据客户编码查询销售订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<List<SalesOrderDTO>>> getOrdersByCustomerCode(@PathVariable String customerCode) {
        log.info("Getting sales orders by customer code: {}", customerCode);
        List<SalesOrderDTO> orders = salesOrderService.getOrdersByCustomerCode(customerCode);
        return ResponseEntity.ok(ApiResponse.success(orders));
    }

    /**
     * 生成订单号
     * @return 订单号
     */
    @GetMapping("/generate-order-no")
    @Operation(summary = "生成订单号", description = "生成订单号")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:create')")
    public ResponseEntity<ApiResponse<String>> generateOrderNo() {
        log.info("Generating sales order no");
        String orderNo = salesOrderService.generateOrderNo();
        return ResponseEntity.ok(ApiResponse.success(orderNo));
    }

    /**
     * 打印订单
     * @param id 订单ID
     * @return 销售订单DTO
     */
    @PutMapping("/{id}/print")
    @Operation(summary = "打印订单", description = "打印订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<SalesOrderDTO>> printOrder(@PathVariable String id) {
        log.info("Printing sales order with id: {}", id);
        SalesOrderDTO order = salesOrderService.printOrder(id);
        return ResponseEntity.ok(ApiResponse.success(order));
    }

    /**
     * 根据客户ID查询历史订单明细
     * @param customerId 客户ID
     * @param keyword 关键词（可选，用于搜索客方货号或品名）
     * @return 历史订单明细列表
     */
    @GetMapping("/customers/{customerId}/historical-items")
    @Operation(summary = "根据客户ID查询历史订单明细", description = "根据客户ID查询历史订单明细")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<List<SalesOrderItemDTO>>> getHistoricalItemsByCustomerId(
            @PathVariable String customerId,
            @RequestParam(required = false) String keyword) {
        log.info("Getting historical items by customer id: {}, keyword: {}", customerId, keyword);
        List<SalesOrderItemDTO> items = salesOrderService.getHistoricalItemsByCustomerId(customerId, keyword);
        return ResponseEntity.ok(ApiResponse.success(items));
    }
}
