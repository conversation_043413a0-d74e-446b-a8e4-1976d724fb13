# 销售订单用料接口文档

## 📋 概述

本文档描述了销售订单用料数据获取接口，用于支持销售订单列表页面的主从表交互功能。当用户点击销售订单列表中的某一行时，前端可以通过此接口获取该订单的详细用料信息，并在下方的"订单用料"标签页中展示。

## 🔗 接口信息

### 基本信息
- **接口名称**: 获取销售订单用料信息
- **接口路径**: `/api/sales-order-materials/by-order/{orderId}`
- **请求方法**: `GET`
- **权限要求**: `sales:order:read`
- **响应格式**: `ApiResponse<List<SalesOrderMaterialDTO>>`

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | String | 是 | 销售订单ID |

#### 请求头
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer Token认证 |

### 请求示例

```bash
GET /api/sales-order-materials/by-order/12345678-1234-1234-1234-123456789012
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 📤 响应格式

### 成功响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "material-id-1",
      "orderItemId": "item-id-1",
      "serialNo": 1,
      "paperQuality": "250g白卡纸",
      "paperWidth": 165.00,
      "paperLength": 230.50,
      "widthOpen": 82.50,
      "lengthOpen": 115.25,
      "boardCount": 500,
      "usage": 1000.00,
      "method": "单张",
      "actualMaterialWidth": 167.40,
      "actualMaterialLength": 232.90,
      "unit": "Inch",
      "dieModel": "是",
      "dieModelNo": "DM001",
      "diePressLine": "是",
      "dieModelPosition": "中心位置",
      "dieOpenCount": 2,
      "supplier": "供应商A",
      "useInventoryCount": 100,
      "pressSizeWidth": "165+130+165",
      "currentInventory": 5000,
      "purchasedCount": 800,
      "inboundCount": 750,
      "materialsReceivedCount": 200,
      "createdBy": "user123",
      "createdByName": "张三",
      "createdTime": "2024-01-15T10:30:00",
      "updatedBy": "user123",
      "updatedByName": "张三",
      "updatedTime": "2024-01-15T14:20:00"
    }
  ]
}
```

### 错误响应

```json
{
  "success": false,
  "code": "404",
  "message": "订单不存在或无用料信息",
  "data": null
}
```

## 📊 数据字段说明

### SalesOrderMaterialDTO 字段详解

| 字段名 | 类型 | 说明 | 对应需求字段 | 备注 |
|--------|------|------|-------------|------|
| `id` | String | 用料记录ID | - | 主键 |
| `orderItemId` | String | 销售订单行项目ID | - | 外键关联 |
| `serialNo` | Integer | 序号 | - | 用于排序 |
| `paperQuality` | String | 纸质 | 纸质 | 如"250g白卡纸" |
| `paperWidth` | BigDecimal | 纸度 | 纸度 | 数值，单位见unit字段 |
| `paperLength` | BigDecimal | 纸长 | 纸长 | 数值，单位见unit字段 |
| `unit` | String | 单位 | 纸度单位/纸长单位 | 默认值"Inch" |
| `widthOpen` | BigDecimal | 度开 | 度开 | - |
| `lengthOpen` | BigDecimal | 长开 | 长开 | - |
| `boardCount` | Integer | 纸板数 | 纸板数 | - |
| `usage` | BigDecimal | 用量 | 用量 | - |
| `method` | String | 方式 | 方式 | 如"单张"、"连续" |
| `actualMaterialWidth` | BigDecimal | 实际用料宽 | 实际用料宽 | - |
| `actualMaterialLength` | BigDecimal | 实际用料长 | 实际用料长 | - |
| `dieModel` | String | 啤模 | 啤模 | "是"表示选中，空表示未选中 |
| `dieModelNo` | String | 啤模编号 | 啤模编号 | - |
| `diePressLine` | String | 啤模压线 | 啤模压线 | "是"表示选中，空表示未选中 |
| `dieModelPosition` | String | 啤模位置 | 啤模位置 | - |
| `dieOpenCount` | Integer | 模开数 | 模开数 | - |
| `supplier` | String | 供应商 | 供应商 | - |
| `useInventoryCount` | Integer | 使用库存数 | 使用库存数 | - |
| `pressSizeWidth` | String | 压线尺寸(纸度) | 压线尺寸(纸度) | 格式如"165+130+165" |
| `currentInventory` | Integer | 当前库存 | 当前库存 | - |
| `purchasedCount` | Integer | 已采购数 | 已采购数 | - |
| `inboundCount` | Integer | 已入库数 | 已入库数 | - |
| `materialsReceivedCount` | Integer | 已领料数 | 已领料数 | - |

### 扩展字段（系统字段）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `createdBy` | String | 创建人ID |
| `createdByName` | String | 创建人姓名 |
| `createdTime` | LocalDateTime | 创建时间 |
| `updatedBy` | String | 更新人ID |
| `updatedByName` | String | 更新人姓名 |
| `updatedTime` | LocalDateTime | 更新时间 |

## 💻 前端集成示例

### TypeScript 接口定义

```typescript
// API响应类型
interface ApiResponse<T> {
  success: boolean;
  code: string;
  message: string;
  data: T;
}

// 销售订单用料DTO
interface SalesOrderMaterialDTO {
  id?: string;
  orderItemId: string;
  serialNo: number;
  paperQuality: string;
  paperWidth: number;
  paperLength: number;
  unit: string;
  widthOpen: number;
  lengthOpen: number;
  boardCount: number;
  usage: number;
  method: string;
  actualMaterialWidth: number;
  actualMaterialLength: number;
  dieModel: string;
  dieModelNo: string;
  diePressLine: string;
  dieModelPosition: string;
  dieOpenCount: number;
  supplier: string;
  useInventoryCount: number;
  pressSizeWidth: string;
  currentInventory: number;
  purchasedCount: number;
  inboundCount: number;
  materialsReceivedCount: number;
  createdBy?: string;
  createdByName?: string;
  createdTime?: string;
  updatedBy?: string;
  updatedByName?: string;
  updatedTime?: string;
}
```

### API服务函数

```typescript
import { get } from '@/services/request';

/**
 * 获取销售订单用料信息
 * @param orderId 销售订单ID
 * @returns 用料信息列表
 */
export async function getSalesOrderMaterials(
  orderId: string
): Promise<ApiResponse<SalesOrderMaterialDTO[]>> {
  return get<SalesOrderMaterialDTO[]>(`/api/sales-order-materials/by-order/${orderId}`);
}
```

### 使用示例

```typescript
// 在销售订单列表页面中使用
const handleRowClick = async (record: SalesOrderDTO) => {
  try {
    const response = await getSalesOrderMaterials(record.id);
    if (response.success) {
      // 渲染到下方的"订单用料"标签页
      setMaterialData(response.data);
      // 切换到用料标签页
      setActiveTab('materials');
    } else {
      message.error(response.message || '获取用料信息失败');
    }
  } catch (error) {
    console.error('获取用料信息失败:', error);
    message.error('获取用料信息失败');
  }
};
```

## 🔧 数据处理建议

### 复选框字段处理

```typescript
// 将字符串转换为布尔值
const formatMaterialData = (materials: SalesOrderMaterialDTO[]) => {
  return materials.map(item => ({
    ...item,
    dieModelChecked: item.dieModel === '是',
    diePressLineChecked: item.diePressLine === '是'
  }));
};
```

### 单位字段处理

```typescript
// 纸度单位和纸长单位都使用unit字段
const getMaterialWithUnits = (material: SalesOrderMaterialDTO) => {
  return {
    ...material,
    paperWidthUnit: material.unit || 'Inch',  // 纸度单位(默认值Inch)
    paperLengthUnit: material.unit || 'Inch'  // 纸长单位(默认值Inch)
  };
};
```

## ⚠️ 注意事项

1. **权限验证**: 确保用户具有`sales:order:read`权限
2. **错误处理**: 接口可能返回空数组，表示该订单暂无用料信息
3. **数据排序**: 返回的数据已按`serialNo`字段排序
4. **单位统一**: `unit`字段同时表示纸度单位和纸长单位，默认为"Inch"
5. **复选框字段**: `dieModel`和`diePressLine`字段值为"是"表示选中状态
6. **压线尺寸格式**: `pressSizeWidth`字段存储格式化字符串，如"165+130+165"

## 🚀 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 401 | 未授权，Token无效或过期 |
| 403 | 权限不足 |
| 404 | 订单不存在 |
| 500 | 服务器内部错误 |

---

**文档版本**: v1.0  
**最后更新**: 2024-01-15  
**维护人员**: 后端开发团队
