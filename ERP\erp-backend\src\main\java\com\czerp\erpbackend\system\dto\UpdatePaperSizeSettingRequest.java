package com.czerp.erpbackend.system.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 更新纸度设置请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePaperSizeSettingRequest {

    /**
     * 纸度(inch)
     */
    @NotNull(message = "纸度(inch)不能为空")
    @DecimalMin(value = "0.01", message = "纸度(inch)必须大于0")
    @Digits(integer = 8, fraction = 2, message = "纸度(inch)格式不正确，最多8位整数，2位小数")
    private BigDecimal paperSizeInch;

    /**
     * 纸度(cm)
     */
    @NotNull(message = "纸度(cm)不能为空")
    @DecimalMin(value = "0.01", message = "纸度(cm)必须大于0")
    @Digits(integer = 8, fraction = 2, message = "纸度(cm)格式不正确，最多8位整数，2位小数")
    private BigDecimal paperSizeCm;

    /**
     * 最大损耗(inch)
     */
    @NotNull(message = "最大损耗(inch)不能为空")
    @DecimalMin(value = "0.00", message = "最大损耗(inch)必须大于等于0")
    @Digits(integer = 8, fraction = 2, message = "最大损耗(inch)格式不正确，最多8位整数，2位小数")
    private BigDecimal maxLossInch;

    /**
     * 最大损耗(cm)
     */
    @NotNull(message = "最大损耗(cm)不能为空")
    @DecimalMin(value = "0.00", message = "最大损耗(cm)必须大于等于0")
    @Digits(integer = 8, fraction = 2, message = "最大损耗(cm)格式不正确，最多8位整数，2位小数")
    private BigDecimal maxLossCm;
}
