Java (Spring Boot) Backend Application Development Rules for AI Code Generation V1.1

1. Document Purpose

This document provides a standardized set of guidelines for AI code generators and human developers building ERP backend services using Java and Spring Boot. The goal is to ensure consistency, maintainability, scalability, robustness, and development efficiency by enforcing code/logic reuse, separation of concerns, clear modular structure, and continuous improvement processes.

2. Core Design Principles (Foundation)

All development activities MUST adhere to these principles:

DRY (Don't Repeat Yourself): MUST NOT duplicate business logic, data processing code, configuration, or constants. If duplication is detected, it MUST be abstracted into utility classes, base classes, services, or configuration properties.

SoC (Separation of Concerns): MUST strictly follow a layered architecture. Ensure distinct responsibilities for: Presentation (Controller), Business Logic (Service), Data Access (Repository/DAO), Domain Model (Entity), and Data Transfer Objects (DTO).

High Cohesion, Low Coupling: Each module/component MUST focus on a single, well-defined responsibility (High Cohesion). Dependencies between modules MUST be minimized and rely on interfaces, not concrete implementations (Low Coupling).

Program to an Interface: MUST depend on abstractions (interfaces) rather than concrete implementations, especially in Service and Repository layers, leveraging Spring Dependency Injection (DI).

3. Architecture & Layering (Mandatory)

A clear layered architecture MUST be implemented. The recommended structure is:

Controller Layer (@RestController):

Responsibility: Receive HTTP requests, perform basic input validation (e.g., using @Valid on DTOs), delegate business logic execution to the Service layer, and assemble DTOs for the HTTP response.

FORBIDDEN: Implementing complex business logic, direct database operations, or transaction management within Controllers.

Focus: HTTP protocol handling, request/response (JSON) transformation.

Data Contract: MUST use DTOs for request bodies and response payloads. MUST NOT expose Entity objects directly via APIs.

Service Layer (@Service):

Responsibility: Implement core business logic, orchestrate calls to one or more Repository methods, manage transactions (@Transactional), enforce business rules, and perform complex validation.

Structure: MUST define a Service interface (e.g., UserService). MUST provide an implementation class named UserServiceImpl implementing the interface. Controllers MUST inject the Service interface.

Cohesion: Services SHOULD encapsulate logic for a specific business domain (e.g., InventoryService, PurchaseOrderService).

Interaction: Can invoke other Services, but circular dependencies MUST be avoided. Consider business boundaries carefully.

Repository/DAO Layer (@Repository):

Responsibility: Data persistence operations; interact with the database.

Technology: RECOMMENDED to use Spring Data JPA. MUST define Repository interfaces (e.g., extending JpaRepository).

FORBIDDEN: Containing any business logic. Repositories are strictly for data CRUD and querying.

Naming: Method names MUST clearly indicate the data operation (follow Spring Data JPA naming conventions or use explicit @Query with clear intent).

Domain/Entity Layer (@Entity):

Responsibility: Represent core business domain objects, typically mapped to database tables using JPA annotations (@Entity, @Table, @Id, @Column, @ManyToOne, @Version, etc.).

Content: Contains object properties and potentially simple, intrinsic behaviors (e.g., status calculation), but MUST NOT contain business process logic.

DTO (Data Transfer Object) Layer:

Responsibility: Plain Old Java Objects (POJOs) used for transferring data between layers (especially Controller <-> Service) and as API contracts. Hides internal Entity structure.

Mapping: RECOMMENDED to use MapStruct for Entity <-> DTO conversion. Manual mapping is acceptable.

Common/Util Layer:

Responsibility: Store cross-cutting, reusable utilities (e.g., date/string manipulation, custom validators), constants, base configurations, and common custom exception classes.

4. Modularity & Package Structure (Mandatory)

Top-Level Modules: The root package MUST be organized by core business domains (e.g., user, permission, product, inventory, purchase, production, sales, system).

Layered Sub-Packages: Within each domain package, sub-packages MUST be created according to the layer structure (e.g., controller, service (containing impl sub-package), repository, entity, dto, exception, enums).

com.czerp.erpbackend  // Base package (Example)
├── common
├── config
├── security
├── user
│   ├── controller
│   ├── dto
│   ├── entity
│   ├── exception
│   ├── repository
│   └── service
│       └── impl      // Service implementations MUST be here
├── product
│   └── ... (similar structure)
├── inventory
│   └── ... (similar structure)
└── ... (other business modules)


Access Control: Use Java access modifiers (public, protected, private, package-private [default]) effectively to enforce low coupling. Cross-domain access MUST generally occur only through public Service interfaces. Avoid direct cross-domain Repository access. Internal helper classes SHOULD use package-private access where possible.

5. Service Layer Rules

Interface First: MUST define a Service interface. Implementation class MUST be named XxxServiceImpl.

Transaction Management:

MUST use @Transactional on public methods that modify data.

SHOULD use @Transactional(readOnly = true) on methods that only read data for optimization.

MUST carefully select propagation behavior and isolation levels only if defaults are insufficient for specific business requirements.

AVOID annotating unnecessary methods (e.g., private methods called within a transactional public method).

Exception Handling:

Services MUST catch specific data access exceptions (if needed for specific handling) and re-throw them as custom business exceptions (e.g., ProductNotFoundException inheriting from a base BusinessException).

MUST NOT swallow exceptions silently. Exceptions should propagate up to be handled by the global exception handler.

MUST define a clear hierarchy of custom business exceptions (e.g., ValidationException, NotFoundException, ConcurrencyException) inheriting from a common BusinessException (which SHOULD extend RuntimeException).

Logic Reuse:

Extract reusable logic into private methods within the service or into separate @Component utility classes injected into the service.

For cross-cutting concerns applicable to multiple services (e.g., specific logging patterns, complex permission checks), CONSIDER using AOP or a shared utility Service.

6. Repository Layer Rules

Technology: RECOMMENDED Spring Data JPA. Define interfaces extending JpaRepository or using relevant Spring Data features.

Queries: Use Spring Data JPA query derivation. For complex queries, MUST use @Query (JPQL/native SQL) or Named Queries.

N+1 Problem: MUST actively prevent N+1 query problems when fetching associations using JOIN FETCH in @Query or using @EntityGraph.

Return Types: Query methods MUST return specific types (Optional<T>, List<T>, Page<T>). MUST NOT return null for methods expected to return collections (return empty collections instead).

Pagination/Sorting: List query methods intended for UI display MUST accept a Pageable parameter.

7. Cross-Cutting Techniques & Practices

Dependency Injection (DI):

MUST use Spring DI.

MUST use constructor injection.

FORBIDDEN: Field injection (@Autowired on fields).

Configuration Management:

MUST use externalized configuration (application.yml or application.properties). application.yml is PREFERRED.

MUST use @ConfigurationProperties for type-safe binding of structured configuration.

FORBIDDEN: Hardcoding secrets (DB passwords, API keys). These MUST be managed via environment variables, external secured files, or a configuration server (e.g., Nacos, Spring Cloud Config).

Validation:

MUST use Bean Validation API (e.g., @NotNull, @Size, @Email, custom validators) on DTOs at the Controller boundary.

MUST trigger validation using @Valid on Controller method parameters.

Complex business rule validation (involving multiple fields or database checks) MUST be performed within the Service layer.

Unified Exception Handling:

MUST implement a global exception handler using @ControllerAdvice and @ExceptionHandler.

Handler MUST catch specific custom business exceptions and general exceptions (like Exception).

Handler MUST return a standardized error response DTO to the client.

Logging:

MUST use SLF4j facade with Logback (default) or Log4j2 implementation.

MUST configure distinct logging levels for different environments (e.g., DEBUG in dev, INFO in prod).

MUST log method entry/exit with parameters/return values (at DEBUG or TRACE level).

MUST log significant business operations and state changes (at INFO level).

MUST log all caught exceptions with stack traces (at ERROR level).

RECOMMENDED: Use MDC (Mapped Diagnostic Context) for request tracing (e.g., logging traceId).

MUST implement specific logging for operations (audit log) and errors as required by the ERP needs, potentially to separate files or databases.

Asynchronous Processing:

RECOMMENDED: Use Spring's @Async or a Message Queue (e.g., RabbitMQ, Kafka) for long-running, non-critical tasks (e.g., sending notifications, complex report generation, post-approval batch updates) to improve API responsiveness.

Caching:

RECOMMENDED: Use Spring Cache abstraction (@Cacheable, @CacheEvict, etc.) with an appropriate provider (e.g., Caffeine for local, Redis for distributed) for frequently accessed, relatively stable data (e.g., dictionaries, system parameters, permissions).

MUST implement strategies to mitigate cache issues (penetration, breakdown, avalanche).

MUST exercise caution when caching highly volatile or critical data (like real-time inventory). Ensure appropriate cache invalidation strategies are in place if caching is used.

8. ERP-Specific Implementation Points

Inventory Management:

Real-time: Core inventory table MUST reflect real-time quantity.

Concurrency: MUST use optimistic locking (@Version annotation on a version column in the inventory entity) for concurrent stock updates. Handle OptimisticLockingFailureException (or similar) with retries or user feedback. Consider pessimistic locking (SELECT ... FOR UPDATE) only for very specific, high-contention scenarios where optimistic locking is insufficient.

Ledger: MUST maintain an inventory_log table recording every stock movement transaction atomically with the inventory update (operation type, source document, item, warehouse, location, quantity change, balance after change, timestamp, operator).

Async Update: CONSIDER updating inventory and log asynchronously (e.g., via event/message queue) after a related document (e.g., Purchase Receipt) is approved to improve the approval API's response time, ensuring eventual consistency.

Document Handling:

Status: MUST use Java enum to represent document statuses (DRAFT, PENDING_APPROVAL, APPROVED, REJECTED, CANCELLED, etc.).

Transitions: Business logic for valid status transitions MUST be implemented in the Service layer. CONSIDER a State Machine pattern for complex workflows.

Atomicity: Operations involving multiple data updates (e.g., approving a sales order and updating inventory) MUST be executed within a single transaction (@Transactional).

Permission Management:

Authentication/Authorization: RECOMMENDED to use Spring Security.

Functional Permissions: MUST control API access using Spring Security mechanisms like @PreAuthorize/@PostAuthorize annotations on Service methods or Controller methods, or URL-based security configurations.

Data Permissions: MUST implement data filtering based on user roles/department/ownership. Common strategies:

Dynamically add criteria in the Service layer before calling the Repository.

Use AOP to intercept Repository calls and inject filters.

(If using Hibernate) Use JPA @Filter/@FilterDef. Choose the most suitable approach based on complexity.

Printing:

Backend Controller MUST provide endpoints returning structured data (DTOs) required for printing.

If backend generates print files (e.g., PDF), this logic SHOULD be encapsulated in a dedicated PrintingService. Libraries like JasperReports or iText/OpenPDF can be used. Template management features MUST be supported.

9. Code Quality & Standards

Naming Conventions: MUST follow standard Java conventions (UpperCamelCase for classes/interfaces/enums, lowerCamelCase for methods/variables, UPPER_SNAKE_CASE for constants). Names MUST be descriptive and unambiguous.

Code Comments:

MUST use Javadoc (/** ... */) for all public APIs (interfaces, public classes, public methods, enums). Javadoc MUST describe purpose, parameters (@param), return values (@return), and exceptions (@throws).

MUST use line (//) or block (/* ... */) comments to explain complex algorithms, business logic, or non-obvious code sections ("why", not just "what").

Use // TODO:, // FIXME:, // HACK: markers for actionable items.

MUST keep comments synchronized with code changes. Outdated comments are forbidden.

AVOID commenting obvious code.

Unit & Integration Testing:

RECOMMENDED: Write unit tests (JUnit 5 + Mockito) for Service layer business logic.

RECOMMENDED: Write integration tests (Spring Boot Test, potentially with Testcontainers or H2) to verify interactions between Controller, Service, and Repository, including database interaction.

Tests SHOULD cover main logic paths, edge cases, and error conditions.

Code Review:

MANDATORY: Code review process MUST be enforced before merging code.

Review checklist MUST include adherence to these rules, readability, logic correctness, performance considerations, security aspects, test coverage, and DRY principle adherence.

10. Summary

Adherence to these rules by both AI generators and human developers is MANDATORY to build a high-quality, maintainable, and scalable ERP backend service. Consistent application of these practices will lead to more efficient delivery of reliable software.