package com.czerp.erpbackend.auth.service;

import com.czerp.erpbackend.auth.dto.LoginRequest;
import com.czerp.erpbackend.auth.dto.LoginResponse;
import com.czerp.erpbackend.auth.dto.RefreshTokenRequest;
import com.czerp.erpbackend.auth.dto.RefreshTokenResponse;
import com.czerp.erpbackend.system.dto.UserDTO;

/**
 * 认证服务接口
 */
public interface AuthService {
    
    /**
     * 用户登录
     * @param request 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest request);
    
    /**
     * 用户登出
     * @param token 令牌
     */
    void logout(String token);
    
    /**
     * 刷新令牌
     * @param request 刷新令牌请求
     * @return 刷新令牌响应
     */
    RefreshTokenResponse refreshToken(RefreshTokenRequest request);
    
    /**
     * 获取当前用户信息
     * @return 用户信息
     */
    UserDTO getCurrentUser();
    
    /**
     * 修改密码
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     */
    void changePassword(String oldPassword, String newPassword);
    
    /**
     * 验证令牌
     * @param token 令牌
     * @return 用户ID
     */
    String validateToken(String token);
} 