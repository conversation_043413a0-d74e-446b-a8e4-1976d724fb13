package com.czerp.erpbackend.sales.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.sales.dto.SalesOrderProcessDTO;
import com.czerp.erpbackend.sales.service.SalesOrderProcessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售订单工序Controller
 */
@RestController
@RequestMapping("/sales/orders/{orderId}/processes")
@Tag(name = "销售订单工序管理", description = "销售订单工序管理相关接口")
@RequiredArgsConstructor
@Slf4j
public class SalesOrderProcessController {

    private final SalesOrderProcessService salesOrderProcessService;

    /**
     * 查询订单工序列表
     * @param orderId 订单ID
     * @return 工序列表
     */
    @GetMapping
    @Operation(summary = "查询订单工序列表", description = "查询订单工序列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<List<SalesOrderProcessDTO>>> getProcesses(@PathVariable String orderId) {
        log.info("Getting processes for order: {}", orderId);
        List<SalesOrderProcessDTO> processes = salesOrderProcessService.getProcessesByOrderId(orderId);
        return ResponseEntity.ok(ApiResponse.success(processes));
    }

    /**
     * 添加订单工序
     * @param orderId 订单ID
     * @param processDTO 工序DTO
     * @return 添加的工序
     */
    @PostMapping
    @Operation(summary = "添加订单工序", description = "添加订单工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<SalesOrderProcessDTO>> addProcess(
            @PathVariable String orderId,
            @Valid @RequestBody SalesOrderProcessDTO processDTO) {
        log.info("Adding process for order: {}, process: {}", orderId, processDTO);
        
        // 确保订单ID一致
        processDTO.setOrderId(orderId);
        
        SalesOrderProcessDTO addedProcess = salesOrderProcessService.addProcess(processDTO);
        return ResponseEntity.ok(ApiResponse.success(addedProcess));
    }

    /**
     * 批量添加订单工序
     * @param orderId 订单ID
     * @param processDTOs 工序DTO列表
     * @return 添加的工序列表
     */
    @PostMapping("/batch")
    @Operation(summary = "批量添加订单工序", description = "批量添加订单工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<List<SalesOrderProcessDTO>>> addProcesses(
            @PathVariable String orderId,
            @Valid @RequestBody List<SalesOrderProcessDTO> processDTOs) {
        log.info("Adding {} processes for order: {}", processDTOs.size(), orderId);
        
        // 确保所有工序的订单ID一致
        processDTOs.forEach(dto -> dto.setOrderId(orderId));
        
        List<SalesOrderProcessDTO> addedProcesses = salesOrderProcessService.addProcesses(processDTOs);
        return ResponseEntity.ok(ApiResponse.success(addedProcesses));
    }

    /**
     * 更新订单工序
     * @param orderId 订单ID
     * @param id 工序ID
     * @param processDTO 工序DTO
     * @return 更新后的工序
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新订单工序", description = "更新订单工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<SalesOrderProcessDTO>> updateProcess(
            @PathVariable String orderId,
            @PathVariable String id,
            @Valid @RequestBody SalesOrderProcessDTO processDTO) {
        log.info("Updating process: {} for order: {}, process: {}", id, orderId, processDTO);
        
        // 确保订单ID一致
        processDTO.setOrderId(orderId);
        
        SalesOrderProcessDTO updatedProcess = salesOrderProcessService.updateProcess(id, processDTO);
        return ResponseEntity.ok(ApiResponse.success(updatedProcess));
    }

    /**
     * 删除订单工序
     * @param orderId 订单ID
     * @param id 工序ID
     * @return 无内容
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除订单工序", description = "删除订单工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<Void>> deleteProcess(
            @PathVariable String orderId,
            @PathVariable String id) {
        log.info("Deleting process: {} for order: {}", id, orderId);
        salesOrderProcessService.deleteProcess(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 删除订单所有工序
     * @param orderId 订单ID
     * @return 无内容
     */
    @DeleteMapping
    @Operation(summary = "删除订单所有工序", description = "删除订单所有工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<Void>> deleteAllProcesses(@PathVariable String orderId) {
        log.info("Deleting all processes for order: {}", orderId);
        salesOrderProcessService.deleteProcessesByOrderId(orderId);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
}
