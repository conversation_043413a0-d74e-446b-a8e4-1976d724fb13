package com.czerp.erpbackend.product.dto;

import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量删除请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchDeleteRequest {
    
    /**
     * ID列表
     */
    @NotEmpty(message = "ID列表不能为空")
    private List<String> ids;
}
