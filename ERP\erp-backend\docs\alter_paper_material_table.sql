-- 第二步：修改paper_material表结构，建立与paper_type表的外键关联

-- 1. 添加临时字段用于迁移
ALTER TABLE paper_material ADD COLUMN paper_type_id INT COMMENT '纸类ID';

-- 2. 更新临时字段，将paper_type字符串映射到paper_type表的ID
UPDATE paper_material pm
JOIN paper_type pt ON pm.paper_type = pt.paper_type_name
SET pm.paper_type_id = pt.id
WHERE pm.paper_type IS NOT NULL;

-- 3. 删除原有字段，保留新字段
ALTER TABLE paper_material DROP COLUMN paper_type;

-- 4. 添加外键约束
ALTER TABLE paper_material 
ADD CONSTRAINT fk_paper_material_paper_type 
FOREIGN KEY (paper_type_id) REFERENCES paper_type(id);

-- 5. 添加索引以提高查询性能
ALTER TABLE paper_material ADD INDEX idx_paper_type_id (paper_type_id);
