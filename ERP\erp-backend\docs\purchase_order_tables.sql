-- 采购订单头表
CREATE TABLE purchase_order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    purchase_order_no VARCHAR(50) NOT NULL COMMENT '采购单号',
    purchase_date DATE COMMENT '采购日期',
    purchaser VARCHAR(50) COMMENT '采购员',
    payment_method VARCHAR(50) COMMENT '付款方式',
    purchase_type VARCHAR(50) COMMENT '采购类型',
    trading_unit VARCHAR(100) COMMENT '交易单位',
    supplier_code VARCHAR(50) COMMENT '供应商编码',
    supplier_name VARCHAR(100) COMMENT '供应商名称',
    address VARCHAR(255) COMMENT '地址',
    phone VARCHAR(20) COMMENT '电话',
    contact_person VARCHAR(50) COMMENT '联系人',
    mobile VARCHAR(20) COMMENT '手机',
    email VARCHAR(100) COMMENT '邮箱',
    remarks VARCHAR(500) COMMENT '备注',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME COMMENT '更新时间',
    UNIQUE KEY uk_purchase_order_no (purchase_order_no)
) COMMENT='采购订单头表';

-- 采购订单行表
CREATE TABLE purchase_order_item (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    purchase_order_id BIGINT NOT NULL COMMENT '采购订单ID',
    paper_quality VARCHAR(100) COMMENT '纸质',
    paper_board_category VARCHAR(100) COMMENT '纸板类别',
    corrugation_type VARCHAR(50) COMMENT '楞别',
    paper_width DECIMAL(10,2) COMMENT '纸度',
    paper_length DECIMAL(10,2) COMMENT '纸长',
    binding_method VARCHAR(50) COMMENT '合订',
    binding_specification VARCHAR(100) COMMENT '合订规格',
    material_change VARCHAR(100) COMMENT '用料异动',
    special_quotation VARCHAR(100) COMMENT '报价特价',
    paper_quotation DECIMAL(10,2) COMMENT '纸质报价',
    discount DECIMAL(10,2) COMMENT '折扣',
    quantity INT COMMENT '数量',
    price DECIMAL(10,2) COMMENT '价格',
    amount DECIMAL(10,2) COMMENT '金额',
    creasing_size VARCHAR(100) COMMENT '压线尺寸(纸度)',
    creasing_method VARCHAR(100) COMMENT '压线方式',
    remarks VARCHAR(500) COMMENT '备注',
    folding_specification VARCHAR(100) COMMENT '折度规格',
    length_meters DECIMAL(10,2) COMMENT '长度(米)',
    area_square_meters DECIMAL(10,2) COMMENT '面积(平米)',
    volume_cubic_meters DECIMAL(10,2) COMMENT '体积(立方米)',
    unit_weight DECIMAL(10,2) COMMENT '单重',
    total_weight_kg DECIMAL(10,2) COMMENT '总重(KG)',
    processing_fee DECIMAL(10,2) COMMENT '加工费',
    currency VARCHAR(20) COMMENT '币种',
    delivery_date DATE COMMENT '交期',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME COMMENT '更新时间',
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_order(id)
) COMMENT='采购订单行表';
