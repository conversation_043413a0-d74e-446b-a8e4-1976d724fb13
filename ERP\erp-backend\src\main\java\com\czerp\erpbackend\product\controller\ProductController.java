package com.czerp.erpbackend.product.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.ImportResult;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.util.ExcelTemplateUtil;
import com.czerp.erpbackend.product.dto.*;
import com.czerp.erpbackend.product.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 货品控制器
 */
@RestController
@RequestMapping("/products")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Product Management", description = "货品管理相关接口")
public class ProductController {

    private final ProductService productService;

    @GetMapping
    @Operation(summary = "获取货品列表", description = "分页查询货品列表，支持多条件筛选")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product:list')")
    public ResponseEntity<ApiResponse<PageResponse<ProductDTO>>> getProducts(ProductQueryRequest request) {
        log.info("Getting products with request: {}", request);

        // 添加调试日志，检查当前用户的权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            log.info("Current user: {}", authentication.getName());
            log.info("Authorities: {}", authentication.getAuthorities());

            boolean hasProductListPermission = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("product:list"));
            log.info("Has product:list permission: {}", hasProductListPermission);
        } else {
            log.warn("No authentication found in security context");
        }

        PageResponse<ProductDTO> products = productService.findProducts(request);
        return ResponseEntity.ok(ApiResponse.success(products));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取货品详情", description = "根据ID获取货品详细信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product:read')")
    public ResponseEntity<ApiResponse<ProductDTO>> getProduct(@PathVariable String id) {
        log.info("Getting product with id: {}", id);
        ProductDTO product = productService.findProductById(id);
        return ResponseEntity.ok(ApiResponse.success(product));
    }

    @PostMapping
    @Operation(summary = "创建货品", description = "创建新货品")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product:create')")
    public ResponseEntity<ApiResponse<ProductDTO>> createProduct(@Valid @RequestBody CreateProductRequest request) {
        log.info("Creating product with request: {}", request);
        ProductDTO product = productService.createProduct(request);
        return ResponseEntity.ok(ApiResponse.success(product));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新货品", description = "更新货品信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product:update')")
    public ResponseEntity<ApiResponse<ProductDTO>> updateProduct(
            @PathVariable String id,
            @Valid @RequestBody UpdateProductRequest request) {
        log.info("Updating product with id: {} and request: {}", id, request);
        ProductDTO product = productService.updateProduct(id, request);
        return ResponseEntity.ok(ApiResponse.success(product));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除货品", description = "删除单个货品")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteProduct(@PathVariable String id) {
        log.info("Deleting product with id: {}", id);
        productService.deleteProduct(id);
        return ResponseEntity.ok(ApiResponse.success());
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除货品", description = "批量删除货品")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product:delete')")
    public ResponseEntity<ApiResponse<Void>> batchDeleteProducts(@Valid @RequestBody BatchDeleteRequest request) {
        log.info("Batch deleting products with ids: {}", request.getIds());
        productService.batchDeleteProducts(request.getIds());
        return ResponseEntity.ok(ApiResponse.success());
    }

    @PatchMapping("/{id}/status")
    @Operation(summary = "切换货品状态", description = "启用/禁用货品")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product:update')")
    public ResponseEntity<ApiResponse<ProductDTO>> toggleProductStatus(
            @PathVariable String id,
            @Valid @RequestBody ToggleStatusRequest request) {
        log.info("Toggling product status with id: {} and disabled: {}", id, request.getDisabled());
        ProductDTO product = productService.toggleProductStatus(id, request.getDisabled());
        return ResponseEntity.ok(ApiResponse.success(product));
    }

    @PutMapping("/price")
    @Operation(summary = "更新货品价格", description = "批量更新货品价格")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product:update')")
    public ResponseEntity<ApiResponse<Void>> updateProductPrices(@Valid @RequestBody UpdateProductPriceRequest request) {
        log.info("Updating product prices with request: {}", request);
        productService.updateProductPrices(request);
        return ResponseEntity.ok(ApiResponse.success());
    }

    @GetMapping("/import/template")
    @Operation(summary = "下载货品导入模板", description = "下载货品导入模板Excel文件")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product:create')")
    public ResponseEntity<byte[]> downloadImportTemplate() {
        log.info("Downloading product import template");

        try {
            // 创建Excel模板
            String[] headers = {"盒式编码*", "盒式名称*", "分类名称", "规格名称", "报价公式*", "算价单位*", "报价单位*", "连接方式", "禁止双驳", "长度大于此值双驳", "跳度公差", "客户编码", "客户名称", "价格", "图片URL", "是否禁用"};
            String[] descriptions = {
                "必填，唯一标识",
                "必填，盒式名称",
                "选填，分类名称",
                "选填，规格名称",
                "必填，报价公式",
                "必填，算价单位",
                "必填，报价单位",
                "选填，连接方式",
                "选填，true表示禁止，false表示允许",
                "选填，数字，单位mm",
                "选填，跳度公差",
                "选填，客户编码",
                "选填，客户名称",
                "选填，货品单价",
                "选填，货品图片URL",
                "选填，true表示禁用，false表示启用"
            };

            byte[] excelBytes = ExcelTemplateUtil.createTemplate("货品导入模板", headers, descriptions);

            // 设置响应头
            HttpHeaders headers2 = new HttpHeaders();
            headers2.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers2.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=product_import_template.xlsx");

            return ResponseEntity.ok()
                    .headers(headers2)
                    .body(excelBytes);
        } catch (Exception e) {
            log.error("Error creating product import template: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "导入货品", description = "通过Excel文件批量导入货品")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product:create')")
    public ResponseEntity<ApiResponse<ImportResult>> importProducts(@RequestParam("file") MultipartFile file) {
        log.info("Importing products from file: {}", file.getOriginalFilename());

        // 获取当前用户信息并记录日志
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUser = authentication != null ? authentication.getName() : "unknown";
        log.info("Import operation initiated by user: {}", currentUser);

        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(ApiResponse.error("请选择要上传的文件"));
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            return ResponseEntity.badRequest().body(ApiResponse.error("请上传Excel文件(.xlsx或.xls)"));
        }

        try {
            // 确保SecurityContext中有当前用户信息
            if (authentication != null && authentication.isAuthenticated() &&
                !authentication.getPrincipal().equals("anonymousUser")) {
                log.info("User is authenticated: {}", currentUser);
                log.info("User authorities: {}", authentication.getAuthorities());
            } else {
                log.warn("No authenticated user found or user is anonymous. Using system user for auditing.");
            }

            // 使用SecurityUtils获取当前用户名，确保与Service层使用相同的方法
            String currentUsername = com.czerp.erpbackend.common.util.SecurityUtils.getCurrentUsername();
            log.info("Current username from SecurityUtils: {}", currentUsername);

            ImportResult result = productService.importProducts(file);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("Error importing products: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("导入失败: " + e.getMessage()));
        }
    }
}
