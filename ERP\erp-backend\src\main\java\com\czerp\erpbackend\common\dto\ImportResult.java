package com.czerp.erpbackend.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 导入结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportResult {
    
    /**
     * 成功数量
     */
    private int success;
    
    /**
     * 失败数量
     */
    private int fail;
    
    /**
     * 错误信息列表
     */
    @Builder.Default
    private List<String> errors = new ArrayList<>();
    
    /**
     * 添加错误信息
     * @param error 错误信息
     */
    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
    }
    
    /**
     * 增加成功数量
     */
    public void incrementSuccess() {
        success++;
    }
    
    /**
     * 增加失败数量
     */
    public void incrementFail() {
        fail++;
    }
}
