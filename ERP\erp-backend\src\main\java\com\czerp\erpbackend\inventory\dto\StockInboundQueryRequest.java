package com.czerp.erpbackend.inventory.dto;

import com.czerp.erpbackend.common.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 入库单查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StockInboundQueryRequest extends PageRequest {

    /**
     * 关键字（入库单号、供应商名称、仓库等）
     */
    private String keyword;

    /**
     * 入库日期开始
     */
    private LocalDate inboundDateStart;

    /**
     * 入库日期结束
     */
    private LocalDate inboundDateEnd;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 仓库
     */
    private String warehouse;

    /**
     * 供应商送货单号
     */
    private String supplierDeliveryNo;

    /**
     * 送货单日期开始
     */
    private LocalDate deliveryDateStart;

    /**
     * 送货单日期结束
     */
    private LocalDate deliveryDateEnd;

    /**
     * 采购单号
     */
    private String purchaseOrderNo;

    /**
     * 创建人
     */
    private String createdBy;
}
