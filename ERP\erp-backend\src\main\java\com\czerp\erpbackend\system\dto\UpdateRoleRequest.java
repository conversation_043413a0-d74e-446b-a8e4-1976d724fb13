package com.czerp.erpbackend.system.dto;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 更新角色请求
 */
@Data
public class UpdateRoleRequest {
    
    /**
     * 角色名称
     */
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String name;
    
    /**
     * 角色描述
     */
    @Size(max = 255, message = "角色描述长度不能超过255个字符")
    private String description;
    
    /**
     * 权限ID列表
     */
    private List<String> permissions;
    
    /**
     * 状态
     */
    @Pattern(regexp = "^(active|inactive)?$", message = "状态值不正确，可选值：active, inactive")
    private String status;

    public String getCode() {
        return "";
    }
}