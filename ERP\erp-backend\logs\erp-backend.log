2025-08-01 09:57:08 [restartedMain] INFO  c.c.erpbackend.ErpBackendApplication - Starting ErpBackendApplication using Java 21.0.5 with PID 28200 (D:\CZERP-JAVA\ERP\erp-backend\target\classes started by Administrator in D:\CZERP-JAVA\ERP\erp-backend)
2025-08-01 09:57:08 [restartedMain] INFO  c.c.erpbackend.ErpBackendApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-01 09:57:11 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-01 09:57:13 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-08-01 09:57:15 [restartedMain] INFO  c.c.e.c.f.config.FilterFieldRegistry - Registered 24 filter fields for module: sales-order
2025-08-01 09:57:15 [restartedMain] INFO  c.c.e.s.c.SalesOrderFilterConfig - Registered 24 filter fields for sales order module
2025-08-01 09:57:15 [restartedMain] INFO  c.c.e.s.s.impl.DictionaryServiceImpl - Dictionary data initialized with 6 entries
2025-08-01 09:57:15 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 09:57:15 [restartedMain] INFO  c.c.erpbackend.config.SecurityConfig - Configuring authorization rules...
2025-08-01 09:57:15 [restartedMain] INFO  c.c.erpbackend.config.SecurityConfig - Authorization rules configured.
2025-08-01 09:57:15 [restartedMain] INFO  c.c.erpbackend.config.SecurityConfig - Creating CorsConfigurationSource Bean...
2025-08-01 09:57:15 [restartedMain] INFO  c.c.erpbackend.config.SecurityConfig - CORS Configuration: Allowed Origins=[http://localhost:3000, http://localhost:8080], Allowed Methods=[GET, POST, PUT, DELETE, OPTIONS, PATCH], Allowed Headers=[Authorization, Content-Type, X-Requested-With, Accept], Allow Credentials=true
2025-08-01 09:57:15 [restartedMain] INFO  c.c.erpbackend.config.SecurityConfig - CorsConfigurationSource Bean created and configured for /**
2025-08-01 09:57:16 [restartedMain] INFO  c.c.erpbackend.ErpBackendApplication - Started ErpBackendApplication in 8.131 seconds (process running for 9.022)
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductPermissionInitializer - Initializing product permissions...
2025-08-01 09:57:16 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 09:57:16 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductPermissionInitializer - Product permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.c.c.CustomerPermissionInitializer - Initializing customer module permissions...
2025-08-01 09:57:16 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-08-01 09:57:16 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.c.c.CustomerPermissionInitializer - Assigning customer permissions to admin role...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.c.c.CustomerPermissionInitializer - All customer permissions already assigned to admin role, skipping
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.c.c.CustomerPermissionInitializer - Customer module permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductRolePermissionInitializer - Initializing product role permissions...
2025-08-01 09:57:16 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductRolePermissionInitializer - All product permissions already assigned to admin role, skipping
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductRolePermissionInitializer - Product role permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.c.c.CustomerBoxInfoPermissionInitializer - Initializing customer box info permissions...
2025-08-01 09:57:16 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-08-01 09:57:16 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-08-01 09:57:16 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.c.c.CustomerBoxInfoPermissionInitializer - Assigning customer box info permissions to admin role...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.c.c.CustomerBoxInfoPermissionInitializer - All customer box info permissions already assigned to admin role, skipping
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.c.c.CustomerBoxInfoPermissionInitializer - Customer box info permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - Initializing measurement unit permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - Initializing measurement unit module permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - Measurement unit module already exists, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - Assigning measurement unit permissions to admin role...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - All measurement unit permissions already assigned to admin role, skipping
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - Measurement unit permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.SupplierPermissionInitializer - Initializing supplier permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.SupplierPermissionInitializer - Supplier permissions already exist, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.SupplierPermissionInitializer - Supplier category permissions already exist, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.SupplierPermissionInitializer - Supplier permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.SupplierRolePermissionInitializer - Initializing supplier role permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.SupplierRolePermissionInitializer - All supplier permissions already assigned to admin role, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.SupplierRolePermissionInitializer - Supplier role permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - Initializing paper type permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - Creating paper type permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - Paper type module already exists, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - Assigning paper type permissions to admin role...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - All permissions already assigned to admin role, skipping
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - Paper type permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Initializing production module permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Initializing production module permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Production module already exists, using existing one.
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Production schedule module already exists, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Assigning production permissions to admin role...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - All production permissions already assigned to admin role
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Production module permissions initialized successfully.
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Initializing paper size setting permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Initializing paper size setting module permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Paper size setting module already exists, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Assigning paper size setting permissions to admin role...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Paper size setting permissions already assigned to admin role, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Paper size setting permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - Initializing process management permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - Initializing process management module permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - Process management module already exists, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - Assigning process management permissions to admin role...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - All process management permissions already assigned to admin role
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - Process management permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Initializing sales order permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Initializing sales order module permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Sales module already exists, checking sales order permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Sales order module already exists, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Assigning sales order permissions to admin role...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - All sales order permissions already assigned to admin role
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Sales order permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperMaterialPermissionInitializer - Initializing paper material permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperMaterialPermissionInitializer - Creating paper material permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperMaterialPermissionInitializer - Paper material module already exists, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperMaterialPermissionInitializer - Paper material permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - Initializing inventory permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - Initializing inventory module permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - Inventory module already exists, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - Assigning inventory permissions to admin role...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - All inventory permissions already assigned to admin role
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - Inventory permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperMaterialRolePermissionInitializer - Initializing paper material role permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperMaterialRolePermissionInitializer - Assigning paper material permissions to admin role...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperMaterialRolePermissionInitializer - Admin role already has all paper material permissions
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.m.i.PaperMaterialRolePermissionInitializer - Paper material role permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.w.c.WarehousePermissionInitializer - Initializing warehouse management permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.w.c.WarehousePermissionInitializer - Warehouse management permissions initialized successfully.
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.w.c.WarehouseRolePermissionInitializer - Initializing warehouse management role permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.w.c.WarehouseRolePermissionInitializer - Warehouse management role permissions initialized successfully.
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.PurchaseOrderPermissionInitializer - Initializing purchase order permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.p.i.PurchaseOrderPermissionInitializer - Purchase order permissions initialized successfully
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Initializing permissions, roles and admin user...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Initializing permissions...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Permissions already initialized, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Initializing roles...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Roles already initialized, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Initializing admin user...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Admin user already exists, checking role assignment...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Admin user already has admin role, skipping...
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Admin user initialized successfully.
2025-08-01 09:57:16 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Initialization completed.
2025-08-01 09:57:21 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-08-01 09:57:21 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-08-01 09:57:21 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-08-01 09:57:21 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-08-01 09:57:21 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-08-01 09:57:21 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
