2025-07-31 16:22:02 [restartedMain] INFO  c.c.erpbackend.ErpBackendApplication - Starting ErpBackendApplication using Java 21.0.5 with PID 8896 (D:\CZERP-JAVA\ERP\erp-backend\target\classes started by Administrator in D:\CZERP-JAVA\ERP\erp-backend)
2025-07-31 16:22:02 [restartedMain] INFO  c.c.erpbackend.ErpBackendApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-31 16:22:06 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-31 16:22:08 [restartedMain] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-07-31 16:22:10 [restartedMain] INFO  c.c.e.c.f.config.FilterFieldRegistry - Registered 24 filter fields for module: sales-order
2025-07-31 16:22:10 [restartedMain] INFO  c.c.e.s.c.SalesOrderFilterConfig - Registered 24 filter fields for sales order module
2025-07-31 16:22:10 [restartedMain] INFO  c.c.e.s.s.impl.DictionaryServiceImpl - Dictionary data initialized with 6 entries
2025-07-31 16:22:10 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-31 16:22:10 [restartedMain] INFO  c.c.erpbackend.config.SecurityConfig - Configuring authorization rules...
2025-07-31 16:22:10 [restartedMain] INFO  c.c.erpbackend.config.SecurityConfig - Authorization rules configured.
2025-07-31 16:22:10 [restartedMain] INFO  c.c.erpbackend.config.SecurityConfig - Creating CorsConfigurationSource Bean...
2025-07-31 16:22:10 [restartedMain] INFO  c.c.erpbackend.config.SecurityConfig - CORS Configuration: Allowed Origins=[http://localhost:3000, http://localhost:8080], Allowed Methods=[GET, POST, PUT, DELETE, OPTIONS, PATCH], Allowed Headers=[Authorization, Content-Type, X-Requested-With, Accept], Allow Credentials=true
2025-07-31 16:22:10 [restartedMain] INFO  c.c.erpbackend.config.SecurityConfig - CorsConfigurationSource Bean created and configured for /**
2025-07-31 16:22:11 [restartedMain] INFO  c.c.erpbackend.ErpBackendApplication - Started ErpBackendApplication in 8.826 seconds (process running for 9.565)
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductPermissionInitializer - Initializing product permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductPermissionInitializer - Product permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.c.c.CustomerPermissionInitializer - Initializing customer module permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.c.c.CustomerPermissionInitializer - Assigning customer permissions to admin role...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.c.c.CustomerPermissionInitializer - All customer permissions already assigned to admin role, skipping
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.c.c.CustomerPermissionInitializer - Customer module permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductRolePermissionInitializer - Initializing product role permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductRolePermissionInitializer - All product permissions already assigned to admin role, skipping
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductRolePermissionInitializer - Product role permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.c.c.CustomerBoxInfoPermissionInitializer - Initializing customer box info permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.c.c.CustomerBoxInfoPermissionInitializer - Assigning customer box info permissions to admin role...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.c.c.CustomerBoxInfoPermissionInitializer - All customer box info permissions already assigned to admin role, skipping
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.c.c.CustomerBoxInfoPermissionInitializer - Customer box info permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - Initializing measurement unit permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - Initializing measurement unit module permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - Measurement unit module already exists, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - Assigning measurement unit permissions to admin role...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - All measurement unit permissions already assigned to admin role, skipping
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.MeasurementUnitPermissionInitializer - Measurement unit permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.SupplierPermissionInitializer - Initializing supplier permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.SupplierPermissionInitializer - Supplier permissions already exist, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.SupplierPermissionInitializer - Supplier category permissions already exist, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.SupplierPermissionInitializer - Supplier permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.SupplierRolePermissionInitializer - Initializing supplier role permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.SupplierRolePermissionInitializer - All supplier permissions already assigned to admin role, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.SupplierRolePermissionInitializer - Supplier role permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Initializing production module permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Initializing production module permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Production module already exists, using existing one.
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Production schedule module already exists, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Assigning production permissions to admin role...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - All production permissions already assigned to admin role
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProductionPermissionInitializer - Production module permissions initialized successfully.
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - Initializing process management permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - Initializing process management module permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - Process management module already exists, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - Assigning process management permissions to admin role...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - All process management permissions already assigned to admin role
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.ProcessPermissionInitializer - Process management permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Initializing paper size setting permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Initializing paper size setting module permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Paper size setting module already exists, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Assigning paper size setting permissions to admin role...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Paper size setting permissions already assigned to admin role, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.PaperSizeSettingPermissionInitializer - Paper size setting permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - Initializing paper type permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - Creating paper type permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - Paper type module already exists, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - Assigning paper type permissions to admin role...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - All permissions already assigned to admin role, skipping
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperTypePermissionInitializer - Paper type permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Initializing sales order permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Initializing sales order module permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Sales module already exists, checking sales order permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Sales order module already exists, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Assigning sales order permissions to admin role...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - All sales order permissions already assigned to admin role
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.i.SalesOrderPermissionInitializer - Sales order permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperMaterialPermissionInitializer - Initializing paper material permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperMaterialPermissionInitializer - Creating paper material permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperMaterialPermissionInitializer - Paper material module already exists, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperMaterialPermissionInitializer - Paper material permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - Initializing inventory permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - Initializing inventory module permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - Inventory module already exists, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - Assigning inventory permissions to admin role...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - All inventory permissions already assigned to admin role
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.i.i.InventoryPermissionInitializer - Inventory permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperMaterialRolePermissionInitializer - Initializing paper material role permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperMaterialRolePermissionInitializer - Assigning paper material permissions to admin role...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperMaterialRolePermissionInitializer - Admin role already has all paper material permissions
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.m.i.PaperMaterialRolePermissionInitializer - Paper material role permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.w.c.WarehousePermissionInitializer - Initializing warehouse management permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.w.c.WarehousePermissionInitializer - Warehouse management permissions initialized successfully.
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.w.c.WarehouseRolePermissionInitializer - Initializing warehouse management role permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.w.c.WarehouseRolePermissionInitializer - Warehouse management role permissions initialized successfully.
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.PurchaseOrderPermissionInitializer - Initializing purchase order permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.p.i.PurchaseOrderPermissionInitializer - Purchase order permissions initialized successfully
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Initializing permissions, roles and admin user...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Initializing permissions...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Permissions already initialized, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Initializing roles...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Roles already initialized, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Initializing admin user...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Admin user already exists, checking role assignment...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Admin user already has admin role, skipping...
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Admin user initialized successfully.
2025-07-31 16:22:11 [restartedMain] INFO  c.c.e.s.init.PermissionInitializer - Initialization completed.
2025-07-31 16:22:41 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 16:22:41 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-31 16:22:41 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 16:22:41 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:22:42 [http-nio-8080-exec-2] ERROR c.c.e.auth.filter.JwtTokenFilter - Could not set user authentication in security context
io.jsonwebtoken.ExpiredJwtException: JWT expired at 2025-07-01T10:25:32Z. Current time: 2025-07-31T08:22:42Z, a difference of 2584630104 milliseconds.  Allowed clock skew: 0 milliseconds.
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:427)
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:529)
	at io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:589)
	at io.jsonwebtoken.impl.ImmutableJwtParser.parseClaimsJws(ImmutableJwtParser.java:173)
	at com.czerp.erpbackend.auth.util.JwtUtil.extractAllClaims(JwtUtil.java:70)
	at com.czerp.erpbackend.auth.util.JwtUtil.extractClaim(JwtUtil.java:57)
	at com.czerp.erpbackend.auth.util.JwtUtil.extractUserId(JwtUtil.java:37)
	at com.czerp.erpbackend.auth.filter.JwtTokenFilter.doFilterInternal(JwtTokenFilter.java:59)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.czerp.erpbackend.config.LoggingCorsFilter.doFilterInternal(LoggingCorsFilter.java:36)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.czerp.erpbackend.config.CustomCorsFilter.doFilterInternal(CustomCorsFilter.java:48)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-31 16:22:52 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: POST /api/auth/login
2025-07-31 16:22:52 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - No JWT token found in request
2025-07-31 16:22:52 [http-nio-8080-exec-5] WARN  c.c.e.a.service.impl.AuthServiceImpl - Login failed for user: 123 - Invalid credentials
2025-07-31 16:22:52 [http-nio-8080-exec-5] ERROR c.c.e.c.e.GlobalExceptionHandler - 认证异常: 用户名或密码错误
org.springframework.security.authentication.BadCredentialsException: 用户名或密码错误
	at com.czerp.erpbackend.auth.service.impl.AuthServiceImpl.login(AuthServiceImpl.java:73)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.czerp.erpbackend.auth.service.impl.AuthServiceImpl$$SpringCGLIB$$0.login(<generated>)
	at com.czerp.erpbackend.auth.controller.AuthController.login(AuthController.java:32)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.czerp.erpbackend.auth.filter.JwtTokenFilter.doFilterInternal(JwtTokenFilter.java:82)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.czerp.erpbackend.config.LoggingCorsFilter.doFilterInternal(LoggingCorsFilter.java:36)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.czerp.erpbackend.config.CustomCorsFilter.doFilterInternal(CustomCorsFilter.java:48)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-31 16:22:52 [http-nio-8080-exec-5] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.security.authentication.BadCredentialsException: 用户名或密码错误]
2025-07-31 16:23:24 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: POST /api/auth/login
2025-07-31 16:23:24 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - No JWT token found in request
2025-07-31 16:23:24 [http-nio-8080-exec-7] WARN  c.c.e.a.service.impl.AuthServiceImpl - Login failed for user: admin - Invalid credentials
2025-07-31 16:23:24 [http-nio-8080-exec-7] ERROR c.c.e.c.e.GlobalExceptionHandler - 认证异常: 用户名或密码错误
org.springframework.security.authentication.BadCredentialsException: 用户名或密码错误
	at com.czerp.erpbackend.auth.service.impl.AuthServiceImpl.login(AuthServiceImpl.java:73)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.czerp.erpbackend.auth.service.impl.AuthServiceImpl$$SpringCGLIB$$0.login(<generated>)
	at com.czerp.erpbackend.auth.controller.AuthController.login(AuthController.java:32)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.czerp.erpbackend.auth.filter.JwtTokenFilter.doFilterInternal(JwtTokenFilter.java:82)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.czerp.erpbackend.config.LoggingCorsFilter.doFilterInternal(LoggingCorsFilter.java:36)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.czerp.erpbackend.config.CustomCorsFilter.doFilterInternal(CustomCorsFilter.java:48)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-31 16:23:24 [http-nio-8080-exec-7] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.security.authentication.BadCredentialsException: 用户名或密码错误]
2025-07-31 16:23:28 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: POST /api/auth/login
2025-07-31 16:23:28 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - No JWT token found in request
2025-07-31 16:23:28 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 16:23:28 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:23:28 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:23:28 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:23:28 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:23:28 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:23:40 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 16:23:40 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:23:40 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:23:40 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:23:40 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:23:40 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:23:41 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/stock-inbounds/items
2025-07-31 16:23:41 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:23:41 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:23:41 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:23:41 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:23:41 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:23:41 [http-nio-8080-exec-6] INFO  c.c.e.i.c.StockInboundController - Finding stock inbound items with request: StockInboundQueryRequest(keyword=null, inboundDateStart=null, inboundDateEnd=null, supplierCode=null, supplierName=null, warehouse=null, supplierDeliveryNo=null, deliveryDateStart=null, deliveryDateEnd=null, purchaseOrderNo=null, createdBy=null)
2025-07-31 16:23:41 [http-nio-8080-exec-6] INFO  c.c.e.i.s.i.StockInboundServiceImpl - Finding stock inbound items with request: StockInboundQueryRequest(keyword=null, inboundDateStart=null, inboundDateEnd=null, supplierCode=null, supplierName=null, warehouse=null, supplierDeliveryNo=null, deliveryDateStart=null, deliveryDateEnd=null, purchaseOrderNo=null, createdBy=null)
2025-07-31 16:23:41 [http-nio-8080-exec-6] INFO  c.c.e.i.s.i.StockInboundServiceImpl - Finding stock inbound items with optimized query, request: StockInboundQueryRequest(keyword=null, inboundDateStart=null, inboundDateEnd=null, supplierCode=null, supplierName=null, warehouse=null, supplierDeliveryNo=null, deliveryDateStart=null, deliveryDateEnd=null, purchaseOrderNo=null, createdBy=null)
2025-07-31 16:23:41 [http-nio-8080-exec-6] INFO  c.c.e.i.s.i.StockInboundServiceImpl - Found 5 stock inbound items with optimized query
2025-07-31 16:24:57 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 16:24:57 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:24:57 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:24:57 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:24:57 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:24:57 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:25:01 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 16:25:01 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:25:01 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:25:01 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:25:01 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:25:01 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:25:11 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 16:25:11 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:25:11 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:25:11 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:25:11 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:25:11 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:25:12 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/production-schedules/items
2025-07-31 16:25:12 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:25:12 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:25:12 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:25:12 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:25:12 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:25:12 [http-nio-8080-exec-3] INFO  c.c.e.p.c.ProductionScheduleController - Finding production schedule items with request: ProductionScheduleQueryRequest(keyword=, scheduleDateStart=null, scheduleDateEnd=null, plannedCompletionDateStart=null, plannedCompletionDateEnd=null, isUrgent=null, isPrinted=null, customerName=, productionOrderNo=, salesOrderNo=, productName=)
2025-07-31 16:27:54 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/production-schedules/items
2025-07-31 16:27:54 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:27:54 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:27:54 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:27:54 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:27:54 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:27:54 [http-nio-8080-exec-6] INFO  c.c.e.p.c.ProductionScheduleController - Finding production schedule items with request: ProductionScheduleQueryRequest(keyword=, scheduleDateStart=null, scheduleDateEnd=null, plannedCompletionDateStart=null, plannedCompletionDateEnd=null, isUrgent=null, isPrinted=null, customerName=, productionOrderNo=, salesOrderNo=, productName=)
2025-07-31 16:56:54 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 16:56:54 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:56:54 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:56:54 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:56:54 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:56:54 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:58:57 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 16:58:57 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:58:57 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:58:57 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:58:57 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:58:57 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:58:57 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/production-schedules/items
2025-07-31 16:58:57 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 16:58:57 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 16:58:57 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 16:58:57 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 16:58:57 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 16:58:57 [http-nio-8080-exec-1] INFO  c.c.e.p.c.ProductionScheduleController - Finding production schedule items with request: ProductionScheduleQueryRequest(keyword=, scheduleDateStart=null, scheduleDateEnd=null, plannedCompletionDateStart=null, plannedCompletionDateEnd=null, isUrgent=null, isPrinted=null, customerName=, productionOrderNo=, salesOrderNo=, productName=)
2025-07-31 17:03:01 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 17:03:01 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:03:01 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:03:01 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:03:01 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:03:01 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:19:21 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 17:19:21 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:19:21 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:19:21 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:19:21 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:19:21 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:19:21 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/stock-inbounds/items
2025-07-31 17:19:21 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:19:21 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:19:21 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:19:21 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:19:21 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:19:21 [http-nio-8080-exec-6] INFO  c.c.e.i.c.StockInboundController - Finding stock inbound items with request: StockInboundQueryRequest(keyword=null, inboundDateStart=null, inboundDateEnd=null, supplierCode=null, supplierName=null, warehouse=null, supplierDeliveryNo=null, deliveryDateStart=null, deliveryDateEnd=null, purchaseOrderNo=null, createdBy=null)
2025-07-31 17:19:21 [http-nio-8080-exec-6] INFO  c.c.e.i.s.i.StockInboundServiceImpl - Finding stock inbound items with request: StockInboundQueryRequest(keyword=null, inboundDateStart=null, inboundDateEnd=null, supplierCode=null, supplierName=null, warehouse=null, supplierDeliveryNo=null, deliveryDateStart=null, deliveryDateEnd=null, purchaseOrderNo=null, createdBy=null)
2025-07-31 17:19:21 [http-nio-8080-exec-6] INFO  c.c.e.i.s.i.StockInboundServiceImpl - Finding stock inbound items with optimized query, request: StockInboundQueryRequest(keyword=null, inboundDateStart=null, inboundDateEnd=null, supplierCode=null, supplierName=null, warehouse=null, supplierDeliveryNo=null, deliveryDateStart=null, deliveryDateEnd=null, purchaseOrderNo=null, createdBy=null)
2025-07-31 17:19:21 [http-nio-8080-exec-6] INFO  c.c.e.i.s.i.StockInboundServiceImpl - Found 5 stock inbound items with optimized query
2025-07-31 17:19:23 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 17:19:23 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:19:23 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:19:23 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:19:23 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:19:23 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:19:26 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 17:19:26 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:19:26 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:19:26 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:19:26 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:19:26 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:24:12 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 17:24:12 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:24:12 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:24:12 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:24:12 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:24:12 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:33 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/users/current
2025-07-31 17:56:33 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:33 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:33 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:33 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:33 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:34 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:34 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:34 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:34 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:34 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:34 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:34 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:34 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:34 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:34 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:34 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:34 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:34 [http-nio-8080-exec-8] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: productName, searchText: null, with filters: provided
2025-07-31 17:56:34 [http-nio-8080-exec-6] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: customerName, searchText: null, with filters: provided
2025-07-31 17:56:34 [http-nio-8080-exec-8] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: productName, searchText: null (using stable legacy implementation)
2025-07-31 17:56:34 [http-nio-8080-exec-6] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: customerName, searchText: null (using stable legacy implementation)
2025-07-31 17:56:34 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:34 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:34 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:34 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:34 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:34 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:34 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:34 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:34 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:34 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:34 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:34 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:34 [http-nio-8080-exec-10] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: salesPerson, searchText: null, with filters: provided
2025-07-31 17:56:34 [http-nio-8080-exec-1] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: supplierName, searchText: null, with filters: provided
2025-07-31 17:56:34 [http-nio-8080-exec-10] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: salesPerson, searchText: null (using stable legacy implementation)
2025-07-31 17:56:34 [http-nio-8080-exec-1] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: supplierName, searchText: null (using stable legacy implementation)
2025-07-31 17:56:34 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query
2025-07-31 17:56:34 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:34 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:34 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:34 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:34 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:34 [http-nio-8080-exec-3] INFO  c.c.e.s.c.SalesOrderQueryController - Querying sales orders with params: SalesOrderQueryParamDTO(keyword=, orderNo=, productionOrderNo=, customerName=, customerOrderNo=, customerProductCode=, productName=, startDate=null, endDate=null, salesPerson=, page=1, pageSize=20, sortField=null, sortOrder=null, filterProductionOrderNo=null, filterOrderNo=null, filterCustomerOrderNo=null, filterCustomerProductCode=null, filterCustomerNames=null, filterProductNames=null, filterSalesPersons=null, filterProductionOrderNos=null, filterOrderNos=null, filterCustomerOrderNos=null, filterCustomerProductCodes=null, filterPurchaseOrderNos=null, filterBindingSpecifications=null, filterSpecifications=null, filterProductionSpecifications=null, filterPressSizeWidths=null, filterPaperTypes=null, filterProcessRequirements=null, filterProcesses=null, filterReceivingUnits=null, filterReceivingPersons=null, filterSupplierNames=null, filterPaperTypeNames=null, filterBoxTypes=null, filterCorrugationTypes=null, filterProductionPaperTypes=null, filterConnectionMethods=null, filterUnits=null, filterCurrencies=null, filterTaxRates=null, filterReceiverPhones=null, filterCreatedBys=null, filterProductionRemarks=null, filterRemarks=null, filterReceivingAddresses=null, filterIsSpecialPrices=null, filterQuantityMin=null, filterQuantityMax=null, filterTotalAmountMin=null, filterTotalAmountMax=null, filterSpareQuantityMin=null, filterSpareQuantityMax=null, filterPriceMin=null, filterPriceMax=null, filterPurchasedQuantityMin=null, filterPurchasedQuantityMax=null, filterPaperQuotationMin=null, filterPaperQuotationMax=null, filterUnitWeightMin=null, filterUnitWeightMax=null, filterTotalWeightMin=null, filterTotalWeightMax=null, filterProductAreaMin=null, filterProductAreaMax=null, filterTotalAreaMin=null, filterTotalAreaMax=null, filterProductVolumeMin=null, filterProductVolumeMax=null, filterTotalVolumeMin=null, filterTotalVolumeMax=null, filterOrderDateStart=null, filterOrderDateEnd=null, filterDeliveryDateStart=null, filterDeliveryDateEnd=null, filterCreatedTimeStart=null, filterCreatedTimeEnd=null, includeFilterOptions=null, filterOptionFields=null)
2025-07-31 17:56:34 [http-nio-8080-exec-3] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Querying sales orders with params: SalesOrderQueryParamDTO(keyword=, orderNo=, productionOrderNo=, customerName=, customerOrderNo=, customerProductCode=, productName=, startDate=null, endDate=null, salesPerson=, page=1, pageSize=20, sortField=null, sortOrder=null, filterProductionOrderNo=null, filterOrderNo=null, filterCustomerOrderNo=null, filterCustomerProductCode=null, filterCustomerNames=null, filterProductNames=null, filterSalesPersons=null, filterProductionOrderNos=null, filterOrderNos=null, filterCustomerOrderNos=null, filterCustomerProductCodes=null, filterPurchaseOrderNos=null, filterBindingSpecifications=null, filterSpecifications=null, filterProductionSpecifications=null, filterPressSizeWidths=null, filterPaperTypes=null, filterProcessRequirements=null, filterProcesses=null, filterReceivingUnits=null, filterReceivingPersons=null, filterSupplierNames=null, filterPaperTypeNames=null, filterBoxTypes=null, filterCorrugationTypes=null, filterProductionPaperTypes=null, filterConnectionMethods=null, filterUnits=null, filterCurrencies=null, filterTaxRates=null, filterReceiverPhones=null, filterCreatedBys=null, filterProductionRemarks=null, filterRemarks=null, filterReceivingAddresses=null, filterIsSpecialPrices=null, filterQuantityMin=null, filterQuantityMax=null, filterTotalAmountMin=null, filterTotalAmountMax=null, filterSpareQuantityMin=null, filterSpareQuantityMax=null, filterPriceMin=null, filterPriceMax=null, filterPurchasedQuantityMin=null, filterPurchasedQuantityMax=null, filterPaperQuotationMin=null, filterPaperQuotationMax=null, filterUnitWeightMin=null, filterUnitWeightMax=null, filterTotalWeightMin=null, filterTotalWeightMax=null, filterProductAreaMin=null, filterProductAreaMax=null, filterTotalAreaMin=null, filterTotalAreaMax=null, filterProductVolumeMin=null, filterProductVolumeMax=null, filterTotalVolumeMin=null, filterTotalVolumeMax=null, filterOrderDateStart=null, filterOrderDateEnd=null, filterDeliveryDateStart=null, filterDeliveryDateEnd=null, filterCreatedTimeStart=null, filterCreatedTimeEnd=null, includeFilterOptions=null, filterOptionFields=null)
2025-07-31 17:56:37 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:37 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:37 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:37 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:37 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:37 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:37 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:37 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:37 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:37 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:37 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:37 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:37 [http-nio-8080-exec-9] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: productionOrderNo, searchText: null, with filters: provided
2025-07-31 17:56:37 [http-nio-8080-exec-8] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: orderNo, searchText: null, with filters: provided
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: customerOrderNo, searchText: null, with filters: provided
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: customerOrderNo, searchText: null (using stable legacy implementation)
2025-07-31 17:56:37 [http-nio-8080-exec-9] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: productionOrderNo, searchText: null (using stable legacy implementation)
2025-07-31 17:56:37 [http-nio-8080-exec-8] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: orderNo, searchText: null (using stable legacy implementation)
2025-07-31 17:56:37 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:37 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:37 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:37 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:37 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:37 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:37 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:37 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:37 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:37 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:37 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:37 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:37 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:37 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:37 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:37 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:37 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:37 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:37 [http-nio-8080-exec-4] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: bindingSpecification, searchText: null, with filters: provided
2025-07-31 17:56:37 [http-nio-8080-exec-3] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: customerProductCode, searchText: null, with filters: provided
2025-07-31 17:56:37 [http-nio-8080-exec-5] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: purchaseOrderNo, searchText: null, with filters: provided
2025-07-31 17:56:37 [http-nio-8080-exec-3] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: customerProductCode, searchText: null (using stable legacy implementation)
2025-07-31 17:56:37 [http-nio-8080-exec-5] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: purchaseOrderNo, searchText: null (using stable legacy implementation)
2025-07-31 17:56:37 [http-nio-8080-exec-4] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: bindingSpecification, searchText: null (using stable legacy implementation)
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:37 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:37 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:37 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:37 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:37 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:37 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:37 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:37 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:37 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:37 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:37 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:37 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:37 [http-nio-8080-exec-10] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: productionSpecification, searchText: null, with filters: provided
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: pressSizeWidth, searchText: null, with filters: provided
2025-07-31 17:56:37 [http-nio-8080-exec-2] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: specification, searchText: null, with filters: provided
2025-07-31 17:56:37 [http-nio-8080-exec-7] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: pressSizeWidth, searchText: null (using stable legacy implementation)
2025-07-31 17:56:37 [http-nio-8080-exec-10] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: productionSpecification, searchText: null (using stable legacy implementation)
2025-07-31 17:56:37 [http-nio-8080-exec-2] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: specification, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-8] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: processes, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-8] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: paperType, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: processRequirements, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-8] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: paperType, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: processRequirements, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: processes, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-1] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-4] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-1] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: paperTypeName, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: productionPaperType, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-4] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: receivingUnit, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: productionPaperType, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-4] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: receivingUnit, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-4] WARN  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Unsupported filter field: receivingUnit
2025-07-31 17:56:38 [http-nio-8080-exec-1] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: paperTypeName, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-7] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-6] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-7] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: createdBy, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-6] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: receiverPhone, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: receiver, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-6] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: receiverPhone, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: receiver, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-6] WARN  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Unsupported filter field: receiverPhone
2025-07-31 17:56:38 [http-nio-8080-exec-5] WARN  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Unsupported filter field: receiver
2025-07-31 17:56:38 [http-nio-8080-exec-7] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: createdBy, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-7] WARN  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Unsupported filter field: createdBy
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-3] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: connectionMethod, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: boxType, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-3] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: corrugationType, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-10] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: connectionMethod, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-9] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: boxType, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-3] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: corrugationType, searchText: null (using stable legacy implementation)
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query/filter-options
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.s.c.SalesOrderQueryController - Getting filter options for field: currency, searchText: null, with filters: provided
2025-07-31 17:56:38 [http-nio-8080-exec-5] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Getting filter options for field: currency, searchText: null (using stable legacy implementation)
2025-07-31 18:09:29 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Processing request: GET /api/sales/orders/query
2025-07-31 18:09:29 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - JWT token found in request
2025-07-31 18:09:29 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Extracted username from token: admin
2025-07-31 18:09:29 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Loaded user details for: admin, authorities: [ROLE_admin, customer, customer:box-info, customer:box-info:create, customer:box-info:delete, customer:box-info:read, customer:box-info:update, customer:category:list, customer:create, customer:delete, customer:list, customer:management, customer:read, customer:update, inventory, inventory:inbound, inventory:inbound:create, inventory:inbound:delete, inventory:inbound:list, inventory:inbound:read, inventory:inbound:update, material, material:paper-material, material:paper-material:create, material:paper-material:delete, material:paper-material:list, material:paper-material:read, material:paper-material:update, material:paper-type, material:paper-type:create, material:paper-type:delete, material:paper-type:list, material:paper-type:read, material:paper-type:update, permission, permission:list, permission:read, process, process:create, process:delete, process:list, process:update, process:view, product, product-category, product-category:create, product-category:delete, product-category:list, product-category:read, product-category:update, product-spec, product-spec:create, product-spec:delete, product-spec:list, product-spec:read, product-spec:update, product:create, product:delete, product:import, product:list, product:read, product:update, production, production:schedule, production:schedule:create, production:schedule:delete, production:schedule:list, production:schedule:read, production:schedule:update, purchase:order, purchase:order:create, purchase:order:delete, purchase:order:read, purchase:order:update, role, role:create, role:delete, role:list, role:read, role:update, sales, sales:delivery, sales:delivery:create, sales:delivery:delete, sales:delivery:list, sales:delivery:read, sales:delivery:update, sales:inventory, sales:inventory:create, sales:inventory:delete, sales:inventory:list, sales:inventory:read, sales:inventory:update, sales:order, sales:order:approve, sales:order:create, sales:order:delete, sales:order:list, sales:order:print, sales:order:read, sales:order:update, sales:production, sales:production:create, sales:production:delete, sales:production:list, sales:production:read, sales:production:update, sales:statistics, sales:statistics:read, supplier, supplier-category, supplier-category:create, supplier-category:delete, supplier-category:list, supplier-category:read, supplier-category:update, supplier:create, supplier:delete, supplier:list, supplier:read, supplier:update, system:measurement-unit, system:measurement-unit:create, system:measurement-unit:delete, system:measurement-unit:list, system:measurement-unit:read, system:measurement-unit:update, system:paper-size-setting, system:paper-size-setting:create, system:paper-size-setting:delete, system:paper-size-setting:list, system:paper-size-setting:read, system:paper-size-setting:update, user, user:create, user:delete, user:list, user:read, user:update, warehouse, warehouse:create, warehouse:delete, warehouse:read, warehouse:update]
2025-07-31 18:09:29 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Token validated successfully for user: admin
2025-07-31 18:09:29 [http-nio-8080-exec-2] INFO  c.c.e.auth.filter.JwtTokenFilter - Authentication set in SecurityContext
2025-07-31 18:09:29 [http-nio-8080-exec-2] INFO  c.c.e.s.c.SalesOrderQueryController - Querying sales orders with params: SalesOrderQueryParamDTO(keyword=, orderNo=, productionOrderNo=, customerName=, customerOrderNo=, customerProductCode=, productName=, startDate=null, endDate=null, salesPerson=, page=1, pageSize=20, sortField=null, sortOrder=null, filterProductionOrderNo=null, filterOrderNo=null, filterCustomerOrderNo=null, filterCustomerProductCode=null, filterCustomerNames=null, filterProductNames=null, filterSalesPersons=null, filterProductionOrderNos=null, filterOrderNos=null, filterCustomerOrderNos=null, filterCustomerProductCodes=null, filterPurchaseOrderNos=null, filterBindingSpecifications=null, filterSpecifications=null, filterProductionSpecifications=null, filterPressSizeWidths=null, filterPaperTypes=null, filterProcessRequirements=null, filterProcesses=null, filterReceivingUnits=null, filterReceivingPersons=null, filterSupplierNames=null, filterPaperTypeNames=null, filterBoxTypes=null, filterCorrugationTypes=null, filterProductionPaperTypes=null, filterConnectionMethods=null, filterUnits=null, filterCurrencies=null, filterTaxRates=null, filterReceiverPhones=null, filterCreatedBys=null, filterProductionRemarks=null, filterRemarks=null, filterReceivingAddresses=null, filterIsSpecialPrices=null, filterQuantityMin=null, filterQuantityMax=null, filterTotalAmountMin=null, filterTotalAmountMax=null, filterSpareQuantityMin=null, filterSpareQuantityMax=null, filterPriceMin=null, filterPriceMax=null, filterPurchasedQuantityMin=null, filterPurchasedQuantityMax=null, filterPaperQuotationMin=null, filterPaperQuotationMax=null, filterUnitWeightMin=null, filterUnitWeightMax=null, filterTotalWeightMin=null, filterTotalWeightMax=null, filterProductAreaMin=null, filterProductAreaMax=null, filterTotalAreaMin=null, filterTotalAreaMax=null, filterProductVolumeMin=null, filterProductVolumeMax=null, filterTotalVolumeMin=null, filterTotalVolumeMax=null, filterOrderDateStart=null, filterOrderDateEnd=null, filterDeliveryDateStart=null, filterDeliveryDateEnd=null, filterCreatedTimeStart=null, filterCreatedTimeEnd=null, includeFilterOptions=null, filterOptionFields=null)
2025-07-31 18:09:29 [http-nio-8080-exec-2] INFO  c.c.e.s.s.i.SalesOrderQueryServiceImpl - Querying sales orders with params: SalesOrderQueryParamDTO(keyword=, orderNo=, productionOrderNo=, customerName=, customerOrderNo=, customerProductCode=, productName=, startDate=null, endDate=null, salesPerson=, page=1, pageSize=20, sortField=null, sortOrder=null, filterProductionOrderNo=null, filterOrderNo=null, filterCustomerOrderNo=null, filterCustomerProductCode=null, filterCustomerNames=null, filterProductNames=null, filterSalesPersons=null, filterProductionOrderNos=null, filterOrderNos=null, filterCustomerOrderNos=null, filterCustomerProductCodes=null, filterPurchaseOrderNos=null, filterBindingSpecifications=null, filterSpecifications=null, filterProductionSpecifications=null, filterPressSizeWidths=null, filterPaperTypes=null, filterProcessRequirements=null, filterProcesses=null, filterReceivingUnits=null, filterReceivingPersons=null, filterSupplierNames=null, filterPaperTypeNames=null, filterBoxTypes=null, filterCorrugationTypes=null, filterProductionPaperTypes=null, filterConnectionMethods=null, filterUnits=null, filterCurrencies=null, filterTaxRates=null, filterReceiverPhones=null, filterCreatedBys=null, filterProductionRemarks=null, filterRemarks=null, filterReceivingAddresses=null, filterIsSpecialPrices=null, filterQuantityMin=null, filterQuantityMax=null, filterTotalAmountMin=null, filterTotalAmountMax=null, filterSpareQuantityMin=null, filterSpareQuantityMax=null, filterPriceMin=null, filterPriceMax=null, filterPurchasedQuantityMin=null, filterPurchasedQuantityMax=null, filterPaperQuotationMin=null, filterPaperQuotationMax=null, filterUnitWeightMin=null, filterUnitWeightMax=null, filterTotalWeightMin=null, filterTotalWeightMax=null, filterProductAreaMin=null, filterProductAreaMax=null, filterTotalAreaMin=null, filterTotalAreaMax=null, filterProductVolumeMin=null, filterProductVolumeMax=null, filterTotalVolumeMin=null, filterTotalVolumeMax=null, filterOrderDateStart=null, filterOrderDateEnd=null, filterDeliveryDateStart=null, filterDeliveryDateEnd=null, filterCreatedTimeStart=null, filterCreatedTimeEnd=null, includeFilterOptions=null, filterOptionFields=null)
