package com.czerp.erpbackend.purchase.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.material.entity.PaperMaterial;
import com.czerp.erpbackend.material.repository.PaperMaterialRepository;
import com.czerp.erpbackend.purchase.dto.SalesOrderItemForPurchaseDTO;
import com.czerp.erpbackend.purchase.dto.SalesOrderItemForPurchaseQueryRequest;
import com.czerp.erpbackend.purchase.service.SalesOrderForPurchaseService;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderMaterialRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderRepository;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用于采购订单引用销售订单的服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SalesOrderForPurchaseServiceImpl implements SalesOrderForPurchaseService {

    private final SalesOrderRepository salesOrderRepository;
    private final SalesOrderItemRepository salesOrderItemRepository;
    private final SalesOrderMaterialRepository salesOrderMaterialRepository;
    private final PaperMaterialRepository paperMaterialRepository;

    @Override
    @Transactional(readOnly = true)
    public PageResponse<SalesOrderItemForPurchaseDTO> findSalesOrderItemsForPurchase(SalesOrderItemForPurchaseQueryRequest request) {
        log.info("Finding sales order items for purchase with request: {}", request);

        // 构建分页参数
        int page = request.getPage() != null ? request.getPage() : 0;
        int size = request.getSize() != null ? request.getSize() : 10;
        Sort sort = Sort.by(Sort.Direction.DESC, "order.orderDate", "productionOrderNo");
        Pageable pageable = PageRequest.of(page, size, sort);

        // 构建查询条件
        Specification<SalesOrderItem> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 关联销售订单表
            Join<SalesOrderItem, SalesOrder> orderJoin = root.join("order", JoinType.INNER);

            // 关键字查询（生产单号、客户名称、品名等）
            if (StringUtils.hasText(request.getKeyword())) {
                String keyword = "%" + request.getKeyword() + "%";
                predicates.add(cb.or(
                    cb.like(root.get("productionOrderNo"), keyword),
                    cb.like(orderJoin.get("customerName"), keyword),
                    cb.like(root.get("productName"), keyword),
                    cb.like(root.get("customerOrderNo"), keyword),
                    cb.like(root.get("customerProductCode"), keyword)
                ));
            }

            // 客户编码
            if (StringUtils.hasText(request.getCustomerCode())) {
                predicates.add(cb.equal(orderJoin.get("customerCode"), request.getCustomerCode()));
            }

            // 订单日期范围
            if (request.getOrderDateStart() != null) {
                predicates.add(cb.greaterThanOrEqualTo(orderJoin.get("orderDate"), request.getOrderDateStart()));
            }
            if (request.getOrderDateEnd() != null) {
                predicates.add(cb.lessThanOrEqualTo(orderJoin.get("orderDate"), request.getOrderDateEnd()));
            }

            // 交期范围
            if (request.getDeliveryDateStart() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("deliveryDate"), request.getDeliveryDateStart()));
            }
            if (request.getDeliveryDateEnd() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("deliveryDate"), request.getDeliveryDateEnd()));
            }

            // 纸质
            if (StringUtils.hasText(request.getPaperType())) {
                predicates.add(cb.equal(root.get("paperType"), request.getPaperType()));
            }

            // 楞别
            if (StringUtils.hasText(request.getCorrugationType())) {
                predicates.add(cb.equal(root.get("corrugationType"), request.getCorrugationType()));
            }

            // 只返回未采购或部分采购的销售订单明细
            predicates.add(cb.or(
                cb.equal(root.get("purchaseStatus"), "NOT_PURCHASED"),
                cb.equal(root.get("purchaseStatus"), "PARTIALLY_PURCHASED")
            ));

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 执行查询
        Page<SalesOrderItem> salesOrderItemPage = salesOrderItemRepository.findAll(spec, pageable);

        // 转换为DTO
        List<SalesOrderItemForPurchaseDTO> dtoList = salesOrderItemPage.getContent().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());

        // 构建分页响应
        return new PageResponse<>(
            dtoList,
            salesOrderItemPage.getTotalElements(),
            salesOrderItemPage.getTotalPages(),
            salesOrderItemPage.getNumber() + 1,
            salesOrderItemPage.getSize()
        );
    }

    @Override
    @Transactional(readOnly = true)
    public SalesOrderItemForPurchaseDTO findSalesOrderItemById(String orderItemId) {
        log.info("Finding sales order item by id: {}", orderItemId);

        SalesOrderItem salesOrderItem = salesOrderItemRepository.findById(orderItemId)
            .orElseThrow(() -> new BusinessException("销售订单明细不存在"));

        return convertToDTO(salesOrderItem);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SalesOrderItemForPurchaseDTO> findSalesOrderItemsByIds(List<String> orderItemIds) {
        log.info("Finding sales order items by ids: {}", orderItemIds);

        List<SalesOrderItem> salesOrderItems = salesOrderItemRepository.findAllById(orderItemIds);

        return salesOrderItems.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    /**
     * 将销售订单明细实体转换为DTO
     * @param item 销售订单明细实体
     * @return 销售订单明细DTO
     */
    private SalesOrderItemForPurchaseDTO convertToDTO(SalesOrderItem item) {
        SalesOrder order = item.getOrder();

        // 获取尺寸单位
        String sizeUnit = item.getSizeUnit() != null ? item.getSizeUnit() : "CM";

        // 构建规格字符串
        String specification = "";
        if (item.getLength() != null && item.getWidth() != null) {
            StringBuilder sb = new StringBuilder();
            sb.append(item.getLength());
            sb.append(" × ");
            sb.append(item.getWidth());
            if (item.getHeight() != null) {
                sb.append(" × ");
                sb.append(item.getHeight());
            }
            sb.append(" ");
            sb.append(sizeUnit);
            specification = sb.toString();
        }

        // 构建生产规格字符串
        String productionSpecification = "";
        if (item.getProductionLength() != null && item.getProductionWidth() != null) {
            StringBuilder sb = new StringBuilder();
            sb.append(item.getProductionLength());
            sb.append(" × ");
            sb.append(item.getProductionWidth());
            if (item.getProductionHeight() != null) {
                sb.append(" × ");
                sb.append(item.getProductionHeight());
            }
            sb.append(" ");
            sb.append(sizeUnit);
            productionSpecification = sb.toString();
        }

        // 查询销售订单材料信息
        List<SalesOrderMaterial> materials = salesOrderMaterialRepository.findByOrderItemIdOrderBySerialNoAsc(item.getId());

        // 从材料信息中获取压线尺寸、压线方式和模开数
        String creasingSize = null;
        String creasingMethod = null;
        Integer mouldOpenCount = null;
        String paperBoardCategory = null;

        // 新增字段 - 从材料信息中获取
        String paperQuality = null;
        BigDecimal paperWidth = null;
        BigDecimal paperLength = null;
        Integer boardCount = null;

        if (!materials.isEmpty()) {
            // 获取第一个材料信息
            SalesOrderMaterial material = materials.get(0);

            // 压线尺寸(纸度)
            creasingSize = material.getPressSizeWidth();

            // 压线方式
            creasingMethod = material.getPressMethod();

            // 模开数
            mouldOpenCount = material.getDieOpenCount();

            // 新增字段 - 从材料信息中获取
            paperQuality = material.getPaperQuality();

            // 纸板类别 - 根据纸质获取纸板类别名称
            paperBoardCategory = getPaperTypeName(paperQuality);

            // 如果获取不到纸板类别，则使用纸质作为备选
            if (!StringUtils.hasText(paperBoardCategory)) {
                paperBoardCategory = paperQuality;
            }

            paperWidth = material.getPaperWidth();
            paperLength = material.getPaperLength();
            boardCount = material.getBoardCount();
        }

        // 计算未采购数量
        Integer purchasedQuantity = item.getPurchasedQuantity() != null ? item.getPurchasedQuantity() : 0;
        Integer totalBoardCount = boardCount != null ? boardCount : 0;
        Integer unpurchasedQuantity = totalBoardCount - purchasedQuantity;

        // 确保未采购数量不为负数
        if (unpurchasedQuantity < 0) {
            unpurchasedQuantity = 0;
        }

        return SalesOrderItemForPurchaseDTO.builder()
            // 根据数据库字段名正确映射
            .productionOrderNo(item.getProductionOrderNo())
            .paperType(item.getPaperType())
            .paperBoardCategory(paperBoardCategory)
            .corrugationType(item.getCorrugationType())
            .width(item.getWidth())
            .length(item.getLength())
            .creasingSize(creasingSize)
            .creasingMethod(creasingMethod)
            .customerName(order.getCustomerName())
            .customerOrderNo(item.getCustomerOrderNo())
            .customerProductCode(item.getCustomerProductCode())
            .productName(item.getProductName())
            .processRequirements(item.getProcessRequirements())
            .boxType(item.getBoxType())
            .specification(specification)
            .orderPaperType(item.getPaperType())
            .mouldOpenCount(mouldOpenCount)
            .quantity(item.getQuantity())
            .deliveryDate(item.getDeliveryDate())
            .salesOrderDeliveryDate(item.getDeliveryDate())
            .remark(item.getRemark())
            .orderId(order.getId())
            .orderItemId(item.getId())
            .purchasedQuantity(purchasedQuantity)
            .purchasedBoardCount(purchasedQuantity) // 设置purchasedBoardCount为purchasedQuantity的别名
            .unpurchasedQuantity(unpurchasedQuantity) // 设置未采购数量
            .purchaseStatus(item.getPurchaseStatus())
            // 新增字段 - 从sales_order表获取
            .orderNo(order.getOrderNo())
            .customerCode(order.getCustomerCode())
            .orderDate(order.getOrderDate())
            // 新增字段 - 从sales_order_item表获取
            .productionRemark(item.getProductionRemark())
            .spareQuantity(item.getSpareQuantity())
            // 新增字段 - 从sales_order_material表获取
            .paperQuality(paperQuality)
            .paperWidth(paperWidth)
            .paperLength(paperLength)
            .boardCount(boardCount)
            .sizeUnit(sizeUnit)
            .productionLength(item.getProductionLength())
            .productionWidth(item.getProductionWidth())
            .productionHeight(item.getProductionHeight())
            .productionSpecification(productionSpecification)
            .build();
    }

    /**
     * 根据纸质名称获取纸板类别名称
     * @param paperQuality 纸质名称
     * @return 纸板类别名称
     */
    private String getPaperTypeName(String paperQuality) {
        if (!StringUtils.hasText(paperQuality)) {
            return null;
        }

        log.debug("Getting paper type name for paper quality: {}", paperQuality);

        // 查询纸质资料
        Optional<PaperMaterial> paperMaterialOpt = paperMaterialRepository.findByPaperName(paperQuality);
        if (paperMaterialOpt.isEmpty()) {
            log.debug("Paper material not found for paper quality: {}", paperQuality);
            return null;
        }

        PaperMaterial paperMaterial = paperMaterialOpt.get();
        if (paperMaterial.getPaperType() == null) {
            log.debug("Paper type is null for paper material: {}", paperMaterial.getPaperName());
            return null;
        }

        String paperTypeName = paperMaterial.getPaperType().getPaperTypeName();
        log.debug("Found paper type name: {} for paper quality: {}", paperTypeName, paperQuality);
        return paperTypeName;
    }
}
