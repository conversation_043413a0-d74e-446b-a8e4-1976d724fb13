package com.czerp.erpbackend.sales.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 销售订单查询DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderQueryDTO {

    /**
     * 关键字(单号、客户名称、客户订单号等)
     */
    private String keyword;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 生产单号
     */
    private String productionOrderNo;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 销售员
     */
    private String salesPerson;

    /**
     * 客户采购员
     */
    private String customerPurchaser;

    /**
     * 订单日期开始
     */
    private LocalDate orderDateStart;

    /**
     * 订单日期结束
     */
    private LocalDate orderDateEnd;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向(asc,desc)
     */
    private String sortOrder;
}
