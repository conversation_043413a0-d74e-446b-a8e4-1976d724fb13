-- 索引状态详细检查脚本
-- 用于确认idx_stock_inbound_item_calculation索引的具体情况

-- 1. 查看索引的详细结构（每个列的信息）
SELECT 
    '=== 索引详细结构分析 ===' as analysis_type;

SELECT 
    index_name,
    column_name,
    seq_in_index,
    cardinality,
    sub_part,
    packed,
    nullable,
    index_type,
    CASE WHEN non_unique = 0 THEN 'UNIQUE' ELSE 'NON-UNIQUE' END as uniqueness
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name = 'stock_inbound_item'
AND index_name = 'idx_stock_inbound_item_calculation'
ORDER BY seq_in_index;

-- 2. 查看所有索引的汇总信息（去重显示）
SELECT 
    '=== 索引汇总信息 ===' as analysis_type;

SELECT 
    index_name,
    GROUP_CONCAT(column_name ORDER BY seq_in_index) as columns,
    COUNT(*) as column_count,
    index_type,
    CASE WHEN non_unique = 0 THEN 'UNIQUE' ELSE 'NON-UNIQUE' END as uniqueness
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name = 'stock_inbound_item'
GROUP BY index_name, index_type, non_unique
ORDER BY 
    CASE 
        WHEN index_name = 'PRIMARY' THEN 1
        WHEN index_name = 'idx_stock_inbound_item_calculation' THEN 2
        ELSE 3
    END, index_name;

-- 3. 检查是否存在重复的索引定义
SELECT 
    '=== 重复索引检查 ===' as analysis_type;

SELECT 
    GROUP_CONCAT(column_name ORDER BY seq_in_index) as column_combination,
    COUNT(DISTINCT index_name) as index_count,
    GROUP_CONCAT(DISTINCT index_name) as index_names
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name = 'stock_inbound_item'
AND index_name != 'PRIMARY'
GROUP BY GROUP_CONCAT(column_name ORDER BY seq_in_index)
HAVING COUNT(DISTINCT index_name) > 1;

-- 4. 验证我们需要的复合索引是否正确
SELECT 
    '=== 复合索引验证 ===' as analysis_type;

SELECT 
    CASE 
        WHEN column_combination = 'purchase_order_item_id,is_deleted,quantity' THEN 'CORRECT'
        ELSE 'INCORRECT'
    END as status,
    column_combination,
    index_names
FROM (
    SELECT 
        GROUP_CONCAT(column_name ORDER BY seq_in_index) as column_combination,
        GROUP_CONCAT(DISTINCT index_name) as index_names
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE()
    AND table_name = 'stock_inbound_item'
    AND index_name = 'idx_stock_inbound_item_calculation'
) as idx_check;

-- 5. 查看索引大小和统计信息
SELECT 
    '=== 索引大小统计 ===' as analysis_type;

SELECT 
    table_name,
    index_name,
    stat_name,
    stat_value,
    CASE 
        WHEN stat_name = 'size' THEN CONCAT(ROUND(stat_value * @@innodb_page_size / 1024 / 1024, 2), ' MB')
        ELSE CAST(stat_value AS CHAR)
    END as formatted_value
FROM mysql.innodb_index_stats 
WHERE database_name = DATABASE()
AND table_name = 'stock_inbound_item'
AND index_name = 'idx_stock_inbound_item_calculation'
ORDER BY stat_name;
