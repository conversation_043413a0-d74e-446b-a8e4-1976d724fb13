# 纸度推荐API文档

## 概述

纸度推荐API用于根据纸盒的宽度和高度，获取不同度开数对应的纸度推荐列表。这些推荐可以帮助用户选择最合适的纸度和度开数组合。

## API接口

### 1. 获取纸度推荐列表（需要权限验证）

**请求URL**：`/system/paper-size-matching/recommendations`

**请求方法**：`POST`

**权限要求**：`system:paper-size-matching:match`

**请求参数**：

```json
{
  "width": 22,    // 纸盒宽度，必填，大于0
  "height": 18,   // 纸盒高度，必填，大于0
  "unit": "CM"    // 长度单位，可选，默认为CM，可选值：CM/cm（厘米）、MM/mm（毫米）、INCH/inch（英寸），大小写不敏感
}
```

**响应结果**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "paperSizeCm": 83.82,
      "paperSizeInch": 33.00,
      "degreeCount": 1,
      "actualUsageInch": 32.40,
      "wastageInch": 0.60
    },
    {
      "paperSizeCm": 165.10,
      "paperSizeInch": 65.00,
      "degreeCount": 2,
      "actualUsageInch": 64.80,
      "wastageInch": 0.20
    }
    // ... 更多推荐
  ]
}
```

### 2. 获取纸度推荐列表（公共接口，无需权限验证）

**请求URL**：`/public/system/paper-size-matching/recommendations`

**请求方法**：`POST`

**请求参数**：

```json
{
  "width": 22,    // 纸盒宽度，必填，大于0
  "height": 18,   // 纸盒高度，必填，大于0
  "unit": "CM"    // 长度单位，可选，默认为CM，可选值：CM/cm（厘米）、MM/mm（毫米）、INCH/inch（英寸），大小写不敏感
}
```

**响应结果**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "paperSizeCm": 83.82,
      "paperSizeInch": 33.00,
      "degreeCount": 1,
      "actualUsageInch": 32.40,
      "wastageInch": 0.60
    },
    {
      "paperSizeCm": 165.10,
      "paperSizeInch": 65.00,
      "degreeCount": 2,
      "actualUsageInch": 64.80,
      "wastageInch": 0.20
    }
    // ... 更多推荐
  ]
}
```

## 错误情况

### 1. 无可用纸度数据

当系统中没有配置任何纸度数据时，返回空列表：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": []
}
```

### 2. 输入参数无效

当输入参数无效（如宽度或高度小于等于0）时，返回：

```json
{
  "code": 400,
  "message": "纸盒宽度必须大于0",
  "data": null
}
```

或

```json
{
  "code": 400,
  "message": "纸盒高度必须大于0",
  "data": null
}
```

## 算法说明

该API使用以下步骤计算纸度推荐列表：

1. 将宽度和高度转换为厘米（如果不是厘米）
2. 应用公式计算实际用料基准值：`(宽*0.5)+0.1+高+(宽*0.5)+0.1`（单位：厘米）
3. 将实际用料基准值转换为英寸
4. 从度开数1开始，逐步增加：
   - 计算当前度开数下的实际用料 = 基准值 * 度开数 + 固定损耗（0.75英寸）
   - 如果实际用料超过最大可用纸度，则停止计算
   - 否则，在可用纸度列表中找到第一个大于等于实际用料的标准纸度
   - 计算纸度损耗 = 标准纸度 - 实际用料
   - 创建一个推荐对象，添加到结果列表中

## 注意事项

1. 输入的宽度和高度可以使用不同的单位（CM/cm、MM/mm、INCH/inch），通过unit参数指定，单位参数大小写不敏感（如"CM"、"cm"、"Cm"都会被识别为厘米）
2. 如果不指定unit参数，默认使用厘米(CM)作为单位
3. 系统内部会将所有单位统一转换为英寸进行计算
4. 度开数范围不是固定的，而是根据现有纸度范围动态计算
5. 如果计算出的实际用料超过最大可用纸度，则不会包含该度开数及更高度开数的推荐
6. 实际用料计算包含固定损耗0.75英寸
