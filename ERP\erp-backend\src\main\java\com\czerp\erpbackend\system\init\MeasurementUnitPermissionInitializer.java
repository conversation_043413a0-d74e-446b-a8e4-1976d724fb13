package com.czerp.erpbackend.system.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 计量单位权限初始化器
 * 负责初始化计量单位模块的权限并分配给管理员角色
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(5) // 在系统权限初始化之后执行
public class MeasurementUnitPermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing measurement unit permissions...");

        // 初始化计量单位模块权限
        initMeasurementUnitPermissions();

        // 为管理员角色分配计量单位模块权限
        assignPermissionsToAdminRole();

        log.info("Measurement unit permissions initialized successfully");
    }

    /**
     * 初始化计量单位模块权限
     */
    private void initMeasurementUnitPermissions() {
        log.info("Initializing measurement unit module permissions...");

        // 检查系统模块是否存在
        String systemModuleId = findPermissionIdByCode("system");
        if (systemModuleId == null) {
            log.warn("System module not found, creating it...");
            systemModuleId = createPermission("系统管理", "system", "MENU", null, "/system", "system/index", "setting", 1);
        }

        // 检查计量单位模块是否已存在
        if (permissionRepository.existsByCode("system:measurement-unit")) {
            log.info("Measurement unit module already exists, skipping...");
            return;
        }

        // 创建计量单位模块菜单
        String measurementUnitModuleId = createPermission("计量单位管理", "system:measurement-unit", "MENU", systemModuleId,
                "/system/measurement-unit", "system/MeasurementUnit", "ruler", 50);

        // 创建计量单位模块按钮权限
        createPermission("计量单位查询", "system:measurement-unit:list", "BUTTON", measurementUnitModuleId, null, null, null, 10);
        createPermission("计量单位详情", "system:measurement-unit:read", "BUTTON", measurementUnitModuleId, null, null, null, 20);
        createPermission("计量单位创建", "system:measurement-unit:create", "BUTTON", measurementUnitModuleId, null, null, null, 30);
        createPermission("计量单位更新", "system:measurement-unit:update", "BUTTON", measurementUnitModuleId, null, null, null, 40);
        createPermission("计量单位删除", "system:measurement-unit:delete", "BUTTON", measurementUnitModuleId, null, null, null, 50);

        log.info("Measurement unit module permissions created successfully");
    }

    /**
     * 为管理员角色分配计量单位模块权限
     */
    private void assignPermissionsToAdminRole() {
        log.info("Assigning measurement unit permissions to admin role...");

        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping permission assignment");
            return;
        }

        // 获取所有计量单位相关权限
        List<String> permissionCodes = Arrays.asList(
                "system:measurement-unit",
                "system:measurement-unit:list",
                "system:measurement-unit:read",
                "system:measurement-unit:create",
                "system:measurement-unit:update",
                "system:measurement-unit:delete"
        );

        List<Permission> permissions = permissionRepository.findByCodeIn(permissionCodes);
        if (permissions.isEmpty()) {
            log.warn("No measurement unit permissions found, skipping permission assignment");
            return;
        }

        // 为管理员角色分配权限
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : permissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString());
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermission.setCreateBy("system");
                rolePermission.setCreateTime(LocalDateTime.now());
                rolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }

        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} measurement unit permissions to admin role", rolePermissions.size());
        } else {
            log.info("All measurement unit permissions already assigned to admin role, skipping");
        }
    }

    /**
     * 创建权限
     * @param name 权限名称
     * @param code 权限编码
     * @param type 权限类型
     * @param parentId 父权限ID
     * @param path 路径
     * @param component 组件
     * @param icon 图标
     * @param sort 排序
     * @return 权限ID
     */
    private String createPermission(String name, String code, String type, String parentId, String path, String component, String icon, int sort) {
        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setName(name);
        permission.setCode(code);
        permission.setType(type);
        permission.setParentId(parentId);
        permission.setPath(path);
        permission.setComponent(component);
        permission.setIcon(icon);
        permission.setSort(sort);
        permission.setStatus("active");
        permission.setCreatedBy("system");
        permission.setCreatedTime(LocalDateTime.now());
        permission.setIsDeleted(false);
        permission = permissionRepository.save(permission);
        return permission.getId();
    }

    /**
     * 根据权限编码查找权限ID
     * @param code 权限编码
     * @return 权限ID
     */
    private String findPermissionIdByCode(String code) {
        Optional<Permission> permission = permissionRepository.findByCode(code);
        return permission.map(Permission::getId).orElse(null);
    }
}
