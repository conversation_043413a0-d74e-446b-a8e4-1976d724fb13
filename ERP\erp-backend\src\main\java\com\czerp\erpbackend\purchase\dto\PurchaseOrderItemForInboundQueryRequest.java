package com.czerp.erpbackend.purchase.dto;

import com.czerp.erpbackend.common.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 用于入库管理引用采购订单明细的查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrderItemForInboundQueryRequest extends PageRequest {
    
    /**
     * 关键字（采购单号、供应商名称、生产单号、客户名称、品名等）
     */
    private String keyword;
    
    /**
     * 供应商编码
     */
    private String supplierCode;
    
    /**
     * 供应商名称
     */
    private String supplierName;
    
    /**
     * 采购单号
     */
    private String purchaseOrderNo;
    
    /**
     * 纸质
     */
    private String paperType;
    
    /**
     * 楞别
     */
    private String corrugationType;
    
    /**
     * 采购日期开始
     */
    private LocalDate purchaseDateStart;
    
    /**
     * 采购日期结束
     */
    private LocalDate purchaseDateEnd;
    
    /**
     * 交期开始
     */
    private LocalDate deliveryDateStart;
    
    /**
     * 交期结束
     */
    private LocalDate deliveryDateEnd;
    
    /**
     * 客户编码
     */
    private String customerCode;
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 生产单号
     */
    private String productionOrderNo;
    
    /**
     * 纸板类别
     */
    private String paperBoardCategory;
}
