package com.czerp.erpbackend.sales.service;

import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.service.impl.ProductionOrderNumberServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProductionOrderNumberServiceTest {

    @Mock
    private SalesOrderItemRepository salesOrderItemRepository;
    
    @InjectMocks
    private ProductionOrderNumberServiceImpl productionOrderNumberService;
    
    private String datePrefix;
    private String productionOrderNoPrefix;
    
    @BeforeEach
    void setUp() {
        // 设置日期前缀
        datePrefix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        productionOrderNoPrefix = "PO" + datePrefix;
    }
    
    @Test
    void testGenerateProductionOrderNumber_FirstNumberOfDay() {
        // 准备测试数据
        SalesOrderItem orderItem = new SalesOrderItem();
        orderItem.setId("test-item-id");
        
        // 配置Mock行为 - 当天没有生产单号
        when(salesOrderItemRepository.findMaxProductionOrderNoByPrefix(productionOrderNoPrefix))
                .thenReturn(null);
        
        // 执行测试
        String result = productionOrderNumberService.generateProductionOrderNumber(orderItem);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.startsWith("PO"));
        assertEquals(productionOrderNoPrefix + "000001", result);
        
        // 验证方法调用
        verify(salesOrderItemRepository).findMaxProductionOrderNoByPrefix(productionOrderNoPrefix);
    }
    
    @Test
    void testGenerateProductionOrderNumber_NextNumber() {
        // 准备测试数据
        SalesOrderItem orderItem = new SalesOrderItem();
        orderItem.setId("test-item-id");
        
        // 配置Mock行为 - 当天已有生产单号
        when(salesOrderItemRepository.findMaxProductionOrderNoByPrefix(productionOrderNoPrefix))
                .thenReturn(productionOrderNoPrefix + "000005");
        
        // 执行测试
        String result = productionOrderNumberService.generateProductionOrderNumber(orderItem);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.startsWith("PO"));
        assertEquals(productionOrderNoPrefix + "000006", result);
        
        // 验证方法调用
        verify(salesOrderItemRepository).findMaxProductionOrderNoByPrefix(productionOrderNoPrefix);
    }
    
    @Test
    void testBatchGenerateProductionOrderNumbers() {
        // 准备测试数据
        List<SalesOrderItem> orderItems = new ArrayList<>();
        SalesOrderItem item1 = new SalesOrderItem();
        item1.setId("test-item-id-1");
        SalesOrderItem item2 = new SalesOrderItem();
        item2.setId("test-item-id-2");
        orderItems.add(item1);
        orderItems.add(item2);
        
        // 配置Mock行为
        when(salesOrderItemRepository.findMaxProductionOrderNoByPrefix(productionOrderNoPrefix))
                .thenReturn(productionOrderNoPrefix + "000010");
        
        // 执行测试
        Map<String, String> result = productionOrderNumberService.batchGenerateProductionOrderNumbers(orderItems);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(productionOrderNoPrefix + "000011", result.get("test-item-id-1"));
        assertEquals(productionOrderNoPrefix + "000012", result.get("test-item-id-2"));
        
        // 验证方法调用
        verify(salesOrderItemRepository).findMaxProductionOrderNoByPrefix(productionOrderNoPrefix);
    }
    
    @Test
    void testBatchGenerateProductionOrderNumbers_FirstNumbersOfDay() {
        // 准备测试数据
        List<SalesOrderItem> orderItems = new ArrayList<>();
        SalesOrderItem item1 = new SalesOrderItem();
        item1.setId("test-item-id-1");
        SalesOrderItem item2 = new SalesOrderItem();
        item2.setId("test-item-id-2");
        orderItems.add(item1);
        orderItems.add(item2);
        
        // 配置Mock行为 - 当天没有生产单号
        when(salesOrderItemRepository.findMaxProductionOrderNoByPrefix(productionOrderNoPrefix))
                .thenReturn(null);
        
        // 执行测试
        Map<String, String> result = productionOrderNumberService.batchGenerateProductionOrderNumbers(orderItems);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(productionOrderNoPrefix + "000001", result.get("test-item-id-1"));
        assertEquals(productionOrderNoPrefix + "000002", result.get("test-item-id-2"));
        
        // 验证方法调用
        verify(salesOrderItemRepository).findMaxProductionOrderNoByPrefix(productionOrderNoPrefix);
    }
    
    @Test
    void testGenerateProductionOrderNumber_InvalidMaxNumber() {
        // 准备测试数据
        SalesOrderItem orderItem = new SalesOrderItem();
        orderItem.setId("test-item-id");
        
        // 配置Mock行为 - 返回无效的最大生产单号
        when(salesOrderItemRepository.findMaxProductionOrderNoByPrefix(productionOrderNoPrefix))
                .thenReturn(productionOrderNoPrefix + "INVALID");
        
        // 执行测试
        String result = productionOrderNumberService.generateProductionOrderNumber(orderItem);
        
        // 验证结果 - 应该从1开始
        assertNotNull(result);
        assertTrue(result.startsWith("PO"));
        assertEquals(productionOrderNoPrefix + "000001", result);
        
        // 验证方法调用
        verify(salesOrderItemRepository).findMaxProductionOrderNoByPrefix(productionOrderNoPrefix);
    }
}
