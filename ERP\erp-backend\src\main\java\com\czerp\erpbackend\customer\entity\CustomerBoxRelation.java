package com.czerp.erpbackend.customer.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 客户盒式关联实体
 */
@Entity
@Table(name = "customer_box_relation")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerBoxRelation extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 客户ID
     */
    @Column(name = "customer_id", length = 36, nullable = false)
    private String customerId;

    /**
     * 客户
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", insertable = false, updatable = false)
    private Customer customer;

    /**
     * 盒式信息ID
     */
    @Column(name = "box_info_id", length = 36, nullable = false)
    private String boxInfoId;

    /**
     * 盒式信息
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "box_info_id", insertable = false, updatable = false)
    private CustomerBoxInfo boxInfo;

    /**
     * 备注
     */
    @Column(name = "remark", length = 255)
    private String remark;
}
