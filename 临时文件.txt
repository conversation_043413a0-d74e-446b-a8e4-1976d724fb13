+--------------------------+---------------------+----------------------------------------------+-----------------------+------------------------+
| TABLE_NAME               | COLUMN_NAME         | CONSTRAINT_NAME                              | REFERENCED_TABLE_NAME | REFERENCED_COLUMN_NAME |
+--------------------------+---------------------+----------------------------------------------+-----------------------+------------------------+
| purchase_order_item      | sales_order_item_id | fk_poi_soi                                   | sales_order_item      | id                     |
| sales_order_item_process | order_item_id       | fk_sales_order_item_process_sales_order_item | sales_order_item      | id                     |
| sales_order_material     | order_item_id       | sales_order_material_ibfk_1                  | sales_order_item      | id                     |
+--------------------------+---------------------+----------------------------------------------+-----------------------+------------------------+
3 <USER> <GROUP> set (0.01 sec)

mysql> SHOW CREATE TABLE czerp_web.sales_order_item_process;
+--------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Table                    | Create Table                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
+--------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| sales_order_item_process | CREATE TABLE `sales_order_item_process` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键ID',
  `order_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '销售订单ID',
  `order_item_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '销售订单明细项ID',
  `sequence` int NOT NULL COMMENT '工序顺序',
  `process_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工序名称',
  `process_requirements` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工艺要求',
  `plate_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '印版编号',
  `ink_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '水墨编号',
  `ink_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '水墨名称',
  `color_count` int DEFAULT NULL COMMENT '颜色数',
  `is_deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `version` int DEFAULT '0' COMMENT '版本号',
  `created_by` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_time` datetime(6) NOT NULL,
  `updated_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `created_by_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_by_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_sales_order_item_process_process_name` (`process_name`),
  KEY `idx_sales_order_item_process_order_id` (`order_id`),
  KEY `idx_sales_order_item_process_order_item_id` (`order_item_id`),
  CONSTRAINT `fk_sales_order_item_process_sales_order` FOREIGN KEY (`order_id`) REFERENCES `sales_order` (`id`),
  CONSTRAINT `fk_sales_order_item_process_sales_order_item` FOREIGN KEY (`order_item_id`) REFERENCES `sales_order_item` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售订单工序表' |
+--------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
1 <USER> <GROUP> set (0.00 sec)

mysql> SHOW CREATE TABLE czerp_web.sales_order_material;
+----------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Table                | Create Table                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
+----------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| sales_order_material | CREATE TABLE `sales_order_material` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `order_item_id` varchar(36) NOT NULL COMMENT '销售订单行项目ID',
  `serial_no` int DEFAULT NULL COMMENT '序号',
  `paper_quality` varchar(100) DEFAULT NULL COMMENT '纸质',
  `paper_width` decimal(10,2) DEFAULT NULL COMMENT '纸度',
  `paper_length` decimal(10,2) DEFAULT NULL COMMENT '纸长',
  `width_open` decimal(10,2) DEFAULT NULL COMMENT '度开',
  `length_open` decimal(10,2) DEFAULT NULL COMMENT '长开',
  `board_count` int DEFAULT NULL COMMENT '纸板数',
  `board_loss` decimal(10,2) DEFAULT NULL COMMENT '纸板损耗',
  `material_usage` decimal(10,2) DEFAULT NULL COMMENT '原料用量',
  `material_usage_count` decimal(10,2) DEFAULT NULL,
  `method` varchar(50) DEFAULT NULL COMMENT '方式',
  `press_size_width` varchar(100) DEFAULT NULL COMMENT '压线尺寸(纸度)',
  `press_method` varchar(50) DEFAULT NULL COMMENT '压线方式',
  `actual_material_width` decimal(10,4) DEFAULT NULL COMMENT '实际用料宽',
  `actual_material_length` decimal(10,4) DEFAULT NULL COMMENT '实际用料长',
  `die_model` varchar(100) DEFAULT NULL COMMENT '啤模',
  `die_model_no` varchar(50) DEFAULT NULL COMMENT '啤模编号',
  `die_open_count` int DEFAULT NULL COMMENT '模开数',
  `die_model_position` varchar(100) DEFAULT NULL COMMENT '啤模位置',
  `die_model_checked` tinyint(1) DEFAULT '0' COMMENT '啤模选中状态',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `current_inventory` int DEFAULT NULL COMMENT '当前库存',
  `available_inventory` int DEFAULT NULL COMMENT '可用库存',
  `use_inventory_count` int DEFAULT NULL COMMENT '使用库存数',
  `actual_material_length_converted` decimal(10,4) DEFAULT NULL COMMENT '实际用料长(转换)',
  `actual_material_width_converted` decimal(10,4) DEFAULT NULL COMMENT '实际用料宽(转换)',
  `supplier` varchar(100) DEFAULT NULL COMMENT '供应商',
  `material_remark` varchar(500) DEFAULT NULL COMMENT '材料备注',
  `purchased_count` int DEFAULT NULL COMMENT '已采购数',
  `die_press_line` varchar(100) DEFAULT NULL COMMENT '啤模压线',
  `press_size_length` decimal(10,2) DEFAULT NULL COMMENT '压线尺寸(纸长)',
  `inbound_count` int DEFAULT NULL COMMENT '已入库数',
  `materials_received_count` int DEFAULT NULL COMMENT '已领料数',
  `created_by` varchar(50) NOT NULL,
  `created_time` datetime(6) NOT NULL,
  `is_deleted` bit(1) NOT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `version` int DEFAULT NULL,
  `created_by_name` varchar(50) DEFAULT NULL,
  `updated_by_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order_item_id` (`order_item_id`),
  KEY `idx_paper_quality` (`paper_quality`),
  KEY `idx_die_model_no` (`die_model_no`),
  KEY `idx_supplier` (`supplier`),
  CONSTRAINT `sales_order_material_ibfk_1` FOREIGN KEY (`order_item_id`) REFERENCES `sales_order_item` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售订单材料信息表' |
+----------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
1 <USER> <GROUP> set (0.01 sec)

mysql> SHOW CREATE TABLE czerp_web.purchase_order_item;
+---------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Table               | Create Table                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
+---------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| purchase_order_item | CREATE TABLE `purchase_order_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `purchase_order_id` bigint NOT NULL COMMENT '采购订单ID',
  `paper_quality` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '纸质',
  `paper_board_category` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '纸板类别',
  `corrugation_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '楞别',
  `paper_width` decimal(10,2) DEFAULT NULL COMMENT '纸度',
  `paper_length` decimal(10,2) DEFAULT NULL COMMENT '纸长',
  `binding_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '合订',
  `binding_specification` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '合订规格',
  `material_change` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用料异动',
  `special_quotation` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '报价特价',
  `paper_quotation` decimal(10,2) DEFAULT NULL COMMENT '纸质报价',
  `discount` decimal(10,2) DEFAULT NULL COMMENT '折扣',
  `quantity` int DEFAULT NULL COMMENT '数量',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '金额',
  `creasing_size` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '压线尺寸(纸度)',
  `creasing_method` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '压线方式',
  `remarks` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `folding_specification` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '折度规格',
  `length_meters` decimal(10,2) DEFAULT NULL COMMENT '长度(米)',
  `area_square_meters` decimal(10,2) DEFAULT NULL COMMENT '面积(平米)',
  `volume_cubic_meters` decimal(10,2) DEFAULT NULL COMMENT '体积(立方米)',
  `unit_weight` decimal(10,2) DEFAULT NULL COMMENT '单重',
  `total_weight_kg` decimal(10,2) DEFAULT NULL COMMENT '总重(KG)',
  `processing_fee` decimal(10,2) DEFAULT NULL COMMENT '加工费',
  `currency` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '币种',
  `delivery_date` date DEFAULT NULL COMMENT '交期',
  `created_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `sales_order_item_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `is_deleted` bit(1) NOT NULL,
  `version` int DEFAULT NULL,
  `CreatedBy` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `UpdatedBy` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `purchase_order_id` (`purchase_order_id`),
  KEY `idx_sales_order_item_id` (`sales_order_item_id`),
  CONSTRAINT `fk_poi_soi` FOREIGN KEY (`sales_order_item_id`) REFERENCES `sales_order_item` (`id`),
  CONSTRAINT `purchase_order_item_ibfk_1` FOREIGN KEY (`purchase_order_id`) REFERENCES `purchase_order` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采购订单行表' |
+---------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
1 <USER> <GROUP> set (0.00 sec)