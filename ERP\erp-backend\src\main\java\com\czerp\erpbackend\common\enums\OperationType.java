package com.czerp.erpbackend.common.enums;

import lombok.Getter;

/**
 * 操作类型枚举
 */
@Getter
public enum OperationType {
    
    /**
     * 入库
     */
    IN("入库"),
    
    /**
     * 出库
     */
    OUT("出库"),
    
    /**
     * 调拨
     */
    TRANSFER("调拨"),
    
    /**
     * 盘点
     */
    INVENTORY("盘点"),
    
    /**
     * 调整
     */
    ADJUST("调整");
    
    private final String description;
    
    OperationType(String description) {
        this.description = description;
    }
}
