-- 迁移脚本：删除重复的审计字段并确保数据迁移
-- 作者：AI助手
-- 日期：2025-04-29

-- 为所有表创建临时备份
-- 注意：在执行此脚本前，请确保已备份数据库

-- 处理 prd_product 表
-- 1. 将大写字段的数据复制到小写字段（如果小写字段为空）
UPDATE prd_product 
SET created_by = COALESCE(created_by, CreatedBy),
    updated_by = COALESCE(updated_by, UpdatedBy),
    is_deleted = COALESCE(is_deleted, IsDeleted);

-- 2. 删除大写字段
ALTER TABLE prd_product
DROP COLUMN CreatedBy,
DROP COLUMN UpdatedBy,
DROP COLUMN IsDeleted,
DROP COLUMN UpdatedTime;

-- 处理其他可能存在相同问题的表
-- 以下是一个通用模板，需要根据实际情况调整表名

-- 检查并处理 cus_customer 表
-- 检查表是否存在这些列
SET @table_name = 'cus_customer';
SET @column_exists = 0;

SELECT COUNT(*) INTO @column_exists 
FROM information_schema.columns 
WHERE table_schema = 'czerp_web' 
AND table_name = @table_name 
AND column_name = 'CreatedBy';

-- 如果列存在，则执行更新和删除操作
SET @sql = IF(@column_exists > 0, 
    CONCAT('UPDATE ', @table_name, ' SET created_by = COALESCE(created_by, CreatedBy), updated_by = COALESCE(updated_by, UpdatedBy), is_deleted = COALESCE(is_deleted, IsDeleted);'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@column_exists > 0, 
    CONCAT('ALTER TABLE ', @table_name, ' DROP COLUMN CreatedBy, DROP COLUMN UpdatedBy, DROP COLUMN IsDeleted, DROP COLUMN UpdatedTime;'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并处理 pur_supplier 表
SET @table_name = 'pur_supplier';
SET @column_exists = 0;

SELECT COUNT(*) INTO @column_exists 
FROM information_schema.columns 
WHERE table_schema = 'czerp_web' 
AND table_name = @table_name 
AND column_name = 'CreatedBy';

SET @sql = IF(@column_exists > 0, 
    CONCAT('UPDATE ', @table_name, ' SET created_by = COALESCE(created_by, CreatedBy), updated_by = COALESCE(updated_by, UpdatedBy), is_deleted = COALESCE(is_deleted, IsDeleted);'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@column_exists > 0, 
    CONCAT('ALTER TABLE ', @table_name, ' DROP COLUMN CreatedBy, DROP COLUMN UpdatedBy, DROP COLUMN IsDeleted, DROP COLUMN UpdatedTime;'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并处理 pur_supplier_category 表
SET @table_name = 'pur_supplier_category';
SET @column_exists = 0;

SELECT COUNT(*) INTO @column_exists 
FROM information_schema.columns 
WHERE table_schema = 'czerp_web' 
AND table_name = @table_name 
AND column_name = 'CreatedBy';

SET @sql = IF(@column_exists > 0, 
    CONCAT('UPDATE ', @table_name, ' SET created_by = COALESCE(created_by, CreatedBy), updated_by = COALESCE(updated_by, UpdatedBy), is_deleted = COALESCE(is_deleted, IsDeleted);'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@column_exists > 0, 
    CONCAT('ALTER TABLE ', @table_name, ' DROP COLUMN CreatedBy, DROP COLUMN UpdatedBy, DROP COLUMN IsDeleted, DROP COLUMN UpdatedTime;'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并处理 cus_customer_category 表
SET @table_name = 'cus_customer_category';
SET @column_exists = 0;

SELECT COUNT(*) INTO @column_exists 
FROM information_schema.columns 
WHERE table_schema = 'czerp_web' 
AND table_name = @table_name 
AND column_name = 'CreatedBy';

SET @sql = IF(@column_exists > 0, 
    CONCAT('UPDATE ', @table_name, ' SET created_by = COALESCE(created_by, CreatedBy), updated_by = COALESCE(updated_by, UpdatedBy), is_deleted = COALESCE(is_deleted, IsDeleted);'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@column_exists > 0, 
    CONCAT('ALTER TABLE ', @table_name, ' DROP COLUMN CreatedBy, DROP COLUMN UpdatedBy, DROP COLUMN IsDeleted, DROP COLUMN UpdatedTime;'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并处理 prd_product_category 表
SET @table_name = 'prd_product_category';
SET @column_exists = 0;

SELECT COUNT(*) INTO @column_exists 
FROM information_schema.columns 
WHERE table_schema = 'czerp_web' 
AND table_name = @table_name 
AND column_name = 'CreatedBy';

SET @sql = IF(@column_exists > 0, 
    CONCAT('UPDATE ', @table_name, ' SET created_by = COALESCE(created_by, CreatedBy), updated_by = COALESCE(updated_by, UpdatedBy), is_deleted = COALESCE(is_deleted, IsDeleted);'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@column_exists > 0, 
    CONCAT('ALTER TABLE ', @table_name, ' DROP COLUMN CreatedBy, DROP COLUMN UpdatedBy, DROP COLUMN IsDeleted, DROP COLUMN UpdatedTime;'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并处理 prd_product_spec 表
SET @table_name = 'prd_product_spec';
SET @column_exists = 0;

SELECT COUNT(*) INTO @column_exists 
FROM information_schema.columns 
WHERE table_schema = 'czerp_web' 
AND table_name = @table_name 
AND column_name = 'CreatedBy';

SET @sql = IF(@column_exists > 0, 
    CONCAT('UPDATE ', @table_name, ' SET created_by = COALESCE(created_by, CreatedBy), updated_by = COALESCE(updated_by, UpdatedBy), is_deleted = COALESCE(is_deleted, IsDeleted);'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@column_exists > 0, 
    CONCAT('ALTER TABLE ', @table_name, ' DROP COLUMN CreatedBy, DROP COLUMN UpdatedBy, DROP COLUMN IsDeleted, DROP COLUMN UpdatedTime;'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加其他表的处理...
-- 可以使用以下查询找出所有包含这些重复字段的表
-- SELECT DISTINCT table_name FROM information_schema.columns WHERE table_schema = 'czerp_web' AND column_name IN ('CreatedBy', 'UpdatedBy', 'IsDeleted', 'UpdatedTime');
