package com.czerp.erpbackend.purchase.repository;

import com.czerp.erpbackend.purchase.entity.Supplier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;

/**
 * 供应商存储库
 */
@Repository
public interface SupplierRepository extends JpaRepository<Supplier, Long> {
    
    /**
     * 根据供应商编码查找供应商
     * @param supplierCode 供应商编码
     * @return 供应商
     */
    Optional<Supplier> findBySupplierCode(String supplierCode);
    
    /**
     * 判断供应商编码是否存在
     * @param supplierCode 供应商编码
     * @return 是否存在
     */
    boolean existsBySupplierCode(String supplierCode);
    
    /**
     * 根据供应商分类ID查询供应商列表
     * @param categoryId 供应商分类ID
     * @param pageable 分页参数
     * @return 供应商列表
     */
    Page<Supplier> findByCategoryId(String categoryId, Pageable pageable);
    
    /**
     * 多条件查询供应商
     * @param keyword 关键词
     * @param categoryId 供应商分类ID
     * @param status 状态
     * @param industry 行业
     * @param region 地区
     * @param lastOrderStartDate 最近下单开始日期
     * @param lastOrderEndDate 最近下单结束日期
     * @param lastReceiveStartDate 最近收货开始日期
     * @param lastReceiveEndDate 最近收货结束日期
     * @param pageable 分页参数
     * @return 供应商分页列表
     */
    @Query("SELECT s FROM Supplier s WHERE " +
           "(:keyword IS NULL OR :keyword = '' OR s.supplierCode LIKE %:keyword% OR s.supplierName LIKE %:keyword%) AND " +
           "(:categoryId IS NULL OR :categoryId = '' OR s.categoryId = :categoryId) AND " +
           "(:status IS NULL OR :status = '' OR s.status = :status) AND " +
           "(:industry IS NULL OR :industry = '' OR s.industry = :industry) AND " +
           "(:region IS NULL OR :region = '' OR s.region = :region) AND " +
           "(:lastOrderStartDate IS NULL OR s.lastOrderDate >= :lastOrderStartDate) AND " +
           "(:lastOrderEndDate IS NULL OR s.lastOrderDate <= :lastOrderEndDate) AND " +
           "(:lastReceiveStartDate IS NULL OR s.lastReceiveDate >= :lastReceiveStartDate) AND " +
           "(:lastReceiveEndDate IS NULL OR s.lastReceiveDate <= :lastReceiveEndDate)")
    Page<Supplier> search(
            @Param("keyword") String keyword,
            @Param("categoryId") String categoryId,
            @Param("status") String status,
            @Param("industry") String industry,
            @Param("region") String region,
            @Param("lastOrderStartDate") LocalDate lastOrderStartDate,
            @Param("lastOrderEndDate") LocalDate lastOrderEndDate,
            @Param("lastReceiveStartDate") LocalDate lastReceiveStartDate,
            @Param("lastReceiveEndDate") LocalDate lastReceiveEndDate,
            Pageable pageable);
}
