package com.czerp.erpbackend.common.filter.controller;

import com.czerp.erpbackend.common.filter.dto.FilterOptionDTO;
import com.czerp.erpbackend.common.filter.dto.FilterRequest;
import com.czerp.erpbackend.common.filter.service.ColumnFilterService;
import com.czerp.erpbackend.common.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 通用列筛选控制器
 * 提供跨模块的列筛选API
 */
@RestController
@RequestMapping("/common/filter")
@RequiredArgsConstructor
@Slf4j
public class ColumnFilterController {
    
    private final ColumnFilterService columnFilterService;
    
    /**
     * 获取指定字段的筛选选项
     * @param moduleCode 模块编码
     * @param fieldName 字段名称
     * @param searchText 搜索文本（可选）
     * @param currentFilters 当前筛选条件（可选）
     * @return 筛选选项列表
     */
    @GetMapping("/options")
    public ApiResponse<List<FilterOptionDTO>> getFilterOptions(
            @RequestParam String moduleCode,
            @RequestParam String fieldName,
            @RequestParam(required = false) String searchText,
            @RequestParam(required = false) Map<String, Object> currentFilters) {
        
        log.debug("Getting filter options for module: {}, field: {}", moduleCode, fieldName);
        
        List<FilterOptionDTO> options = columnFilterService.getFilterOptions(
                moduleCode, fieldName, searchText, currentFilters);
        
        return ApiResponse.success(options);
    }
    
    /**
     * 获取指定字段的筛选选项（POST方式，支持复杂参数）
     * @param request 筛选请求
     * @return 筛选选项列表
     */
    @PostMapping("/options")
    public ApiResponse<List<FilterOptionDTO>> getFilterOptions(@RequestBody FilterRequest request) {
        log.debug("Getting filter options for request: {}", request);
        
        List<FilterOptionDTO> options = columnFilterService.getFilterOptions(request);
        
        return ApiResponse.success(options);
    }
    
    /**
     * 批量获取多个字段的筛选选项
     * @param moduleCode 模块编码
     * @param fieldNames 字段名称列表
     * @param currentFilters 当前筛选条件
     * @return 字段名称到筛选选项列表的映射
     */
    @GetMapping("/batch-options")
    public ApiResponse<Map<String, List<FilterOptionDTO>>> getBatchFilterOptions(
            @RequestParam String moduleCode,
            @RequestParam List<String> fieldNames,
            @RequestParam(required = false) Map<String, Object> currentFilters) {
        
        log.debug("Getting batch filter options for module: {}, fields: {}", moduleCode, fieldNames);
        
        Map<String, List<FilterOptionDTO>> options = columnFilterService.getBatchFilterOptions(
                moduleCode, fieldNames, currentFilters);
        
        return ApiResponse.success(options);
    }
    
    /**
     * 检查模块是否支持指定字段的筛选
     * @param moduleCode 模块编码
     * @param fieldName 字段名称
     * @return 是否支持
     */
    @GetMapping("/supported")
    public ApiResponse<Boolean> isFieldSupported(
            @RequestParam String moduleCode,
            @RequestParam String fieldName) {
        
        boolean supported = columnFilterService.isFieldSupported(moduleCode, fieldName);
        
        return ApiResponse.success(supported);
    }
    
    /**
     * 获取模块支持的所有筛选字段
     * @param moduleCode 模块编码
     * @return 支持的字段名称列表
     */
    @GetMapping("/fields")
    public ApiResponse<List<String>> getSupportedFields(@RequestParam String moduleCode) {
        List<String> fields = columnFilterService.getSupportedFields(moduleCode);
        
        return ApiResponse.success(fields);
    }
}
