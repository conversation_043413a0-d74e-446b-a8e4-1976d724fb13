package com.czerp.erpbackend.production.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 生产排程单明细DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductionScheduleItemDTO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 排程单ID
     */
    private String scheduleId;

    /**
     * 急单
     */
    private Boolean isUrgent;

    /**
     * 急单序号
     */
    private Integer urgentSequence;

    /**
     * 已打印
     */
    private Boolean isPrinted;

    /**
     * 计划完成日期
     */
    private LocalDate plannedCompletionDate;

    /**
     * 排程数量
     */
    private Integer scheduleQuantity;

    /**
     * 包装数
     */
    private Integer packageCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 销售订单明细ID
     */
    private String salesOrderItemId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 版本号
     */
    private Integer version;

    // 关联销售订单信息（用于查询显示）
    /**
     * 销售订单号
     */
    private String salesOrderNo;

    /**
     * 生产单号
     */
    private String productionOrderNo;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 工艺要求
     */
    private String processRequirements;

    /**
     * 规格
     */
    private String specification;

    /**
     * 生产规格
     */
    private String productionSpecification;

    /**
     * 销售数量
     */
    private Integer salesQuantity;

    /**
     * 送货日期
     */
    private LocalDate deliveryDate;

    // ========== 引用销售订单接口专用字段 ==========

    /**
     * 本次排程数
     */
    private Integer currentScheduleQuantity;

    /**
     * 已排程数
     */
    private Integer scheduledQuantity;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 客方货号
     */
    private String customerProductNo;

    /**
     * 品名
     */
    private String productName2;

    /**
     * 盒式
     */
    private String boxType;

    /**
     * 纸质
     */
    private String paperType;

    /**
     * 生产纸质
     */
    private String productionPaperType;

    /**
     * 订单日期
     */
    private LocalDate orderDate;

    /**
     * 已采购数
     */
    private Integer purchasedQuantity;

    /**
     * 到料日期
     */
    private LocalDate materialArrivalDate;

    /**
     * 已到料数
     */
    private Integer arrivedQuantity;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 是否含税
     */
    private Boolean isTaxed;

    /**
     * 纸板类型
     */
    private String paperBoardType;

    /**
     * 长
     */
    private java.math.BigDecimal length;

    /**
     * 宽
     */
    private java.math.BigDecimal width;

    /**
     * 高
     */
    private java.math.BigDecimal height;

    /**
     * 生产长
     */
    private java.math.BigDecimal productionLength;

    /**
     * 生产宽
     */
    private java.math.BigDecimal productionWidth;

    /**
     * 生产高
     */
    private java.math.BigDecimal productionHeight;

    /**
     * 订单数
     */
    private Integer orderQuantity;

    /**
     * 备品数
     */
    private Integer spareQuantity;

    /**
     * 纸度
     */
    private String paperWidth;

    /**
     * 纸长
     */
    private java.math.BigDecimal paperLength;

    /**
     * 实际用料宽
     */
    private java.math.BigDecimal actualMaterialWidth;

    /**
     * 实际用料长
     */
    private java.math.BigDecimal actualMaterialLength;

    /**
     * 实际用料单位
     */
    private String actualMaterialUnit;

    /**
     * 原料规格
     */
    private String materialSpecification;

    /**
     * 未送货数
     */
    private Integer undeliveredQuantity;

    /**
     * 成品库存数
     */
    private Integer finishedStockQuantity;

    /**
     * 生产备注
     */
    private String productionRemark;

    /**
     * 工序（格式化后的）
     */
    private String processSequence;
}
