package com.czerp.erpbackend.product.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.product.dto.CreateCategoryRequest;
import com.czerp.erpbackend.product.dto.ProductCategoryDTO;
import com.czerp.erpbackend.product.dto.UpdateCategoryRequest;
import com.czerp.erpbackend.product.service.ProductCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品分类控制器
 */
@RestController
@RequestMapping("/product-categories")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Product Category Management", description = "产品分类管理相关接口")
public class ProductCategoryController {
    
    private final ProductCategoryService categoryService;
    
    @GetMapping
    @Operation(summary = "获取分类列表", description = "获取所有分类列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-category:list')")
    public ResponseEntity<ApiResponse<List<ProductCategoryDTO>>> getAllCategories() {
        log.info("Getting all categories");
        List<ProductCategoryDTO> categories = categoryService.findAllCategories();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
    
    @GetMapping("/tree")
    @Operation(summary = "获取分类树", description = "获取分类树结构")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-category:list')")
    public ResponseEntity<ApiResponse<List<ProductCategoryDTO>>> getCategoryTree() {
        log.info("Getting category tree");
        List<ProductCategoryDTO> categoryTree = categoryService.findCategoryTree();
        return ResponseEntity.ok(ApiResponse.success(categoryTree));
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取分类详情", description = "根据ID获取分类详细信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-category:read')")
    public ResponseEntity<ApiResponse<ProductCategoryDTO>> getCategory(@PathVariable String id) {
        log.info("Getting category with id: {}", id);
        ProductCategoryDTO category = categoryService.findCategoryById(id);
        return ResponseEntity.ok(ApiResponse.success(category));
    }
    
    @PostMapping
    @Operation(summary = "创建分类", description = "创建新分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-category:create')")
    public ResponseEntity<ApiResponse<ProductCategoryDTO>> createCategory(@Valid @RequestBody CreateCategoryRequest request) {
        log.info("Creating category with request: {}", request);
        ProductCategoryDTO category = categoryService.createCategory(request);
        return ResponseEntity.ok(ApiResponse.success(category));
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新分类", description = "更新分类信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-category:update')")
    public ResponseEntity<ApiResponse<ProductCategoryDTO>> updateCategory(
            @PathVariable String id,
            @Valid @RequestBody UpdateCategoryRequest request) {
        log.info("Updating category with id: {} and request: {}", id, request);
        ProductCategoryDTO category = categoryService.updateCategory(id, request);
        return ResponseEntity.ok(ApiResponse.success(category));
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除分类", description = "删除分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-category:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteCategory(@PathVariable String id) {
        log.info("Deleting category with id: {}", id);
        categoryService.deleteCategory(id);
        return ResponseEntity.ok(ApiResponse.success());
    }
}
