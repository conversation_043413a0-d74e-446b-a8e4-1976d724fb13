package com.czerp.erpbackend.product.repository;

import com.czerp.erpbackend.product.entity.ProductSpec;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 产品规格存储库
 */
@Repository
public interface ProductSpecRepository extends JpaRepository<ProductSpec, String> {
    
    /**
     * 根据编码查找规格
     * @param code 编码
     * @return 规格
     */
    Optional<ProductSpec> findByCode(String code);
    
    /**
     * 判断编码是否存在
     * @param code 编码
     * @return 是否存在
     */
    boolean existsByCode(String code);
}
