package com.czerp.erpbackend.inventory.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 创建入库单明细请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateStockInboundItemRequest {

    /**
     * 来源采购订单明细ID
     */
    private Long purchaseOrderItemId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 收备品数
     */
    private Integer spareQuantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 面积(平米)
     */
    private BigDecimal areaSquareMeters;

    /**
     * 单重
     */
    private BigDecimal unitWeight;

    /**
     * 重量(KG)
     */
    private BigDecimal weightKg;

    /**
     * 每板数
     */
    private Integer quantityPerBoard;

    /**
     * 税率%
     */
    private BigDecimal taxRate;

    /**
     * 币别
     */
    private String currency;

    /**
     * 供应商送货单号
     */
    private String supplierDeliveryNo;

    /**
     * 折度规格
     */
    private String foldingSpecification;

    /**
     * 折算数量
     */
    private BigDecimal conversionQuantity;

    /**
     * 折算单价
     */
    private BigDecimal conversionPrice;

    /**
     * 折算金额
     */
    private BigDecimal conversionAmount;

    /**
     * 备注
     */
    private String remark;
}
