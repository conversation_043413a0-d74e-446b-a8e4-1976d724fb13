-- 销售订单页面数据库表结构

CREATE TABLE sales_order_query (
    id VARCHAR(36) PRIMARY KEY,
    production_order_no VARCHAR(50) COMMENT '生产单号',
    sales_order_no VARCHAR(50) COMMENT '销售单号',
    order_date DATE COMMENT '日期',
    delivery_date DATE COMMENT '交期',
    customer_name VARCHAR(200) COMMENT '客户名称',
    customer_order_no VARCHAR(50) COMMENT '客户订单号',
    customer_product_code VARCHAR(100) COMMENT '客方货号',
    product_name VARCHAR(200) COMMENT '品名',
    process_requirements VARCHAR(500) COMMENT '工艺要求',
    box_type VARCHAR(50) COMMENT '盒式',
    paper_type VARCHAR(100) COMMENT '纸质',
    production_paper_type VARCHAR(100) COMMENT '生产纸质',
    specification VARCHAR(100) COMMENT '规格',
    production_specification VARCHAR(100) COMMENT '生产规格',
    quantity INT COMMENT '数量',
    spare_quantity INT COMMENT '备品数',
    price DECIMAL(18,4) COMMENT '单价',
    amount DECIMAL(18,2) COMMENT '金额',
    is_special_price TINYINT(1) COMMENT '特价',
    production_remark VARCHAR(500) COMMENT '生产备注',
    remark VARCHAR(500) COMMENT '备注',
    process VARCHAR(100) COMMENT '工序',
    connection_method VARCHAR(50) COMMENT '连接方式',
    board_type VARCHAR(50) COMMENT '纸板类别',
    line_size_width DECIMAL(10,2) COMMENT '压线尺寸(纸度)',
    unit VARCHAR(20) COMMENT '单位',
    paper_quotation DECIMAL(18,4) COMMENT '纸质报价',
    currency VARCHAR(20) COMMENT '币别',
    unit_weight DECIMAL(10,4) COMMENT '单重',
    total_weight DECIMAL(18,2) COMMENT '总重(KG)',
    product_area DECIMAL(10,4) COMMENT '产品面积',
    total_area DECIMAL(18,2) COMMENT '总面积(平米)',
    product_volume DECIMAL(10,4) COMMENT '产品体积',
    total_volume DECIMAL(18,2) COMMENT '总体积(立方米)',
    tax_rate DECIMAL(5,2) COMMENT '税率',
    die_cutting_count INT COMMENT '模开数',
    gross_profit DECIMAL(18,2) COMMENT '毛利金额',
    profit_rate DECIMAL(5,2) COMMENT '利润率%',
    receiving_unit VARCHAR(200) COMMENT '收货单位',
    receiver VARCHAR(50) COMMENT '收货人',
    receiver_phone VARCHAR(50) COMMENT '收货人电话',
    receiving_address VARCHAR(500) COMMENT '收货地址',
    sales_person VARCHAR(50) COMMENT '销售员',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME COMMENT '创建时间',
    is_taxed TINYINT(1) COMMENT '是否含税',
    corrugation_type VARCHAR(50) COMMENT '楞别',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单查询表';

-- 创建索引以提高查询性能
CREATE INDEX idx_production_order_no ON sales_order_query(production_order_no);
CREATE INDEX idx_sales_order_no ON sales_order_query(sales_order_no);
CREATE INDEX idx_order_date ON sales_order_query(order_date);
CREATE INDEX idx_customer_name ON sales_order_query(customer_name);
CREATE INDEX idx_customer_order_no ON sales_order_query(customer_order_no);
