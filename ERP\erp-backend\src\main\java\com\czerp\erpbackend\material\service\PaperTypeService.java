package com.czerp.erpbackend.material.service;

import com.czerp.erpbackend.material.dto.PaperTypeDTO;
import com.czerp.erpbackend.material.dto.request.CreatePaperTypeRequest;
import com.czerp.erpbackend.material.dto.request.UpdatePaperTypeRequest;

import java.util.List;

/**
 * 纸质类别服务接口
 */
public interface PaperTypeService {

    /**
     * 查询所有纸质类别
     * @return 纸质类别列表
     */
    List<PaperTypeDTO> findAllPaperTypes();

    /**
     * 根据ID查询纸质类别
     * @param id 纸质类别ID
     * @return 纸质类别信息
     */
    PaperTypeDTO findPaperTypeById(Integer id);

    /**
     * 根据纸板类别名称查询纸质类别
     * @param paperTypeName 纸板类别名称
     * @return 纸质类别信息
     */
    PaperTypeDTO findPaperTypeByName(String paperTypeName);

    /**
     * 创建纸质类别
     * @param request 创建请求
     * @return 纸质类别信息
     */
    PaperTypeDTO createPaperType(CreatePaperTypeRequest request);

    /**
     * 更新纸质类别
     * @param id 纸质类别ID
     * @param request 更新请求
     * @return 纸质类别信息
     */
    PaperTypeDTO updatePaperType(Integer id, UpdatePaperTypeRequest request);

    /**
     * 删除纸质类别
     * @param id 纸质类别ID
     */
    void deletePaperType(Integer id);

    /**
     * 批量删除纸质类别
     * @param ids 纸质类别ID列表
     */
    void batchDeletePaperTypes(List<Integer> ids);
}
