package com.czerp.erpbackend.purchase.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 用于入库管理引用采购订单明细的DTO
 * 包含入库单需要的采购订单明细字段
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderItemForInboundDTO {
    
    /**
     * 采购订单明细ID
     */
    private Long id;
    
    /**
     * 采购订单ID
     */
    private Long purchaseOrderId;
    
    /**
     * 本次入库数 (从采购数量字段中获取，可编辑)
     */
    private Integer inboundQuantity;
    
    /**
     * 入库金额 (对应采购单金额字段)
     */
    private BigDecimal inboundAmount;
    
    /**
     * 供应商名称
     */
    private String supplierName;
    
    /**
     * 采购单号
     */
    private String purchaseOrderNo;
    
    /**
     * 采购日期
     */
    private LocalDate purchaseDate;
    
    /**
     * 采购数量
     */
    private Integer quantity;
    
    /**
     * 纸质
     */
    private String paperQuality;
    
    /**
     * 规格 (对应采购单合订规格字段)
     */
    private String bindingSpecification;
    
    /**
     * 已入库数
     */
    private Integer receivedQuantity;
    
    /**
     * 已退货数
     */
    private Integer returnedQuantity;
    
    /**
     * 未入库数 (动态计算，采购数量-已入库数+已退货数)
     */
    private Integer unreceivedQuantity;
    
    /**
     * 生产单号
     */
    private String productionOrderNo;
    
    /**
     * 客户编码
     */
    private String customerCode;
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 客户订单号
     */
    private String customerOrderNo;
    
    /**
     * 客方货号
     */
    private String customerProductCode;
    
    /**
     * 品名
     */
    private String productName;
    
    /**
     * 工艺要求
     */
    private String processRequirements;
    
    /**
     * 产品 (盒式+订单纸质使用空格分隔)
     */
    private String product;
    
    /**
     * 产品规格
     */
    private String productSpecification;
    
    /**
     * 产品生产规格
     */
    private String productionSpecification;
    
    /**
     * 销售单交期
     */
    private LocalDate salesOrderDeliveryDate;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 纸板类别
     */
    private String paperBoardCategory;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 金额
     */
    private BigDecimal amount;
    
    /**
     * 交期
     */
    private LocalDate deliveryDate;
    
    /**
     * 纸度
     */
    private BigDecimal paperWidth;
    
    /**
     * 纸长
     */
    private BigDecimal paperLength;
    
    /**
     * 楞别
     */
    private String corrugationType;
    
    /**
     * 压线尺寸(纸度)
     */
    private String creasingSize;
    
    /**
     * 压线方式
     */
    private String creasingMethod;
    
    /**
     * 折度规格
     */
    private String foldingSpecification;
    
    /**
     * 长度(米)
     */
    private BigDecimal lengthMeters;
    
    /**
     * 面积(平米)
     */
    private BigDecimal areaSquareMeters;
    
    /**
     * 体积(立方米)
     */
    private BigDecimal volumeCubicMeters;
    
    /**
     * 单重
     */
    private BigDecimal unitWeight;
    
    /**
     * 总重(KG)
     */
    private BigDecimal totalWeightKg;
    
    /**
     * 加工费
     */
    private BigDecimal processingFee;
    
    /**
     * 币种
     */
    private String currency;
    
    /**
     * 备注
     */
    private String remarks;
    
    /**
     * 销售订单明细ID (关联字段)
     */
    private String sourceSalesOrderItemId;
    
    /**
     * 销售单号
     */
    private String salesOrderNo;
    
    /**
     * 销售日期
     */
    private LocalDate salesOrderDate;
    
    /**
     * 盒式
     */
    private String boxType;
    
    /**
     * 订单纸质
     */
    private String orderPaperType;
    
    /**
     * 订单数
     */
    private Integer orderQuantity;
    
    /**
     * 纸板数
     */
    private Integer boardCount;
    
    /**
     * 模开数
     */
    private Integer dieOpenCount;
    
    /**
     * 纸质报价
     */
    private BigDecimal paperQuotation;
    
    /**
     * 折扣
     */
    private BigDecimal discount;
}
