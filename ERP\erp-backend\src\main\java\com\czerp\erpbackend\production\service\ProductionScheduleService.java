package com.czerp.erpbackend.production.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.production.dto.*;

/**
 * 生产排程单服务接口
 */
public interface ProductionScheduleService {

    /**
     * 分页查询生产排程单
     * @param request 查询请求
     * @return 生产排程单分页列表
     */
    PageResponse<ProductionScheduleDTO> findProductionSchedules(ProductionScheduleQueryRequest request);

    /**
     * 分页查询生产排程单明细（按明细行级别分页）
     * @param request 查询请求
     * @return 生产排程单明细分页列表
     */
    PageResponse<ProductionScheduleItemDTO> findProductionScheduleItems(ProductionScheduleQueryRequest request);

    /**
     * 根据ID查询生产排程单
     * @param id 排程单ID
     * @return 生产排程单
     */
    ProductionScheduleDTO findProductionScheduleById(String id);

    /**
     * 创建生产排程单
     * @param request 创建请求
     * @return 生产排程单
     */
    ProductionScheduleDTO createProductionSchedule(CreateProductionScheduleRequest request);

    /**
     * 更新生产排程单
     * @param id 排程单ID
     * @param request 更新请求
     * @return 生产排程单
     */
    ProductionScheduleDTO updateProductionSchedule(String id, UpdateProductionScheduleRequest request);

    /**
     * 删除生产排程单
     * @param id 排程单ID
     */
    void deleteProductionSchedule(String id);

    /**
     * 生成排程单号
     * @return 排程单号
     */
    String generateScheduleNo();

    /**
     * 批量更新打印状态
     * @param itemIds 明细ID列表
     * @param isPrinted 是否已打印
     */
    void updatePrintStatus(java.util.List<String> itemIds, Boolean isPrinted);

    /**
     * 查询可引用的销售订单明细列表（用于生产排程单引用销售订单）
     * @param request 查询请求
     * @return 销售订单明细分页列表（包含完整的引用数据）
     */
    PageResponse<ProductionScheduleItemDTO> findSalesOrderItemsForReference(ProductionScheduleQueryRequest request);
}
