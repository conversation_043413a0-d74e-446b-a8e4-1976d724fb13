package com.czerp.erpbackend.product.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.product.dto.*;

import java.util.List;

/**
 * 货品服务接口
 */
public interface ProductService {

    /**
     * 分页查询货品列表
     * @param request 查询请求
     * @return 货品分页列表
     */
    PageResponse<ProductDTO> findProducts(ProductQueryRequest request);

    /**
     * 根据ID查询货品
     * @param id 货品ID
     * @return 货品信息
     */
    ProductDTO findProductById(String id);

    /**
     * 查询所有启用的货品
     * @return 货品列表
     */
    List<ProductDTO> findActiveProducts();

    /**
     * 创建货品
     * @param request 创建请求
     * @return 货品信息
     */
    ProductDTO createProduct(CreateProductRequest request);

    /**
     * 更新货品
     * @param id 货品ID
     * @param request 更新请求
     * @return 货品信息
     */
    ProductDTO updateProduct(String id, UpdateProductRequest request);

    /**
     * 删除货品
     * @param id 货品ID
     */
    void deleteProduct(String id);

    /**
     * 批量删除货品
     * @param ids 货品ID列表
     */
    void batchDeleteProducts(List<String> ids);

    /**
     * 切换货品状态
     * @param id 货品ID
     * @param disabled 是否禁用
     * @return 货品信息
     */
    ProductDTO toggleProductStatus(String id, boolean disabled);

    /**
     * 批量更新货品价格
     * @param request 更新请求
     */
    void updateProductPrices(UpdateProductPriceRequest request);

    /**
     * 导入货品
     * @param file Excel文件
     * @return 导入结果
     */
    com.czerp.erpbackend.common.dto.ImportResult importProducts(org.springframework.web.multipart.MultipartFile file);
}
