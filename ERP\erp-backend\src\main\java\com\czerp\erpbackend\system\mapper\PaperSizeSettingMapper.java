package com.czerp.erpbackend.system.mapper;

import com.czerp.erpbackend.system.dto.CreatePaperSizeSettingRequest;
import com.czerp.erpbackend.system.dto.PaperSizeSettingDTO;
import com.czerp.erpbackend.system.dto.UpdatePaperSizeSettingRequest;
import com.czerp.erpbackend.system.entity.PaperSizeSetting;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

/**
 * 纸度设置Mapper
 */
@Mapper(componentModel = "spring")
public interface PaperSizeSettingMapper {

    /**
     * 实体转DTO
     * @param entity 实体
     * @return DTO
     */
    PaperSizeSettingDTO toDto(PaperSizeSetting entity);

    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    PaperSizeSetting toEntity(CreatePaperSizeSettingRequest request);

    /**
     * 更新请求更新实体
     * @param request 更新请求
     * @param entity 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntityFromRequest(UpdatePaperSizeSettingRequest request, @MappingTarget PaperSizeSetting entity);
}
