package com.czerp.erpbackend.purchase.service;

import java.util.List;
import java.util.Set;

/**
 * 采购订单明细缓存管理服务接口
 * 提供统一的缓存失效和管理功能
 */
public interface PurchaseOrderItemCacheService {

    /**
     * 清除单个采购订单明细的所有相关缓存
     * 
     * @param purchaseOrderItemId 采购订单明细ID
     */
    void evictPurchaseOrderItemCache(Long purchaseOrderItemId);

    /**
     * 批量清除多个采购订单明细的所有相关缓存
     * 
     * @param purchaseOrderItemIds 采购订单明细ID集合
     */
    void evictPurchaseOrderItemCaches(Set<Long> purchaseOrderItemIds);

    /**
     * 清除单个采购订单明细的已入库数量缓存
     * 
     * @param purchaseOrderItemId 采购订单明细ID
     */
    void evictReceivedQuantityCache(Long purchaseOrderItemId);

    /**
     * 批量清除多个采购订单明细的已入库数量缓存
     * 
     * @param purchaseOrderItemIds 采购订单明细ID集合
     */
    void evictReceivedQuantityCaches(Set<Long> purchaseOrderItemIds);

    /**
     * 清除单个采购订单明细的可用性缓存
     * 
     * @param purchaseOrderItemId 采购订单明细ID
     */
    void evictAvailabilityCache(Long purchaseOrderItemId);

    /**
     * 批量清除多个采购订单明细的可用性缓存
     * 
     * @param purchaseOrderItemIds 采购订单明细ID集合
     */
    void evictAvailabilityCaches(Set<Long> purchaseOrderItemIds);

    /**
     * 清除所有采购订单明细相关缓存
     * 谨慎使用，仅在系统维护或数据修复时使用
     */
    void evictAllPurchaseOrderItemCaches();

    /**
     * 预热缓存 - 为指定的采购订单明细预加载缓存
     * 
     * @param purchaseOrderItemIds 采购订单明细ID集合
     */
    void warmUpCaches(List<Long> purchaseOrderItemIds);

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    CacheStatistics getCacheStatistics();

    /**
     * 缓存统计信息
     */
    class CacheStatistics {
        private long receivedQuantityCacheSize;
        private long availabilityCacheSize;
        private long receivedQuantityHitCount;
        private long receivedQuantityMissCount;
        private long availabilityHitCount;
        private long availabilityMissCount;

        // Getters and Setters
        public long getReceivedQuantityCacheSize() {
            return receivedQuantityCacheSize;
        }

        public void setReceivedQuantityCacheSize(long receivedQuantityCacheSize) {
            this.receivedQuantityCacheSize = receivedQuantityCacheSize;
        }

        public long getAvailabilityCacheSize() {
            return availabilityCacheSize;
        }

        public void setAvailabilityCacheSize(long availabilityCacheSize) {
            this.availabilityCacheSize = availabilityCacheSize;
        }

        public long getReceivedQuantityHitCount() {
            return receivedQuantityHitCount;
        }

        public void setReceivedQuantityHitCount(long receivedQuantityHitCount) {
            this.receivedQuantityHitCount = receivedQuantityHitCount;
        }

        public long getReceivedQuantityMissCount() {
            return receivedQuantityMissCount;
        }

        public void setReceivedQuantityMissCount(long receivedQuantityMissCount) {
            this.receivedQuantityMissCount = receivedQuantityMissCount;
        }

        public long getAvailabilityHitCount() {
            return availabilityHitCount;
        }

        public void setAvailabilityHitCount(long availabilityHitCount) {
            this.availabilityHitCount = availabilityHitCount;
        }

        public long getAvailabilityMissCount() {
            return availabilityMissCount;
        }

        public void setAvailabilityMissCount(long availabilityMissCount) {
            this.availabilityMissCount = availabilityMissCount;
        }

        @Override
        public String toString() {
            return "CacheStatistics{" +
                    "receivedQuantityCacheSize=" + receivedQuantityCacheSize +
                    ", availabilityCacheSize=" + availabilityCacheSize +
                    ", receivedQuantityHitCount=" + receivedQuantityHitCount +
                    ", receivedQuantityMissCount=" + receivedQuantityMissCount +
                    ", availabilityHitCount=" + availabilityHitCount +
                    ", availabilityMissCount=" + availabilityMissCount +
                    '}';
        }
    }
}
