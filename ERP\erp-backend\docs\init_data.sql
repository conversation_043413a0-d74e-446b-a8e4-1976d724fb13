-- 初始化数据脚本
-- 用于初始化系统中的部门、角色、权限等数据

-- 清空现有数据（谨慎使用，确保备份）
-- DELETE FROM sys_user_role;
-- DELETE FROM sys_role_permission;
-- DELETE FROM sys_permission;
-- DELETE FROM sys_role;
-- DELETE FROM sys_department;

-- 设置当前时间
SET @now = NOW();
SET @admin_id = (SELECT id FROM sys_user WHERE username = 'admin' LIMIT 1);

-- 初始化部门数据
INSERT INTO sys_department (id, created_by, created_time, is_deleted, updated_by, updated_time, version, code, level, manager_id, name, parent_id, sort, status) VALUES
('d001', @admin_id, @now, 0, NULL, NULL, 0, 'HQ', 1, @admin_id, '总部', NULL, 1, 'active'),
('d002', @admin_id, @now, 0, NULL, NULL, 0, 'RD', 2, @admin_id, '研发部', 'd001', 1, 'active'),
('d003', @admin_id, @now, 0, NULL, NULL, 0, 'SALES', 2, @admin_id, '销售部', 'd001', 2, 'active'),
('d004', @admin_id, @now, 0, NULL, NULL, 0, 'HR', 2, @admin_id, '人力资源', 'd001', 3, 'active'),
('d005', @admin_id, @now, 0, NULL, NULL, 0, 'FINANCE', 2, @admin_id, '财务部', 'd001', 4, 'active'),
('d006', @admin_id, @now, 0, NULL, NULL, 0, 'FRONTEND', 3, @admin_id, '前端组', 'd002', 1, 'active'),
('d007', @admin_id, @now, 0, NULL, NULL, 0, 'BACKEND', 3, @admin_id, '后端组', 'd002', 2, 'active'),
('d008', @admin_id, @now, 0, NULL, NULL, 0, 'QA', 3, @admin_id, '测试组', 'd002', 3, 'active'),
('d009', @admin_id, @now, 0, NULL, NULL, 0, 'DEVOPS', 3, @admin_id, '运维组', 'd002', 4, 'active');

-- 初始化权限数据
-- 用户管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, updated_by, updated_time, version, code, component, icon, name, parent_id, path, sort, status, type) VALUES
('p001', @admin_id, @now, 0, NULL, NULL, 0, 'user', 'system/user/index', 'user', '用户管理', NULL, '/system/users', 10, 'active', 'menu'),
('p002', @admin_id, @now, 0, NULL, NULL, 0, 'user:list', NULL, NULL, '用户列表', 'p001', NULL, 11, 'active', 'button'),
('p003', @admin_id, @now, 0, NULL, NULL, 0, 'user:read', NULL, NULL, '用户详情', 'p001', NULL, 12, 'active', 'button'),
('p004', @admin_id, @now, 0, NULL, NULL, 0, 'user:create', NULL, NULL, '创建用户', 'p001', NULL, 13, 'active', 'button'),
('p005', @admin_id, @now, 0, NULL, NULL, 0, 'user:update', NULL, NULL, '更新用户', 'p001', NULL, 14, 'active', 'button'),
('p006', @admin_id, @now, 0, NULL, NULL, 0, 'user:delete', NULL, NULL, '删除用户', 'p001', NULL, 15, 'active', 'button');

-- 角色管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, updated_by, updated_time, version, code, component, icon, name, parent_id, path, sort, status, type) VALUES
('p007', @admin_id, @now, 0, NULL, NULL, 0, 'role', 'system/role/index', 'team', '角色管理', NULL, '/system/roles', 20, 'active', 'menu'),
('p008', @admin_id, @now, 0, NULL, NULL, 0, 'role:list', NULL, NULL, '角色列表', 'p007', NULL, 21, 'active', 'button'),
('p009', @admin_id, @now, 0, NULL, NULL, 0, 'role:read', NULL, NULL, '角色详情', 'p007', NULL, 22, 'active', 'button'),
('p010', @admin_id, @now, 0, NULL, NULL, 0, 'role:create', NULL, NULL, '创建角色', 'p007', NULL, 23, 'active', 'button'),
('p011', @admin_id, @now, 0, NULL, NULL, 0, 'role:update', NULL, NULL, '更新角色', 'p007', NULL, 24, 'active', 'button'),
('p012', @admin_id, @now, 0, NULL, NULL, 0, 'role:delete', NULL, NULL, '删除角色', 'p007', NULL, 25, 'active', 'button');

-- 权限管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, updated_by, updated_time, version, code, component, icon, name, parent_id, path, sort, status, type) VALUES
('p013', @admin_id, @now, 0, NULL, NULL, 0, 'permission', 'system/permission/index', 'safety', '权限管理', NULL, '/system/permissions', 30, 'active', 'menu'),
('p014', @admin_id, @now, 0, NULL, NULL, 0, 'permission:list', NULL, NULL, '权限列表', 'p013', NULL, 31, 'active', 'button'),
('p015', @admin_id, @now, 0, NULL, NULL, 0, 'permission:read', NULL, NULL, '权限详情', 'p013', NULL, 32, 'active', 'button');

-- 部门管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, updated_by, updated_time, version, code, component, icon, name, parent_id, path, sort, status, type) VALUES
('p016', @admin_id, @now, 0, NULL, NULL, 0, 'department', 'system/department/index', 'apartment', '部门管理', NULL, '/system/departments', 40, 'active', 'menu'),
('p017', @admin_id, @now, 0, NULL, NULL, 0, 'department:list', NULL, NULL, '部门列表', 'p016', NULL, 41, 'active', 'button'),
('p018', @admin_id, @now, 0, NULL, NULL, 0, 'department:read', NULL, NULL, '部门详情', 'p016', NULL, 42, 'active', 'button'),
('p019', @admin_id, @now, 0, NULL, NULL, 0, 'department:create', NULL, NULL, '创建部门', 'p016', NULL, 43, 'active', 'button'),
('p020', @admin_id, @now, 0, NULL, NULL, 0, 'department:update', NULL, NULL, '更新部门', 'p016', NULL, 44, 'active', 'button'),
('p021', @admin_id, @now, 0, NULL, NULL, 0, 'department:delete', NULL, NULL, '删除部门', 'p016', NULL, 45, 'active', 'button');

-- 初始化角色数据
INSERT INTO sys_role (id, created_by, created_time, is_deleted, updated_by, updated_time, version, code, description, name, status) VALUES
('r001', @admin_id, @now, 0, NULL, NULL, 0, 'admin', '系统管理员，拥有所有权限', '管理员', 'active'),
('r002', @admin_id, @now, 0, NULL, NULL, 0, 'user', '普通用户，拥有基本权限', '普通用户', 'active'),
('r003', @admin_id, @now, 0, NULL, NULL, 0, 'hr', '人力资源，负责人员管理', '人力资源', 'active'),
('r004', @admin_id, @now, 0, NULL, NULL, 0, 'finance', '财务人员，负责财务管理', '财务人员', 'active');

-- 为管理员角色分配所有权限
INSERT INTO sys_role_permission (id, create_by, create_time, permission_id, role_id) VALUES
('rp001', @admin_id, @now, 'p001', 'r001'),
('rp002', @admin_id, @now, 'p002', 'r001'),
('rp003', @admin_id, @now, 'p003', 'r001'),
('rp004', @admin_id, @now, 'p004', 'r001'),
('rp005', @admin_id, @now, 'p005', 'r001'),
('rp006', @admin_id, @now, 'p006', 'r001'),
('rp007', @admin_id, @now, 'p007', 'r001'),
('rp008', @admin_id, @now, 'p008', 'r001'),
('rp009', @admin_id, @now, 'p009', 'r001'),
('rp010', @admin_id, @now, 'p010', 'r001'),
('rp011', @admin_id, @now, 'p011', 'r001'),
('rp012', @admin_id, @now, 'p012', 'r001'),
('rp013', @admin_id, @now, 'p013', 'r001'),
('rp014', @admin_id, @now, 'p014', 'r001'),
('rp015', @admin_id, @now, 'p015', 'r001'),
('rp016', @admin_id, @now, 'p016', 'r001'),
('rp017', @admin_id, @now, 'p017', 'r001'),
('rp018', @admin_id, @now, 'p018', 'r001'),
('rp019', @admin_id, @now, 'p019', 'r001'),
('rp020', @admin_id, @now, 'p020', 'r001'),
('rp021', @admin_id, @now, 'p021', 'r001');

-- 为普通用户角色分配基本权限
INSERT INTO sys_role_permission (id, create_by, create_time, permission_id, role_id) VALUES
('rp022', @admin_id, @now, 'p002', 'r002'), -- 用户列表
('rp023', @admin_id, @now, 'p003', 'r002'), -- 用户详情
('rp024', @admin_id, @now, 'p008', 'r002'), -- 角色列表
('rp025', @admin_id, @now, 'p009', 'r002'), -- 角色详情
('rp026', @admin_id, @now, 'p017', 'r002'); -- 部门列表

-- 为人力资源角色分配权限
INSERT INTO sys_role_permission (id, create_by, create_time, permission_id, role_id) VALUES
('rp027', @admin_id, @now, 'p001', 'r003'), -- 用户管理
('rp028', @admin_id, @now, 'p002', 'r003'), -- 用户列表
('rp029', @admin_id, @now, 'p003', 'r003'), -- 用户详情
('rp030', @admin_id, @now, 'p004', 'r003'), -- 创建用户
('rp031', @admin_id, @now, 'p005', 'r003'), -- 更新用户
('rp032', @admin_id, @now, 'p016', 'r003'), -- 部门管理
('rp033', @admin_id, @now, 'p017', 'r003'), -- 部门列表
('rp034', @admin_id, @now, 'p018', 'r003'); -- 部门详情

-- 为财务人员角色分配权限
INSERT INTO sys_role_permission (id, create_by, create_time, permission_id, role_id) VALUES
('rp035', @admin_id, @now, 'p002', 'r004'), -- 用户列表
('rp036', @admin_id, @now, 'p003', 'r004'), -- 用户详情
('rp037', @admin_id, @now, 'p017', 'r004'); -- 部门列表

-- 为管理员用户分配管理员角色
INSERT INTO sys_user_role (id, create_by, create_time, role_id, user_id) VALUES
('ur001', @admin_id, @now, 'r001', @admin_id);

-- 创建测试用户
INSERT INTO sys_user (id, created_by, created_time, is_deleted, updated_by, updated_time, version, avatar, department_id, email, last_login_time, nickname, password, phone, position, status, username) VALUES
('u002', @admin_id, @now, 0, NULL, NULL, 0, NULL, 'd004', '<EMAIL>', NULL, '人力资源', '$2a$10$ySG2lkvjFHY5O0./CPIE1OI8VJsuKYEzOYzqIa7AJR6sEgSzUFOAm', '13900000001', 'HR Manager', 'active', 'hr'),
('u003', @admin_id, @now, 0, NULL, NULL, 0, NULL, 'd005', '<EMAIL>', NULL, '财务人员', '$2a$10$ySG2lkvjFHY5O0./CPIE1OI8VJsuKYEzOYzqIa7AJR6sEgSzUFOAm', '13900000002', 'Finance Manager', 'active', 'finance'),
('u004', @admin_id, @now, 0, NULL, NULL, 0, NULL, 'd002', '<EMAIL>', NULL, '开发人员', '$2a$10$ySG2lkvjFHY5O0./CPIE1OI8VJsuKYEzOYzqIa7AJR6sEgSzUFOAm', '13900000003', 'Developer', 'active', 'dev');

-- 为测试用户分配角色
INSERT INTO sys_user_role (id, create_by, create_time, role_id, user_id) VALUES
('ur002', @admin_id, @now, 'r003', 'u002'), -- HR用户分配HR角色
('ur003', @admin_id, @now, 'r004', 'u003'), -- 财务用户分配财务角色
('ur004', @admin_id, @now, 'r002', 'u004'); -- 开发用户分配普通用户角色
