package com.czerp.erpbackend.warehouse.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.warehouse.dto.CreateWarehouseRequest;
import com.czerp.erpbackend.warehouse.dto.UpdateWarehouseRequest;
import com.czerp.erpbackend.warehouse.dto.WarehouseDTO;
import com.czerp.erpbackend.warehouse.dto.WarehouseQueryRequest;
import com.czerp.erpbackend.warehouse.service.WarehouseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 仓库控制器
 */
@RestController
@RequestMapping("/warehouses")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Warehouse Management", description = "仓库管理相关接口")
public class WarehouseController {

    private final WarehouseService warehouseService;

    /**
     * 分页查询仓库列表
     */
    @GetMapping
    @Operation(summary = "分页查询仓库列表", description = "根据条件分页查询仓库列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('warehouse:read')")
    public ResponseEntity<ApiResponse<PageResponse<WarehouseDTO>>> getWarehouses(
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "备料仓标识") @RequestParam(required = false) Boolean isMaterialWarehouse,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdTime") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDirection) {
        
        log.info("Getting warehouses with keyword: {}, isMaterialWarehouse: {}, page: {}, size: {}", 
                keyword, isMaterialWarehouse, page, size);

        WarehouseQueryRequest request = WarehouseQueryRequest.builder()
                .keyword(keyword)
                .isMaterialWarehouse(isMaterialWarehouse)
                .page(page)
                .size(size)
                .sortBy(sortBy)
                .sortDirection(sortDirection)
                .build();

        PageResponse<WarehouseDTO> result = warehouseService.findWarehouses(request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    /**
     * 获取所有仓库列表（不分页）
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有仓库列表", description = "获取所有仓库列表，不分页")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('warehouse:read')")
    public ResponseEntity<ApiResponse<List<WarehouseDTO>>> getAllWarehouses() {
        log.info("Getting all warehouses");
        List<WarehouseDTO> result = warehouseService.findAllWarehouses();
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    /**
     * 根据ID查询仓库详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询仓库详情", description = "根据ID查询仓库详情")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('warehouse:read')")
    public ResponseEntity<ApiResponse<WarehouseDTO>> getWarehouse(@PathVariable Long id) {
        log.info("Getting warehouse with id: {}", id);
        WarehouseDTO result = warehouseService.findWarehouseById(id);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    /**
     * 创建仓库
     */
    @PostMapping
    @Operation(summary = "创建仓库", description = "创建新的仓库")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('warehouse:create')")
    public ResponseEntity<ApiResponse<WarehouseDTO>> createWarehouse(@Valid @RequestBody CreateWarehouseRequest request) {
        log.info("Creating warehouse with request: {}", request);
        WarehouseDTO result = warehouseService.createWarehouse(request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    /**
     * 更新仓库
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新仓库", description = "根据ID更新仓库信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('warehouse:update')")
    public ResponseEntity<ApiResponse<WarehouseDTO>> updateWarehouse(
            @PathVariable Long id,
            @Valid @RequestBody UpdateWarehouseRequest request) {
        log.info("Updating warehouse with id: {} and request: {}", id, request);
        WarehouseDTO result = warehouseService.updateWarehouse(id, request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    /**
     * 删除仓库
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除仓库", description = "根据ID删除仓库")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('warehouse:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteWarehouse(@PathVariable Long id) {
        log.info("Deleting warehouse with id: {}", id);
        warehouseService.deleteWarehouse(id);
        return ResponseEntity.ok(ApiResponse.success());
    }

    /**
     * 批量删除仓库
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除仓库", description = "根据ID列表批量删除仓库")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('warehouse:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteWarehouses(@RequestBody List<Long> ids) {
        log.info("Batch deleting warehouses with ids: {}", ids);
        warehouseService.deleteWarehouses(ids);
        return ResponseEntity.ok(ApiResponse.success());
    }

    /**
     * 检查仓库名称是否存在
     */
    @GetMapping("/check-name")
    @Operation(summary = "检查仓库名称", description = "检查仓库名称是否已存在")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('warehouse:read')")
    public ResponseEntity<ApiResponse<Boolean>> checkWarehouseName(
            @Parameter(description = "仓库名称") @RequestParam String warehouseName,
            @Parameter(description = "排除的ID") @RequestParam(required = false) Long excludeId) {
        log.info("Checking warehouse name: {}, excludeId: {}", warehouseName, excludeId);
        
        boolean exists;
        if (excludeId != null) {
            exists = warehouseService.existsByWarehouseName(warehouseName, excludeId);
        } else {
            exists = warehouseService.existsByWarehouseName(warehouseName);
        }
        
        return ResponseEntity.ok(ApiResponse.success(exists));
    }
}
