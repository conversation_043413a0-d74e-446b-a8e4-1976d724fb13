package com.czerp.erpbackend.inventory.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 库存管理权限初始化器
 * 负责初始化库存管理模块的权限并分配给管理员角色
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(15) // 在系统权限初始化之后执行
public class InventoryPermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing inventory permissions...");

        // 初始化库存管理模块权限
        initInventoryPermissions();

        // 为管理员角色分配库存管理模块权限
        assignPermissionsToAdminRole();

        log.info("Inventory permissions initialized successfully");
    }

    /**
     * 初始化库存管理模块权限
     */
    private void initInventoryPermissions() {
        log.info("Initializing inventory module permissions...");

        // 检查库存管理模块是否已存在
        if (permissionRepository.existsByCode("inventory")) {
            log.info("Inventory module already exists, skipping...");
            return;
        }

        // 创建库存管理模块菜单
        String inventoryModuleId = createPermission("库存管理", "inventory", "MENU", null,
                "/inventory", "inventory/index", "warehouse", 40);

        // 创建入库管理子模块
        String inboundModuleId = createPermission("入库管理", "inventory:inbound", "MENU", inventoryModuleId,
                "/inventory/inbound", "inventory/inbound/index", "inbox", 10);

        // 创建入库管理按钮权限
        createPermission("入库单列表", "inventory:inbound:list", "BUTTON", inboundModuleId, null, null, null, 11);
        createPermission("入库单详情", "inventory:inbound:read", "BUTTON", inboundModuleId, null, null, null, 12);
        createPermission("创建入库单", "inventory:inbound:create", "BUTTON", inboundModuleId, null, null, null, 13);
        createPermission("更新入库单", "inventory:inbound:update", "BUTTON", inboundModuleId, null, null, null, 14);
        createPermission("删除入库单", "inventory:inbound:delete", "BUTTON", inboundModuleId, null, null, null, 15);

        log.info("Inventory module permissions created successfully");
    }

    /**
     * 为管理员角色分配库存管理模块权限
     */
    private void assignPermissionsToAdminRole() {
        log.info("Assigning inventory permissions to admin role...");

        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping inventory permissions assignment");
            return;
        }

        // 获取所有库存管理相关权限
        List<String> permissionCodes = Arrays.asList(
                // 库存管理模块
                "inventory",
                // 入库管理
                "inventory:inbound", "inventory:inbound:list", "inventory:inbound:read",
                "inventory:inbound:create", "inventory:inbound:update", "inventory:inbound:delete"
        );

        List<Permission> permissions = permissionRepository.findByCodeIn(permissionCodes);

        // 为管理员角色分配权限
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : permissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString());
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermission.setCreateBy("system");
                rolePermission.setCreateTime(LocalDateTime.now());
                rolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }

        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} inventory permissions to admin role", rolePermissions.size());
        } else {
            log.info("All inventory permissions already assigned to admin role");
        }
    }

    /**
     * 创建权限
     */
    private String createPermission(String name, String code, String type, String parentId,
                                    String path, String component, String icon, Integer sort) {
        log.info("Creating permission: {} ({})", name, code);

        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setCode(code);
        permission.setName(name);
        permission.setType(type);
        permission.setParentId(parentId);
        permission.setPath(path);
        permission.setComponent(component);
        permission.setIcon(icon);
        permission.setSort(sort);
        permission.setStatus("active");
        permission.setCreatedBy("system");
        permission.setCreatedTime(LocalDateTime.now());
        permission.setIsDeleted(false);

        Permission savedPermission = permissionRepository.save(permission);
        log.info("Permission created: {} with ID: {}", code, savedPermission.getId());

        return savedPermission.getId();
    }

    /**
     * 根据权限代码查找权限ID
     */
    private String findPermissionIdByCode(String code) {
        return permissionRepository.findByCode(code)
                .map(Permission::getId)
                .orElse(null);
    }
}
