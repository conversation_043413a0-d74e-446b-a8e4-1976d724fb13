# 退出登录接口使用说明

## 接口概述

退出登录接口用于使当前用户的令牌失效，防止令牌被继续使用。系统使用令牌黑名单机制来实现这一功能。

## 接口详情

- **URL**: `/api/auth/logout`
- **Method**: `POST`
- **请求头**: `Authorization: Bearer {token}`
- **请求体**:
  ```json
  {
    "token": "eyJhbGciOiJIUzI1NiJ9..."  // JWT令牌
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "code": "200",
    "message": "操作成功",
    "data": null
  }
  ```

## 实现原理

1. 用户调用退出登录接口，提供当前的JWT令牌
2. 后端将令牌添加到黑名单中，并记录令牌的过期时间
3. 后端清除当前的安全上下文
4. 当用户再次使用该令牌访问需要认证的接口时，JWT过滤器会检查令牌是否在黑名单中
5. 如果令牌在黑名单中，请求会被拒绝，返回401状态码
6. 系统会定期清理黑名单中已过期的令牌

## 前端实现示例

```typescript
// 退出登录函数
async function logout() {
  try {
    // 获取当前令牌
    const token = localStorage.getItem('token');
    
    if (!token) {
      console.warn('No token found, user is already logged out');
      return true;
    }
    
    // 调用退出登录接口
    const response = await axios.post('/api/auth/logout', { token }, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    // 清除本地存储的认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    
    // 清除状态管理中的用户信息
    // 例如，如果使用Pinia:
    // userStore.clearUserState();
    
    console.log('Logout successful');
    return true;
  } catch (error) {
    console.error('Logout failed:', error);
    return false;
  }
}
```

## 注意事项

1. 前端在调用退出登录接口后，应该清除本地存储的所有认证信息
2. 前端应该将用户重定向到登录页面
3. 如果退出登录接口调用失败，前端仍然应该清除本地存储的认证信息
4. 在分布式环境中，令牌黑名单应该使用Redis等分布式缓存来实现

## 安全建议

1. 使用HTTPS协议保护API通信
2. 设置合理的令牌过期时间
3. 实现令牌刷新机制，减少长期令牌的使用
4. 在敏感操作前验证用户身份
5. 监控异常的登录和退出行为
