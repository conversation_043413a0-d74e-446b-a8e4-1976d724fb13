package com.czerp.erpbackend.common.dto;

import lombok.Data;
import org.springframework.data.domain.Sort;

/**
 * 分页请求
 */
@Data
public class PageRequest {
    
    /**
     * 页码（从0开始）
     */
    private Integer page = 0;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方向
     */
    private String sortDirection;
    
    /**
     * 转换为Spring Data的分页请求
     * @return org.springframework.data.domain.PageRequest
     */
    public org.springframework.data.domain.PageRequest toPageRequest() {
        if (sortField != null && !sortField.isEmpty()) {
            Sort.Direction direction = Sort.Direction.ASC;
            if (sortDirection != null && sortDirection.equalsIgnoreCase("desc")) {
                direction = Sort.Direction.DESC;
            }
            return org.springframework.data.domain.PageRequest.of(page, size, Sort.by(direction, sortField));
        }
        return org.springframework.data.domain.PageRequest.of(page, size);
    }
}
