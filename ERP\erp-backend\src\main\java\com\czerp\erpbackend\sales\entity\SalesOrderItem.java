package com.czerp.erpbackend.sales.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 销售订单明细实体
 */
@Entity
@Table(name = "sales_order_item")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderItem extends BaseEntity {

    /**
     * 创建人姓名
     */
    @Column(name = "created_by_name", length = 50)
    private String createdByName;

    /**
     * 更新人姓名
     */
    @Column(name = "updated_by_name", length = 50)
    private String updatedByName;

    /**
     * 明细ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 订单ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private SalesOrder order;

    /**
     * 生产单号
     */
    @Column(name = "production_order_no", length = 50)
    private String productionOrderNo;

    /**
     * 客户订单号
     */
    @Column(name = "customer_order_no", length = 50)
    private String customerOrderNo;

    /**
     * 客方货号
     */
    @Column(name = "customer_product_code", length = 100)
    private String customerProductCode;

    /**
     * 品名
     */
    @Column(name = "product_name", length = 200)
    private String productName;

    /**
     * 工艺要求
     */
    @Column(name = "process_requirements", length = 500)
    private String processRequirements;

    /**
     * 是否含税
     */
    @Column(name = "is_taxed")
    private Boolean isTaxed;

    /**
     * 盒式
     */
    @Column(name = "box_type", length = 50)
    private String boxType;

    /**
     * 纸质
     */
    @Column(name = "paper_type", length = 100)
    private String paperType;

    /**
     * 楞别
     */
    @Column(name = "corrugation_type", length = 50)
    private String corrugationType;

    /**
     * 生产纸质
     */
    @Column(name = "production_paper_type", length = 100)
    private String productionPaperType;

    /**
     * 长
     */
    @Column(name = "length", precision = 10, scale = 2)
    private BigDecimal length;

    /**
     * 宽
     */
    @Column(name = "width", precision = 10, scale = 2)
    private BigDecimal width;

    /**
     * 高
     */
    @Column(name = "height", precision = 10, scale = 2)
    private BigDecimal height;

    /**
     * 尺寸单位
     */
    @Column(name = "size_unit", length = 10)
    private String sizeUnit;

    /**
     * 数量
     */
    @Column(name = "quantity")
    private Integer quantity;

    /**
     * 备品数
     */
    @Column(name = "spare_quantity")
    private Integer spareQuantity;

    /**
     * 单价
     */
    @Column(name = "price", precision = 18, scale = 4)
    private BigDecimal price;

    /**
     * 金额
     */
    @Column(name = "amount", precision = 18, scale = 2)
    private BigDecimal amount;

    /**
     * 送货日期
     */
    @Column(name = "delivery_date")
    private LocalDate deliveryDate;

    /**
     * 特价
     */
    @Column(name = "is_special_price")
    private Boolean isSpecialPrice;

    /**
     * 纸质报价
     */
    @Column(name = "paper_quotation", precision = 18, scale = 4)
    private BigDecimal paperQuotation;

    /**
     * 连接方式
     */
    @Column(name = "connection_method", length = 50)
    private String connectionMethod;

    /**
     * 钉口
     */
    @Column(name = "staple_position", length = 50)
    private String staplePosition;

    /**
     * 包装数
     */
    @Column(name = "packaging_count")
    private Integer packagingCount;

    /**
     * 生产备注
     */
    @Column(name = "production_remark", length = 500)
    private String productionRemark;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 生产长
     */
    @Column(name = "production_length", precision = 10, scale = 2)
    private BigDecimal productionLength;

    /**
     * 生产宽
     */
    @Column(name = "production_width", precision = 10, scale = 2)
    private BigDecimal productionWidth;

    /**
     * 生产高
     */
    @Column(name = "production_height", precision = 10, scale = 2)
    private BigDecimal productionHeight;

    /**
     * 成套比例
     */
    @Column(name = "spare_ratio", precision = 10, scale = 4)
    private BigDecimal spareRatio;

    /**
     * 成套数量
     */
    @Column(name = "spare_quantity_total")
    private Integer spareQuantityTotal;

    /**
     * 当前库存
     */
    @Column(name = "current_inventory")
    private Integer currentInventory;

    /**
     * 可用库存
     */
    @Column(name = "available_inventory")
    private Integer availableInventory;

    /**
     * 使用库存
     */
    @Column(name = "use_inventory")
    private Integer useInventory;

    /**
     * 特殊规格
     */
    @Column(name = "is_special_specification")
    private Boolean isSpecialSpecification;

    /**
     * 单重
     */
    @Column(name = "unit_weight", precision = 10, scale = 4)
    private BigDecimal unitWeight;

    /**
     * 总重(KG)
     */
    @Column(name = "total_weight", precision = 18, scale = 2)
    private BigDecimal totalWeight;

    /**
     * 产品面积
     */
    @Column(name = "product_area", precision = 10, scale = 4)
    private BigDecimal productArea;

    /**
     * 总面积(平米)
     */
    @Column(name = "total_area", precision = 18, scale = 2)
    private BigDecimal totalArea;

    /**
     * 产品体积
     */
    @Column(name = "product_volume", precision = 10, scale = 4)
    private BigDecimal productVolume;

    /**
     * 总体积(立方米)
     */
    @Column(name = "total_volume", precision = 18, scale = 2)
    private BigDecimal totalVolume;

    /**
     * 部件
     */
    @Column(name = "component", length = 100)
    private String component;

    /**
     * 样品
     */
    @Column(name = "is_sample")
    private Boolean isSample;

    /**
     * 已送货数
     */
    @Column(name = "delivered_quantity")
    private Integer deliveredQuantity;

    /**
     * 已送备品数
     */
    @Column(name = "delivered_spare_quantity")
    private Integer deliveredSpareQuantity;

    /**
     * 退货数
     */
    @Column(name = "returned_quantity")
    private Integer returnedQuantity;

    /**
     * 安全库存数
     */
    @Column(name = "safety_stock")
    private Integer safetyStock;

    /**
     * 已对账数
     */
    @Column(name = "reconciliation_quantity")
    private Integer reconciliationQuantity;

    /**
     * 单位
     */
    @Column(name = "unit", length = 20)
    private String unit;

    /**
     * 币种
     */
    @Column(name = "currency", length = 20)
    private String currency;

    /**
     * 税率
     */
    @Column(name = "tax_rate", precision = 5, scale = 2)
    private BigDecimal taxRate;

    /**
     * 采购状态（NOT_PURCHASED: 未采购, PARTIALLY_PURCHASED: 部分采购, FULLY_PURCHASED: 完全采购）
     */
    @Column(name = "purchase_status", length = 20)
    private String purchaseStatus = "NOT_PURCHASED";

    /**
     * 已采购数量
     */
    @Column(name = "purchased_quantity")
    private Integer purchasedQuantity = 0;
}
