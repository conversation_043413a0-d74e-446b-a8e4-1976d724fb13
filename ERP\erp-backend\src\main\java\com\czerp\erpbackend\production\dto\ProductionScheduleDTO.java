package com.czerp.erpbackend.production.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 生产排程单DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductionScheduleDTO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 排程单号
     */
    private String scheduleNo;

    /**
     * 排程日期
     */
    private LocalDate scheduleDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 生产排程明细列表
     */
    private List<ProductionScheduleItemDTO> items;
}
