package com.czerp.erpbackend.purchase.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemForInboundDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemForInboundQueryRequest;

import java.util.List;

/**
 * 用于入库管理引用采购订单的服务接口
 */
public interface PurchaseOrderForInboundService {
    
    /**
     * 分页查询可用于入库的采购订单明细
     * @param request 查询请求
     * @return 采购订单明细分页列表
     */
    PageResponse<PurchaseOrderItemForInboundDTO> findPurchaseOrderItemsForInbound(PurchaseOrderItemForInboundQueryRequest request);
    
    /**
     * 根据ID查询采购订单明细
     * @param purchaseOrderItemId 采购订单明细ID
     * @return 采购订单明细
     */
    PurchaseOrderItemForInboundDTO findPurchaseOrderItemById(Long purchaseOrderItemId);
    
    /**
     * 根据多个ID查询采购订单明细
     * @param purchaseOrderItemIds 采购订单明细ID列表
     * @return 采购订单明细列表
     */
    List<PurchaseOrderItemForInboundDTO> findPurchaseOrderItemsByIds(List<Long> purchaseOrderItemIds);
}
