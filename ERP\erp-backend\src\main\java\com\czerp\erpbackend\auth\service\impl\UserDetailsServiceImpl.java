package com.czerp.erpbackend.auth.service.impl;

import com.czerp.erpbackend.system.entity.User;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.UserRepository;
import com.czerp.erpbackend.system.repository.UserRoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserRepository userRepository;
    private final UserRoleRepository userRoleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + username));

        // 获取用户角色和权限
        List<GrantedAuthority> authorities = new ArrayList<>();

        try {
            // 获取用户角色
            List<String> roleCodes = userRoleRepository.findRoleCodesByUserId(user.getId());
            log.debug("User {} has roles: {}", username, roleCodes);

            // 将角色添加为权限（带ROLE_前缀）
            if (roleCodes != null && !roleCodes.isEmpty()) {
                List<SimpleGrantedAuthority> roleAuthorities = roleCodes.stream()
                        .map(roleCode -> new SimpleGrantedAuthority("ROLE_" + roleCode))
                        .collect(Collectors.toList());
                authorities.addAll(roleAuthorities);

                // 获取用户权限
                List<String> permissionCodes = rolePermissionRepository.findPermissionCodesByRoleCodesIn(roleCodes);
                log.debug("User {} has permissions: {}", username, permissionCodes);

                // 将权限添加为权限（不带前缀）
                if (permissionCodes != null && !permissionCodes.isEmpty()) {
                    List<SimpleGrantedAuthority> permissionAuthorities = permissionCodes.stream()
                            .map(SimpleGrantedAuthority::new)
                            .collect(Collectors.toList());
                    authorities.addAll(permissionAuthorities);
                }
            }
        } catch (Exception e) {
            log.error("Error loading user authorities for user: {}", username, e);
            // 添加一个默认角色，确保用户至少有一个权限
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        }

        // 如果用户没有任何权限，添加一个默认角色
        if (authorities.isEmpty()) {
            log.warn("User {} has no authorities, adding default ROLE_USER", username);
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        }

        // Ensure user status is considered (e.g., locked or inactive users should not be able to log in)
        boolean accountNonLocked = true; // Assuming status check needs implementation
        boolean credentialsNonExpired = true;
        boolean accountNonExpired = true;
        // 检查用户状态，只有active状态的用户才能登录
        boolean enabled = user.getStatus() != null && user.getStatus().equalsIgnoreCase("active");

        return new org.springframework.security.core.userdetails.User(
                user.getUsername(),
                user.getPassword(), // Assuming password in DB is already encoded
                enabled, // isEnabled
                accountNonExpired, // isAccountNonExpired
                credentialsNonExpired, // isCredentialsNonExpired
                accountNonLocked, // isAccountNonLocked
                authorities);
    }
}