package com.czerp.erpbackend.warehouse.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.warehouse.dto.CreateWarehouseRequest;
import com.czerp.erpbackend.warehouse.dto.UpdateWarehouseRequest;
import com.czerp.erpbackend.warehouse.dto.WarehouseDTO;
import com.czerp.erpbackend.warehouse.dto.WarehouseQueryRequest;

import java.util.List;

/**
 * 仓库服务接口
 */
public interface WarehouseService {

    /**
     * 分页查询仓库列表
     * @param request 查询请求
     * @return 仓库分页列表
     */
    PageResponse<WarehouseDTO> findWarehouses(WarehouseQueryRequest request);

    /**
     * 获取所有仓库列表（不分页）
     * @return 仓库列表
     */
    List<WarehouseDTO> findAllWarehouses();

    /**
     * 根据ID查询仓库
     * @param id 仓库ID
     * @return 仓库信息
     */
    WarehouseDTO findWarehouseById(Long id);

    /**
     * 创建仓库
     * @param request 创建仓库请求
     * @return 仓库信息
     */
    WarehouseDTO createWarehouse(CreateWarehouseRequest request);

    /**
     * 更新仓库
     * @param id 仓库ID
     * @param request 更新仓库请求
     * @return 仓库信息
     */
    WarehouseDTO updateWarehouse(Long id, UpdateWarehouseRequest request);

    /**
     * 删除仓库
     * @param id 仓库ID
     */
    void deleteWarehouse(Long id);

    /**
     * 批量删除仓库
     * @param ids 仓库ID列表
     */
    void deleteWarehouses(List<Long> ids);

    /**
     * 检查仓库名称是否存在
     * @param warehouseName 仓库名称
     * @return 是否存在
     */
    boolean existsByWarehouseName(String warehouseName);

    /**
     * 检查仓库名称是否存在（排除指定ID）
     * @param warehouseName 仓库名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsByWarehouseName(String warehouseName, Long excludeId);
}
