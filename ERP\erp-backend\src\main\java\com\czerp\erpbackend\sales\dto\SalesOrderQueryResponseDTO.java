package com.czerp.erpbackend.sales.dto;

import com.czerp.erpbackend.common.dto.PageResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 销售订单查询响应DTO
 * 扩展PageResponse以支持筛选选项和统计信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderQueryResponseDTO {
    
    /**
     * 分页数据
     */
    private PageResponse<SalesOrderQueryResultDTO> pageData;
    
    /**
     * 筛选选项数据
     * key: 字段名称
     * value: 该字段的筛选选项列表
     */
    private Map<String, List<FilterOptionDTO>> filterOptions;
    
    /**
     * 筛选统计信息
     */
    private FilterStatsDTO filterStats;
    
    /**
     * 筛选统计信息DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterStatsDTO {
        /**
         * 总记录数（未筛选）
         */
        private Long totalRecords;
        
        /**
         * 筛选后记录数
         */
        private Long filteredRecords;
        
        /**
         * 活跃筛选条件数
         */
        private Integer activeFilterCount;
    }
    
    /**
     * 创建简单响应（不包含筛选选项）
     * @param pageData 分页数据
     * @return 查询响应
     */
    public static SalesOrderQueryResponseDTO simple(PageResponse<SalesOrderQueryResultDTO> pageData) {
        return SalesOrderQueryResponseDTO.builder()
                .pageData(pageData)
                .build();
    }
    
    /**
     * 创建完整响应（包含筛选选项）
     * @param pageData 分页数据
     * @param filterOptions 筛选选项
     * @param filterStats 筛选统计
     * @return 查询响应
     */
    public static SalesOrderQueryResponseDTO full(
            PageResponse<SalesOrderQueryResultDTO> pageData,
            Map<String, List<FilterOptionDTO>> filterOptions,
            FilterStatsDTO filterStats) {
        return SalesOrderQueryResponseDTO.builder()
                .pageData(pageData)
                .filterOptions(filterOptions)
                .filterStats(filterStats)
                .build();
    }
}
