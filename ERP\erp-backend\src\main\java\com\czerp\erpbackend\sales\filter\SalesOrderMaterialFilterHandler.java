package com.czerp.erpbackend.sales.filter;

import com.czerp.erpbackend.common.filter.dto.FilterOptionDTO;
import com.czerp.erpbackend.common.filter.dto.FilterRequest;
import com.czerp.erpbackend.common.filter.handler.FilterHandler;
import com.czerp.erpbackend.common.filter.metadata.FilterFieldMetadata;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售订单用料筛选处理器
 * 处理用料相关的筛选字段：pressSizeWidth
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SalesOrderMaterialFilterHandler implements FilterHandler {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    public List<FilterOptionDTO> getFilterOptions(FilterRequest request, FilterFieldMetadata metadata) {
        log.debug("Getting material filter options for field: {}", request.getFieldName());
        
        try {
            switch (request.getFieldName()) {
                case "pressSizeWidth":
                    return getPressSizeWidthOptions(request);
                default:
                    log.warn("Unsupported material field: {}", request.getFieldName());
                    return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Failed to get material filter options for field: {}, error: {}", 
                     request.getFieldName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public boolean supports(String fieldName) {
        return "pressSizeWidth".equals(fieldName);
    }
    
    /**
     * 获取压线尺寸筛选选项
     */
    private List<FilterOptionDTO> getPressSizeWidthOptions(FilterRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<SalesOrderMaterial> materialRoot = query.from(SalesOrderMaterial.class);
        Join<SalesOrderMaterial, SalesOrderItem> materialItemJoin = materialRoot.join("orderItem", JoinType.INNER);

        // 选择压线尺寸，去重
        query.select(materialRoot.get("pressSizeWidth")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(request.getSearchText())) {
            predicates.add(cb.like(materialRoot.get("pressSizeWidth"), "%" + request.getSearchText() + "%"));
        }

        // 过滤空值
        predicates.add(cb.isNotNull(materialRoot.get("pressSizeWidth")));
        predicates.add(cb.notEqual(materialRoot.get("pressSizeWidth"), ""));

        // 添加级联筛选条件（排除当前字段）
        if (Boolean.TRUE.equals(request.getEnableCascade())) {
            addMaterialCascadeFilterConditions(cb, materialRoot, materialItemJoin, request, predicates, "pressSizeWidth");
        }

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(materialRoot.get("pressSizeWidth")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(request.getMaxResults() != null ? request.getMaxResults() : 50)
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }
    
    /**
     * 为用料表子查询添加级联筛选条件
     */
    private void addMaterialCascadeFilterConditions(CriteriaBuilder cb, Root<SalesOrderMaterial> materialRoot,
                                                   Join<SalesOrderMaterial, SalesOrderItem> materialItemJoin,
                                                   FilterRequest request, List<Predicate> predicates, String excludeField) {
        if (request.getCurrentFilters() == null || request.getCurrentFilters().isEmpty()) {
            return;
        }

        // TODO: 实现级联筛选逻辑
        // 这里需要根据当前筛选条件构建子查询，与原有的CascadeFilterManager逻辑保持一致
        log.debug("Cascade filtering for material field: {} not fully implemented yet", excludeField);
    }
}
