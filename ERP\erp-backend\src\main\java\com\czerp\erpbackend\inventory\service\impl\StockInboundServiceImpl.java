package com.czerp.erpbackend.inventory.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.inventory.dto.CreateStockInboundItemRequest;
import com.czerp.erpbackend.inventory.dto.CreateStockInboundRequest;
import com.czerp.erpbackend.inventory.dto.StockInboundDTO;
import com.czerp.erpbackend.inventory.dto.StockInboundItemDTO;
import com.czerp.erpbackend.inventory.dto.StockInboundQueryRequest;
import com.czerp.erpbackend.inventory.dto.UpdateStockInboundRequest;
import com.czerp.erpbackend.inventory.entity.StockInbound;
import com.czerp.erpbackend.inventory.entity.StockInboundItem;
import com.czerp.erpbackend.inventory.repository.StockInboundItemRepository;
import com.czerp.erpbackend.inventory.repository.StockInboundRepository;
import com.czerp.erpbackend.inventory.service.StockInboundService;
import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.cache.CacheManager;
import org.springframework.context.ApplicationEventPublisher;
import com.czerp.erpbackend.inventory.event.StockInboundCacheEvictionEvent;

/**
 * 入库单服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StockInboundServiceImpl implements StockInboundService {

    private final StockInboundRepository stockInboundRepository;
    private final StockInboundItemRepository stockInboundItemRepository;
    private final SalesOrderItemRepository salesOrderItemRepository;
    private final PurchaseOrderItemRepository purchaseOrderItemRepository;
    private final CacheManager cacheManager;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String generateInboundNo() {
        String prefix = "RK" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String maxInboundNo = stockInboundRepository.findMaxInboundNoByPrefix(prefix);
        
        if (maxInboundNo == null) {
            return prefix + "001";
        }
        
        String sequenceStr = maxInboundNo.substring(prefix.length());
        int sequence = Integer.parseInt(sequenceStr) + 1;
        return prefix + String.format("%03d", sequence);
    }

    @Override
    @Transactional
    public StockInboundDTO createStockInbound(CreateStockInboundRequest request) {
        log.info("Creating stock inbound with request: {}", request);

        // 收集需要清除缓存的采购订单明细ID
        Set<Long> purchaseOrderItemIds = new HashSet<>();

        // 创建入库单实体
        StockInbound stockInbound = new StockInbound();
        stockInbound.setInboundNo(generateInboundNo());
        stockInbound.setInboundDate(request.getInboundDate());
        stockInbound.setWarehouse(request.getWarehouse());
        stockInbound.setRemark(request.getRemark());
        stockInbound.setSupplierCode(request.getSupplierCode());
        stockInbound.setSupplierName(request.getSupplierName());
        stockInbound.setSupplierDeliveryNo(request.getSupplierDeliveryNo());
        stockInbound.setDeliveryDate(request.getDeliveryDate());
        stockInbound.setIsDeleted(false);

        // 创建入库单明细
        for (CreateStockInboundItemRequest itemRequest : request.getItems()) {
            StockInboundItem item = createStockInboundItem(itemRequest);
            stockInbound.addItem(item);

            // 收集采购订单明细ID用于缓存清除
            if (itemRequest.getPurchaseOrderItemId() != null) {
                purchaseOrderItemIds.add(itemRequest.getPurchaseOrderItemId());
            }
        }

        // 保存入库单
        StockInbound savedStockInbound = stockInboundRepository.save(stockInbound);

        // 发布缓存失效事件（事务提交后异步处理）
        publishCacheEvictionEvent(StockInboundCacheEvictionEvent.EventType.CREATED,
                                 savedStockInbound.getId(), purchaseOrderItemIds);

        log.info("Created stock inbound with ID: {} and inbound no: {}, will clear cache for {} purchase order items",
                savedStockInbound.getId(), savedStockInbound.getInboundNo(), purchaseOrderItemIds.size());

        return convertToDTO(savedStockInbound);
    }

    @Override
    @Transactional
    public StockInboundDTO updateStockInbound(Long id, UpdateStockInboundRequest request) {
        log.info("Updating stock inbound with ID: {} and request: {}", id, request);

        StockInbound stockInbound = stockInboundRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("入库单不存在，ID: " + id));

        // 收集原有的采购订单明细ID（用于缓存清除）
        Set<Long> originalPurchaseOrderItemIds = stockInbound.getItems().stream()
                .map(item -> item.getPurchaseOrderItem())
                .filter(Objects::nonNull)
                .map(PurchaseOrderItem::getId)
                .collect(Collectors.toSet());

        // 收集新的采购订单明细ID
        Set<Long> newPurchaseOrderItemIds = new HashSet<>();

        // 更新入库单基本信息
        stockInbound.setInboundDate(request.getInboundDate());
        stockInbound.setWarehouse(request.getWarehouse());
        stockInbound.setRemark(request.getRemark());
        stockInbound.setSupplierCode(request.getSupplierCode());
        stockInbound.setSupplierName(request.getSupplierName());
        stockInbound.setSupplierDeliveryNo(request.getSupplierDeliveryNo());
        stockInbound.setDeliveryDate(request.getDeliveryDate());

        // 清除现有明细
        stockInbound.getItems().clear();

        // 添加新的明细
        for (CreateStockInboundItemRequest itemRequest : request.getItems()) {
            StockInboundItem item = createStockInboundItem(itemRequest);
            stockInbound.addItem(item);

            // 收集新的采购订单明细ID
            if (itemRequest.getPurchaseOrderItemId() != null) {
                newPurchaseOrderItemIds.add(itemRequest.getPurchaseOrderItemId());
            }
        }

        // 保存更新
        StockInbound savedStockInbound = stockInboundRepository.save(stockInbound);

        // 合并需要清除缓存的采购订单明细ID（原有的 + 新的）
        Set<Long> allAffectedIds = new HashSet<>();
        allAffectedIds.addAll(originalPurchaseOrderItemIds);
        allAffectedIds.addAll(newPurchaseOrderItemIds);

        // 发布缓存失效事件（事务提交后异步处理）
        publishCacheEvictionEvent(StockInboundCacheEvictionEvent.EventType.UPDATED,
                                 savedStockInbound.getId(), allAffectedIds);

        log.info("Updated stock inbound with ID: {}, will clear cache for {} purchase order items",
                savedStockInbound.getId(), allAffectedIds.size());

        return convertToDTO(savedStockInbound);
    }

    @Override
    @Transactional(readOnly = true)
    public StockInboundDTO getStockInboundById(Long id) {
        log.info("Getting stock inbound by ID: {}", id);
        
        StockInbound stockInbound = stockInboundRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("入库单不存在，ID: " + id));
        
        return convertToDTO(stockInbound);
    }

    @Override
    @Transactional(readOnly = true)
    public StockInboundDTO getStockInboundByNo(String inboundNo) {
        log.info("Getting stock inbound by inbound no: {}", inboundNo);
        
        StockInbound stockInbound = stockInboundRepository.findByInboundNo(inboundNo);
        if (stockInbound == null) {
            throw new RuntimeException("入库单不存在，入库单号: " + inboundNo);
        }
        
        return convertToDTO(stockInbound);
    }

    @Override
    @Transactional
    public void deleteStockInbound(Long id) {
        log.info("Deleting stock inbound with ID: {}", id);

        StockInbound stockInbound = stockInboundRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("入库单不存在，ID: " + id));

        // 收集需要清除缓存的采购订单明细ID
        Set<Long> purchaseOrderItemIds = stockInbound.getItems().stream()
                .map(item -> item.getPurchaseOrderItem())
                .filter(Objects::nonNull)
                .map(PurchaseOrderItem::getId)
                .collect(Collectors.toSet());

        // 软删除入库单
        stockInbound.setIsDeleted(true);
        stockInboundRepository.save(stockInbound);

        // 发布缓存失效事件（事务提交后异步处理）
        publishCacheEvictionEvent(StockInboundCacheEvictionEvent.EventType.DELETED,
                                 id, purchaseOrderItemIds);

        log.info("Deleted stock inbound with ID: {}, will clear cache for {} purchase order items",
                id, purchaseOrderItemIds.size());
    }

    /**
     * 创建入库单明细实体
     */
    private StockInboundItem createStockInboundItem(CreateStockInboundItemRequest request) {
        StockInboundItem item = new StockInboundItem();
        
        // 设置采购订单明细关联
        if (request.getPurchaseOrderItemId() != null) {
            PurchaseOrderItem purchaseOrderItem = purchaseOrderItemRepository.findById(request.getPurchaseOrderItemId())
                    .orElseThrow(() -> new RuntimeException("采购订单明细不存在，ID: " + request.getPurchaseOrderItemId()));
            item.setPurchaseOrderItem(purchaseOrderItem);
        }
        
        // 设置明细字段
        item.setQuantity(request.getQuantity());
        item.setSpareQuantity(request.getSpareQuantity());
        item.setPrice(request.getPrice());
        item.setAreaSquareMeters(request.getAreaSquareMeters());
        item.setUnitWeight(request.getUnitWeight());
        item.setWeightKg(request.getWeightKg());
        item.setQuantityPerBoard(request.getQuantityPerBoard());
        item.setTaxRate(request.getTaxRate());
        item.setCurrency(request.getCurrency());
        item.setSupplierDeliveryNo(request.getSupplierDeliveryNo());
        item.setFoldingSpecification(request.getFoldingSpecification());
        item.setConversionQuantity(request.getConversionQuantity());
        item.setConversionPrice(request.getConversionPrice());
        item.setConversionAmount(request.getConversionAmount());
        item.setRemark(request.getRemark());
        item.setIsDeleted(false);
        
        return item;
    }

    /**
     * 转换为DTO
     */
    private StockInboundDTO convertToDTO(StockInbound stockInbound) {
        List<StockInboundItemDTO> itemDTOs = stockInbound.getItems().stream()
                .map(this::convertItemToDTO)
                .collect(Collectors.toList());
        
        return StockInboundDTO.builder()
                .id(stockInbound.getId())
                .inboundNo(stockInbound.getInboundNo())
                .inboundDate(stockInbound.getInboundDate())
                .warehouse(stockInbound.getWarehouse())
                .remark(stockInbound.getRemark())
                .supplierCode(stockInbound.getSupplierCode())
                .supplierName(stockInbound.getSupplierName())
                .supplierDeliveryNo(stockInbound.getSupplierDeliveryNo())
                .deliveryDate(stockInbound.getDeliveryDate())
                .createdBy(stockInbound.getCreatedBy())
                .createdTime(stockInbound.getCreatedTime())
                .updatedBy(stockInbound.getUpdatedBy())
                .updatedTime(stockInbound.getUpdatedTime())
                .version(stockInbound.getVersion())
                .items(itemDTOs)
                .build();
    }

    /**
     * 转换明细为DTO
     */
    private StockInboundItemDTO convertItemToDTO(StockInboundItem item) {
        StockInboundItemDTO.StockInboundItemDTOBuilder builder = StockInboundItemDTO.builder()
                .id(item.getId())
                .inboundId(item.getStockInbound().getInboundNo())
                .purchaseOrderItemId(item.getPurchaseOrderItem() != null ? item.getPurchaseOrderItem().getId() : null)
                .quantity(item.getQuantity())
                .spareQuantity(item.getSpareQuantity())
                .price(item.getPrice())
                .areaSquareMeters(item.getAreaSquareMeters())
                .unitWeight(item.getUnitWeight())
                .weightKg(item.getWeightKg())
                .quantityPerBoard(item.getQuantityPerBoard())
                .taxRate(item.getTaxRate())
                .currency(item.getCurrency())
                .supplierDeliveryNo(item.getSupplierDeliveryNo())
                .foldingSpecification(item.getFoldingSpecification())
                .conversionQuantity(item.getConversionQuantity())
                .conversionPrice(item.getConversionPrice())
                .conversionAmount(item.getConversionAmount())
                .remark(item.getRemark())
                .createdBy(item.getCreatedBy())
                .createdTime(item.getCreatedTime())
                .updatedBy(item.getUpdatedBy())
                .updatedTime(item.getUpdatedTime())
                .version(item.getVersion());

        // 映射采购单相关数据
        if (item.getPurchaseOrderItem() != null) {
            PurchaseOrderItem purchaseOrderItem = item.getPurchaseOrderItem();
            PurchaseOrder purchaseOrder = purchaseOrderItem.getPurchaseOrder();

            if (purchaseOrder != null) {
                builder.purchaseOrderNo(purchaseOrder.getPurchaseOrderNo())
                       .purchaseDate(purchaseOrder.getPurchaseDate())
                       .tradingUnit(purchaseOrder.getTradingUnit());
            }

            builder.purchaseOrderQuantity(purchaseOrderItem.getQuantity())
                   .paperQuality(purchaseOrderItem.getPaperQuality())
                   .paperBoardCategory(purchaseOrderItem.getPaperBoardCategory())
                   .corrugationType(purchaseOrderItem.getCorrugationType())
                   .paperQuotation(purchaseOrderItem.getPaperQuotation())
                   .discount(purchaseOrderItem.getDiscount());

            // 构建规格字段 (纸度×纸长)
            if (purchaseOrderItem.getPaperWidth() != null && purchaseOrderItem.getPaperLength() != null) {
                String specification = purchaseOrderItem.getPaperWidth() + "×" + purchaseOrderItem.getPaperLength();
                builder.specification(specification);
            }
        }

        // 映射销售单相关数据
        if (item.getPurchaseOrderItem() != null &&
            StringUtils.hasText(item.getPurchaseOrderItem().getSourceSalesOrderItemId())) {

            String sourceSalesOrderItemId = item.getPurchaseOrderItem().getSourceSalesOrderItemId();

            // 查询销售订单明细
            Optional<SalesOrderItem> salesOrderItemOpt = salesOrderItemRepository.findById(sourceSalesOrderItemId);
            if (salesOrderItemOpt.isPresent()) {
                SalesOrderItem salesOrderItem = salesOrderItemOpt.get();
                SalesOrder salesOrder = salesOrderItem.getOrder();

                if (salesOrder != null) {
                    builder.salesOrderNo(salesOrder.getOrderNo())
                           .salesOrderDate(salesOrder.getOrderDate())
                           .customerName(salesOrder.getCustomerName())
                           .salesPerson(salesOrder.getSalesPerson());
                }

                builder.productionOrderNo(salesOrderItem.getProductionOrderNo())
                       .customerOrderNo(salesOrderItem.getCustomerOrderNo())
                       .customerProductCode(salesOrderItem.getCustomerProductCode())
                       .productName(salesOrderItem.getProductName())
                       .orderQuantity(salesOrderItem.getQuantity())
                       .productPrice(salesOrderItem.getPrice())
                       .productAmount(salesOrderItem.getAmount())
                       .salesRemark(salesOrderItem.getRemark())
                       .processRequirements(salesOrderItem.getProcessRequirements()); // 直接从销售订单明细获取工艺要求

                // 构建产品字段 (盒式+订单纸质)
                if (StringUtils.hasText(salesOrderItem.getBoxType()) && StringUtils.hasText(salesOrderItem.getPaperType())) {
                    String product = salesOrderItem.getBoxType() + " " + salesOrderItem.getPaperType();
                    builder.product(product);
                }

                // 构建产品规格字段 (长×宽×高)
                if (salesOrderItem.getLength() != null && salesOrderItem.getWidth() != null && salesOrderItem.getHeight() != null) {
                    String productSpecification = salesOrderItem.getLength() + "×" +
                                                salesOrderItem.getWidth() + "×" +
                                                salesOrderItem.getHeight();
                    if (StringUtils.hasText(salesOrderItem.getSizeUnit())) {
                        productSpecification += salesOrderItem.getSizeUnit();
                    }
                    builder.productSpecification(productSpecification);
                }
            }
        }

        // 计算金额字段
        if (item.getPrice() != null && item.getQuantity() != null) {
            BigDecimal amount = item.getPrice().multiply(BigDecimal.valueOf(item.getQuantity()));
            builder.amount(amount);
        }

        return builder.build();
    }

    /**
     * 优化的DTO转换方法：处理JOIN查询返回的Object[]数组
     * Object[]数组包含：StockInboundItem, StockInbound, PurchaseOrderItem, PurchaseOrder, SalesOrderItem, SalesOrder
     */
    private StockInboundItemDTO convertJoinResultToDTO(Object[] result) {
        StockInboundItem item = (StockInboundItem) result[0];
        StockInbound stockInbound = (StockInbound) result[1];
        PurchaseOrderItem purchaseOrderItem = (PurchaseOrderItem) result[2];
        PurchaseOrder purchaseOrder = (PurchaseOrder) result[3];
        SalesOrderItem salesOrderItem = (SalesOrderItem) result[4];
        SalesOrder salesOrder = (SalesOrder) result[5];

        StockInboundItemDTO.StockInboundItemDTOBuilder builder = StockInboundItemDTO.builder()
                .id(item.getId())
                .inboundId(stockInbound.getInboundNo())
                .purchaseOrderItemId(purchaseOrderItem != null ? purchaseOrderItem.getId() : null)
                .quantity(item.getQuantity())
                .spareQuantity(item.getSpareQuantity())
                .price(item.getPrice())
                .areaSquareMeters(item.getAreaSquareMeters())
                .unitWeight(item.getUnitWeight())
                .weightKg(item.getWeightKg())
                .quantityPerBoard(item.getQuantityPerBoard())
                .taxRate(item.getTaxRate())
                .currency(item.getCurrency())
                .supplierDeliveryNo(item.getSupplierDeliveryNo())
                .foldingSpecification(item.getFoldingSpecification())
                .conversionQuantity(item.getConversionQuantity())
                .conversionPrice(item.getConversionPrice())
                .conversionAmount(item.getConversionAmount())
                .remark(item.getRemark())
                .createdBy(item.getCreatedBy())
                .createdTime(item.getCreatedTime())
                .updatedBy(item.getUpdatedBy())
                .updatedTime(item.getUpdatedTime())
                .version(item.getVersion());

        // 映射采购单相关数据（已通过JOIN获取，无需额外查询）
        if (purchaseOrderItem != null) {
            if (purchaseOrder != null) {
                builder.purchaseOrderNo(purchaseOrder.getPurchaseOrderNo())
                       .purchaseDate(purchaseOrder.getPurchaseDate())
                       .tradingUnit(purchaseOrder.getTradingUnit());
            }

            builder.purchaseOrderQuantity(purchaseOrderItem.getQuantity())
                   .paperQuality(purchaseOrderItem.getPaperQuality())
                   .paperBoardCategory(purchaseOrderItem.getPaperBoardCategory())
                   .corrugationType(purchaseOrderItem.getCorrugationType())
                   .paperQuotation(purchaseOrderItem.getPaperQuotation())
                   .discount(purchaseOrderItem.getDiscount());

            // 构建规格字段 (纸度×纸长)
            if (purchaseOrderItem.getPaperWidth() != null && purchaseOrderItem.getPaperLength() != null) {
                String specification = purchaseOrderItem.getPaperWidth() + "×" + purchaseOrderItem.getPaperLength();
                builder.specification(specification);
            }
        }

        // 映射销售单相关数据（已通过JOIN获取，无需额外查询）
        if (salesOrderItem != null) {
            if (salesOrder != null) {
                builder.salesOrderNo(salesOrder.getOrderNo())
                       .salesOrderDate(salesOrder.getOrderDate())
                       .customerName(salesOrder.getCustomerName())
                       .salesPerson(salesOrder.getSalesPerson());
            }

            builder.productionOrderNo(salesOrderItem.getProductionOrderNo())
                   .customerOrderNo(salesOrderItem.getCustomerOrderNo())
                   .customerProductCode(salesOrderItem.getCustomerProductCode())
                   .productName(salesOrderItem.getProductName())
                   .orderQuantity(salesOrderItem.getQuantity())
                   .productPrice(salesOrderItem.getPrice())
                   .productAmount(salesOrderItem.getAmount())
                   .salesRemark(salesOrderItem.getRemark())
                   .processRequirements(salesOrderItem.getProcessRequirements()); // 直接从销售订单明细获取工艺要求

            // 构建产品字段 (盒式+订单纸质)
            if (StringUtils.hasText(salesOrderItem.getBoxType()) && StringUtils.hasText(salesOrderItem.getPaperType())) {
                String product = salesOrderItem.getBoxType() + " " + salesOrderItem.getPaperType();
                builder.product(product);
            }

            // 构建产品规格字段 (长×宽×高)
            if (salesOrderItem.getLength() != null && salesOrderItem.getWidth() != null && salesOrderItem.getHeight() != null) {
                String productSpecification = salesOrderItem.getLength() + "×" +
                                            salesOrderItem.getWidth() + "×" +
                                            salesOrderItem.getHeight();
                if (StringUtils.hasText(salesOrderItem.getSizeUnit())) {
                    productSpecification += salesOrderItem.getSizeUnit();
                }
                builder.productSpecification(productSpecification);
            }
        }

        // 计算金额字段
        if (item.getPrice() != null && item.getQuantity() != null) {
            BigDecimal amount = item.getPrice().multiply(BigDecimal.valueOf(item.getQuantity()));
            builder.amount(amount);
        }

        return builder.build();
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<StockInboundDTO> findStockInbounds(StockInboundQueryRequest request) {
        log.info("Finding stock inbounds with request: {}", request);

        // 构建分页参数
        int page = request.getPage() != null ? request.getPage() : 0;
        int size = request.getSize() != null ? request.getSize() : 10;
        Sort sort = Sort.by(Sort.Direction.DESC, "inboundDate", "inboundNo");
        Pageable pageable = PageRequest.of(page, size, sort);

        // 构建查询条件
        Specification<StockInbound> spec = buildStockInboundSpecification(request);

        // 执行查询
        Page<StockInbound> stockInboundPage = stockInboundRepository.findAll(spec, pageable);

        // 转换为DTO
        List<StockInboundDTO> stockInboundDTOs = stockInboundPage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        return PageResponse.<StockInboundDTO>builder()
                .content(stockInboundDTOs)
                .page(stockInboundPage.getNumber())
                .size(stockInboundPage.getSize())
                .totalElements(stockInboundPage.getTotalElements())
                .totalPages(stockInboundPage.getTotalPages())
                .first(stockInboundPage.isFirst())
                .last(stockInboundPage.isLast())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<StockInboundItemDTO> findStockInboundItems(StockInboundQueryRequest request) {
        log.info("Finding stock inbound items with request: {}", request);

        // 使用优化查询方法
        return findStockInboundItemsOptimized(request);
    }

    /**
     * 优化的入库明细查询方法：使用JOIN查询避免N+1问题
     */
    @Transactional(readOnly = true)
    public PageResponse<StockInboundItemDTO> findStockInboundItemsOptimized(StockInboundQueryRequest request) {
        log.info("Finding stock inbound items with optimized query, request: {}", request);

        // 构建分页参数
        int page = request.getPage() != null ? request.getPage() : 0;
        int size = request.getSize() != null ? request.getSize() : 10;
        Pageable pageable = PageRequest.of(page, size);

        // 执行优化查询
        Page<Object[]> resultPage = stockInboundItemRepository.findStockInboundItemsWithJoin(
                request.getKeyword(),
                request.getInboundDateStart(),
                request.getInboundDateEnd(),
                request.getSupplierCode(),
                request.getSupplierName(),
                request.getWarehouse(),
                request.getSupplierDeliveryNo(),
                request.getDeliveryDateStart(),
                request.getDeliveryDateEnd(),
                request.getCreatedBy(),
                pageable);

        // 转换为DTO（使用优化的转换方法）
        List<StockInboundItemDTO> stockInboundItemDTOs = resultPage.getContent().stream()
                .map(this::convertJoinResultToDTO)
                .collect(Collectors.toList());

        log.info("Found {} stock inbound items with optimized query", stockInboundItemDTOs.size());

        return PageResponse.<StockInboundItemDTO>builder()
                .content(stockInboundItemDTOs)
                .page(resultPage.getNumber())
                .size(resultPage.getSize())
                .totalElements(resultPage.getTotalElements())
                .totalPages(resultPage.getTotalPages())
                .first(resultPage.isFirst())
                .last(resultPage.isLast())
                .build();
    }

    /**
     * 原始查询方法（保留作为备用）
     */
    @Transactional(readOnly = true)
    public PageResponse<StockInboundItemDTO> findStockInboundItemsOriginal(StockInboundQueryRequest request) {
        log.info("Finding stock inbound items with original query, request: {}", request);

        // 构建分页参数
        int page = request.getPage() != null ? request.getPage() : 0;
        int size = request.getSize() != null ? request.getSize() : 10;
        Sort sort = Sort.by(Sort.Direction.DESC, "stockInbound.inboundDate", "stockInbound.inboundNo", "id");
        Pageable pageable = PageRequest.of(page, size, sort);

        // 构建查询条件
        Specification<StockInboundItem> spec = buildStockInboundItemSpecification(request);

        // 执行查询
        Page<StockInboundItem> stockInboundItemPage = stockInboundItemRepository.findAll(spec, pageable);

        // 转换为DTO
        List<StockInboundItemDTO> stockInboundItemDTOs = stockInboundItemPage.getContent().stream()
                .map(this::convertItemToDTO)
                .collect(Collectors.toList());

        return PageResponse.<StockInboundItemDTO>builder()
                .content(stockInboundItemDTOs)
                .page(stockInboundItemPage.getNumber())
                .size(stockInboundItemPage.getSize())
                .totalElements(stockInboundItemPage.getTotalElements())
                .totalPages(stockInboundItemPage.getTotalPages())
                .first(stockInboundItemPage.isFirst())
                .last(stockInboundItemPage.isLast())
                .build();
    }

    /**
     * 构建入库单查询条件
     */
    private Specification<StockInbound> buildStockInboundSpecification(StockInboundQueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 软删除过滤
            predicates.add(criteriaBuilder.equal(root.get("isDeleted"), false));

            // 关键字搜索
            if (StringUtils.hasText(request.getKeyword())) {
                String keyword = "%" + request.getKeyword().trim() + "%";
                Predicate keywordPredicate = criteriaBuilder.or(
                        criteriaBuilder.like(root.get("inboundNo"), keyword),
                        criteriaBuilder.like(root.get("supplierName"), keyword),
                        criteriaBuilder.like(root.get("warehouse"), keyword),
                        criteriaBuilder.like(root.get("supplierDeliveryNo"), keyword)
                );
                predicates.add(keywordPredicate);
            }

            // 入库日期范围
            if (request.getInboundDateStart() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("inboundDate"), request.getInboundDateStart()));
            }
            if (request.getInboundDateEnd() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("inboundDate"), request.getInboundDateEnd()));
            }

            // 供应商编码
            if (StringUtils.hasText(request.getSupplierCode())) {
                predicates.add(criteriaBuilder.equal(root.get("supplierCode"), request.getSupplierCode()));
            }

            // 供应商名称
            if (StringUtils.hasText(request.getSupplierName())) {
                predicates.add(criteriaBuilder.like(root.get("supplierName"), "%" + request.getSupplierName() + "%"));
            }

            // 仓库
            if (StringUtils.hasText(request.getWarehouse())) {
                predicates.add(criteriaBuilder.like(root.get("warehouse"), "%" + request.getWarehouse() + "%"));
            }

            // 供应商送货单号
            if (StringUtils.hasText(request.getSupplierDeliveryNo())) {
                predicates.add(criteriaBuilder.like(root.get("supplierDeliveryNo"), "%" + request.getSupplierDeliveryNo() + "%"));
            }

            // 送货单日期范围
            if (request.getDeliveryDateStart() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("deliveryDate"), request.getDeliveryDateStart()));
            }
            if (request.getDeliveryDateEnd() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("deliveryDate"), request.getDeliveryDateEnd()));
            }

            // 创建人
            if (StringUtils.hasText(request.getCreatedBy())) {
                predicates.add(criteriaBuilder.equal(root.get("createdBy"), request.getCreatedBy()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 构建入库单明细查询条件
     */
    private Specification<StockInboundItem> buildStockInboundItemSpecification(StockInboundQueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 软删除过滤
            predicates.add(criteriaBuilder.equal(root.get("isDeleted"), false));

            // 入库单软删除过滤
            Join<StockInboundItem, StockInbound> stockInboundJoin = root.join("stockInbound", JoinType.INNER);
            predicates.add(criteriaBuilder.equal(stockInboundJoin.get("isDeleted"), false));

            // 关键字搜索
            if (StringUtils.hasText(request.getKeyword())) {
                String keyword = "%" + request.getKeyword().trim() + "%";

                // 左连接采购订单和采购订单明细
                Join<StockInboundItem, PurchaseOrderItem> purchaseOrderItemJoin = root.join("purchaseOrderItem", JoinType.LEFT);
                Join<PurchaseOrderItem, PurchaseOrder> purchaseOrderJoin = purchaseOrderItemJoin.join("purchaseOrder", JoinType.LEFT);

                Predicate keywordPredicate = criteriaBuilder.or(
                        criteriaBuilder.like(stockInboundJoin.get("inboundNo"), keyword),
                        criteriaBuilder.like(stockInboundJoin.get("supplierName"), keyword),
                        criteriaBuilder.like(stockInboundJoin.get("warehouse"), keyword),
                        criteriaBuilder.like(stockInboundJoin.get("supplierDeliveryNo"), keyword),
                        criteriaBuilder.like(purchaseOrderJoin.get("purchaseOrderNo"), keyword)
                );
                predicates.add(keywordPredicate);
            }

            // 入库日期范围
            if (request.getInboundDateStart() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(stockInboundJoin.get("inboundDate"), request.getInboundDateStart()));
            }
            if (request.getInboundDateEnd() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(stockInboundJoin.get("inboundDate"), request.getInboundDateEnd()));
            }

            // 供应商编码
            if (StringUtils.hasText(request.getSupplierCode())) {
                predicates.add(criteriaBuilder.equal(stockInboundJoin.get("supplierCode"), request.getSupplierCode()));
            }

            // 供应商名称
            if (StringUtils.hasText(request.getSupplierName())) {
                predicates.add(criteriaBuilder.like(stockInboundJoin.get("supplierName"), "%" + request.getSupplierName() + "%"));
            }

            // 仓库
            if (StringUtils.hasText(request.getWarehouse())) {
                predicates.add(criteriaBuilder.like(stockInboundJoin.get("warehouse"), "%" + request.getWarehouse() + "%"));
            }

            // 供应商送货单号
            if (StringUtils.hasText(request.getSupplierDeliveryNo())) {
                predicates.add(criteriaBuilder.like(stockInboundJoin.get("supplierDeliveryNo"), "%" + request.getSupplierDeliveryNo() + "%"));
            }

            // 送货单日期范围
            if (request.getDeliveryDateStart() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(stockInboundJoin.get("deliveryDate"), request.getDeliveryDateStart()));
            }
            if (request.getDeliveryDateEnd() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(stockInboundJoin.get("deliveryDate"), request.getDeliveryDateEnd()));
            }

            // 采购单号
            if (StringUtils.hasText(request.getPurchaseOrderNo())) {
                Join<StockInboundItem, PurchaseOrderItem> purchaseOrderItemJoin = root.join("purchaseOrderItem", JoinType.LEFT);
                Join<PurchaseOrderItem, PurchaseOrder> purchaseOrderJoin = purchaseOrderItemJoin.join("purchaseOrder", JoinType.LEFT);
                predicates.add(criteriaBuilder.like(purchaseOrderJoin.get("purchaseOrderNo"), "%" + request.getPurchaseOrderNo() + "%"));
            }

            // 创建人
            if (StringUtils.hasText(request.getCreatedBy())) {
                predicates.add(criteriaBuilder.equal(stockInboundJoin.get("createdBy"), request.getCreatedBy()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 发布缓存失效事件
     *
     * @param eventType 事件类型
     * @param stockInboundId 入库单ID
     * @param purchaseOrderItemIds 受影响的采购订单明细ID集合
     */
    private void publishCacheEvictionEvent(StockInboundCacheEvictionEvent.EventType eventType,
                                          Long stockInboundId, Set<Long> purchaseOrderItemIds) {
        try {
            StockInboundCacheEvictionEvent event = new StockInboundCacheEvictionEvent(
                this, eventType, stockInboundId, purchaseOrderItemIds);
            eventPublisher.publishEvent(event);
            log.debug("Published cache eviction event: {}", event);
        } catch (Exception e) {
            log.error("Failed to publish cache eviction event for stock inbound: {}", stockInboundId, e);
            // 事件发布失败时，回退到同步缓存清除
            evictPurchaseOrderItemCaches(purchaseOrderItemIds);
        }
    }

    /**
     * 清除采购订单明细相关缓存（同步方式，作为事件机制的备用方案）
     *
     * @param purchaseOrderItemIds 采购订单明细ID集合
     */
    private void evictPurchaseOrderItemCaches(Set<Long> purchaseOrderItemIds) {
        if (purchaseOrderItemIds == null || purchaseOrderItemIds.isEmpty()) {
            return;
        }

        try {
            // 清除已入库数量缓存
            var receivedQuantityCache = cacheManager.getCache("receivedQuantity");
            if (receivedQuantityCache != null) {
                for (Long itemId : purchaseOrderItemIds) {
                    receivedQuantityCache.evict(itemId);
                }
                log.debug("Evicted receivedQuantity cache for {} items", purchaseOrderItemIds.size());
            }

            // 清除采购订单明细可用性缓存
            var availabilityCache = cacheManager.getCache("purchaseOrderItemAvailability");
            if (availabilityCache != null) {
                for (Long itemId : purchaseOrderItemIds) {
                    availabilityCache.evict(itemId);
                }
                log.debug("Evicted purchaseOrderItemAvailability cache for {} items", purchaseOrderItemIds.size());
            }

            log.info("Successfully evicted caches for purchase order items: {}", purchaseOrderItemIds);
        } catch (Exception e) {
            log.error("Failed to evict caches for purchase order items: {}", purchaseOrderItemIds, e);
            // 缓存清除失败不应该影响业务流程，只记录错误日志
        }
    }

}
