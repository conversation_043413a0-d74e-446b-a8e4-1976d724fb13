package com.czerp.erpbackend.customer.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.customer.dto.CustomerBoxInfoDTO;
import com.czerp.erpbackend.customer.dto.CustomerBoxInfoQueryRequest;
import com.czerp.erpbackend.customer.service.CustomerBoxInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户盒式信息控制器
 */
@RestController
@RequestMapping("/customer-box-infos")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Customer Box Info Management", description = "客户盒式信息管理相关接口")
public class CustomerBoxInfoController {

    private final CustomerBoxInfoService customerBoxInfoService;

    /**
     * 分页查询客户盒式信息列表
     * @param request 查询请求
     * @return 客户盒式信息分页列表
     */
    @GetMapping
    @Operation(summary = "分页查询客户盒式信息列表", description = "分页查询客户盒式信息列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:box-info:read')")
    public ResponseEntity<ApiResponse<PageResponse<CustomerBoxInfoDTO>>> findCustomerBoxInfos(CustomerBoxInfoQueryRequest request) {
        log.info("Finding customer box infos with request: {}", request);
        PageResponse<CustomerBoxInfoDTO> response = customerBoxInfoService.findCustomerBoxInfos(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据ID查询客户盒式信息
     * @param id 客户盒式信息ID
     * @return 客户盒式信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询客户盒式信息", description = "根据ID查询客户盒式信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:box-info:read')")
    public ResponseEntity<ApiResponse<CustomerBoxInfoDTO>> findCustomerBoxInfoById(@PathVariable String id) {
        log.info("Finding customer box info by id: {}", id);
        CustomerBoxInfoDTO response = customerBoxInfoService.findCustomerBoxInfoById(id);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据盒式编码查询客户盒式信息
     * @param code 盒式编码
     * @return 客户盒式信息
     */
    @GetMapping("/code/{code}")
    @Operation(summary = "根据盒式编码查询客户盒式信息", description = "根据盒式编码查询客户盒式信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:box-info:read')")
    public ResponseEntity<ApiResponse<CustomerBoxInfoDTO>> findCustomerBoxInfoByCode(@PathVariable String code) {
        log.info("Finding customer box info by code: {}", code);
        CustomerBoxInfoDTO response = customerBoxInfoService.findCustomerBoxInfoByCode(code);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 查询所有启用的客户盒式信息
     * @return 客户盒式信息列表
     */
    @GetMapping("/active")
    @Operation(summary = "查询所有启用的客户盒式信息", description = "查询所有启用的客户盒式信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:box-info:read')")
    public ResponseEntity<ApiResponse<List<CustomerBoxInfoDTO>>> findActiveCustomerBoxInfos() {
        log.info("Finding all active customer box infos");
        List<CustomerBoxInfoDTO> response = customerBoxInfoService.findActiveCustomerBoxInfos();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 查询默认盒式
     * @return 默认盒式
     */
    @GetMapping("/default")
    @Operation(summary = "查询默认盒式", description = "查询默认盒式")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:box-info:read')")
    public ResponseEntity<ApiResponse<CustomerBoxInfoDTO>> findDefaultCustomerBoxInfo() {
        log.info("Finding default customer box info");
        CustomerBoxInfoDTO response = customerBoxInfoService.findDefaultCustomerBoxInfo();
        return ResponseEntity.ok(ApiResponse.success(response));
    }
}
