好的，这是一份描述该混合纸度推荐算法的 Markdown 格式说明文档，不包含具体代码，供开发时参考。

# 混合纸度推荐算法说明

## 1. 概述

本算法旨在根据输入的“宽度”和“高度”，从一个固定的纸度列表中推荐一个最合适的纸度值。算法采用混合策略：

*   当基础计算值远小于最小可用纸度时，优先考虑材料利用率（单位成本），并选择成本接近最优的选项中尺寸最小的纸度。
*   当基础计算值大于等于最小可用纸度时，采用简单的向上查找策略，选择第一个大于计算值的可用纸度。

## 2. 固定数据

*   **可用纸度列表 (PaperSizes):** 一组预定义、升序排列的纸度值。
    `[29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 58, 62, 66, 70, 74, 78, 82, 86, 90, 94, 98, 99, 102, 104, 106, 110]`
*   **最小纸度 (MinPaperSize):** 上述列表中的最小值，即 `29`。

## 3. 核心计算

*   **基础计算值 (CalculatedValue):** `(宽度 + 高度) / 2.54`

## 4. 算法逻辑

算法根据 `CalculatedValue` 与 `MinPaperSize` 的比较结果，分为两种主要场景：

---

### 场景一：`CalculatedValue < MinPaperSize` (计算值小于 29)

**目标:** 寻找单位成本最优（或接近最优）且尺寸尽可能小的纸度，以提高材料利用率。

**步骤:**

1.  **定义所需尺寸 (RequiredDim):** 在此场景下，`RequiredDim = CalculatedValue`。
2.  **遍历所有可用纸度 (PaperSizes):** 对列表中的每一个 `CurrentSize` 执行以下计算：
    *   计算可生产数量 (`NumberOfItems`): `向下取整(CurrentSize / RequiredDim)`。
    *   如果 `NumberOfItems > 0` (即该纸度至少能生产一个):
        *   计算单位成本 (`CostPerItem`): `CurrentSize / NumberOfItems`。
        *   记录下这对结果：`{ size: CurrentSize, cost: CostPerItem }`。
3.  **查找绝对最低成本 (AbsoluteMinCost):** 从所有计算出的 `CostPerItem` 中找到最小值。
4.  **处理无可行解:** 如果没有任何纸度能生产至少一个物品（即 `RequiredDim` 大于所有 `CurrentSize`，或计算错误导致 `RequiredDim <= 0`），则算法无法推荐，应返回 `null` 或错误标识。
5.  **确定成本容差阈值 (CostThreshold):**
    *   引入一个 **成本容差率 (CostTolerance)** 参数（例如 0.01 表示 1%，0.05 表示 5%）。这是一个可配置的业务参数。
    *   计算阈值：`CostThreshold = AbsoluteMinCost * (1 + CostTolerance)`。
6.  **筛选最优候选集:** 从步骤 2 记录的所有有效结果中，筛选出满足 `cost <= CostThreshold` 的所有记录。
7.  **选择最终纸度:** 在上一步筛选出的候选集中，选择具有 **最小 `size`** 的那条记录。这个 `size` 就是最终推荐的纸度。

---

### 场景二：`CalculatedValue >= MinPaperSize` (计算值大于等于 29)

**目标:** 寻找刚好能满足需求的、下一个更大的标准纸度。

**步骤:**

1.  **遍历已排序的可用纸度列表 (PaperSizes):**
2.  查找第一个满足 `CurrentSize > CalculatedValue` 的纸度 `CurrentSize`。
3.  **返回结果:** 立即返回找到的这个 `CurrentSize`。
4.  **处理超出最大值:** 如果遍历完整个列表都没有找到满足条件的 `CurrentSize`（意味着 `CalculatedValue` 大于或等于列表中的最大纸度），则表示无法满足需求或超出范围，应返回 `null` 或错误标识。

---

## 5. 关键参数

*   **成本容差率 (CostTolerance):** (仅用于场景一)
    *   **作用:** 定义了多大范围内的成本可以被认为是“接近最优”。允许在单位成本略有增加的情况下，优先选择尺寸更小的纸度，避免不必要的浪费。
    *   **取值:** 通常是一个小的正数，如 `0.01` (1%), `0.03` (3%), `0.05` (5%)。
    *   **影响:** 值越小，越倾向于选择成本绝对最低的纸度；值越大，越可能选择成本稍高但尺寸更小的纸度。需要根据实际业务和成本敏感度进行调整。

## 6. 输入

*   `宽度 (Width)`: 数值类型，代表物品宽度。
*   `高度 (Height)`: 数值类型，代表物品高度。

## 7. 输出

*   推荐的纸度值 (`number`): 算法根据上述逻辑计算出的最佳纸度。
*   `null` 或特定错误标识: 在无法找到合适纸度（如超出最大值、无法生产等）的情况下返回。

## 8. 总结

该混合算法结合了两种策略：对于小尺寸物品，它侧重于经济性，通过单位成本计算和容差选择，避免了大纸张的浪费；对于较大尺寸物品，它采用简单的向上匹配，保证使用满足需求的最小标准规格。
