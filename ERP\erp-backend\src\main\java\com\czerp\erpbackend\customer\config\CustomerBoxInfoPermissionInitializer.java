package com.czerp.erpbackend.customer.config;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import com.czerp.erpbackend.system.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 客户盒式信息权限初始化
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(4)
public class CustomerBoxInfoPermissionInitializer implements CommandLineRunner {

    private final PermissionService permissionService;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing customer box info permissions...");

        try {
            // 获取客户管理权限
            Permission customerManagement = permissionService.findByCode("customer:management")
                    .orElseThrow(() -> new RuntimeException("Customer management permission not found"));

            // 创建客户盒式信息权限
            createCustomerBoxInfoPermissions(customerManagement.getId());

            // 为管理员角色分配客户盒式信息权限
            assignPermissionsToAdminRole();

            log.info("Customer box info permissions initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize customer box info permissions", e);
        }
    }

    /**
     * 创建客户盒式信息权限
     * @param parentId 父权限ID
     */
    private void createCustomerBoxInfoPermissions(String parentId) {
        // 客户盒式信息菜单
        Permission boxInfoMenu = new Permission();
        boxInfoMenu.setId(UUID.randomUUID().toString());
        boxInfoMenu.setName("客户盒式信息");
        boxInfoMenu.setCode("customer:box-info");
        boxInfoMenu.setType("menu");
        boxInfoMenu.setParentId(parentId);
        boxInfoMenu.setPath("/customer/box-info");
        boxInfoMenu.setComponent("customer/CustomerBoxInfoList");
        boxInfoMenu.setIcon("inbox");
        boxInfoMenu.setSort(30);
        boxInfoMenu.setStatus("active");

        Permission savedMenu = permissionService.saveIfNotExists(boxInfoMenu);

        // 客户盒式信息按钮权限
        List<Permission> permissions = new ArrayList<>();

        // 查看权限
        Permission viewPermission = new Permission();
        viewPermission.setId(UUID.randomUUID().toString());
        viewPermission.setName("查看客户盒式信息");
        viewPermission.setCode("customer:box-info:read");
        viewPermission.setType("button");
        viewPermission.setParentId(savedMenu.getId());
        viewPermission.setSort(10);
        permissions.add(viewPermission);

        // 创建权限
        Permission createPermission = new Permission();
        createPermission.setId(UUID.randomUUID().toString());
        createPermission.setName("创建客户盒式信息");
        createPermission.setCode("customer:box-info:create");
        createPermission.setType("button");
        createPermission.setParentId(savedMenu.getId());
        createPermission.setSort(20);
        permissions.add(createPermission);

        // 更新权限
        Permission updatePermission = new Permission();
        updatePermission.setId(UUID.randomUUID().toString());
        updatePermission.setName("更新客户盒式信息");
        updatePermission.setCode("customer:box-info:update");
        updatePermission.setType("button");
        updatePermission.setParentId(savedMenu.getId());
        updatePermission.setSort(30);
        permissions.add(updatePermission);

        // 删除权限
        Permission deletePermission = new Permission();
        deletePermission.setId(UUID.randomUUID().toString());
        deletePermission.setName("删除客户盒式信息");
        deletePermission.setCode("customer:box-info:delete");
        deletePermission.setType("button");
        deletePermission.setParentId(savedMenu.getId());
        deletePermission.setSort(40);
        permissions.add(deletePermission);

        // 保存权限
        permissionService.saveAllIfNotExist(permissions);
    }

    /**
     * 为管理员角色分配客户盒式信息权限
     */
    private void assignPermissionsToAdminRole() {
        log.info("Assigning customer box info permissions to admin role...");

        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping permission assignment");
            return;
        }

        // 获取所有客户盒式信息相关权限
        List<String> permissionCodes = Arrays.asList(
                "customer:box-info",
                "customer:box-info:read",
                "customer:box-info:create",
                "customer:box-info:update",
                "customer:box-info:delete"
        );

        List<Permission> permissions = permissionService.findByCodes(permissionCodes);

        // 为管理员角色分配权限
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : permissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString());
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermission.setCreateBy("system");
                rolePermission.setCreateTime(LocalDateTime.now());
                rolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }

        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} customer box info permissions to admin role", rolePermissions.size());
        } else {
            log.info("All customer box info permissions already assigned to admin role, skipping");
        }
    }
}
