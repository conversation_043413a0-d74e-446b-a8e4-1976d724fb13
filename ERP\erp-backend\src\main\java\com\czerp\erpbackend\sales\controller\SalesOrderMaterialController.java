package com.czerp.erpbackend.sales.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.sales.dto.SalesOrderMaterialDTO;
import com.czerp.erpbackend.sales.service.SalesOrderMaterialService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售订单材料信息Controller
 */
@RestController
@RequestMapping("/sales/orders/materials")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "销售订单材料信息管理", description = "销售订单材料信息管理相关接口")
public class SalesOrderMaterialController {

    private final SalesOrderMaterialService salesOrderMaterialService;

    /**
     * 根据订单行项目ID查询材料信息
     * @param orderItemId 订单行项目ID
     * @return 材料信息列表
     */
    @GetMapping("/by-order-item/{orderItemId}")
    @Operation(summary = "根据订单行项目ID查询材料信息", description = "根据订单行项目ID查询材料信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<List<SalesOrderMaterialDTO>>> getMaterialsByOrderItemId(@PathVariable String orderItemId) {
        log.info("Getting materials by order item id: {}", orderItemId);
        List<SalesOrderMaterialDTO> materials = salesOrderMaterialService.getMaterialsByOrderItemId(orderItemId);
        return ResponseEntity.ok(ApiResponse.success(materials));
    }

    /**
     * 根据订单ID查询所有行项目的材料信息
     * @param orderId 订单ID
     * @return 材料信息列表
     */
    @GetMapping("/by-order/{orderId}")
    @Operation(summary = "根据订单ID查询所有行项目的材料信息", description = "根据订单ID查询所有行项目的材料信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<List<SalesOrderMaterialDTO>>> getMaterialsByOrderId(@PathVariable String orderId) {
        log.info("Getting materials by order id: {}", orderId);
        List<SalesOrderMaterialDTO> materials = salesOrderMaterialService.getMaterialsByOrderId(orderId);
        return ResponseEntity.ok(ApiResponse.success(materials));
    }

    /**
     * 保存材料信息
     * @param materialDTO 材料信息DTO
     * @return 保存后的材料信息
     */
    @PostMapping
    @Operation(summary = "保存材料信息", description = "保存材料信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:create')")
    public ResponseEntity<ApiResponse<SalesOrderMaterialDTO>> saveMaterial(@Valid @RequestBody SalesOrderMaterialDTO materialDTO) {
        log.info("Saving material: {}", materialDTO);
        SalesOrderMaterialDTO savedMaterial = salesOrderMaterialService.saveMaterial(materialDTO);
        return ResponseEntity.ok(ApiResponse.success(savedMaterial));
    }

    /**
     * 批量保存材料信息
     * @param materialDTOs 材料信息DTO列表
     * @return 保存后的材料信息列表
     */
    @PostMapping("/batch")
    @Operation(summary = "批量保存材料信息", description = "批量保存材料信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:create')")
    public ResponseEntity<ApiResponse<List<SalesOrderMaterialDTO>>> saveMaterials(@Valid @RequestBody List<SalesOrderMaterialDTO> materialDTOs) {
        log.info("Saving materials: {}", materialDTOs);
        List<SalesOrderMaterialDTO> savedMaterials = salesOrderMaterialService.saveMaterials(materialDTOs);
        return ResponseEntity.ok(ApiResponse.success(savedMaterials));
    }

    /**
     * 更新材料信息
     * @param id 材料信息ID
     * @param materialDTO 材料信息DTO
     * @return 更新后的材料信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新材料信息", description = "更新材料信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<SalesOrderMaterialDTO>> updateMaterial(@PathVariable String id, @Valid @RequestBody SalesOrderMaterialDTO materialDTO) {
        log.info("Updating material: id={}, materialDTO={}", id, materialDTO);
        SalesOrderMaterialDTO updatedMaterial = salesOrderMaterialService.updateMaterial(id, materialDTO);
        return ResponseEntity.ok(ApiResponse.success(updatedMaterial));
    }

    /**
     * 删除材料信息
     * @param id 材料信息ID
     * @return 无内容
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除材料信息", description = "删除材料信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteMaterial(@PathVariable String id) {
        log.info("Deleting material: {}", id);
        salesOrderMaterialService.deleteMaterial(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 根据订单行项目ID删除材料信息
     * @param orderItemId 订单行项目ID
     * @return 无内容
     */
    @DeleteMapping("/by-order-item/{orderItemId}")
    @Operation(summary = "根据订单行项目ID删除材料信息", description = "根据订单行项目ID删除材料信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteMaterialsByOrderItemId(@PathVariable String orderItemId) {
        log.info("Deleting materials by order item id: {}", orderItemId);
        salesOrderMaterialService.deleteMaterialsByOrderItemId(orderItemId);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
}
