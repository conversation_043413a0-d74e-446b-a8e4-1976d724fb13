package com.czerp.erpbackend.customer.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.customer.dto.CustomerDTO;
import com.czerp.erpbackend.customer.dto.CustomerQueryRequest;
import com.czerp.erpbackend.customer.entity.Customer;
import com.czerp.erpbackend.customer.mapper.CustomerMapper;
import com.czerp.erpbackend.customer.repository.CustomerRepository;
import com.czerp.erpbackend.customer.service.impl.CustomerServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CustomerServiceTest {

    @Mock
    private CustomerRepository customerRepository;

    @Mock
    private CustomerMapper customerMapper;

    @InjectMocks
    private CustomerServiceImpl customerService;

    private List<Customer> customers;
    private List<CustomerDTO> customerDTOs;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        customers = new ArrayList<>();
        customerDTOs = new ArrayList<>();

        Customer customer = new Customer();
        customer.setId("1");
        customer.setCustomerCode("C001");
        customer.setCustomerName("测试客户");
        customers.add(customer);

        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setId("1");
        customerDTO.setCustomerCode("C001");
        customerDTO.setCustomerName("测试客户");
        customerDTOs.add(customerDTO);

        // 配置Mock行为
        when(customerMapper.toDto(any(Customer.class))).thenReturn(customerDTO);
    }

    @Test
    void testFindCustomersWithPageZero() {
        // 准备测试数据
        CustomerQueryRequest request = new CustomerQueryRequest();
        request.setPage(0); // 测试页码为0的情况
        request.setSize(10);

        Page<Customer> page = new PageImpl<>(customers);
        when(customerRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // 执行测试
        PageResponse<CustomerDTO> response = customerService.findCustomers(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        assertEquals("1", response.getContent().get(0).getId());
        assertEquals("C001", response.getContent().get(0).getCustomerCode());
        assertEquals("测试客户", response.getContent().get(0).getCustomerName());
    }

    @Test
    void testFindCustomersWithPageOne() {
        // 准备测试数据
        CustomerQueryRequest request = new CustomerQueryRequest();
        request.setPage(1); // 测试页码为1的情况
        request.setSize(10);

        Page<Customer> page = new PageImpl<>(customers);
        when(customerRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // 执行测试
        PageResponse<CustomerDTO> response = customerService.findCustomers(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        assertEquals("1", response.getContent().get(0).getId());
        assertEquals("C001", response.getContent().get(0).getCustomerCode());
        assertEquals("测试客户", response.getContent().get(0).getCustomerName());
    }
}
