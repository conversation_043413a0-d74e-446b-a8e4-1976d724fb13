package com.czerp.erpbackend.common.constant;

/**
 * 用户状态枚举
 */
public enum UserStatus {
    /**
     * 启用
     */
    ENABLED("active"),

    /**
     * 停用
     */
    DISABLED("inactive"),

    /**
     * 锁定
     */
    LOCKED("locked");

    private final String value;

    UserStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }

    /**
     * 根据值获取枚举
     * @param value 值
     * @return 枚举
     */
    public static UserStatus fromValue(String value) {
        for (UserStatus status : UserStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown UserStatus value: " + value);
    }
}