package com.czerp.erpbackend.inventory.repository;

import com.czerp.erpbackend.inventory.entity.StockInbound;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 入库单Repository
 */
@Repository
public interface StockInboundRepository extends JpaRepository<StockInbound, Long>, JpaSpecificationExecutor<StockInbound> {

    /**
     * 根据入库单号查询入库单
     * @param inboundNo 入库单号
     * @return 入库单
     */
    StockInbound findByInboundNo(String inboundNo);

    /**
     * 检查入库单号是否存在
     * @param inboundNo 入库单号
     * @return 是否存在
     */
    boolean existsByInboundNo(String inboundNo);

    /**
     * 查询指定前缀的最大入库单号
     * @param prefix 前缀
     * @return 最大入库单号
     */
    @Query("SELECT MAX(s.inboundNo) FROM StockInbound s WHERE s.inboundNo LIKE :prefix%")
    String findMaxInboundNoByPrefix(@Param("prefix") String prefix);
}
