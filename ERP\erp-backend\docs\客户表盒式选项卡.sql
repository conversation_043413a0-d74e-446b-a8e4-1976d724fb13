CREATE TABLE czerp_web.customer_box_info (
    id VARCHAR(36) NOT NULL COMMENT '主键ID',
    box_code VARCHAR(50) NOT NULL COMMENT '盒式编码',
    box_name VARCHAR(100) NOT NULL COMMENT '盒式名称',
    quote_formula VARCHAR(255) NULL COMMENT '报价公式',
    calculation_unit VARCHAR(50) NULL COMMENT '算价单位',
    quote_unit VARCHAR(50) NULL COMMENT '报价单位',
    connection_method VARCHAR(100) NULL COMMENT '连接方式',
    forbid_double_fluting TINYINT(1) NOT NULL DEFAULT 0 COMMENT '禁止双驳',
    double_fluting_length_threshold DECIMAL(10,2) NULL COMMENT '长度大于此值双驳',
    pitch_tolerance VARCHAR(100) NULL COMMENT '跳度公差',
    has_box_graphic TINYINT(1) NOT NULL DEFAULT 0 COMMENT '盒式图形',
    is_default TINYINT(1) NOT NULL DEFAULT 0 COMMENT '默认盒式',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态(active-启用,inactive-禁用)',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) NULL COMMENT '更新人',
    updated_time DATETIME NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    version INT NULL COMMENT '版本号',
    PRIMARY KEY (id),
    UNIQUE KEY uk_box_code (box_code),
    KEY idx_box_name (box_name),
    KEY idx_status (status),
    KEY idx_is_default (is_default),
    KEY idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客户盒式信息表';