package com.czerp.erpbackend.customer.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.customer.dto.CustomerCategoryDTO;
import com.czerp.erpbackend.customer.dto.CustomerCategoryQueryRequest;
import com.czerp.erpbackend.customer.entity.CustomerCategory;
import com.czerp.erpbackend.customer.mapper.CustomerCategoryMapper;
import com.czerp.erpbackend.customer.repository.CustomerCategoryRepository;
import com.czerp.erpbackend.customer.repository.CustomerRepository;
import com.czerp.erpbackend.customer.service.impl.CustomerCategoryServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CustomerCategoryServiceTest {

    @Mock
    private CustomerCategoryRepository categoryRepository;

    @Mock
    private CustomerRepository customerRepository;

    @Mock
    private CustomerCategoryMapper categoryMapper;

    @InjectMocks
    private CustomerCategoryServiceImpl categoryService;

    private List<CustomerCategory> categories;
    private List<CustomerCategoryDTO> categoryDTOs;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        categories = new ArrayList<>();
        categoryDTOs = new ArrayList<>();

        CustomerCategory category = new CustomerCategory();
        category.setId("1");
        category.setCategoryCode("CAT001");
        category.setCategoryName("测试分类");
        categories.add(category);

        CustomerCategoryDTO categoryDTO = new CustomerCategoryDTO();
        categoryDTO.setId("1");
        categoryDTO.setCategoryCode("CAT001");
        categoryDTO.setCategoryName("测试分类");
        categoryDTOs.add(categoryDTO);

        // 配置Mock行为
        when(categoryMapper.toDto(any(CustomerCategory.class))).thenReturn(categoryDTO);
    }

    @Test
    void testFindCategoriesWithPageZero() {
        // 准备测试数据
        CustomerCategoryQueryRequest request = new CustomerCategoryQueryRequest();
        request.setPage(0); // 测试页码为0的情况
        request.setSize(10);

        Page<CustomerCategory> page = new PageImpl<>(categories);
        when(categoryRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // 执行测试
        PageResponse<CustomerCategoryDTO> response = categoryService.findCategories(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        assertEquals("1", response.getContent().get(0).getId());
        assertEquals("CAT001", response.getContent().get(0).getCategoryCode());
        assertEquals("测试分类", response.getContent().get(0).getCategoryName());
    }

    @Test
    void testFindCategoriesWithPageOne() {
        // 准备测试数据
        CustomerCategoryQueryRequest request = new CustomerCategoryQueryRequest();
        request.setPage(1); // 测试页码为1的情况
        request.setSize(10);

        Page<CustomerCategory> page = new PageImpl<>(categories);
        when(categoryRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // 执行测试
        PageResponse<CustomerCategoryDTO> response = categoryService.findCategories(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        assertEquals("1", response.getContent().get(0).getId());
        assertEquals("CAT001", response.getContent().get(0).getCategoryCode());
        assertEquals("测试分类", response.getContent().get(0).getCategoryName());
    }
}
