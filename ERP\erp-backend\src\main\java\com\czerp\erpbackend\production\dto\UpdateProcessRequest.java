package com.czerp.erpbackend.production.dto;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新工序请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateProcessRequest {
    
    /**
     * 工序名称
     */
    @Size(max = 100, message = "工序名称长度不能超过100个字符")
    private String process;
    
    /**
     * 工艺要求
     */
    @Size(max = 500, message = "工艺要求长度不能超过500个字符")
    private String processRequirements;
    
    /**
     * 印版编号
     */
    @Size(max = 100, message = "印版编号长度不能超过100个字符")
    private String printingPlateNo;
    
    /**
     * 水墨编号
     */
    @Size(max = 100, message = "水墨编号长度不能超过100个字符")
    private String inkNo;
    
    /**
     * 水墨名称
     */
    @Size(max = 100, message = "水墨名称长度不能超过100个字符")
    private String inkName;
    
    /**
     * 颜色数
     */
    private Integer colorCount;
}
