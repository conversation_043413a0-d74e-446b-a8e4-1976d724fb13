spring:
  application:
    name: erp-backend
  datasource:
    url: ***************************************************************************************************************************************************
    username: root
    password: Sunshine2022.
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: com.czerp.erpbackend.config.CustomPhysicalNamingStrategy
    show-sql: false
    properties:
      hibernate:
        format_sql: false
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

server:
  port: 8080
  servlet:
    context-path: /api

jwt:
  secret: 5367566B59703373367639792F423F4528482B4D6251655468576D5A71347437
  expiration: 86400
  refresh-expiration: 604800
  blacklist:
    cleanup-interval: 3600000  # 清理黑名单的时间间隔（毫秒），默认1小时

logging:
  level:
    root: WARN
    com.czerp: INFO
    com.czerp.erpbackend.auth: INFO
    com.czerp.erpbackend.system: INFO
    com.czerp.erpbackend.product: INFO
    com.czerp.erpbackend.purchase: INFO
    org.hibernate.SQL: ERROR
    org.hibernate.type.descriptor.sql.BasicBinder: ERROR
    org.springframework.security: INFO
    org.springframework.web: INFO
    org.springframework.web.cors: INFO
    org.springframework.web.filter: INFO
    org.springframework.security.web.FilterChainProxy: INFO
    org.springframework.security.web.access: INFO
    org.springframework.security.web.util.matcher: INFO
  file:
    name: logs/erp-backend.log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
