package com.czerp.erpbackend.sales.service.impl;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.common.util.IdGenerator;
import com.czerp.erpbackend.sales.dto.SalesOrderMaterialDTO;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderMaterialRepository;
import com.czerp.erpbackend.sales.service.SalesOrderMaterialService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售订单材料信息Service实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SalesOrderMaterialServiceImpl implements SalesOrderMaterialService {

    private final SalesOrderMaterialRepository salesOrderMaterialRepository;
    private final SalesOrderItemRepository salesOrderItemRepository;

    /**
     * 根据订单行项目ID查询材料信息
     * @param orderItemId 订单行项目ID
     * @return 材料信息列表
     */
    @Override
    public List<SalesOrderMaterialDTO> getMaterialsByOrderItemId(String orderItemId) {
        List<SalesOrderMaterial> materials = salesOrderMaterialRepository.findByOrderItemIdOrderBySerialNoAsc(orderItemId);
        return materials.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 根据订单ID查询所有行项目的材料信息
     * @param orderId 订单ID
     * @return 材料信息列表
     */
    @Override
    public List<SalesOrderMaterialDTO> getMaterialsByOrderId(String orderId) {
        // 1. 查询订单的所有行项目
        List<SalesOrderItem> orderItems = salesOrderItemRepository.findByOrderId(orderId);
        if (orderItems.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 获取所有行项目ID
        List<String> orderItemIds = orderItems.stream()
                .map(SalesOrderItem::getId)
                .collect(Collectors.toList());

        // 3. 查询所有行项目的材料信息
        List<SalesOrderMaterial> materials = salesOrderMaterialRepository.findByOrderItemIdIn(orderItemIds);

        // 4. 转换为DTO
        return materials.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 保存材料信息
     * @param materialDTO 材料信息DTO
     * @return 保存后的材料信息
     */
    @Override
    @Transactional
    public SalesOrderMaterialDTO saveMaterial(SalesOrderMaterialDTO materialDTO) {
        // 1. 验证订单行项目ID
        if (!StringUtils.hasText(materialDTO.getOrderItemId())) {
            log.error("Order item ID is empty");
            throw new BusinessException("订单行项目ID不能为空");
        }

        // 2. 检查订单行项目是否存在
        log.info("Checking if order item exists: {}", materialDTO.getOrderItemId());
        Optional<SalesOrderItem> orderItemOpt = salesOrderItemRepository.findById(materialDTO.getOrderItemId());
        if (orderItemOpt.isEmpty()) {
            log.error("Order item not found: {}", materialDTO.getOrderItemId());
            throw new BusinessException("订单行项目不存在");
        }
        log.info("Order item found: {}", orderItemOpt.get().getId());

        // 3. 创建实体对象
        SalesOrderMaterial material = convertToEntity(materialDTO);

        // 4. 设置ID（如果是新增）
        if (!StringUtils.hasText(material.getId())) {
            material.setId(IdGenerator.generateId());
            log.info("Generated new ID for material: {}", material.getId());
        }

        // 5. 设置订单行项目
        material.setOrderItem(orderItemOpt.get());

        // 6. 保存
        try {
            log.info("Saving material entity: id={}, orderItemId={}, paperQuality={}, dieModelChecked={}",
                    material.getId(), material.getOrderItem().getId(),
                    material.getPaperQuality(), material.getDieModelChecked());
            SalesOrderMaterial savedMaterial = salesOrderMaterialRepository.save(material);
            log.info("Material saved successfully: {}", savedMaterial.getId());

            // 7. 转换为DTO并返回
            SalesOrderMaterialDTO resultDTO = convertToDTO(savedMaterial);
            log.info("Converted to DTO: {}", resultDTO.getId());
            return resultDTO;
        } catch (Exception e) {
            log.error("Error saving material: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 批量保存材料信息
     * @param materialDTOs 材料信息DTO列表
     * @return 保存后的材料信息列表
     */
    @Override
    @Transactional
    public List<SalesOrderMaterialDTO> saveMaterials(List<SalesOrderMaterialDTO> materialDTOs) {
        if (materialDTOs == null || materialDTOs.isEmpty()) {
            log.info("No materials to save");
            return new ArrayList<>();
        }

        log.info("Saving {} materials", materialDTOs.size());
        List<SalesOrderMaterialDTO> savedMaterials = new ArrayList<>();
        for (SalesOrderMaterialDTO materialDTO : materialDTOs) {
            try {
                log.info("Saving material: orderItemId={}, serialNo={}, paperQuality={}, dieModelChecked={}",
                        materialDTO.getOrderItemId(), materialDTO.getSerialNo(),
                        materialDTO.getPaperQuality(), materialDTO.getDieModelChecked());
                SalesOrderMaterialDTO saved = saveMaterial(materialDTO);
                savedMaterials.add(saved);
                log.info("Successfully saved material with ID: {}", saved.getId());
            } catch (Exception e) {
                log.error("Error saving material: {}", e.getMessage(), e);
            }
        }

        log.info("Saved {} out of {} materials", savedMaterials.size(), materialDTOs.size());
        return savedMaterials;
    }

    /**
     * 更新材料信息
     * @param id 材料信息ID
     * @param materialDTO 材料信息DTO
     * @return 更新后的材料信息
     */
    @Override
    @Transactional
    public SalesOrderMaterialDTO updateMaterial(String id, SalesOrderMaterialDTO materialDTO) {
        // 1. 检查ID
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("材料信息ID不能为空");
        }

        // 2. 检查材料信息是否存在
        Optional<SalesOrderMaterial> materialOpt = salesOrderMaterialRepository.findById(id);
        if (materialOpt.isEmpty()) {
            throw new BusinessException("材料信息不存在");
        }

        // 3. 获取现有材料信息
        SalesOrderMaterial existingMaterial = materialOpt.get();

        // 4. 更新字段（保留原有订单行项目关联）
        SalesOrderMaterial material = convertToEntity(materialDTO);
        material.setId(id);
        material.setOrderItem(existingMaterial.getOrderItem());

        // 5. 保存
        SalesOrderMaterial updatedMaterial = salesOrderMaterialRepository.save(material);

        // 6. 转换为DTO并返回
        return convertToDTO(updatedMaterial);
    }

    /**
     * 删除材料信息
     * @param id 材料信息ID
     */
    @Override
    @Transactional
    public void deleteMaterial(String id) {
        if (!StringUtils.hasText(id)) {
            throw new BusinessException("材料信息ID不能为空");
        }

        salesOrderMaterialRepository.deleteById(id);
    }

    /**
     * 根据订单行项目ID删除材料信息
     * @param orderItemId 订单行项目ID
     */
    @Override
    @Transactional
    public void deleteMaterialsByOrderItemId(String orderItemId) {
        if (!StringUtils.hasText(orderItemId)) {
            throw new BusinessException("订单行项目ID不能为空");
        }

        salesOrderMaterialRepository.deleteByOrderItemId(orderItemId);
    }

    /**
     * 将实体转换为DTO
     * @param material 材料信息实体
     * @return 材料信息DTO
     */
    private SalesOrderMaterialDTO convertToDTO(SalesOrderMaterial material) {
        if (material == null) {
            return null;
        }

        SalesOrderMaterialDTO dto = new SalesOrderMaterialDTO();
        dto.setId(material.getId());
        dto.setOrderItemId(material.getOrderItem().getId());
        dto.setSerialNo(material.getSerialNo());
        dto.setPaperQuality(material.getPaperQuality());
        dto.setPaperWidth(material.getPaperWidth());
        dto.setPaperLength(material.getPaperLength());
        dto.setWidthOpen(material.getWidthOpen());
        dto.setLengthOpen(material.getLengthOpen());
        dto.setBoardCount(material.getBoardCount());
        dto.setBoardLoss(material.getBoardLoss());
        dto.setMaterialUsage(material.getMaterialUsage());
        dto.setUsage(material.getUsage());
        dto.setMethod(material.getMethod());
        dto.setPressSizeWidth(material.getPressSizeWidth());
        dto.setPressMethod(material.getPressMethod());
        dto.setActualMaterialWidth(material.getActualMaterialWidth());
        dto.setActualMaterialLength(material.getActualMaterialLength());
        dto.setDieModel(material.getDieModel());
        dto.setDieModelNo(material.getDieModelNo());
        dto.setDieOpenCount(material.getDieOpenCount());
        dto.setDieModelPosition(material.getDieModelPosition());
        dto.setDieModelChecked(material.getDieModelChecked());
        dto.setUnit(material.getUnit());
        dto.setCurrentInventory(material.getCurrentInventory());
        dto.setAvailableInventory(material.getAvailableInventory());
        dto.setUseInventoryCount(material.getUseInventoryCount());
        dto.setActualMaterialLengthConverted(material.getActualMaterialLengthConverted());
        dto.setActualMaterialWidthConverted(material.getActualMaterialWidthConverted());
        dto.setSupplier(material.getSupplier());
        dto.setMaterialRemark(material.getMaterialRemark());
        dto.setPurchasedCount(material.getPurchasedCount());
        dto.setDiePressLine(material.getDiePressLine());
        dto.setPressSizeLength(material.getPressSizeLength());
        dto.setInboundCount(material.getInboundCount());
        dto.setMaterialsReceivedCount(material.getMaterialsReceivedCount());
        dto.setCreatedBy(material.getCreatedBy());
        dto.setCreatedByName(material.getCreatedByName());
        dto.setCreatedTime(material.getCreatedTime());
        dto.setUpdatedBy(material.getUpdatedBy());
        dto.setUpdatedByName(material.getUpdatedByName());
        dto.setUpdatedTime(material.getUpdatedTime());

        return dto;
    }

    /**
     * 将DTO转换为实体
     * @param dto 材料信息DTO
     * @return 材料信息实体
     */
    private SalesOrderMaterial convertToEntity(SalesOrderMaterialDTO dto) {
        if (dto == null) {
            return null;
        }

        SalesOrderMaterial material = new SalesOrderMaterial();
        material.setId(dto.getId());
        material.setSerialNo(dto.getSerialNo());
        material.setPaperQuality(dto.getPaperQuality());
        material.setPaperWidth(dto.getPaperWidth());
        material.setPaperLength(dto.getPaperLength());
        material.setWidthOpen(dto.getWidthOpen());
        material.setLengthOpen(dto.getLengthOpen());
        material.setBoardCount(dto.getBoardCount());
        material.setBoardLoss(dto.getBoardLoss());
        material.setMaterialUsage(dto.getMaterialUsage());
        material.setUsage(dto.getUsage());
        material.setMethod(dto.getMethod());
        material.setPressSizeWidth(dto.getPressSizeWidth());
        material.setPressMethod(dto.getPressMethod());
        material.setActualMaterialWidth(dto.getActualMaterialWidth());
        material.setActualMaterialLength(dto.getActualMaterialLength());
        material.setDieModel(dto.getDieModel());
        material.setDieModelNo(dto.getDieModelNo());
        material.setDieOpenCount(dto.getDieOpenCount());
        material.setDieModelPosition(dto.getDieModelPosition());
        material.setDieModelChecked(dto.getDieModelChecked());
        material.setUnit(dto.getUnit());
        material.setCurrentInventory(dto.getCurrentInventory());
        material.setAvailableInventory(dto.getAvailableInventory());
        material.setUseInventoryCount(dto.getUseInventoryCount());
        material.setActualMaterialLengthConverted(dto.getActualMaterialLengthConverted());
        material.setActualMaterialWidthConverted(dto.getActualMaterialWidthConverted());
        material.setSupplier(dto.getSupplier());
        material.setMaterialRemark(dto.getMaterialRemark());
        material.setPurchasedCount(dto.getPurchasedCount());
        material.setDiePressLine(dto.getDiePressLine());
        material.setPressSizeLength(dto.getPressSizeLength());
        material.setInboundCount(dto.getInboundCount());
        material.setMaterialsReceivedCount(dto.getMaterialsReceivedCount());
        material.setCreatedByName(dto.getCreatedByName());
        material.setUpdatedByName(dto.getUpdatedByName());

        return material;
    }
}
