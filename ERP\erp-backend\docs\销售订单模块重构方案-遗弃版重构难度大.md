# 销售订单模块重构方案

## 1. 问题分析

当前`SalesOrderServiceImpl`类存在以下主要问题：

1. **职责过多**：单个服务类承担了订单创建、订单明细处理、工序管理、用料管理等多种职责
2. **高耦合**：与多个其他服务和仓库紧密耦合
3. **事务边界过大**：整个订单创建/更新过程在一个大事务中完成
4. **缺乏领域模型**：业务逻辑分散在服务层，领域模型贫血
5. **ID映射逻辑重复**：前端ID与后端ID的映射逻辑在多处重复
6. **错误处理不一致**：部分地方抛出异常，部分地方仅记录日志
7. **缺乏事件驱动机制**：使用直接方法调用而非事件通知

## 2. 重构目标

1. **职责分离**：将不同职责分配到专门的服务类中
2. **降低耦合**：通过领域事件和接口抽象减少直接依赖
3. **合理的事务边界**：细化事务范围，提高并发性能
4. **丰富领域模型**：将业务逻辑从服务层移至领域模型
5. **统一错误处理**：采用一致的错误处理策略
6. **提高可测试性**：使各组件易于单元测试
7. **增强可维护性**：提高代码可读性和可理解性

## 3. 架构设计

### 3.1 分层架构

采用经典的四层架构，但强化领域层：

1. **表示层**：Controller，负责API接口
2. **应用层**：Application Service，编排业务流程
3. **领域层**：Domain Model + Domain Service，实现核心业务逻辑
4. **基础设施层**：Repository，外部服务集成等

### 3.2 领域模型设计

#### 核心聚合根

1. **订单聚合（Order Aggregate）**
   - 订单（Order）- 聚合根
   - 订单明细项（OrderItem）- 实体
   - 订单状态（OrderStatus）- 值对象

2. **工序聚合（Process Aggregate）**
   - 工序（Process）- 聚合根
   - 工序步骤（ProcessStep）- 实体

3. **用料聚合（Material Aggregate）**
   - 用料（Material）- 聚合根
   - 用料明细（MaterialDetail）- 实体

#### 领域服务

1. **订单领域服务（OrderDomainService）**
   - 订单创建逻辑
   - 订单状态转换逻辑
   - 订单号生成逻辑

2. **工序领域服务（ProcessDomainService）**
   - 工序创建和管理逻辑
   - 工序与订单明细项关联逻辑

3. **用料领域服务（MaterialDomainService）**
   - 用料创建和管理逻辑
   - 用料与订单明细项关联逻辑

### 3.3 应用服务设计

1. **订单应用服务（OrderApplicationService）**
   - 创建订单
   - 更新订单
   - 查询订单

2. **工序应用服务（ProcessApplicationService）**
   - 管理工序
   - 查询工序

3. **用料应用服务（MaterialApplicationService）**
   - 管理用料
   - 查询用料

### 3.4 事件驱动设计

#### 领域事件

1. **订单创建事件（OrderCreatedEvent）**
   - 包含订单ID和订单明细项ID映射
   - 触发工序和用料处理

2. **订单更新事件（OrderUpdatedEvent）**
   - 包含订单ID和订单明细项ID映射
   - 触发工序和用料更新

3. **订单明细项创建事件（OrderItemCreatedEvent）**
   - 包含订单明细项信息
   - 触发生产单号生成

#### 事件处理器

1. **工序事件处理器（ProcessEventHandler）**
   - 监听订单事件
   - 处理工序数据

2. **用料事件处理器（MaterialEventHandler）**
   - 监听订单事件
   - 处理用料数据

3. **生产单号事件处理器（ProductionOrderNumberHandler）**
   - 监听订单明细项事件
   - 生成生产单号

## 4. 实施步骤

### 阶段一：基础设施准备

1. **引入事件框架**
   - 集成Spring事件机制或轻量级消息队列
   - 实现事件发布和订阅机制

2. **设计通用组件**
   - ID生成器
   - 事务管理器
   - 异常处理框架

3. **设计领域模型基类**
   - 实体基类
   - 值对象基类
   - 聚合根基类

### 阶段二：领域模型实现

1. **实现订单聚合**
   - 订单实体
   - 订单明细项实体
   - 订单状态值对象

2. **实现工序聚合**
   - 工序实体
   - 工序步骤实体

3. **实现用料聚合**
   - 用料实体
   - 用料明细实体

4. **实现领域服务**
   - 订单领域服务
   - 工序领域服务
   - 用料领域服务

### 阶段三：应用服务实现

1. **实现订单应用服务**
   - 创建订单
   - 更新订单
   - 查询订单

2. **实现工序应用服务**
   - 管理工序
   - 查询工序

3. **实现用料应用服务**
   - 管理用料
   - 查询用料

### 阶段四：事件机制实现

1. **实现领域事件**
   - 订单创建事件
   - 订单更新事件
   - 订单明细项创建事件

2. **实现事件处理器**
   - 工序事件处理器
   - 用料事件处理器
   - 生产单号事件处理器

### 阶段五：API层调整

1. **调整Controller**
   - 使用新的应用服务
   - 统一响应格式

2. **实现DTO转换**
   - 请求DTO到领域模型的转换
   - 领域模型到响应DTO的转换

### 阶段六：测试与验证

1. **单元测试**
   - 领域模型测试
   - 应用服务测试
   - 事件处理器测试

2. **集成测试**
   - API测试
   - 事务测试
   - 性能测试

## 5. 代码示例

### 5.1 领域模型示例

```java
// 订单聚合根
@Entity
@Table(name = "sales_order")
public class Order extends AggregateRoot {
    // 基本属性
    
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<OrderItem> items = new ArrayList<>();
    
    // 领域行为
    public void addItem(OrderItem item) {
        items.add(item);
        item.setOrder(this);
        registerEvent(new OrderItemAddedEvent(this, item));
    }
    
    public void updateStatus(OrderStatus newStatus) {
        // 状态转换逻辑
        this.status = newStatus;
        registerEvent(new OrderStatusChangedEvent(this, newStatus));
    }
}

// 订单明细项实体
@Entity
@Table(name = "sales_order_item")
public class OrderItem extends Entity {
    // 基本属性
    
    @ManyToOne
    @JoinColumn(name = "order_id")
    private Order order;
    
    // 领域行为
    public void assignProductionOrderNumber(String productionOrderNumber) {
        this.productionOrderNumber = productionOrderNumber;
        registerEvent(new ProductionOrderNumberAssignedEvent(this));
    }
}
```

### 5.2 领域服务示例

```java
@Service
public class OrderDomainService {
    private final OrderNumberGenerator orderNumberGenerator;
    
    public OrderDomainService(OrderNumberGenerator orderNumberGenerator) {
        this.orderNumberGenerator = orderNumberGenerator;
    }
    
    public void assignOrderNumber(Order order) {
        String orderNumber = orderNumberGenerator.generate();
        order.setOrderNo(orderNumber);
    }
    
    public void validateOrder(Order order) {
        // 订单验证逻辑
    }
}
```

### 5.3 应用服务示例

```java
@Service
@Transactional
public class OrderApplicationService {
    private final OrderRepository orderRepository;
    private final OrderDomainService orderDomainService;
    private final EventPublisher eventPublisher;
    
    // 构造函数注入
    
    public OrderDTO createOrder(CreateOrderCommand command) {
        // 1. 创建订单聚合
        Order order = new Order();
        // 2. 设置基本属性
        order.setCustomerCode(command.getCustomerCode());
        // ...其他属性设置
        
        // 3. 领域服务处理
        orderDomainService.assignOrderNumber(order);
        orderDomainService.validateOrder(order);
        
        // 4. 添加订单明细
        for (CreateOrderItemCommand itemCommand : command.getItems()) {
            OrderItem item = new OrderItem();
            // 设置明细属性
            order.addItem(item);
        }
        
        // 5. 保存聚合
        Order savedOrder = orderRepository.save(order);
        
        // 6. 发布事件
        eventPublisher.publish(new OrderCreatedEvent(savedOrder));
        
        // 7. 返回DTO
        return orderMapper.toDTO(savedOrder);
    }
}
```

### 5.4 事件处理示例

```java
@Component
public class ProcessEventHandler {
    private final ProcessApplicationService processService;
    
    @EventListener
    public void handleOrderCreatedEvent(OrderCreatedEvent event) {
        Order order = event.getOrder();
        
        // 处理工序数据
        if (event.getProcesses() != null) {
            for (ProcessDTO processDTO : event.getProcesses()) {
                // 使用订单明细项ID映射
                String realOrderItemId = order.getItemIdMapping().get(processDTO.getOrderItemId());
                if (realOrderItemId != null) {
                    processDTO.setOrderItemId(realOrderItemId);
                    processService.createProcess(processDTO);
                }
            }
        }
    }
}
```

## 6. 收益与风险

### 收益

1. **代码可维护性提高**：职责清晰，模块化程度高
2. **业务逻辑更清晰**：核心逻辑集中在领域模型中
3. **可测试性增强**：各组件可独立测试
4. **并发性能提升**：细化事务边界，减少锁竞争
5. **扩展性增强**：新功能可以通过添加新的事件处理器实现

### 风险

1. **重构工作量大**：需要对现有代码进行大量修改
2. **学习曲线**：团队需要学习领域驱动设计和事件驱动架构
3. **性能开销**：事件机制可能带来一定的性能开销
4. **复杂性增加**：整体架构复杂度提高

## 7. 结论

通过采用领域驱动设计和事件驱动架构，可以有效解决当前`SalesOrderServiceImpl`类中存在的职责不清晰和高耦合问题。重构后的系统将具有更好的可维护性、可测试性和可扩展性，能够更好地支持业务的发展和变化。

重构工作可以分阶段进行，先搭建基础设施，然后逐步实现领域模型、应用服务和事件机制，最后调整API层并进行全面测试。这种渐进式的重构方式可以降低风险，确保系统的稳定性。
