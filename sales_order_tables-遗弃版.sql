-- 销售订单表结构
-- 创建日期：2023-12-01

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 销售订单主表
CREATE TABLE IF NOT EXISTS `sal_order` (
  `id` varchar(36) NOT NULL COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '销售单号',
  `production_order_no` varchar(50) DEFAULT NULL COMMENT '生产单号',
  `customer_id` varchar(36) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `customer_order_no` varchar(50) DEFAULT NULL COMMENT '客户订单号',
  `order_date` date NOT NULL COMMENT '日期',
  `delivery_date` date DEFAULT NULL COMMENT '交期',
  `currency` varchar(20) DEFAULT NULL COMMENT '币别',
  `sales_person` varchar(50) DEFAULT NULL COMMENT '销售员',
  `total_amount` decimal(15,2) DEFAULT 0.00 COMMENT '金额总计',
  `total_weight` decimal(15,3) DEFAULT 0.000 COMMENT '总重(KG)',
  `total_area` decimal(15,3) DEFAULT 0.000 COMMENT '总面积(平米)',
  `total_volume` decimal(15,3) DEFAULT 0.000 COMMENT '总体积(立方米)',
  `tax_rate` decimal(5,2) DEFAULT NULL COMMENT '税率',
  `is_taxed` tinyint(1) DEFAULT 0 COMMENT '是否含税',
  `production_status` varchar(20) DEFAULT 'pending' COMMENT '成品状态(pending-待生产,producing-生产中,completed-已完成)',
  `inventory_status` varchar(20) DEFAULT 'pending' COMMENT '成品入库(pending-待入库,partial-部分入库,completed-已入库)',
  `delivery_status` varchar(20) DEFAULT 'pending' COMMENT '送货状态(pending-待送货,partial-部分送货,completed-已送货)',
  `delivered_quantity` int DEFAULT 0 COMMENT '已送货数',
  `spare_delivered_quantity` int DEFAULT 0 COMMENT '已送备品数',
  `undelivered_quantity` int DEFAULT 0 COMMENT '未送货数',
  `undelivered_spare_quantity` int DEFAULT 0 COMMENT '未送备品数',
  `returned_quantity` int DEFAULT 0 COMMENT '退货数',
  `reconciled_quantity` int DEFAULT 0 COMMENT '已对账数',
  `last_delivery_date` date DEFAULT NULL COMMENT '送货日期',
  `last_inventory_date` date DEFAULT NULL COMMENT '最近入库日期',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `production_remark` varchar(500) DEFAULT NULL COMMENT '生产备注',
  `is_terminated` tinyint(1) DEFAULT 0 COMMENT '中止',
  `created_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_print_time` datetime DEFAULT NULL COMMENT '最近打印时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_production_order_no` (`production_order_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_customer_name` (`customer_name`),
  KEY `idx_customer_order_no` (`customer_order_no`),
  KEY `idx_order_date` (`order_date`),
  KEY `idx_delivery_date` (`delivery_date`),
  KEY `idx_sales_person` (`sales_person`),
  KEY `idx_production_status` (`production_status`),
  KEY `idx_delivery_status` (`delivery_status`),
  KEY `idx_inventory_status` (`inventory_status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单主表';

-- 销售订单明细表
CREATE TABLE IF NOT EXISTS `sal_order_item` (
  `id` varchar(36) NOT NULL COMMENT '明细ID',
  `order_id` varchar(36) NOT NULL COMMENT '订单ID',
  `product_id` varchar(36) DEFAULT NULL COMMENT '产品ID',
  `customer_product_code` varchar(50) DEFAULT NULL COMMENT '客方货号',
  `product_name` varchar(100) NOT NULL COMMENT '品名',
  `process_requirements` text DEFAULT NULL COMMENT '工艺要求',
  `box_type` varchar(50) DEFAULT NULL COMMENT '盒式',
  `paper_type` varchar(50) DEFAULT NULL COMMENT '纸质',
  `production_paper_type` varchar(50) DEFAULT NULL COMMENT '生产纸质',
  `specification` varchar(100) DEFAULT NULL COMMENT '规格',
  `production_specification` varchar(100) DEFAULT NULL COMMENT '生产规格',
  `quantity` int NOT NULL DEFAULT 0 COMMENT '数量',
  `spare_quantity` int DEFAULT 0 COMMENT '备品数',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `price` decimal(15,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(15,2) DEFAULT 0.00 COMMENT '金额',
  `is_special_price` tinyint(1) DEFAULT 0 COMMENT '特价',
  `spare_ratio` decimal(5,2) DEFAULT NULL COMMENT '成套比例',
  `use_inventory` int DEFAULT 0 COMMENT '使用库存',
  `process` varchar(100) DEFAULT NULL COMMENT '工序',
  `connection_method` varchar(50) DEFAULT NULL COMMENT '连接方式',
  `board_type` varchar(50) DEFAULT NULL COMMENT '纸板类别',
  `line_size_width` decimal(10,2) DEFAULT NULL COMMENT '压线尺寸(纸度)',
  `line_size_length` decimal(10,2) DEFAULT NULL COMMENT '压线尺寸(纸长)',
  `die_cutting_count` int DEFAULT NULL COMMENT '模开数',
  `use_board_inventory` int DEFAULT 0 COMMENT '使用纸板库存数',
  `unit_weight` decimal(10,3) DEFAULT NULL COMMENT '单重',
  `product_area` decimal(10,3) DEFAULT NULL COMMENT '产品面积',
  `product_volume` decimal(10,3) DEFAULT NULL COMMENT '产品体积',
  `corrugated_type` varchar(50) DEFAULT NULL COMMENT '楞别',
  `component` varchar(100) DEFAULT NULL COMMENT '部件',
  `receiver_unit` varchar(100) DEFAULT NULL COMMENT '收货单位',
  `receiver` varchar(50) DEFAULT NULL COMMENT '收货人',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人电话',
  `shipping_address` varchar(200) DEFAULT NULL COMMENT '收货地址',
  `customer_purchaser` varchar(50) DEFAULT NULL COMMENT '客户采购员',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `production_order_no` varchar(50) DEFAULT NULL COMMENT '生产单号',
  `created_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_customer_product_code` (`customer_product_code`),
  KEY `idx_product_name` (`product_name`),
  KEY `idx_specification` (`specification`),
  KEY `idx_production_order_no` (`production_order_no`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单明细表';

-- 销售订单生产排程表
CREATE TABLE IF NOT EXISTS `sal_order_production_schedule` (
  `id` varchar(36) NOT NULL COMMENT '排程ID',
  `order_id` varchar(36) NOT NULL COMMENT '订单ID',
  `order_item_id` varchar(36) NOT NULL COMMENT '订单明细ID',
  `schedule_no` varchar(50) NOT NULL COMMENT '排程编号',
  `scheduled_quantity` int NOT NULL DEFAULT 0 COMMENT '排程数量',
  `scheduled_date` date NOT NULL COMMENT '排程日期',
  `production_line` varchar(50) DEFAULT NULL COMMENT '生产线',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态(pending-待生产,producing-生产中,completed-已完成)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_schedule_no` (`schedule_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_item_id` (`order_item_id`),
  KEY `idx_scheduled_date` (`scheduled_date`),
  KEY `idx_status` (`status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单生产排程表';

-- 销售订单送货记录表
CREATE TABLE IF NOT EXISTS `sal_order_delivery` (
  `id` varchar(36) NOT NULL COMMENT '送货ID',
  `order_id` varchar(36) NOT NULL COMMENT '订单ID',
  `order_item_id` varchar(36) NOT NULL COMMENT '订单明细ID',
  `delivery_no` varchar(50) NOT NULL COMMENT '送货单号',
  `delivery_date` date NOT NULL COMMENT '送货日期',
  `delivered_quantity` int NOT NULL DEFAULT 0 COMMENT '送货数量',
  `spare_delivered_quantity` int DEFAULT 0 COMMENT '送货备品数量',
  `receiver` varchar(50) DEFAULT NULL COMMENT '收货人',
  `delivery_address` varchar(200) DEFAULT NULL COMMENT '送货地址',
  `status` varchar(20) DEFAULT 'delivered' COMMENT '状态(delivered-已送货,returned-已退货)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_delivery_no` (`delivery_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_item_id` (`order_item_id`),
  KEY `idx_delivery_date` (`delivery_date`),
  KEY `idx_status` (`status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单送货记录表';

-- 销售订单入库记录表
CREATE TABLE IF NOT EXISTS `sal_order_inventory` (
  `id` varchar(36) NOT NULL COMMENT '入库ID',
  `order_id` varchar(36) NOT NULL COMMENT '订单ID',
  `order_item_id` varchar(36) NOT NULL COMMENT '订单明细ID',
  `inventory_no` varchar(50) NOT NULL COMMENT '入库单号',
  `inventory_date` date NOT NULL COMMENT '入库日期',
  `inventory_quantity` int NOT NULL DEFAULT 0 COMMENT '入库数量',
  `warehouse_id` varchar(36) DEFAULT NULL COMMENT '仓库ID',
  `warehouse_name` varchar(100) DEFAULT NULL COMMENT '仓库名称',
  `location_id` varchar(36) DEFAULT NULL COMMENT '库位ID',
  `location_name` varchar(100) DEFAULT NULL COMMENT '库位名称',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_inventory_no` (`inventory_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_item_id` (`order_item_id`),
  KEY `idx_inventory_date` (`inventory_date`),
  KEY `idx_warehouse_id` (`warehouse_id`),
  KEY `idx_location_id` (`location_id`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单入库记录表';

-- 添加外键约束
ALTER TABLE `sal_order_item`
  ADD CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `sal_order` (`id`) ON DELETE CASCADE;

ALTER TABLE `sal_order_production_schedule`
  ADD CONSTRAINT `fk_production_schedule_order` FOREIGN KEY (`order_id`) REFERENCES `sal_order` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_production_schedule_order_item` FOREIGN KEY (`order_item_id`) REFERENCES `sal_order_item` (`id`) ON DELETE CASCADE;

ALTER TABLE `sal_order_delivery`
  ADD CONSTRAINT `fk_delivery_order` FOREIGN KEY (`order_id`) REFERENCES `sal_order` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_delivery_order_item` FOREIGN KEY (`order_item_id`) REFERENCES `sal_order_item` (`id`) ON DELETE CASCADE;

ALTER TABLE `sal_order_inventory`
  ADD CONSTRAINT `fk_inventory_order` FOREIGN KEY (`order_id`) REFERENCES `sal_order` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inventory_order_item` FOREIGN KEY (`order_item_id`) REFERENCES `sal_order_item` (`id`) ON DELETE CASCADE;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
