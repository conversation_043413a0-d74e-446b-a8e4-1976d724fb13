package com.czerp.erpbackend.system.entity.enums;

import lombok.Getter;

/**
 * 权限类型枚举
 */
@Getter
public enum PermissionType {
    
    /**
     * 菜单
     */
    MENU("menu", "菜单"),
    
    /**
     * 按钮
     */
    BUTTON("button", "按钮"),
    
    /**
     * 接口
     */
    API("api", "接口");
    
    private final String code;
    private final String description;
    
    PermissionType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据编码获取权限类型
     * @param code 编码
     * @return 权限类型
     */
    public static PermissionType fromCode(String code) {
        for (PermissionType type : PermissionType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 