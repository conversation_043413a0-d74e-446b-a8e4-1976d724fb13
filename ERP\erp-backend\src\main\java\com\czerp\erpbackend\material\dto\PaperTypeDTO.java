package com.czerp.erpbackend.material.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 纸质类别DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaperTypeDTO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 纸板类别名称
     */
    private String paperTypeName;

    /**
     * 打钉钉口(inch)
     */
    private BigDecimal staplingFlapInch;

    /**
     * 打钉钉口(cm)
     */
    private BigDecimal staplingFlapCm;

    /**
     * 粘箱钉口(inch)
     */
    private BigDecimal gluingFlapInch;

    /**
     * 粘箱钉口(cm)
     */
    private BigDecimal gluingFlapCm;

    /**
     * 加分(mm)
     */
    private BigDecimal addMarginMm;

    /**
     * 缩分(mm)
     */
    private BigDecimal reduceMarginMm;

    /**
     * 厚度(mm)
     */
    private BigDecimal thicknessMm;

    /**
     * 层数
     */
    private Integer layerCount;

    /**
     * 每板数
     */
    private Integer sheetsPerBoard;
}
