package com.czerp.erpbackend.system.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 系统角色实体
 */
@Entity
@Table(name = "sys_role")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Role extends BaseEntity {
    
    /**
     * 角色ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;
    
    /**
     * 角色名称
     */
    @Column(name = "name", length = 50, nullable = false)
    private String name;
    
    /**
     * 角色编码
     */
    @Column(name = "code", length = 50, nullable = false, unique = true)
    private String code;
    
    /**
     * 角色描述
     */
    @Column(name = "description", length = 255)
    private String description;
    
    /**
     * 状态(active-启用,inactive-停用)
     */
    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";
} 