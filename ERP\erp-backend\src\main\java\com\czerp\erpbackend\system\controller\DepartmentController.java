package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.system.entity.Department;
import com.czerp.erpbackend.system.repository.DepartmentRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 部门控制器
 */
@RestController
@RequestMapping("/system/departments")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Department Management", description = "部门管理相关接口")
public class DepartmentController {
    
    private final DepartmentRepository departmentRepository;
    
    @GetMapping
    @Operation(summary = "获取部门列表", description = "获取所有部门列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('department:list')")
    public ResponseEntity<ApiResponse<List<Department>>> getAllDepartments() {
        log.info("Getting all departments");
        List<Department> departments = departmentRepository.findAll();
        return ResponseEntity.ok(ApiResponse.success(departments));
    }
}
