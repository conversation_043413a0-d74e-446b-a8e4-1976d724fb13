package com.czerp.erpbackend.production.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

/**
 * 工序查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessQueryRequest {
    
    /**
     * 关键词（工序名称、工艺要求、印版编号、水墨编号、水墨名称）
     */
    private String keyword;
    
    /**
     * 开始时间
     */
    private LocalDateTime startDateTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endDateTime;
    
    /**
     * 页码（从1开始）
     */
    private Integer page;
    
    /**
     * 每页大小
     */
    private Integer size;
}
