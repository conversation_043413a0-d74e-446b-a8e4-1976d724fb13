# 采购订单引用API文档（用于入库管理）

## 1. 概述

采购订单引用API提供了查询采购订单明细的功能，用于在创建入库单时引用采购订单数据。这些接口只返回未完全入库的采购订单明细，以避免重复入库。当入库单引用采购订单明细创建后，系统会自动更新采购订单明细的已入库数量。

## 2. 接口列表

| 接口名称 | 请求方式 | 接口路径 | 描述 |
| --- | --- | --- | --- |
| 分页查询可用于入库的采购订单明细 | GET | /purchase-orders-for-inbound/items | 分页查询可用于入库的采购订单明细 |
| 根据ID查询采购订单明细 | GET | /purchase-orders-for-inbound/items/{id} | 根据ID查询采购订单明细 |
| 根据多个ID查询采购订单明细 | GET | /purchase-orders-for-inbound/items/batch | 根据多个ID查询采购订单明细 |

## 3. 接口详情

### 3.1 分页查询可用于入库的采购订单明细

该接口只返回未完全入库的采购订单明细（数量 > 已入库数）。

#### 请求

**请求方式**: GET  
**请求路径**: `/purchase-orders-for-inbound/items`

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| page | int | 是 | 页码（从0开始） | 0 |
| size | int | 是 | 每页大小 | 20 |
| keyword | string | 否 | 关键字（采购单号、供应商名称、生产单号等） | "CG202401001" |
| supplierCode | string | 否 | 供应商编码 | "SUP001" |
| supplierName | string | 否 | 供应商名称 | "XX纸业" |
| purchaseOrderNo | string | 否 | 采购单号 | "CG202401001" |
| paperType | string | 否 | 纸质 | "白卡纸" |
| corrugationType | string | 否 | 楞别 | "3A" |
| purchaseDateStart | date | 否 | 采购日期开始 | "2024-01-01" |
| purchaseDateEnd | date | 否 | 采购日期结束 | "2024-01-31" |
| deliveryDateStart | date | 否 | 交期开始 | "2024-02-01" |
| deliveryDateEnd | date | 否 | 交期结束 | "2024-02-28" |
| customerCode | string | 否 | 客户编码 | "CUS001" |
| customerName | string | 否 | 客户名称 | "XX公司" |
| productionOrderNo | string | 否 | 生产单号 | "P202401001" |
| paperBoardCategory | string | 否 | 纸板类别 | "白卡纸" |

#### 响应

**响应格式**: JSON

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": 1,
        "purchaseOrderId": 1,
        "inboundQuantity": 1000,
        "inboundAmount": 5000.00,
        "supplierName": "XX纸业有限公司",
        "purchaseOrderNo": "CG202401001",
        "purchaseDate": "2024-01-15",
        "quantity": 1000,
        "paperQuality": "白卡纸",
        "bindingSpecification": "对开",
        "receivedQuantity": 0,
        "returnedQuantity": 0,
        "unreceivedQuantity": 1000,
        "productionOrderNo": "P202401001",
        "customerCode": "CUS001",
        "customerName": "XX包装公司",
        "customerOrderNo": "XS202401001",
        "customerProductCode": "PKG001",
        "productName": "包装盒",
        "processRequirements": "开槽→打角→打钉",
        "product": "天地盒 白卡纸",
        "productSpecification": "300*200*100mm",
        "productionSpecification": "310*210*105mm",
        "salesOrderDeliveryDate": "2024-02-15",
        "createdBy": "admin",
        "paperBoardCategory": "白卡纸",
        "price": 5.00,
        "amount": 5000.00,
        "deliveryDate": "2024-02-10",
        "paperWidth": 300.00,
        "paperLength": 200.00,
        "corrugationType": "3A",
        "creasingSize": "280*180",
        "creasingMethod": "压线",
        "foldingSpecification": "对开",
        "lengthMeters": 100.00,
        "areaSquareMeters": 60.00,
        "volumeCubicMeters": 6.00,
        "unitWeight": 0.05,
        "totalWeightKg": 50.00,
        "processingFee": 100.00,
        "currency": "CNY",
        "remarks": "备注信息",
        "sourceSalesOrderItemId": "sales-item-001",
        "salesOrderNo": "XS202401001",
        "salesOrderDate": "2024-01-10",
        "boxType": "天地盒",
        "orderPaperType": "白卡纸",
        "orderQuantity": 1000,
        "boardCount": 10,
        "dieOpenCount": 100,
        "paperQuotation": 4.80,
        "discount": 0.96
      }
    ],
    "page": 0,
    "size": 20,
    "totalElements": 1,
    "totalPages": 1,
    "first": true,
    "last": true
  }
}
```

### 3.2 根据ID查询采购订单明细

该接口只返回未完全入库的采购订单明细（数量 > 已入库数）。如果指定ID的采购订单明细已完全入库，则返回null。

#### 请求

**请求方式**: GET
**请求路径**: `/purchase-orders-for-inbound/items/{id}`

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| id | long | 是 | 采购订单明细ID | 1 |

#### 响应

**响应格式**: JSON

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "purchaseOrderId": 1,
    "inboundQuantity": 1000,
    "inboundAmount": 5000.00,
    "supplierName": "XX纸业有限公司",
    "purchaseOrderNo": "CG202401001",
    "purchaseDate": "2024-01-15",
    "quantity": 1000,
    "paperQuality": "白卡纸",
    "bindingSpecification": "对开",
    "receivedQuantity": 0,
    "returnedQuantity": 0,
    "unreceivedQuantity": 1000,
    "productionOrderNo": "P202401001",
    "customerCode": "CUS001",
    "customerName": "XX包装公司",
    "customerOrderNo": "XS202401001",
    "customerProductCode": "PKG001",
    "productName": "包装盒",
    "processRequirements": "开槽→打角→打钉",
    "product": "天地盒 白卡纸",
    "productSpecification": "300*200*100mm",
    "productionSpecification": "310*210*105mm",
    "salesOrderDeliveryDate": "2024-02-15",
    "createdBy": "admin",
    "paperBoardCategory": "白卡纸",
    "price": 5.00,
    "amount": 5000.00,
    "deliveryDate": "2024-02-10"
  }
}
```

### 3.3 根据多个ID查询采购订单明细

该接口只返回未完全入库的采购订单明细（数量 > 已入库数）。已完全入库的采购订单明细将被自动过滤掉。

#### 请求

**请求方式**: GET
**请求路径**: `/purchase-orders-for-inbound/items/batch`

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| ids | long[] | 是 | 采购订单明细ID列表 | [1,2,3] |

#### 响应

**响应格式**: JSON

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "purchaseOrderId": 1,
      "inboundQuantity": 1000,
      "inboundAmount": 5000.00,
      "supplierName": "XX纸业有限公司",
      "purchaseOrderNo": "CG202401001",
      "purchaseDate": "2024-01-15",
      "quantity": 1000,
      "paperQuality": "白卡纸",
      "bindingSpecification": "对开",
      "receivedQuantity": 0,
      "returnedQuantity": 0,
      "unreceivedQuantity": 1000,
      "productionOrderNo": "P202401001",
      "customerCode": "CUS001",
      "customerName": "XX包装公司"
    }
  ]
}
```

## 4. 字段说明

### 4.1 核心字段

| 字段名 | 类型 | 描述 | 来源 |
| --- | --- | --- | --- |
| inboundQuantity | int | 本次入库数（可编辑） | 默认为采购数量 |
| inboundAmount | decimal | 入库金额 | 对应采购单金额字段 |
| supplierName | string | 供应商名称 | 采购订单头表 |
| purchaseOrderNo | string | 采购单号 | 采购订单头表 |
| purchaseDate | date | 采购日期 | 采购订单头表 |
| quantity | int | 采购数量 | 采购订单明细表 |
| paperQuality | string | 纸质 | 采购订单明细表 |
| bindingSpecification | string | 规格（合订规格） | 采购订单明细表 |
| receivedQuantity | int | 已入库数 | 采购订单明细表 |
| returnedQuantity | int | 已退货数 | 计算字段（暂时为0） |
| unreceivedQuantity | int | 未入库数 | 动态计算：采购数量-已入库数+已退货数 |

### 4.2 销售订单关联字段

| 字段名 | 类型 | 描述 | 来源 |
| --- | --- | --- | --- |
| productionOrderNo | string | 生产单号 | 销售订单明细表 |
| customerCode | string | 客户编码 | 销售订单头表 |
| customerName | string | 客户名称 | 销售订单头表 |
| customerOrderNo | string | 客户订单号 | 销售订单明细表 |
| customerProductCode | string | 客方货号 | 销售订单明细表 |
| productName | string | 品名 | 销售订单明细表 |
| processRequirements | string | 工艺要求 | 销售订单明细表 |
| product | string | 产品（盒式+订单纸质） | 组合字段 |
| productSpecification | string | 产品规格 | 销售订单明细表 |
| productionSpecification | string | 产品生产规格 | 销售订单明细表 |
| salesOrderDeliveryDate | date | 销售单交期 | 销售订单明细表 |
| boardCount | int | 纸板数 | 销售订单材料表 |
| dieOpenCount | int | 模开数 | 销售订单材料表 |

## 5. 权限要求

- `inventory:inbound:create` - 查询可用于入库的采购订单明细
- `inventory:inbound:read` - 查询采购订单明细详情

## 6. 注意事项

1. **所有接口**都只返回未完全入库的采购订单明细（数量 > 已入库数）
2. 根据ID查询接口：如果指定ID的采购订单明细已完全入库，返回null
3. 批量查询接口：已完全入库的采购订单明细将被自动过滤掉
4. 当入库单引用采购订单明细创建后，需要更新采购订单明细的已入库数量
5. 未入库数是动态计算的，公式为：采购数量 - 已入库数 + 已退货数
6. 产品字段是盒式和订单纸质的组合，使用空格分隔
7. 销售订单相关字段通过sourceSalesOrderItemId关联获取
