# 新建采购订单字段提交分析报告

## 概述

本报告基于前端代码（`czerp-frontend/src/views/purchase/PurchaseOrderEdit.vue`）的深入分析，详细说明了新建采购订单时提交到后端的字段情况，包括哪些字段被提交、哪些字段被排除，以及数据处理逻辑。

## 分析依据

- **主要文件**: `czerp-frontend/src/views/purchase/PurchaseOrderEdit.vue`
- **关键方法**: `handleSave()` (第521-610行)
- **类型定义**: `czerp-frontend/src/types/purchaseOrder.d.ts`
- **分析日期**: 2024-12-28

## 数据提交逻辑

### 主表单字段处理

#### 提交的字段（12个）
| 字段名 | 中文名称 | 类型 | 说明 |
|--------|----------|------|------|
| `supplierCode` | 供应商编码 | string | 必填，用于标识供应商 |
| `supplierName` | 供应商名称 | string | 供应商显示名称 |
| `purchaseDate` | 采购日期 | string | 格式：YYYY-MM-DD |
| `purchaseType` | 采购类型 | string | 可选值：normal/urgent/planned |
| `purchaser` | 采购员 | string | 必填，采购员用户名 |
| `paymentMethod` | 付款方式 | string | 如：现金、月结30天等 |
| `tradingUnit` | 交易单位 | string | 交易单位信息 |
| `address` | 地址 | string | 供应商地址 |
| `contactPerson` | 联系人 | string | 联系人姓名 |
| `phone` | 电话 | string | 联系电话 |
| `remarks` | 备注 | string | 订单级别备注 |
| `items` | 明细项 | array | 处理后的明细项数组 |

#### 排除的字段（4个）
| 字段名 | 排除原因 | 说明 |
|--------|----------|------|
| `id` | 新建时不需要 | 主表单ID，编辑时会重新添加 |
| `supplierId` | 使用supplierCode替代 | 供应商内部ID，不直接提交 |
| `productionOrderNo` | 主表单不需要 |
| `purchaseDateObj` | UI辅助字段 | dayjs日期对象，仅用于UI组件 |

### 明细项字段处理

#### 提交的字段（25个核心业务字段）
| 字段名 | 中文名称 | 类型 | 说明 |
|--------|----------|------|------|
| `paperQuality` | 纸质 | string | 纸张质量类型 |
| `paperBoardCategory` | 纸板类别 | string | 纸板分类 |
| `corrugationType` | 楞别 | string | 瓦楞纸楞别 |
| `paperWidth` | 纸度 | number | 纸张宽度 |
| `paperLength` | 纸长 | number | 纸张长度 |
| `bindingMethod` | 合订 | boolean | 是否合订（强制转换为布尔值） |
| `bindingSpecification` | 合订规格 | string | 合订规格说明 |
| `materialChange` | 用料异动 | boolean | 是否用料异动（强制转换为布尔值） |
| `specialQuotation` | 报价特价 | string | 特殊报价信息 |
| `paperQuotation` | 纸质报价 | number | 纸质报价金额 |
| `discount` | 折扣 | number | 折扣比例 |
| `quantity` | 数量 | number | 采购数量 |
| `price` | 价格 | number | 单价 |
| `amount` | 金额 | number | 总金额 |
| `creasingSize` | 压线尺寸(纸度) | string | 压线尺寸规格 |
| `creasingMethod` | 压线方式 | string | 压线工艺方法 |
| `remarks` | 备注 | string | 明细项备注 |
| `foldingSpecification` | 折度规格 | string | 折叠规格 |
| `lengthMeters` | 长度(米) | number | 长度单位：米 |
| `areaSquareMeters` | 面积(平米) | number | 面积单位：平方米 |
| `volumeCubicMeters` | 体积(立方米) | number | 体积单位：立方米 |
| `unitWeight` | 单重 | number | 单位重量 |
| `totalWeightKg` | 总重(KG) | number | 总重量，单位：KG |
| `processingFee` | 加工费 | number | 加工费用 |
| `currency` | 币种 | string | 货币类型，默认CNY |
| `deliveryDate` | 交期 | string | 交货日期，格式：YYYY-MM-DD |

#### 排除的字段（23个）

##### UI控制字段（3个）
| 字段名 | 排除原因 |
|--------|----------|
| `tempId` | UI临时ID，仅用于前端表格行标识 |
| `isEditing` | UI编辑状态标识 |
| `deliveryDateObj` | dayjs日期对象，仅用于UI日期选择器 |

##### 系统ID字段（1个）
| 字段名 | 排除原因 |
|--------|----------|
| `id` | 明细项ID，新建时由后端生成 |

##### 生产单相关字段（1个）
| 字段名 | 排除原因 |
|--------|----------|
| `productionOrderNo` | 生产单号，在明细项级别不需要提交 |

##### 库存统计字段（1个）
| 字段名 | 排除原因 |
|--------|----------|
| `receivedQuantity` | 已入库数，仅用于UI显示，不参与创建 |

##### 销售订单关联显示字段（17个）
这些字段主要用于从销售订单引用时的UI显示，不参与采购订单的实际创建：

| 字段名 | 中文名称 | 排除原因 |
|--------|----------|----------|
| `orderMaterialPaper` | 订单用料纸质 | 销售订单显示字段 |
| `orderMaterial` | 订单用料 | 销售订单显示字段 |
| `productionRemark` | 生产备注 | 销售订单显示字段 |
| `orderPaperType` | 订单纸质 | 销售订单显示字段 |
| `orderMaterialCorrugate` | 订单用料纸质楞别 | 销售订单显示字段 |
| `boardRequirement` | 订单纸板需求数 | 销售订单显示字段 |
| `customerName` | 客户名称 | 销售订单显示字段 |
| `customerOrderNo` | 客户订单号 | 销售订单显示字段 |
| `customerProductNo` | 客方货号 | 销售订单显示字段 |
| `processRequirements` | 工艺要求 | 销售订单显示字段 |
| `productName` | 品名 | 销售订单显示字段 |
| `boxType` | 盒式 | 销售订单显示字段 |
| `productSpec` | 规格 | 销售订单显示字段 |
| `productionSpec` | 生产规格 | 销售订单显示字段 |
| `orderQuantity` | 订单产品数 | 销售订单显示字段 |
| `orderDate` | 订单日期 | 销售订单显示字段 |
| `salesOrderDeliveryDate` | 销售单交期 | 销售订单显示字段 |

## 特殊处理逻辑

### 1. 布尔值字段转换
```javascript
// 确保合订和用料异动字段是布尔值
bindingMethod: !!rest.bindingMethod,
materialChange: !!rest.materialChange
```

### 2. 日期字段处理
- `purchaseDate`: 从 `purchaseDateObj` (dayjs对象) 转换为字符串格式
- `deliveryDate`: 保留字符串格式，排除对应的dayjs对象

### 3. 新建 vs 编辑模式差异
- **新建模式**: 排除 `id`, `supplierId`, `productionOrderNo`
- **编辑模式**: 会重新添加主表单的 `id` 字段


## 总结

1. **主表单共提交12个字段**，排除4个UI相关字段
2. **明细项共提交25个核心业务字段**，排除23个字段
3. **排除的字段主要分为5类**：UI控制、系统ID、生产单相关、库存统计、销售订单关联显示
4. **特殊处理**：布尔值强制转换、日期格式转换
5. **设计理念**：严格分离UI展示字段和业务数据字段，确保提交到后端的数据纯净且符合业务需求

## 代码位置参考

- 主要逻辑：`czerp-frontend/src/views/purchase/PurchaseOrderEdit.vue:521-610`
- 表单定义：`czerp-frontend/src/views/purchase/PurchaseOrderEdit.vue:293-308`  
- 新增行结构：`czerp-frontend/src/views/purchase/PurchaseOrderEdit.vue:430-485`
- 类型定义：`czerp-frontend/src/types/purchaseOrder.d.ts` 