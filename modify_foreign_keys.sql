-- 修改外键约束脚本
-- 作者：Augment Agent
-- 日期：2023-05-17
-- 描述：修改引用sales_order_item表的外键约束，添加ON DELETE CASCADE选项

-- 开始事务
START TRANSACTION;

-- 1. 修改sales_order_item_process表的外键约束
-- 先删除现有约束
ALTER TABLE sales_order_item_process
DROP FOREIGN KEY fk_sales_order_item_process_sales_order_item;

-- 添加带有CASCADE选项的新约束
ALTER TABLE sales_order_item_process
ADD CONSTRAINT fk_sales_order_item_process_sales_order_item
FOREIGN KEY (order_item_id) REFERENCES sales_order_item(id)
ON DELETE CASCADE;

-- 2. sales_order_material表已经设置了ON DELETE CASCADE，不需要修改

-- 3. 修改purchase_order_item表的外键约束
-- 先删除现有约束
ALTER TABLE purchase_order_item
DROP FOREIGN KEY fk_poi_soi;

-- 添加带有CASCADE选项的新约束
ALTER TABLE purchase_order_item
ADD CONSTRAINT fk_poi_soi
FOREIGN KEY (sales_order_item_id) REFERENCES sales_order_item(id)
ON DELETE CASCADE;

-- 提交事务
COMMIT;

-- 验证修改结果
SELECT
    kcu.TABLE_NAME,
    kcu.COLUMN_NAME,
    kcu.CONSTRAINT_NAME,
    kcu.REFERENCED_TABLE_NAME,
    kcu.REFERENCED_COLUMN_NAME,
    rc.DELETE_RULE
FROM
    information_schema.REFERENTIAL_CONSTRAINTS rc
JOIN
    information_schema.KEY_COLUMN_USAGE kcu
ON
    rc.CONSTRAINT_SCHEMA = kcu.CONSTRAINT_SCHEMA
    AND rc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
WHERE
    kcu.REFERENCED_TABLE_SCHEMA = 'czerp_web'
    AND kcu.REFERENCED_TABLE_NAME = 'sales_order_item';
