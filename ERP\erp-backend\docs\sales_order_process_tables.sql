-- 销售订单工序表（简化版）
CREATE TABLE sales_order_item_process (
    id VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    order_item_id VARCHAR(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '销售订单明细ID',
    sequence INT NOT NULL COMMENT '工序顺序',
    process_name VARCHAR(50) NOT NULL COMMENT '工序名称',
    process_requirements VARCHAR(500) COMMENT '工艺要求',
    plate_number VARCHAR(50) COMMENT '印版编号',
    ink_number VARCHAR(50) COMMENT '水墨编号',
    ink_name VARCHAR(100) COMMENT '水墨名称',
    color_count INT COMMENT '颜色数',
    is_deleted BIT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    version INT DEFAULT 0 COMMENT '版本号',
    CONSTRAINT fk_sales_order_item_process_item FOREIGN KEY (order_item_id) REFERENCES sales_order_item(id)
) COMMENT='销售订单工序表';

-- 创建索引
CREATE INDEX idx_sales_order_item_process_order_item_id ON sales_order_item_process(order_item_id);
CREATE INDEX idx_sales_order_item_process_process_name ON sales_order_item_process(process_name);
