package com.czerp.erpbackend.product.dto;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 更新货品请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateProductRequest {
    
    /**
     * 货品名称
     */
    @Size(max = 100, message = "货品名称长度不能超过100个字符")
    private String name;
    
    /**
     * 分类ID
     */
    private String categoryId;
    
    /**
     * 规格ID
     */
    private String specId;
    
    /**
     * 单位
     */
    @Size(max = 20, message = "单位长度不能超过20个字符")
    private String unit;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
    
    /**
     * 图片URL
     */
    @Size(max = 255, message = "图片URL长度不能超过255个字符")
    private String imageUrl;
}
