package com.czerp.erpbackend.system.service.impl;

import com.czerp.erpbackend.system.service.DictionaryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典服务实现类
 * 目前使用硬编码方式提供字典数据，后续可以扩展为从数据库读取
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DictionaryServiceImpl implements DictionaryService {
    
    /**
     * 字典数据映射
     * key: 字典编码
     * value: 字典选项列表
     */
    private static final Map<String, List<String>> DICTIONARY_DATA = new HashMap<>();
    
    static {
        // 初始化字典数据
        initializeDictionaryData();
    }
    
    /**
     * 初始化字典数据
     */
    private static void initializeDictionaryData() {
        // 盒式字典 - 简化版本，实际使用时可以从数据库加载
        DICTIONARY_DATA.put("dict_box_type", Arrays.asList(
            "0201", "0202", "0203", "0204", "0205", "0206", "0207", "0208", "0209", "0210",
            "0300", "0301", "0302", "0303", "0304", "0305", "0400", "0401", "0402", "0403"
        ));
        
        // 楞别字典
        DICTIONARY_DATA.put("dict_corrugation_type", Arrays.asList(
            "A楞", "B楞", "C楞", "E楞", "F楞", "G楞", "AB楞", "BC楞", "AC楞", "BE楞", "CE楞", "EF楞",
            "ABC楞", "ABE楞", "BCE楞", "ACE楞", "BEF楞", "CEF楞", "ABCE楞", "BCEF楞"
        ));
        
        // 连接方式字典
        DICTIONARY_DATA.put("dict_connection_method", Arrays.asList(
            "胶水粘合", "钉箱", "胶带封箱", "热熔胶", "双面胶", "搭扣", "插扣", "锁扣", "魔术贴", "其他"
        ));
        
        // 钉位字典
        DICTIONARY_DATA.put("dict_staple_position", Arrays.asList(
            "上钉", "下钉", "左钉", "右钉", "四角钉", "中心钉", "双钉", "三钉", "四钉", "无钉"
        ));
        
        // 单位字典
        DICTIONARY_DATA.put("dict_unit", Arrays.asList(
            "个", "只", "套", "件", "箱", "包", "袋", "盒", "张", "米", "厘米", "毫米", "千克", "克", "吨"
        ));
        
        // 币种字典
        DICTIONARY_DATA.put("dict_currency", Arrays.asList(
            "CNY", "USD", "EUR", "JPY", "GBP", "HKD", "AUD", "CAD", "SGD", "KRW"
        ));
        
        log.info("Dictionary data initialized with {} entries", DICTIONARY_DATA.size());
    }
    
    @Override
    public List<String> getOptionsByCode(String dictionaryCode) {
        return getOptionsByCode(dictionaryCode, null);
    }
    
    @Override
    public List<String> getOptionsByCode(String dictionaryCode, String searchText) {
        if (!StringUtils.hasText(dictionaryCode)) {
            log.warn("Dictionary code is empty");
            return List.of();
        }
        
        List<String> options = DICTIONARY_DATA.get(dictionaryCode);
        if (options == null) {
            log.warn("Dictionary code not found: {}", dictionaryCode);
            return List.of();
        }
        
        // 如果有搜索文本，进行过滤
        if (StringUtils.hasText(searchText)) {
            String lowerSearchText = searchText.toLowerCase();
            return options.stream()
                    .filter(option -> option.toLowerCase().contains(lowerSearchText))
                    .collect(Collectors.toList());
        }
        
        return options;
    }
    
    @Override
    public boolean existsByCode(String dictionaryCode) {
        return StringUtils.hasText(dictionaryCode) && DICTIONARY_DATA.containsKey(dictionaryCode);
    }
}
