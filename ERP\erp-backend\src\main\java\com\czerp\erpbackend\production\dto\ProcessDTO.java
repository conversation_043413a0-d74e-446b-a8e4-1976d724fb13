package com.czerp.erpbackend.production.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

/**
 * 工序DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessDTO {
    
    /**
     * 工序ID
     */
    private Long id;
    
    /**
     * 工序名称
     */
    private String process;
    
    /**
     * 工艺要求
     */
    private String processRequirements;
    
    /**
     * 印版编号
     */
    private String printingPlateNo;
    
    /**
     * 水墨编号
     */
    private String inkNo;
    
    /**
     * 水墨名称
     */
    private String inkName;
    
    /**
     * 颜色数
     */
    private Integer colorCount;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 创建人姓名
     */
    private String createdByName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 更新人姓名
     */
    private String updatedByName;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
