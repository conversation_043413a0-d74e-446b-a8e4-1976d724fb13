package com.czerp.erpbackend.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 刷新令牌响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefreshTokenResponse {
    
    /**
     * 新JWT令牌
     */
    private String token;
    
    /**
     * 新刷新令牌
     */
    private String refreshToken;
    
    /**
     * 过期时间(秒)
     */
    private Long expiresIn;
} 