package com.czerp.erpbackend.system.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 角色权限关联实体
 */
@Entity
@Table(name = "sys_role_permission")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class RolePermission implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 关联ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;
    
    /**
     * 角色ID
     */
    @Column(name = "role_id", length = 36, nullable = false)
    private String roleId;
    
    /**
     * 权限ID
     */
    @Column(name = "permission_id", length = 36, nullable = false)
    private String permissionId;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    @CreatedBy
    @Column(name = "create_by", length = 36)
    private String createBy;
} 