package com.czerp.erpbackend.system.service.impl;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.system.dto.LengthUnit;
import com.czerp.erpbackend.system.dto.PaperSizeMatchingRequest;
import com.czerp.erpbackend.system.dto.PaperSizeMatchingResponse;
import com.czerp.erpbackend.system.dto.PaperSizeRecommendation;
import com.czerp.erpbackend.system.dto.PaperSizeSettingDTO;
import com.czerp.erpbackend.system.entity.PaperSizeSetting;
import com.czerp.erpbackend.system.mapper.PaperSizeSettingMapper;
import com.czerp.erpbackend.system.repository.PaperSizeSettingRepository;
import com.czerp.erpbackend.system.service.PaperSizeMatchingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 纸度匹配服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaperSizeMatchingServiceImpl implements PaperSizeMatchingService {

    private final PaperSizeSettingRepository paperSizeSettingRepository;
    private final PaperSizeSettingMapper paperSizeSettingMapper;

    /**
     * 厘米到英寸的转换因子
     */
    private static final BigDecimal CM_TO_INCH_FACTOR = new BigDecimal("2.54");

    /**
     * 毫米到英寸的转换因子
     */
    private static final BigDecimal MM_TO_INCH_FACTOR = new BigDecimal("25.4");

    /**
     * 成本容差率，用于场景一，定义了多大范围内的成本可以被认为是"接近最优"
     * 值越小，越倾向于选择成本绝对最低的纸度；值越大，越可能选择成本稍高但尺寸更小的纸度
     */
    private static final BigDecimal COST_TOLERANCE = new BigDecimal("0.03"); // 3%

    /**
     * 查找最佳纸度
     * @param request 纸度匹配请求
     * @return 纸度匹配响应
     */
    @Override
    public PaperSizeMatchingResponse findBestPaperSize(PaperSizeMatchingRequest request) {
        log.debug("Finding best paper size for width: {}, height: {}", request.getWidth(), request.getHeight());

        // 1. 输入参数校验
        if (request.getWidth() == null || request.getWidth().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("纸盒宽度必须大于0");
        }
        if (request.getHeight() == null || request.getHeight().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("纸盒高度必须大于0");
        }

        // 2. 根据单位转换为英寸
        BigDecimal widthInInch = convertToInch(request.getWidth(), request.getUnit());
        BigDecimal heightInInch = convertToInch(request.getHeight(), request.getUnit());
        log.debug("Converted dimensions: width={} inches, height={} inches", widthInInch, heightInInch);

        // 3. 计算基准值
        BigDecimal calculatedValue = widthInInch.add(heightInInch);
        log.debug("Calculated value: {}", calculatedValue);

        // 4. 获取可用纸度列表
        List<PaperSizeSetting> availablePaperSizes = paperSizeSettingRepository.findByIsDeletedFalseOrderByPaperSizeInchAsc();
        if (availablePaperSizes.isEmpty()) {
            log.warn("No available paper sizes found");
            return PaperSizeMatchingResponse.builder()
                    .message("无可用纸度数据")
                    .build();
        }

        // 5. 获取最小和最大纸度值
        PaperSizeSetting minPaperSize = availablePaperSizes.get(0);
        PaperSizeSetting maxPaperSize = availablePaperSizes.get(availablePaperSizes.size() - 1);

        // 6. 应用混合算法
        PaperSizeSetting bestPaperSize = null;

        // 场景一：calculatedValue < MinPaperSize (计算值小于最小纸度)
        if (calculatedValue.compareTo(minPaperSize.getPaperSizeInch()) < 0) {
            log.debug("Scenario 1: calculatedValue {} is less than minimum paper size {}", calculatedValue, minPaperSize.getPaperSizeInch());

            // 定义所需尺寸
            BigDecimal requiredDim = calculatedValue;

            // 用于存储每个纸度的计算结果
            List<PaperSizeResult> results = new ArrayList<>();

            // 遍历所有可用纸度
            for (PaperSizeSetting currentSize : availablePaperSizes) {
                // 计算可生产数量
                BigDecimal numberOfItemsDecimal = currentSize.getPaperSizeInch().divide(requiredDim, 6, RoundingMode.FLOOR);
                int numberOfItems = numberOfItemsDecimal.intValue();

                // 如果该纸度至少能生产一个
                if (numberOfItems > 0) {
                    // 计算单位成本
                    BigDecimal costPerItem = currentSize.getPaperSizeInch().divide(new BigDecimal(numberOfItems), 6, RoundingMode.HALF_UP);

                    // 记录结果
                    results.add(new PaperSizeResult(currentSize, costPerItem, numberOfItems));
                }
            }

            // 如果没有任何纸度能生产至少一个物品
            if (results.isEmpty()) {
                log.warn("No paper size can produce at least one item for requiredDim: {}", requiredDim);
                return PaperSizeMatchingResponse.builder()
                        .message("未找到合适的纸度，所需尺寸过大")
                        .build();
            }

            // 查找绝对最低成本
            BigDecimal absoluteMinCost = results.stream()
                    .map(PaperSizeResult::getCostPerItem)
                    .min(Comparator.naturalOrder())
                    .orElse(BigDecimal.ZERO);

            // 确定成本容差阈值
            BigDecimal costThreshold = absoluteMinCost.multiply(BigDecimal.ONE.add(COST_TOLERANCE));

            // 筛选最优候选集
            List<PaperSizeResult> candidates = results.stream()
                    .filter(result -> result.getCostPerItem().compareTo(costThreshold) <= 0)
                    .toList();

            // 在候选集中选择具有最小size的记录
            bestPaperSize = candidates.stream()
                    .min(Comparator.comparing(result -> result.getPaperSize().getPaperSizeInch()))
                    .map(PaperSizeResult::getPaperSize)
                    .orElse(null);

            // 如果找到了最佳纸度，记录日志
            if (bestPaperSize != null) {
                log.debug("Found best paper size {} for scenario 1 with calculated value {}", bestPaperSize.getPaperSizeInch(), calculatedValue);
            }
        }
        // 场景二：calculatedValue >= MinPaperSize (计算值大于等于最小纸度)
        else {
            log.debug("Scenario 2: calculatedValue {} is greater than or equal to minimum paper size {}", calculatedValue, minPaperSize.getPaperSizeInch());

            // 如果calculatedValue大于或等于最大值，无法匹配
            if (calculatedValue.compareTo(maxPaperSize.getPaperSizeInch()) >= 0) {
                log.warn("calculatedValue {} is greater than or equal to maximum paper size {}", calculatedValue, maxPaperSize.getPaperSizeInch());
                return PaperSizeMatchingResponse.builder()
                        .message("计算值超出最大纸度范围")
                        .build();
            }

            // 找到第一个严格大于calculatedValue的值
            for (PaperSizeSetting paperSize : availablePaperSizes) {
                if (paperSize.getPaperSizeInch().compareTo(calculatedValue) > 0) {
                    bestPaperSize = paperSize;
                    log.debug("Found first paper size {} strictly greater than calculatedValue {}", bestPaperSize.getPaperSizeInch(), calculatedValue);
                    break;
                }
            }
        }

        // 7. 生成结果
        if (bestPaperSize == null) {
            // 理论上不应该到达这里，因为我们已经处理了所有可能的情况
            log.error("Unexpected situation: no paper size found for calculatedValue {}", calculatedValue);
            return PaperSizeMatchingResponse.builder()
                    .message("未找到合适的纸度，发生意外错误")
                    .build();
        }

        // 为了保持与原有逻辑的兼容性，我们仍然计算浪费率和可生产数量
        // 但这些值不再用于决定最佳纸度，只是为了保持API响应格式一致
        BigDecimal boxSizeInch = calculatedValue; // 使用计算出的基准值
        BigDecimal numBoxesDecimal = bestPaperSize.getPaperSizeInch().divide(boxSizeInch, 6, RoundingMode.FLOOR);
        int maxBoxesForBestSize = numBoxesDecimal.intValue();

        BigDecimal totalSizeUsed = boxSizeInch.multiply(new BigDecimal(maxBoxesForBestSize));
        BigDecimal wasteAmount = bestPaperSize.getPaperSizeInch().subtract(totalSizeUsed);
        BigDecimal wastePercentage = wasteAmount.divide(bestPaperSize.getPaperSizeInch(), 6, RoundingMode.HALF_UP);

        // 格式化浪费率为百分比字符串
        DecimalFormat df = new DecimalFormat("0.##%");
        String wastePercentageFormatted = df.format(wastePercentage);

        // 构建并返回响应
        return PaperSizeMatchingResponse.builder()
                .bestPaperSize(paperSizeSettingMapper.toDto(bestPaperSize))
                .maxBoxesProducible(maxBoxesForBestSize)
                .wastePercentage(wastePercentage)
                .wastePercentageFormatted(wastePercentageFormatted)
                .message("成功找到最佳纸度")
                .build();
    }

    /**
     * 将指定单位的长度值转换为英寸
     * @param value 长度值
     * @param unit 长度单位
     * @return 转换后的英寸值
     */
    private BigDecimal convertToInch(BigDecimal value, LengthUnit unit) {
        if (value == null) {
            return BigDecimal.ZERO;
        }

        switch (unit) {
            case CM:
                return value.divide(CM_TO_INCH_FACTOR, 6, RoundingMode.HALF_UP);
            case MM:
                return value.divide(MM_TO_INCH_FACTOR, 6, RoundingMode.HALF_UP);
            case INCH:
                return value;
            default:
                log.warn("Unknown length unit: {}, using CM as default", unit);
                return value.divide(CM_TO_INCH_FACTOR, 6, RoundingMode.HALF_UP);
        }
    }

    /**
     * 将指定单位的长度值转换为厘米
     * @param value 长度值
     * @param unit 长度单位
     * @return 转换后的厘米值
     */
    private BigDecimal convertToCm(BigDecimal value, LengthUnit unit) {
        if (value == null) {
            return BigDecimal.ZERO;
        }

        switch (unit) {
            case CM:
                return value;
            case MM:
                return value.divide(new BigDecimal("10"), 6, RoundingMode.HALF_UP);
            case INCH:
                return value.multiply(CM_TO_INCH_FACTOR);
            default:
                log.warn("Unknown length unit: {}, using CM as default", unit);
                return value;
        }
    }

    /**
     * 获取纸度推荐列表
     * @param request 纸度匹配请求
     * @return 纸度推荐列表
     */
    @Override
    public List<PaperSizeRecommendation> getPaperSizeRecommendations(PaperSizeMatchingRequest request) {
        log.debug("Getting paper size recommendations for width: {}, height: {}, unit: {}",
                request.getWidth(), request.getHeight(), request.getUnit());

        // 1. 输入参数校验
        if (request.getWidth() == null || request.getWidth().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("纸盒宽度必须大于0");
        }
        if (request.getHeight() == null || request.getHeight().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("纸盒高度必须大于0");
        }

        // 2. 将宽度和高度转换为厘米（如果不是厘米）
        BigDecimal widthCm = convertToCm(request.getWidth(), request.getUnit());
        BigDecimal heightCm = convertToCm(request.getHeight(), request.getUnit());
        log.debug("Converted dimensions: width={} cm, height={} cm", widthCm, heightCm);

        // 3. 应用公式：(宽*0.5)+0.1+高+(宽*0.5)+0.1
        BigDecimal halfWidth = widthCm.multiply(new BigDecimal("0.5"));
        BigDecimal actualUsageCm = halfWidth.add(new BigDecimal("0.1"))
                                  .add(heightCm)
                                  .add(halfWidth)
                                  .add(new BigDecimal("0.1"));

        // 4. 转换为英寸
        BigDecimal actualUsageBaseInch = actualUsageCm.divide(CM_TO_INCH_FACTOR, 6, RoundingMode.HALF_UP);
        log.debug("Actual usage base (inch): {}", actualUsageBaseInch);

        // 5. 固定损耗（英寸）
        BigDecimal FIXED_WASTAGE_INCH = new BigDecimal("0.75");

        // 6. 获取可用纸度列表
        List<PaperSizeSetting> availablePaperSizes = paperSizeSettingRepository.findByIsDeletedFalseOrderByPaperSizeInchAsc();
        if (availablePaperSizes.isEmpty()) {
            log.warn("No available paper sizes found");
            return Collections.emptyList();
        }

        // 7. 获取最大可用纸度
        BigDecimal maxPaperSizeInch = availablePaperSizes.get(availablePaperSizes.size() - 1).getPaperSizeInch();
        log.debug("Maximum available paper size (inch): {}", maxPaperSizeInch);

        // 8. 结果列表
        List<PaperSizeRecommendation> recommendations = new ArrayList<>();

        // 9. 从度开数1开始，逐步增加
        int degreeCount = 1;
        while (true) {
            // 计算当前度开数下的实际用料
            BigDecimal actualUsageInch = actualUsageBaseInch.multiply(new BigDecimal(degreeCount))
                                       .add(FIXED_WASTAGE_INCH);

            log.debug("Degree count: {}, actual usage (inch): {}", degreeCount, actualUsageInch);

            // 如果实际用料超过最大可用纸度，则停止计算
            if (actualUsageInch.compareTo(maxPaperSizeInch) > 0) {
                log.debug("Actual usage exceeds maximum paper size, stopping calculation");
                break;
            }

            // 在可用纸度列表中找到第一个大于等于实际用料的标准纸度
            PaperSizeSetting recommendedPaperSize = null;
            for (PaperSizeSetting paperSize : availablePaperSizes) {
                if (paperSize.getPaperSizeInch().compareTo(actualUsageInch) >= 0) {
                    recommendedPaperSize = paperSize;
                    break;
                }
            }

            // 如果找不到合适的纸度（理论上不应该发生），则跳过当前度开数
            if (recommendedPaperSize == null) {
                log.warn("No suitable paper size found for degree count: {}, actual usage: {}",
                        degreeCount, actualUsageInch);
                degreeCount++;
                continue;
            }

            // 计算纸度损耗
            BigDecimal wastageInch = recommendedPaperSize.getPaperSizeInch().subtract(actualUsageInch);

            // 创建推荐对象并添加到结果列表
            PaperSizeRecommendation recommendation = PaperSizeRecommendation.builder()
                    .paperSizeCm(recommendedPaperSize.getPaperSizeCm())
                    .paperSizeInch(recommendedPaperSize.getPaperSizeInch())
                    .degreeCount(degreeCount)
                    .actualUsageInch(actualUsageInch.setScale(2, RoundingMode.HALF_UP))
                    .wastageInch(wastageInch.setScale(2, RoundingMode.HALF_UP))
                    .build();

            recommendations.add(recommendation);
            log.debug("Added recommendation: {}", recommendation);

            // 增加度开数
            degreeCount++;
        }

        return recommendations;
    }

    /**
     * 纸度计算结果类，用于存储每个纸度的计算结果
     */
    private static class PaperSizeResult {
        private final PaperSizeSetting paperSize;
        private final BigDecimal costPerItem;
        private final int numberOfItems;

        public PaperSizeResult(PaperSizeSetting paperSize, BigDecimal costPerItem, int numberOfItems) {
            this.paperSize = paperSize;
            this.costPerItem = costPerItem;
            this.numberOfItems = numberOfItems;
        }

        public PaperSizeSetting getPaperSize() {
            return paperSize;
        }

        public BigDecimal getCostPerItem() {
            return costPerItem;
        }

        public int getNumberOfItems() {
            return numberOfItems;
        }
    }
}
