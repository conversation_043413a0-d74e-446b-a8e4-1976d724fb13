-- 创建新表
CREATE TABLE sales_order_material (
    id VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    order_item_id VARCHAR(36) NOT NULL COMMENT '销售订单行项目ID',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    created_by_name VA<PERSON><PERSON><PERSON>(50) COMMENT '创建人姓名',
    created_time DATETIME NOT NULL COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_by_name VARCHAR(50) COMMENT '更新人姓名',
    updated_time DATETIME COMMENT '更新时间',
    version INT DEFAULT 0 COMMENT '版本号',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    serial_no INT COMMENT '序号',
    paper_quality VARCHAR(100) COMMENT '纸质',
    paper_width DECIMAL(10,2) COMMENT '纸度',
    paper_length DECIMAL(10,2) COMMENT '纸长',
    width_open DECIMAL(10,2) COMMENT '度开',
    length_open DECIMAL(10,2) COMMENT '长开',
    board_count INT COMMENT '纸板数',
    board_loss DECIMAL(10,2) COMMENT '纸板损耗',
    material_usage DECIMAL(10,2) COMMENT '原料用量',
    material_usage_count DECIMAL(10,2) COMMENT '用量',
    method VARCHAR(50) COMMENT '方式',
    press_size_width VARCHAR(100) COMMENT '压线尺寸(纸度)',
    press_method VARCHAR(50) COMMENT '压线方式',
    actual_material_width DECIMAL(10,4) COMMENT '实际用料宽',
    actual_material_length DECIMAL(10,4) COMMENT '实际用料长',
    die_model VARCHAR(100) COMMENT '啤模',
    die_model_no VARCHAR(50) COMMENT '啤模编号',
    die_open_count INT COMMENT '模开数',
    die_model_position VARCHAR(100) COMMENT '啤模位置',
    die_model_checked TINYINT(1) DEFAULT 0 COMMENT '啤模选中状态',
    unit VARCHAR(20) COMMENT '单位',
    current_inventory INT COMMENT '当前库存',
    available_inventory INT COMMENT '可用库存',
    use_inventory_count INT COMMENT '使用库存数',
    actual_material_length_converted DECIMAL(10,4) COMMENT '实际用料长(转换)',
    actual_material_width_converted DECIMAL(10,4) COMMENT '实际用料宽(转换)',
    supplier VARCHAR(100) COMMENT '供应商',
    material_remark VARCHAR(500) COMMENT '材料备注',
    purchased_count INT COMMENT '已采购数',
    die_press_line VARCHAR(100) COMMENT '啤模压线',
    press_size_length DECIMAL(10,2) COMMENT '压线尺寸(纸长)',
    inbound_count INT COMMENT '已入库数',
    materials_received_count INT COMMENT '已领料数',
    INDEX idx_order_item_id (order_item_id),
    INDEX idx_paper_quality (paper_quality),
    INDEX idx_die_model_no (die_model_no),
    INDEX idx_supplier (supplier),
    FOREIGN KEY (order_item_id) REFERENCES sales_order_item(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单材料信息表';
