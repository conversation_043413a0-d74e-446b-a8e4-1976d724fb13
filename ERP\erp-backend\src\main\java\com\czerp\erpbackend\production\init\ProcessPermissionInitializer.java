package com.czerp.erpbackend.production.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 工序管理权限初始化器
 * 负责初始化工序管理模块的权限并分配给管理员角色
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(10) // 在系统权限初始化之后执行
public class ProcessPermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing process management permissions...");

        // 初始化工序管理模块权限
        initProcessPermissions();

        // 为管理员角色分配工序管理模块权限
        assignPermissionsToAdminRole();

        log.info("Process management permissions initialized successfully");
    }

    /**
     * 初始化工序管理模块权限
     */
    private void initProcessPermissions() {
        log.info("Initializing process management module permissions...");

        // 检查生产管理模块是否存在
        String productionModuleId = findPermissionIdByCode("production");
        if (productionModuleId == null) {
            log.info("Production module not found, creating it...");
            productionModuleId = createPermission("生产管理", "production", "menu", null, "/production", "production/index", "tool", 30);
        }

        // 检查工序管理模块是否已存在
        if (permissionRepository.existsByCode("process")) {
            log.info("Process management module already exists, skipping...");
            return;
        }

        // 创建工序管理模块菜单
        String processModuleId = createPermission("工序管理", "process", "menu", productionModuleId, "/production/processes", "production/ProcessList", "setting", 10);

        // 创建工序管理模块按钮权限
        createPermission("工序列表", "process:list", "button", processModuleId, null, null, null, 11);
        createPermission("工序详情", "process:view", "button", processModuleId, null, null, null, 12);
        createPermission("创建工序", "process:create", "button", processModuleId, null, null, null, 13);
        createPermission("更新工序", "process:update", "button", processModuleId, null, null, null, 14);
        createPermission("删除工序", "process:delete", "button", processModuleId, null, null, null, 15);

        log.info("Process management module permissions created successfully");
    }

    /**
     * 为管理员角色分配工序管理模块权限
     */
    private void assignPermissionsToAdminRole() {
        log.info("Assigning process management permissions to admin role...");

        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping permission assignment");
            return;
        }

        // 获取所有工序管理相关权限
        List<String> permissionCodes = Arrays.asList(
                "production",
                "process",
                "process:list",
                "process:view",
                "process:create",
                "process:update",
                "process:delete"
        );

        List<Permission> permissions = permissionRepository.findByCodeIn(permissionCodes);

        // 为管理员角色分配权限
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : permissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString());
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermission.setCreateBy("system");
                rolePermission.setCreateTime(LocalDateTime.now());
                rolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }

        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} process management permissions to admin role", rolePermissions.size());
        } else {
            log.info("All process management permissions already assigned to admin role");
        }
    }

    /**
     * 根据权限编码查找权限ID
     * @param code 权限编码
     * @return 权限ID
     */
    private String findPermissionIdByCode(String code) {
        Optional<Permission> permission = permissionRepository.findByCode(code);
        return permission.map(Permission::getId).orElse(null);
    }

    /**
     * 创建权限
     * @param name 权限名称
     * @param code 权限编码
     * @param type 权限类型
     * @param parentId 父权限ID
     * @param path 路径
     * @param component 组件
     * @param icon 图标
     * @param sort 排序
     * @return 权限ID
     */
    private String createPermission(String name, String code, String type, String parentId, String path, String component, String icon, Integer sort) {
        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setName(name);
        permission.setCode(code);
        permission.setType(type);
        permission.setParentId(parentId);
        permission.setPath(path);
        permission.setComponent(component);
        permission.setIcon(icon);
        permission.setSort(sort);
        permission.setStatus("1"); // 启用状态
        permission.setCreatedBy("system");
        permission.setCreatedTime(LocalDateTime.now());
        permission.setIsDeleted(false);
        permission = permissionRepository.save(permission);
        log.info("Created permission: {}", permission.getCode());
        return permission.getId();
    }
}
