-- 修改供应商分类表字段名称，从大写驼峰风格改为小写下划线风格
-- 备份表数据
CREATE TABLE pur_supplier_category_backup LIKE pur_supplier_category;
INSERT INTO pur_supplier_category_backup SELECT * FROM pur_supplier_category;

-- 修改字段名称
ALTER TABLE pur_supplier_category 
    CHANGE COLUMN `CategoryID` `id` VARCHAR(36) NOT NULL COMMENT '分类ID',
    CHANGE COLUMN `CategoryCode` `category_code` VARCHAR(50) NOT NULL COMMENT '分类编码',
    CHANGE COLUMN `CategoryName` `category_name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    CHANGE COLUMN `SortOrder` `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序',
    CHANGE COLUMN `Status` `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态(active-启用,inactive-停用)',
    CHANGE COLUMN `Remark` `remark` VARCHAR(255) COMMENT '备注',
    CHANGE COLUMN `CreatedBy` `created_by` VARCHAR(50) NOT NULL COMMENT '创建人',
    CHANGE COLUMN `CreatedAt` `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    CHANGE COLUMN `UpdatedBy` `updated_by` VARCHAR(50) COMMENT '更新人',
    CHANGE COLUMN `UpdatedAt` `updated_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 添加版本和逻辑删除字段（与BaseEntity一致）
ALTER TABLE pur_supplier_category
    ADD COLUMN `version` INT DEFAULT 0 COMMENT '版本号（乐观锁）',
    ADD COLUMN `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（逻辑删除）';

-- 重建索引
ALTER TABLE pur_supplier_category
    DROP INDEX idx_category_code,
    DROP INDEX idx_category_name,
    DROP INDEX idx_status;

ALTER TABLE pur_supplier_category
    ADD INDEX idx_category_code (category_code),
    ADD INDEX idx_category_name (category_name),
    ADD INDEX idx_status (status);

-- 验证修改是否成功
-- SELECT * FROM pur_supplier_category LIMIT 10;

-- 如果一切正常，可以删除备份表（谨慎操作）
-- DROP TABLE pur_supplier_category_backup;
