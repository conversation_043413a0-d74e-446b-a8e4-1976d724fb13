package com.czerp.erpbackend.purchase.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.purchase.service.PurchaseOrderItemCacheService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 采购订单明细缓存管理控制器
 * 提供缓存监控和管理功能
 */
@RestController
@RequestMapping("/purchase-order-item-cache")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "采购订单明细缓存管理", description = "采购订单明细缓存管理相关接口")
public class PurchaseOrderItemCacheController {

    private final PurchaseOrderItemCacheService cacheService;

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取缓存统计信息", description = "获取采购订单明细相关缓存的统计信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:cache:read')")
    public ResponseEntity<ApiResponse<PurchaseOrderItemCacheService.CacheStatistics>> getCacheStatistics() {
        log.info("Getting cache statistics");
        PurchaseOrderItemCacheService.CacheStatistics statistics = cacheService.getCacheStatistics();
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }

    /**
     * 清除指定采购订单明细的缓存
     * 
     * @param purchaseOrderItemId 采购订单明细ID
     * @return 操作结果
     */
    @DeleteMapping("/items/{purchaseOrderItemId}")
    @Operation(summary = "清除指定采购订单明细缓存", description = "清除指定采购订单明细的所有相关缓存")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:cache:manage')")
    public ResponseEntity<ApiResponse<String>> evictPurchaseOrderItemCache(
            @PathVariable Long purchaseOrderItemId) {
        log.info("Evicting cache for purchase order item: {}", purchaseOrderItemId);
        cacheService.evictPurchaseOrderItemCache(purchaseOrderItemId);
        return ResponseEntity.ok(ApiResponse.success("缓存清除成功"));
    }

    /**
     * 批量清除采购订单明细缓存
     * 
     * @param purchaseOrderItemIds 采购订单明细ID列表
     * @return 操作结果
     */
    @DeleteMapping("/items/batch")
    @Operation(summary = "批量清除采购订单明细缓存", description = "批量清除多个采购订单明细的所有相关缓存")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:cache:manage')")
    public ResponseEntity<ApiResponse<String>> evictPurchaseOrderItemCaches(
            @RequestBody Set<Long> purchaseOrderItemIds) {
        log.info("Evicting cache for {} purchase order items", purchaseOrderItemIds.size());
        cacheService.evictPurchaseOrderItemCaches(purchaseOrderItemIds);
        return ResponseEntity.ok(ApiResponse.success("批量缓存清除成功"));
    }

    /**
     * 清除所有采购订单明细相关缓存
     * 
     * @return 操作结果
     */
    @DeleteMapping("/all")
    @Operation(summary = "清除所有采购订单明细缓存", description = "清除所有采购订单明细相关缓存，谨慎使用")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:cache:admin')")
    public ResponseEntity<ApiResponse<String>> evictAllPurchaseOrderItemCaches() {
        log.warn("Evicting ALL purchase order item caches - admin operation");
        cacheService.evictAllPurchaseOrderItemCaches();
        return ResponseEntity.ok(ApiResponse.success("所有缓存清除成功"));
    }

    /**
     * 预热缓存
     * 
     * @param purchaseOrderItemIds 采购订单明细ID列表
     * @return 操作结果
     */
    @PostMapping("/warm-up")
    @Operation(summary = "预热缓存", description = "为指定的采购订单明细预加载缓存")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:cache:manage')")
    public ResponseEntity<ApiResponse<String>> warmUpCaches(
            @RequestBody List<Long> purchaseOrderItemIds) {
        log.info("Warming up cache for {} purchase order items", purchaseOrderItemIds.size());
        cacheService.warmUpCaches(purchaseOrderItemIds);
        return ResponseEntity.ok(ApiResponse.success("缓存预热已启动"));
    }
}
