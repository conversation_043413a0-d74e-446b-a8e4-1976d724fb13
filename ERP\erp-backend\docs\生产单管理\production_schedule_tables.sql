-- 生产单管理数据库表结构
-- 使用小写下划线命名规则
-- 包含基础审计字段

-- 生产排程单头表
CREATE TABLE production_schedule (
    id VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    schedule_no VARCHAR(50) NOT NULL COMMENT '排程单号',
    schedule_date DATE COMMENT '排程日期',
    remark VARCHAR(500) COMMENT '备注',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 0 COMMENT '版本号(乐观锁)',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    UNIQUE KEY uk_schedule_no (schedule_no),
    KEY idx_schedule_date (schedule_date),
    KEY idx_created_time (created_time),
    KEY idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产排程单头表';

-- 生产排程单明细表
CREATE TABLE production_schedule_item (
    id VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    schedule_id VARCHAR(36) NOT NULL COMMENT '排程单ID',
    is_urgent TINYINT(1) DEFAULT 0 COMMENT '急单',
    urgent_sequence INT COMMENT '急单序号',
    is_printed TINYINT(1) DEFAULT 0 COMMENT '已打印',
    planned_completion_date DATE COMMENT '计划完成日期',
    schedule_quantity INT COMMENT '排程数量',
    package_count INT COMMENT '包装数',
    remark VARCHAR(500) COMMENT '备注',
    sales_order_item_id VARCHAR(36) COMMENT '销售订单明细ID',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 0 COMMENT '版本号(乐观锁)',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    FOREIGN KEY (schedule_id) REFERENCES production_schedule(id) ON DELETE CASCADE,
    FOREIGN KEY (sales_order_item_id) REFERENCES sales_order_item(id) ON DELETE SET NULL,
    KEY idx_schedule_id (schedule_id),
    KEY idx_is_urgent (is_urgent),
    KEY idx_urgent_sequence (urgent_sequence),
    KEY idx_is_printed (is_printed),
    KEY idx_planned_completion_date (planned_completion_date),
    KEY idx_sales_order_item_id (sales_order_item_id),
    KEY idx_created_time (created_time),
    KEY idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产排程单明细表';