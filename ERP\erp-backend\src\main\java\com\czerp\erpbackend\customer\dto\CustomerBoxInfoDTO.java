package com.czerp.erpbackend.customer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户盒式信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerBoxInfoDTO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 盒式编码
     */
    private String boxCode;

    /**
     * 盒式名称
     */
    private String boxName;

    /**
     * 报价公式
     */
    private String quoteFormula;

    /**
     * 算价单位
     */
    private String calculationUnit;

    /**
     * 报价单位
     */
    private String quoteUnit;

    /**
     * 连接方式
     */
    private String connectionMethod;

    /**
     * 禁止双驳
     */
    private Boolean forbidDoubleFluting;

    /**
     * 长度大于此值双驳
     */
    private BigDecimal doubleFlutingLengthThreshold;

    /**
     * 跳度公差
     */
    private String pitchTolerance;

    /**
     * 盒式图形
     */
    private Boolean hasBoxGraphic;

    /**
     * 默认盒式
     */
    private Boolean isDefault;

    /**
     * 状态(active-启用,inactive-禁用)
     */
    private String status;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
