package com.czerp.erpbackend.common.exception;

/**
 * 资源不存在异常
 */
public class NotFoundException extends BusinessException {
    
    public NotFoundException(String message) {
        super(SystemErrorCode.DATA_NOT_FOUND.getCode(), message);
    }
    
    public NotFoundException(String entityName, String id) {
        super(SystemErrorCode.DATA_NOT_FOUND.getCode(), String.format("%s with id %s not found", entityName, id));
    }
    
    public NotFoundException(String entityName, Long id) {
        this(entityName, String.valueOf(id));
    }
}
