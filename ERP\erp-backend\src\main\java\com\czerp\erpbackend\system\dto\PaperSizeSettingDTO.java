package com.czerp.erpbackend.system.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 纸度设置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaperSizeSettingDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 纸度(inch)
     */
    private BigDecimal paperSizeInch;

    /**
     * 纸度(cm)
     */
    private BigDecimal paperSizeCm;

    /**
     * 最大损耗(inch)
     */
    private BigDecimal maxLossInch;

    /**
     * 最大损耗(cm)
     */
    private BigDecimal maxLossCm;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
