package com.czerp.erpbackend.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.stream.Collectors;

/**
 * 记录CORS相关信息的过滤器
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class LoggingCorsFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hai<PERSON> filterChain)
            throws ServletException, IOException {

        // 只记录关键的CORS信息，注释掉详细日志
        if ("OPTIONS".equals(request.getMethod())) {
            log.debug("CORS preflight request: {} {}", request.getMethod(), request.getRequestURI());
        }

        // 继续过滤器链
        filterChain.doFilter(request, response);

        // 只在DEBUG级别记录CORS响应状态
        if ("OPTIONS".equals(request.getMethod())) {
            log.debug("CORS preflight response status: {}", response.getStatus());
        }
    }
}
