# 客户盒式信息管理API文档

## 目录

- [1. 概述](#1-概述)
- [2. 通用数据结构](#2-通用数据结构)
- [3. 客户盒式信息管理API](#3-客户盒式信息管理api)
  - [3.1 获取客户盒式信息列表](#31-获取客户盒式信息列表)
  - [3.2 获取客户盒式信息详情](#32-获取客户盒式信息详情)
  - [3.3 根据盒式编码查询客户盒式信息](#33-根据盒式编码查询客户盒式信息)
  - [3.4 查询所有启用的客户盒式信息](#34-查询所有启用的客户盒式信息)
  - [3.5 查询默认盒式](#35-查询默认盒式)
- [4. 公共接口](#4-公共接口)
  - [4.1 查询所有启用的客户盒式信息（公共）](#41-查询所有启用的客户盒式信息公共)
  - [4.2 查询默认盒式（公共）](#42-查询默认盒式公共)
- [5. 前端适配指南](#5-前端适配指南)
  - [5.1 数据模型映射](#51-数据模型映射)
  - [5.2 请求参数适配](#52-请求参数适配)
  - [5.3 响应数据适配](#53-响应数据适配)

## 1. 概述

客户盒式信息管理模块提供了对客户盒式信息的查询功能，包括分页查询、根据ID查询、根据编码查询、查询所有启用的盒式信息和查询默认盒式等操作。本文档详细描述了后端API的接口定义和数据结构，以及前端适配的相关指南。

## 2. 通用数据结构

### 2.1 API响应结构

所有API响应都遵循以下统一格式：

```typescript
interface ApiResponse<T> {
  success: boolean;       // 请求是否成功
  code: string;           // 状态码
  message: string;        // 提示信息
  data: T;                // 响应数据
}
```

### 2.2 分页响应结构

分页查询接口的响应数据结构如下：

```typescript
interface PageResponse<T> {
  content: T[];           // 当前页数据
  totalElements: number;  // 总记录数
  totalPages: number;     // 总页数
  page: number;           // 当前页码
  size: number;           // 每页大小
  first: boolean;         // 是否第一页
  last: boolean;          // 是否最后一页
  empty: boolean;         // 是否为空
}
```

### 2.3 客户盒式信息数据结构

```typescript
interface CustomerBoxInfoDTO {
  id: string;                           // 主键ID
  boxCode: string;                      // 盒式编码
  boxName: string;                      // 盒式名称
  quoteFormula: string;                 // 报价公式
  calculationUnit: string;              // 算价单位
  quoteUnit: string;                    // 报价单位
  connectionMethod: string;             // 连接方式
  forbidDoubleFluting: boolean;         // 禁止双驳
  doubleFlutingLengthThreshold: number; // 长度大于此值双驳
  pitchTolerance: string;               // 跳度公差
  hasBoxGraphic: boolean;               // 盒式图形
  isDefault: boolean;                   // 默认盒式
  status: string;                       // 状态(active-启用,inactive-禁用)
  createdBy: string;                    // 创建人
  createdTime: string;                  // 创建时间
  updatedBy: string;                    // 更新人
  updatedTime: string;                  // 更新时间
}
```

## 3. 客户盒式信息管理API

### 3.1 获取客户盒式信息列表

#### 请求信息

- **URL**: `/api/customer-box-infos`
- **方法**: GET
- **描述**: 分页查询客户盒式信息列表
- **权限**: `customer:box-info:read`

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| keyword | string | 否 | 关键字（盒式编码或盒式名称） |
| status | string | 否 | 状态（active-启用,inactive-禁用） |
| isDefault | boolean | 否 | 是否默认盒式 |
| page | number | 否 | 页码（从1开始，默认1） |
| size | number | 否 | 每页大小（默认10） |
| sortField | string | 否 | 排序字段 |
| sortDirection | string | 否 | 排序方向（asc-升序,desc-降序） |

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": "1",
        "boxCode": "BOX001",
        "boxName": "标准盒式",
        "quoteFormula": "(L+W)*2*H*单价",
        "calculationUnit": "mm",
        "quoteUnit": "元/平方米",
        "connectionMethod": "胶粘",
        "forbidDoubleFluting": false,
        "doubleFlutingLengthThreshold": 1000.00,
        "pitchTolerance": "±2mm",
        "hasBoxGraphic": true,
        "isDefault": true,
        "status": "active",
        "createdBy": "admin",
        "createdTime": "2023-01-01T00:00:00",
        "updatedBy": "admin",
        "updatedTime": "2023-01-01T00:00:00"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "page": 1,
    "size": 10,
    "first": true,
    "last": true,
    "empty": false
  }
}
```

### 3.2 获取客户盒式信息详情

#### 请求信息

- **URL**: `/api/customer-box-infos/{id}`
- **方法**: GET
- **描述**: 根据ID查询客户盒式信息
- **权限**: `customer:box-info:read`

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| id | string | 是 | 客户盒式信息ID |

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": "1",
    "boxCode": "BOX001",
    "boxName": "标准盒式",
    "quoteFormula": "(L+W)*2*H*单价",
    "calculationUnit": "mm",
    "quoteUnit": "元/平方米",
    "connectionMethod": "胶粘",
    "forbidDoubleFluting": false,
    "doubleFlutingLengthThreshold": 1000.00,
    "pitchTolerance": "±2mm",
    "hasBoxGraphic": true,
    "isDefault": true,
    "status": "active",
    "createdBy": "admin",
    "createdTime": "2023-01-01T00:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-01-01T00:00:00"
  }
}
```

### 3.3 根据盒式编码查询客户盒式信息

#### 请求信息

- **URL**: `/api/customer-box-infos/code/{code}`
- **方法**: GET
- **描述**: 根据盒式编码查询客户盒式信息
- **权限**: `customer:box-info:read`

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| code | string | 是 | 盒式编码 |

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": "1",
    "boxCode": "BOX001",
    "boxName": "标准盒式",
    "quoteFormula": "(L+W)*2*H*单价",
    "calculationUnit": "mm",
    "quoteUnit": "元/平方米",
    "connectionMethod": "胶粘",
    "forbidDoubleFluting": false,
    "doubleFlutingLengthThreshold": 1000.00,
    "pitchTolerance": "±2mm",
    "hasBoxGraphic": true,
    "isDefault": true,
    "status": "active",
    "createdBy": "admin",
    "createdTime": "2023-01-01T00:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-01-01T00:00:00"
  }
}
```

### 3.4 查询所有启用的客户盒式信息

#### 请求信息

- **URL**: `/api/customer-box-infos/active`
- **方法**: GET
- **描述**: 查询所有启用的客户盒式信息
- **权限**: `customer:box-info:read`

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "boxCode": "BOX001",
      "boxName": "标准盒式",
      "quoteFormula": "(L+W)*2*H*单价",
      "calculationUnit": "mm",
      "quoteUnit": "元/平方米",
      "connectionMethod": "胶粘",
      "forbidDoubleFluting": false,
      "doubleFlutingLengthThreshold": 1000.00,
      "pitchTolerance": "±2mm",
      "hasBoxGraphic": true,
      "isDefault": true,
      "status": "active",
      "createdBy": "admin",
      "createdTime": "2023-01-01T00:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-01-01T00:00:00"
    }
  ]
}
```

### 3.5 查询默认盒式

#### 请求信息

- **URL**: `/api/customer-box-infos/default`
- **方法**: GET
- **描述**: 查询默认盒式
- **权限**: `customer:box-info:read`

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": "1",
    "boxCode": "BOX001",
    "boxName": "标准盒式",
    "quoteFormula": "(L+W)*2*H*单价",
    "calculationUnit": "mm",
    "quoteUnit": "元/平方米",
    "connectionMethod": "胶粘",
    "forbidDoubleFluting": false,
    "doubleFlutingLengthThreshold": 1000.00,
    "pitchTolerance": "±2mm",
    "hasBoxGraphic": true,
    "isDefault": true,
    "status": "active",
    "createdBy": "admin",
    "createdTime": "2023-01-01T00:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-01-01T00:00:00"
  }
}
```

## 4. 公共接口

### 4.1 查询所有启用的客户盒式信息（公共）

#### 请求信息

- **URL**: `/api/public/customer-box-infos`
- **方法**: GET
- **描述**: 查询所有启用的客户盒式信息，供其他模块使用
- **权限**: 无需权限

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "boxCode": "BOX001",
      "boxName": "标准盒式",
      "quoteFormula": "(L+W)*2*H*单价",
      "calculationUnit": "mm",
      "quoteUnit": "元/平方米",
      "connectionMethod": "胶粘",
      "forbidDoubleFluting": false,
      "doubleFlutingLengthThreshold": 1000.00,
      "pitchTolerance": "±2mm",
      "hasBoxGraphic": true,
      "isDefault": true,
      "status": "active",
      "createdBy": "admin",
      "createdTime": "2023-01-01T00:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-01-01T00:00:00"
    }
  ]
}
```

### 4.2 查询默认盒式（公共）

#### 请求信息

- **URL**: `/api/public/customer-box-infos/default`
- **方法**: GET
- **描述**: 查询默认盒式，供其他模块使用
- **权限**: 无需权限

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": "1",
    "boxCode": "BOX001",
    "boxName": "标准盒式",
    "quoteFormula": "(L+W)*2*H*单价",
    "calculationUnit": "mm",
    "quoteUnit": "元/平方米",
    "connectionMethod": "胶粘",
    "forbidDoubleFluting": false,
    "doubleFlutingLengthThreshold": 1000.00,
    "pitchTolerance": "±2mm",
    "hasBoxGraphic": true,
    "isDefault": true,
    "status": "active",
    "createdBy": "admin",
    "createdTime": "2023-01-01T00:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-01-01T00:00:00"
  }
}
```

## 5. 前端适配指南

### 5.1 数据模型映射

前端可以定义以下接口来映射后端的数据结构：

```typescript
// 客户盒式信息接口
export interface CustomerBoxInfo {
  id: string;
  boxCode: string;                      // 盒式编码
  boxName: string;                      // 盒式名称
  quoteFormula: string;                 // 报价公式
  calculationUnit: string;              // 算价单位
  quoteUnit: string;                    // 报价单位
  connectionMethod: string;             // 连接方式
  forbidDoubleFluting: boolean;         // 禁止双驳
  doubleFlutingLengthThreshold: number; // 长度大于此值双驳
  pitchTolerance: string;               // 跳度公差
  hasBoxGraphic: boolean;               // 盒式图形
  isDefault: boolean;                   // 默认盒式
  status: string;                       // 状态
  createdBy: string;                    // 创建人
  createdTime: string;                  // 创建时间
  updatedBy: string;                    // 更新人
  updatedTime: string;                  // 更新时间
}

// 查询参数接口
export interface CustomerBoxInfoQueryParams {
  keyword?: string;       // 关键词搜索（编码、名称）
  status?: string;        // 状态
  isDefault?: boolean;    // 是否默认盒式
  page: number;           // 当前页码
  size: number;           // 每页条数
  sortField?: string;     // 排序字段
  sortDirection?: string; // 排序方向
}

// 分页结果接口
export interface PageResult<T> {
  content: T[];           // 数据列表
  totalElements: number;  // 总记录数
  totalPages: number;     // 总页数
  page: number;           // 当前页码
  size: number;           // 每页条数
  first: boolean;         // 是否第一页
  last: boolean;          // 是否最后一页
  empty: boolean;         // 是否为空
}
```

### 5.2 请求参数适配

#### 获取客户盒式信息列表

前端需要将自己的查询参数适配为后端接受的格式：

```typescript
// 前端查询参数
const queryParams: CustomerBoxInfoQueryParams = {
  keyword: 'BOX',
  status: 'active',
  isDefault: true,
  page: 1,
  size: 10,
  sortField: 'boxCode',
  sortDirection: 'asc'
};

// 调用API
const response = await api.get('/api/customer-box-infos', { params: queryParams });
```

### 5.3 响应数据适配

前端需要将后端返回的数据适配为前端使用的格式：

```typescript
// 处理分页响应
function handlePageResponse(response: any): PageResult<CustomerBoxInfo> {
  const { data } = response;
  return {
    content: data.content,
    totalElements: data.totalElements,
    totalPages: data.totalPages,
    page: data.page,
    size: data.size,
    first: data.first,
    last: data.last,
    empty: data.empty
  };
}

// 处理单个对象响应
function handleSingleResponse(response: any): CustomerBoxInfo {
  return response.data;
}

// 处理列表响应
function handleListResponse(response: any): CustomerBoxInfo[] {
  return response.data;
}
```

### 5.4 API服务示例

```typescript
import { get } from './request';
import type { CustomerBoxInfo, CustomerBoxInfoQueryParams, PageResult } from '@/types/customer';
import type { ApiResponse } from '@/types/api';

/**
 * 获取客户盒式信息列表
 * @param params 查询参数
 */
export function getCustomerBoxInfoList(params: CustomerBoxInfoQueryParams): Promise<ApiResponse<PageResult<CustomerBoxInfo>>> {
  return get<PageResult<CustomerBoxInfo>>('/customer-box-infos', params);
}

/**
 * 根据ID获取客户盒式信息
 * @param id 客户盒式信息ID
 */
export function getCustomerBoxInfoById(id: string): Promise<ApiResponse<CustomerBoxInfo>> {
  return get<CustomerBoxInfo>(`/customer-box-infos/${id}`);
}

/**
 * 根据盒式编码获取客户盒式信息
 * @param code 盒式编码
 */
export function getCustomerBoxInfoByCode(code: string): Promise<ApiResponse<CustomerBoxInfo>> {
  return get<CustomerBoxInfo>(`/customer-box-infos/code/${code}`);
}

/**
 * 获取所有启用的客户盒式信息
 */
export function getActiveCustomerBoxInfos(): Promise<ApiResponse<CustomerBoxInfo[]>> {
  return get<CustomerBoxInfo[]>('/customer-box-infos/active');
}

/**
 * 获取默认盒式
 */
export function getDefaultCustomerBoxInfo(): Promise<ApiResponse<CustomerBoxInfo>> {
  return get<CustomerBoxInfo>('/customer-box-infos/default');
}

/**
 * 获取所有启用的客户盒式信息（公共接口）
 */
export function getPublicActiveCustomerBoxInfos(): Promise<ApiResponse<CustomerBoxInfo[]>> {
  return get<CustomerBoxInfo[]>('/public/customer-box-infos');
}

/**
 * 获取默认盒式（公共接口）
 */
export function getPublicDefaultCustomerBoxInfo(): Promise<ApiResponse<CustomerBoxInfo>> {
  return get<CustomerBoxInfo>('/public/customer-box-infos/default');
}
```
