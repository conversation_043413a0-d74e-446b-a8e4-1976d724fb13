-- 插入计量单位数据
INSERT INTO measurement_unit (
    unit_name, 
    sort_order, 
    is_default_for_new_material, 
    is_dimension_unit, 
    is_default_dimension_unit, 
    is_default_thickness_unit, 
    is_default_length_unit, 
    status, 
    created_by, 
    created_time, 
    is_deleted
) VALUES 
-- 长度单位
('Inch', 10, FALSE, TRUE, FALSE, TRUE, FALSE, 'active', 'system', NOW(), FALSE),
('MM', 20, FALSE, TRUE, TRUE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('CM', 30, TRUE, TRUE, FALSE, FALSE, TRUE, 'active', 'system', NOW(), FALSE),

-- 面积单位
('mm2', 40, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),

-- 数量单位
('桶', 50, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('KG', 60, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('箱', 70, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('卷', 80, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('张', 90, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('个', 100, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('双', 110, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('包', 120, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('笔', 130, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE),
('PCS', 140, FALSE, FALSE, FALSE, FALSE, FALSE, 'active', 'system', NOW(), FALSE);

-- 如果您想清空表后再插入数据，可以先执行以下语句：
-- TRUNCATE TABLE measurement_unit;
