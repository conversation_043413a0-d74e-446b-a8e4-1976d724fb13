package com.czerp.erpbackend.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 缓存配置
 * 支持异步事件处理和缓存管理
 */
@Configuration
@EnableCaching
@EnableAsync
public class CacheConfig implements CachingConfigurer {

    /**
     * 缓存管理器
     * @return CacheManager
     */
    @Bean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager(
                "users",
                "roles",
                "permissions",
                "products",
                "categories",
                "customers",
                "customer_categories",
                "suppliers",
                "warehouses",
                "locations",
                "dictionaries",
                // 采购订单明细动态计算相关缓存
                "receivedQuantity",                    // 已入库数量缓存
                "purchaseOrderItemAvailability"       // 采购订单明细可用性缓存
        );
    }
}
