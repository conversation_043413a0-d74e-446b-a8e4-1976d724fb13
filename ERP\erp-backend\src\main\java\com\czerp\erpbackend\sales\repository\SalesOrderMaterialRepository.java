package com.czerp.erpbackend.sales.repository;

import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 销售订单材料信息Repository
 */
@Repository
public interface SalesOrderMaterialRepository extends JpaRepository<SalesOrderMaterial, String>, JpaSpecificationExecutor<SalesOrderMaterial> {

    /**
     * 根据订单行项目ID查询材料信息
     * @param orderItemId 订单行项目ID
     * @return 材料信息列表
     */
    List<SalesOrderMaterial> findByOrderItemIdOrderBySerialNoAsc(String orderItemId);

    /**
     * 根据订单行项目ID删除材料信息
     * @param orderItemId 订单行项目ID
     */
    void deleteByOrderItemId(String orderItemId);

    /**
     * 根据订单行项目ID列表查询材料信息
     * @param orderItemIds 订单行项目ID列表
     * @return 材料信息列表
     */
    List<SalesOrderMaterial> findByOrderItemIdIn(List<String> orderItemIds);
}
