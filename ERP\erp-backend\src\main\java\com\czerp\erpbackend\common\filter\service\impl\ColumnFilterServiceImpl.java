package com.czerp.erpbackend.common.filter.service.impl;

import com.czerp.erpbackend.common.filter.builder.CascadeFilterBuilder;
import com.czerp.erpbackend.common.filter.builder.FilterQueryBuilder;
import com.czerp.erpbackend.common.filter.config.FilterFieldRegistry;
import com.czerp.erpbackend.common.filter.dto.FilterOptionDTO;
import com.czerp.erpbackend.common.filter.dto.FilterRequest;
import com.czerp.erpbackend.common.filter.metadata.FilterFieldMetadata;
import com.czerp.erpbackend.common.filter.service.ColumnFilterService;
import com.czerp.erpbackend.common.filter.handler.FilterHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用列筛选服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ColumnFilterServiceImpl implements ColumnFilterService {
    
    private final FilterFieldRegistry fieldRegistry;
    private final FilterQueryBuilder queryBuilder;
    private final CascadeFilterBuilder cascadeBuilder;
    private final ApplicationContext applicationContext;
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    @Transactional(readOnly = true)
    public List<FilterOptionDTO> getFilterOptions(String moduleCode, String fieldName, 
                                                 String searchText, Map<String, Object> currentFilters) {
        FilterRequest request = FilterRequest.builder()
                .moduleCode(moduleCode)
                .fieldName(fieldName)
                .searchText(searchText)
                .currentFilters(currentFilters)
                .enableCascade(true)
                .maxResults(50)
                .build();
        
        return getFilterOptions(request);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<FilterOptionDTO> getFilterOptions(FilterRequest request) {
        log.debug("Getting filter options for module: {}, field: {}", 
                 request.getModuleCode(), request.getFieldName());
        
        // 获取字段元数据
        FilterFieldMetadata metadata = fieldRegistry.getFieldMetadata(
                request.getModuleCode(), request.getFieldName());
        
        if (metadata == null) {
            log.warn("Unsupported filter field: {} in module: {}", 
                    request.getFieldName(), request.getModuleCode());
            return Collections.emptyList();
        }
        
        // 根据筛选类型选择处理方式
        switch (metadata.getFilterType()) {
            case DYNAMIC_QUERY:
                return getDynamicQueryOptions(request, metadata);
            case STATIC_DICTIONARY:
                return getStaticDictionaryOptions(request, metadata);
            case CUSTOM_HANDLER:
                return getCustomHandlerOptions(request, metadata);
            default:
                log.warn("Unknown filter type: {} for field: {}", 
                        metadata.getFilterType(), request.getFieldName());
                return Collections.emptyList();
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public Map<String, List<FilterOptionDTO>> getBatchFilterOptions(String moduleCode, 
                                                                   List<String> fieldNames, 
                                                                   Map<String, Object> currentFilters) {
        Map<String, List<FilterOptionDTO>> result = new HashMap<>();
        
        for (String fieldName : fieldNames) {
            List<FilterOptionDTO> options = getFilterOptions(moduleCode, fieldName, null, currentFilters);
            if (!options.isEmpty()) {
                result.put(fieldName, options);
            }
        }
        
        return result;
    }
    
    @Override
    public boolean isFieldSupported(String moduleCode, String fieldName) {
        return fieldRegistry.isFieldSupported(moduleCode, fieldName);
    }
    
    @Override
    public List<String> getSupportedFields(String moduleCode) {
        return fieldRegistry.getSupportedFields(moduleCode);
    }
    
    /**
     * 获取动态查询选项
     */
    private List<FilterOptionDTO> getDynamicQueryOptions(FilterRequest request, FilterFieldMetadata metadata) {
        try {
            List<String> results;

            // 🔥 修复：根据是否启用级联筛选选择不同的查询方法
            if (Boolean.TRUE.equals(request.getEnableCascade()) &&
                request.getCurrentFilters() != null && !request.getCurrentFilters().isEmpty()) {

                // 获取模块字段映射
                Map<String, FilterFieldMetadata> moduleFieldsMap =
                        fieldRegistry.getModuleFields(request.getModuleCode());

                // 执行带级联筛选的查询
                results = queryBuilder.executeQueryWithCascade(
                        entityManager,
                        metadata.getEntityClass(),
                        metadata,
                        request.getSearchText(),
                        request.getMaxResults(),
                        request.getCurrentFilters(),
                        request.getFieldName(),  // 排除当前字段
                        moduleFieldsMap
                );
            } else {
                // 执行基础查询
                results = queryBuilder.executeBasicQuery(
                        entityManager,
                        metadata.getEntityClass(),
                        metadata,
                        request.getSearchText(),
                        request.getMaxResults()
                );
            }

            // 转换为DTO
            return results.stream()
                    .filter(StringUtils::hasText)
                    .map(FilterOptionDTO::of)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Failed to get dynamic query options for field: {}, error: {}",
                     request.getFieldName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 获取静态字典选项
     */
    private List<FilterOptionDTO> getStaticDictionaryOptions(FilterRequest request, FilterFieldMetadata metadata) {
        try {
            // TODO: 集成字典服务
            log.debug("Static dictionary options for field: {} not implemented yet", request.getFieldName());
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Failed to get static dictionary options for field: {}, error: {}", 
                     request.getFieldName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 获取自定义处理器选项
     */
    private List<FilterOptionDTO> getCustomHandlerOptions(FilterRequest request, FilterFieldMetadata metadata) {
        try {
            FilterHandler handler = null;

            // 🔥 修复：优先使用Bean名称，回退到类名
            if (StringUtils.hasText(metadata.getCustomHandlerBean())) {
                // 使用Bean名称获取处理器
                handler = (FilterHandler) applicationContext.getBean(metadata.getCustomHandlerBean());
            } else if (StringUtils.hasText(metadata.getCustomHandlerClass())) {
                // 回退到类名获取处理器
                handler = (FilterHandler) applicationContext.getBean(Class.forName(metadata.getCustomHandlerClass()));
            } else {
                log.warn("Custom handler not specified for field: {}", request.getFieldName());
                return Collections.emptyList();
            }

            return handler.getFilterOptions(request, metadata);

        } catch (Exception e) {
            log.error("Failed to get custom handler options for field: {}, error: {}",
                     request.getFieldName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    

}
