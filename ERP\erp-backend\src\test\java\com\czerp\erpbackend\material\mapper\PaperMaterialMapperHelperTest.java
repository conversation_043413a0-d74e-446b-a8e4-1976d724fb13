package com.czerp.erpbackend.material.mapper;

import com.czerp.erpbackend.material.dto.PaperMaterialDTO;
import com.czerp.erpbackend.material.dto.PaperTypeDTO;
import com.czerp.erpbackend.material.entity.PaperMaterial;
import com.czerp.erpbackend.material.entity.PaperType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaperMaterialMapperHelperTest {

    @Mock
    private PaperTypeMapperHelper paperTypeMapperHelper;

    @InjectMocks
    private PaperMaterialMapperHelper paperMaterialMapperHelper;

    private PaperType paperType;
    private PaperMaterial paperMaterial;
    private PaperTypeDTO paperTypeDTO;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        paperType = new PaperType();
        paperType.setId(1);
        paperType.setPaperTypeName("三坑");
        paperType.setStaplingFlapInch(new BigDecimal("1.50"));
        paperType.setStaplingFlapCm(new BigDecimal("3.81"));
        paperType.setGluingFlapInch(new BigDecimal("1.50"));
        paperType.setGluingFlapCm(new BigDecimal("3.81"));
        paperType.setAddMarginMm(new BigDecimal("1.00")); // 设置加分值
        paperType.setReduceMarginMm(null);
        paperType.setThicknessMm(new BigDecimal("9.00"));
        paperType.setLayerCount(7);
        paperType.setSheetsPerBoard(null);

        paperMaterial = new PaperMaterial();
        paperMaterial.setId(1L);
        paperMaterial.setPaperCode("P001");
        paperMaterial.setPaperName("测试纸质");
        paperMaterial.setPaperType(paperType);
        paperMaterial.setFluteType("A");
        paperMaterial.setIsStandard(true);

        paperTypeDTO = PaperTypeDTO.builder()
                .id(1)
                .paperTypeName("三坑")
                .staplingFlapInch(new BigDecimal("1.50"))
                .staplingFlapCm(new BigDecimal("3.81"))
                .gluingFlapInch(new BigDecimal("1.50"))
                .gluingFlapCm(new BigDecimal("3.81"))
                .addMarginMm(new BigDecimal("1.00")) // 确保DTO中也有加分值
                .reduceMarginMm(null)
                .thicknessMm(new BigDecimal("9.00"))
                .layerCount(7)
                .sheetsPerBoard(null)
                .build();

        // 配置mock
        when(paperTypeMapperHelper.manualToDto(any(PaperType.class))).thenReturn(paperTypeDTO);
    }

    @Test
    void manualToDto_shouldMapAllFields() {
        // 执行测试
        PaperMaterialDTO result = paperMaterialMapperHelper.manualToDto(paperMaterial);

        // 验证结果
        assertNotNull(result);
        assertEquals(paperMaterial.getId(), result.getId());
        assertEquals(paperMaterial.getPaperCode(), result.getPaperCode());
        assertEquals(paperMaterial.getPaperName(), result.getPaperName());
        assertEquals(paperMaterial.getFluteType(), result.getFluteType());
        assertEquals(paperMaterial.getIsStandard(), result.getIsStandard());

        // 验证paperType属性
        assertNotNull(result.getPaperType());
        assertEquals(paperTypeDTO.getId(), result.getPaperType().getId());
        assertEquals(paperTypeDTO.getPaperTypeName(), result.getPaperType().getPaperTypeName());
        
        // 验证addMarginMm字段是否被正确映射
        assertNotNull(result.getPaperType().getAddMarginMm());
        assertEquals(new BigDecimal("1.00"), result.getPaperType().getAddMarginMm());
    }

    @Test
    void manualToDtoList_shouldMapAllItems() {
        // 准备测试数据
        List<PaperMaterial> materials = Arrays.asList(paperMaterial, paperMaterial);

        // 执行测试
        List<PaperMaterialDTO> results = paperMaterialMapperHelper.manualToDtoList(materials);

        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());
        
        // 验证每个项目的paperType属性中的addMarginMm字段是否被正确映射
        for (PaperMaterialDTO dto : results) {
            assertNotNull(dto.getPaperType());
            assertNotNull(dto.getPaperType().getAddMarginMm());
            assertEquals(new BigDecimal("1.00"), dto.getPaperType().getAddMarginMm());
        }
    }
}
