package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.system.dto.PaperSizeMatchingRequest;
import com.czerp.erpbackend.system.dto.PaperSizeRecommendation;
import com.czerp.erpbackend.system.service.PaperSizeMatchingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 纸度推荐控制器
 */
@RestController
@RequestMapping("/system/paper-size-matching/recommendations")
@Tag(name = "纸度推荐", description = "纸度推荐相关接口")
@RequiredArgsConstructor
@Slf4j
public class PaperSizeRecommendationController {
    
    private final PaperSizeMatchingService paperSizeMatchingService;
    
    /**
     * 获取纸度推荐列表
     * @param request 纸度匹配请求
     * @return 纸度推荐列表
     */
    @PostMapping
    @Operation(summary = "获取纸度推荐列表", description = "根据纸盒宽度和高度获取纸度推荐列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:paper-size-matching:match')")
    public ResponseEntity<ApiResponse<List<PaperSizeRecommendation>>> getPaperSizeRecommendations(
            @Valid @RequestBody PaperSizeMatchingRequest request) {
        log.info("Getting paper size recommendations with request: {}", request);
        List<PaperSizeRecommendation> recommendations = 
                paperSizeMatchingService.getPaperSizeRecommendations(request);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
}
