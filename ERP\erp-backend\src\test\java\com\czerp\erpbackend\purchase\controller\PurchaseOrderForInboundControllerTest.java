package com.czerp.erpbackend.purchase.controller;

import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemForInboundDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemForInboundQueryRequest;
import com.czerp.erpbackend.purchase.service.PurchaseOrderForInboundService;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 采购订单引用控制器测试类
 */
@WebMvcTest(PurchaseOrderForInboundController.class)
class PurchaseOrderForInboundControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PurchaseOrderForInboundService purchaseOrderForInboundService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @WithMockUser(authorities = {"inventory:inbound:create"})
    void testFindPurchaseOrderItemsForInbound() throws Exception {
        // 准备测试数据
        PurchaseOrderItemForInboundDTO dto = PurchaseOrderItemForInboundDTO.builder()
            .id(1L)
            .purchaseOrderId(1L)
            .inboundQuantity(1000)
            .inboundAmount(new BigDecimal("5000.00"))
            .supplierName("XX纸业有限公司")
            .purchaseOrderNo("CG202401001")
            .purchaseDate(LocalDate.of(2024, 1, 15))
            .quantity(1000)
            .paperQuality("白卡纸")
            .bindingSpecification("对开")
            .receivedQuantity(0)
            .returnedQuantity(0)
            .unreceivedQuantity(1000)
            .productionOrderNo("P202401001")
            .customerCode("CUS001")
            .customerName("XX包装公司")
            .customerOrderNo("XS202401001")
            .customerProductCode("PKG001")
            .productName("包装盒")
            .processRequirements("开槽→打角→打钉")
            .product("天地盒 白卡纸")
            .productSpecification("300*200*100mm")
            .productionSpecification("310*210*105mm")
            .salesOrderDeliveryDate(LocalDate.of(2024, 2, 15))
            .createdBy("admin")
            .paperBoardCategory("白卡纸")
            .price(new BigDecimal("5.00"))
            .amount(new BigDecimal("5000.00"))
            .deliveryDate(LocalDate.of(2024, 2, 10))
            .build();

        PageResponse<PurchaseOrderItemForInboundDTO> pageResponse = PageResponse.<PurchaseOrderItemForInboundDTO>builder()
            .content(Arrays.asList(dto))
            .page(0)
            .size(20)
            .totalElements(1L)
            .totalPages(1)
            .first(true)
            .last(true)
            .build();

        when(purchaseOrderForInboundService.findPurchaseOrderItemsForInbound(any(PurchaseOrderItemForInboundQueryRequest.class)))
            .thenReturn(pageResponse);

        // 执行测试
        mockMvc.perform(get("/purchase-orders-for-inbound/items")
                .param("page", "0")
                .param("size", "20")
                .param("keyword", "CG202401001")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data.content[0].id").value(1))
            .andExpect(jsonPath("$.data.content[0].purchaseOrderNo").value("CG202401001"))
            .andExpect(jsonPath("$.data.content[0].supplierName").value("XX纸业有限公司"))
            .andExpect(jsonPath("$.data.content[0].productionOrderNo").value("P202401001"))
            .andExpect(jsonPath("$.data.content[0].customerName").value("XX包装公司"))
            .andExpect(jsonPath("$.data.content[0].unreceivedQuantity").value(1000))
            .andExpect(jsonPath("$.data.totalElements").value(1));
    }

    @Test
    @WithMockUser(authorities = {"inventory:inbound:read"})
    void testFindPurchaseOrderItemById() throws Exception {
        // 准备测试数据
        PurchaseOrderItemForInboundDTO dto = PurchaseOrderItemForInboundDTO.builder()
            .id(1L)
            .purchaseOrderId(1L)
            .supplierName("XX纸业有限公司")
            .purchaseOrderNo("CG202401001")
            .quantity(1000)
            .unreceivedQuantity(1000)
            .build();

        when(purchaseOrderForInboundService.findPurchaseOrderItemById(anyLong()))
            .thenReturn(dto);

        // 执行测试
        mockMvc.perform(get("/purchase-orders-for-inbound/items/1")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data.id").value(1))
            .andExpect(jsonPath("$.data.purchaseOrderNo").value("CG202401001"))
            .andExpect(jsonPath("$.data.supplierName").value("XX纸业有限公司"));
    }

    @Test
    @WithMockUser(authorities = {"inventory:inbound:read"})
    void testFindPurchaseOrderItemsByIds() throws Exception {
        // 准备测试数据
        List<PurchaseOrderItemForInboundDTO> dtoList = Arrays.asList(
            PurchaseOrderItemForInboundDTO.builder()
                .id(1L)
                .purchaseOrderNo("CG202401001")
                .supplierName("XX纸业有限公司")
                .build(),
            PurchaseOrderItemForInboundDTO.builder()
                .id(2L)
                .purchaseOrderNo("CG202401002")
                .supplierName("YY纸业有限公司")
                .build()
        );

        when(purchaseOrderForInboundService.findPurchaseOrderItemsByIds(anyList()))
            .thenReturn(dtoList);

        // 执行测试
        mockMvc.perform(get("/purchase-orders-for-inbound/items/batch")
                .param("ids", "1", "2")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data").isArray())
            .andExpect(jsonPath("$.data.length()").value(2))
            .andExpect(jsonPath("$.data[0].id").value(1))
            .andExpect(jsonPath("$.data[1].id").value(2));
    }
}
