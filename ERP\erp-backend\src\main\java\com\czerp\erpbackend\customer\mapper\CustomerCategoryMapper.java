package com.czerp.erpbackend.customer.mapper;

import com.czerp.erpbackend.customer.dto.CreateCustomerCategoryRequest;
import com.czerp.erpbackend.customer.dto.CustomerCategoryDTO;
import com.czerp.erpbackend.customer.dto.UpdateCustomerCategoryRequest;
import com.czerp.erpbackend.customer.entity.CustomerCategory;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * 客户分类Mapper
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomerCategoryMapper {
    
    /**
     * 实体转DTO
     * @param category 实体
     * @return DTO
     */
    @Mapping(source = "createdTime", target = "createTime")
    @Mapping(source = "updatedTime", target = "updateTime")
    CustomerCategoryDTO toDto(CustomerCategory category);
    
    /**
     * 实体列表转DTO列表
     * @param categories 实体列表
     * @return DTO列表
     */
    List<CustomerCategoryDTO> toDtoList(List<CustomerCategory> categories);
    
    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    CustomerCategory toEntity(CreateCustomerCategoryRequest request);
    
    /**
     * 更新实体
     * @param request 更新请求
     * @param category 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "categoryCode", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateCustomerCategoryRequest request, @MappingTarget CustomerCategory category);
}
