package com.czerp.erpbackend.purchase.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.purchase.dto.SalesOrderItemForPurchaseDTO;
import com.czerp.erpbackend.purchase.dto.SalesOrderItemForPurchaseQueryRequest;
import com.czerp.erpbackend.purchase.service.SalesOrderForPurchaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 销售订单引用控制器
 * 用于采购订单引用销售订单数据
 */
@RestController
@RequestMapping("/sales-orders-for-purchase")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "销售订单引用", description = "用于采购订单引用销售订单数据的接口")
public class SalesOrderForPurchaseController {

    private final SalesOrderForPurchaseService salesOrderForPurchaseService;

    /**
     * 分页查询可用于采购的销售订单明细
     * @param request 查询请求
     * @return 销售订单明细分页列表
     */
    @GetMapping("/items")
    @Operation(summary = "分页查询可用于采购的销售订单明细", description = "分页查询可用于采购的销售订单明细")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('purchase:order:create')")
    public ResponseEntity<ApiResponse<PageResponse<SalesOrderItemForPurchaseDTO>>> findSalesOrderItemsForPurchase(SalesOrderItemForPurchaseQueryRequest request) {
        log.info("Finding sales order items for purchase with request: {}", request);
        PageResponse<SalesOrderItemForPurchaseDTO> response = salesOrderForPurchaseService.findSalesOrderItemsForPurchase(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据ID查询销售订单明细
     * @param id 销售订单明细ID
     * @return 销售订单明细
     */
    @GetMapping("/items/{id}")
    @Operation(summary = "根据ID查询销售订单明细", description = "根据ID查询销售订单明细")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('purchase:order:create')")
    public ResponseEntity<ApiResponse<SalesOrderItemForPurchaseDTO>> findSalesOrderItemById(@PathVariable("id") String id) {
        log.info("Finding sales order item by id: {}", id);
        SalesOrderItemForPurchaseDTO item = salesOrderForPurchaseService.findSalesOrderItemById(id);
        return ResponseEntity.ok(ApiResponse.success(item));
    }

    /**
     * 根据多个ID查询销售订单明细
     * @param ids 销售订单明细ID列表
     * @return 销售订单明细列表
     */
    @GetMapping("/items/batch")
    @Operation(summary = "根据多个ID查询销售订单明细", description = "根据多个ID查询销售订单明细")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('purchase:order:create')")
    public ResponseEntity<ApiResponse<List<SalesOrderItemForPurchaseDTO>>> findSalesOrderItemsByIds(@RequestParam List<String> ids) {
        log.info("Finding sales order items by ids: {}", ids);
        List<SalesOrderItemForPurchaseDTO> items = salesOrderForPurchaseService.findSalesOrderItemsByIds(ids);
        return ResponseEntity.ok(ApiResponse.success(items));
    }
}
