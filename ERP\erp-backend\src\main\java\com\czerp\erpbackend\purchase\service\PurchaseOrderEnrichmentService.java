package com.czerp.erpbackend.purchase.service;

import com.czerp.erpbackend.material.entity.PaperMaterial;
import com.czerp.erpbackend.material.repository.PaperMaterialRepository;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemDTO;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderMaterialRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 采购订单数据丰富服务
 * 负责为采购订单DTO添加销售订单相关数据
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PurchaseOrderEnrichmentService {

    private final SalesOrderItemRepository salesOrderItemRepository;
    private final SalesOrderMaterialRepository salesOrderMaterialRepository;
    private final PaperMaterialRepository paperMaterialRepository;

    /**
     * 为采购订单DTO列表添加销售订单相关数据
     * @param dtos 采购订单DTO列表
     */
    @Transactional(readOnly = true)
    public void enrichWithSalesOrderData(List<PurchaseOrderDTO> dtos) {
        log.debug("Enriching {} purchase order DTOs with sales order data", dtos.size());

        // 收集所有采购订单明细的sourceSalesOrderItemId
        List<String> salesOrderItemIds = collectSalesOrderItemIds(dtos);
        if (salesOrderItemIds.isEmpty()) {
            log.debug("No sales order item IDs found, skipping enrichment");
            return;
        }

        // 批量查询销售订单数据
        Map<String, SalesOrderItem> salesOrderItemMap = fetchSalesOrderItems(salesOrderItemIds);
        Map<String, List<SalesOrderMaterial>> materialsByItemId = fetchSalesOrderMaterials(salesOrderItemIds);

        // 为每个采购订单DTO设置销售订单相关字段
        for (PurchaseOrderDTO dto : dtos) {
            enrichPurchaseOrderDTO(dto, salesOrderItemMap, materialsByItemId);
        }

        log.debug("Finished enriching purchase order DTOs with sales order data");
    }

    /**
     * 收集所有采购订单明细的sourceSalesOrderItemId
     * @param dtos 采购订单DTO列表
     * @return 销售订单明细ID列表
     */
    private List<String> collectSalesOrderItemIds(List<PurchaseOrderDTO> dtos) {
        return dtos.stream()
            .filter(dto -> dto.getItems() != null && !dto.getItems().isEmpty())
            .flatMap(dto -> dto.getItems().stream())
            .map(PurchaseOrderItemDTO::getSourceSalesOrderItemId)
            .filter(StringUtils::hasText)
            .distinct()
            .collect(Collectors.toList());
    }

    /**
     * 批量查询销售订单明细
     * @param salesOrderItemIds 销售订单明细ID列表
     * @return 销售订单明细Map，key为ID，value为实体
     */
    private Map<String, SalesOrderItem> fetchSalesOrderItems(List<String> salesOrderItemIds) {
        log.debug("Fetching {} sales order items", salesOrderItemIds.size());
        List<SalesOrderItem> salesOrderItems = salesOrderItemRepository.findAllById(salesOrderItemIds);
        log.debug("Found {} sales order items", salesOrderItems.size());
        return salesOrderItems.stream()
            .collect(Collectors.toMap(SalesOrderItem::getId, item -> item));
    }

    /**
     * 批量查询销售订单材料
     * @param salesOrderItemIds 销售订单明细ID列表
     * @return 销售订单材料Map，key为销售订单明细ID，value为材料列表
     */
    private Map<String, List<SalesOrderMaterial>> fetchSalesOrderMaterials(List<String> salesOrderItemIds) {
        log.debug("Fetching sales order materials for {} items", salesOrderItemIds.size());
        List<SalesOrderMaterial> materials = salesOrderMaterialRepository.findByOrderItemIdIn(salesOrderItemIds);
        log.debug("Found {} sales order materials", materials.size());
        return materials.stream()
            .collect(Collectors.groupingBy(material -> material.getOrderItem().getId()));
    }

    /**
     * 为采购订单DTO设置销售订单相关字段
     * @param dto 采购订单DTO
     * @param salesOrderItemMap 销售订单明细Map
     * @param materialsByItemId 销售订单材料Map
     */
    private void enrichPurchaseOrderDTO(PurchaseOrderDTO dto,
                                       Map<String, SalesOrderItem> salesOrderItemMap,
                                       Map<String, List<SalesOrderMaterial>> materialsByItemId) {
        if (dto.getItems() == null || dto.getItems().isEmpty()) {
            return;
        }

        for (PurchaseOrderItemDTO itemDTO : dto.getItems()) {
            String sourceSalesOrderItemId = itemDTO.getSourceSalesOrderItemId();
            if (!StringUtils.hasText(sourceSalesOrderItemId) || !salesOrderItemMap.containsKey(sourceSalesOrderItemId)) {
                continue;
            }

            SalesOrderItem salesOrderItem = salesOrderItemMap.get(sourceSalesOrderItemId);
            SalesOrder salesOrder = salesOrderItem.getOrder();

            // 设置销售订单相关字段
            enrichPurchaseOrderItemDTO(itemDTO, salesOrderItem, salesOrder, materialsByItemId);
        }
    }

    /**
     * 为采购订单明细DTO设置销售订单相关字段
     * @param itemDTO 采购订单明细DTO
     * @param salesOrderItem 销售订单明细
     * @param salesOrder 销售订单
     * @param materialsByItemId 销售订单材料Map
     */
    private void enrichPurchaseOrderItemDTO(PurchaseOrderItemDTO itemDTO,
                                           SalesOrderItem salesOrderItem,
                                           SalesOrder salesOrder,
                                           Map<String, List<SalesOrderMaterial>> materialsByItemId) {
        // 设置销售订单字段
        itemDTO.setCustomerName(salesOrder.getCustomerName());
        itemDTO.setSalesPerson(salesOrder.getSalesPerson());

        // 设置销售订单明细字段
        itemDTO.setCustomerOrderNo(salesOrderItem.getCustomerOrderNo());
        itemDTO.setCustomerProductCode(salesOrderItem.getCustomerProductCode());
        itemDTO.setProductName(salesOrderItem.getProductName());
        itemDTO.setProcessRequirements(salesOrderItem.getProcessRequirements());
        itemDTO.setBoxType(salesOrderItem.getBoxType());

        // 设置订单纸质
        String paperType = salesOrderItem.getPaperType();
        itemDTO.setOrderPaperType(paperType);
        log.debug("Set order paper type: {} for purchase order item: {}", paperType, itemDTO.getId());

        // 设置销售订单交期
        itemDTO.setSalesOrderDeliveryDate(salesOrderItem.getDeliveryDate());

        // 设置销售订单创建时间
        itemDTO.setSalesOrderCreatedTime(salesOrderItem.getCreatedTime());

        // 设置销售订单明细生产备注
        itemDTO.setSalesOrderProductionRemark(salesOrderItem.getProductionRemark());

        // 设置销售订单明细备注
        itemDTO.setSalesOrderRemark(salesOrderItem.getRemark());

        // 设置产品规格
        String productSpecification = formatSpecification(
            salesOrderItem.getLength(),
            salesOrderItem.getWidth(),
            salesOrderItem.getHeight(),
            salesOrderItem.getSizeUnit()
        );
        itemDTO.setProductSpecification(productSpecification);

        // 设置生产规格
        String productionSpecification = formatSpecification(
            salesOrderItem.getProductionLength(),
            salesOrderItem.getProductionWidth(),
            salesOrderItem.getProductionHeight(),
            salesOrderItem.getSizeUnit()
        );

        // 如果生产规格为空，则使用产品规格
        if (StringUtils.isEmpty(productionSpecification)) {
            productionSpecification = productSpecification;
        }

        itemDTO.setProductionSpecification(productionSpecification);

        // 设置模开数和纸质相关信息
        List<SalesOrderMaterial> materials = materialsByItemId.getOrDefault(salesOrderItem.getId(), Collections.emptyList());

        // 如果没有材料信息，尝试使用销售订单明细的数量作为纸板数
        if (materials.isEmpty()) {
            Integer quantity = salesOrderItem.getQuantity();
            if (quantity != null && quantity > 0) {
                itemDTO.setBoardCount(quantity);
                log.debug("No materials found for sales order item {}, using order quantity {} as board count",
                        salesOrderItem.getId(), quantity);
            }
        } else {
            SalesOrderMaterial material = materials.get(0);

            // 获取第一个材料的模开数
            itemDTO.setDieOpenCount(material.getDieOpenCount());

            // 计算所有材料的总纸板数
            Integer totalBoardCount = 0;
            for (SalesOrderMaterial mat : materials) {
                if (mat.getBoardCount() != null) {
                    totalBoardCount += mat.getBoardCount();
                }
            }

            // 设置纸板数
            Integer finalBoardCount = totalBoardCount > 0 ? totalBoardCount : material.getBoardCount();
            itemDTO.setBoardCount(finalBoardCount);
            log.debug("Set board count: {} for purchase order item: {}", finalBoardCount, itemDTO.getId());

            // 获取纸质名称
            String paperQuality = material.getPaperQuality();

            // 如果获取到了纸质，则根据纸质获取纸板类别（无论paperBoardCategory是否已有值）
            if (StringUtils.hasText(paperQuality)) {
                // 根据纸质名称获取纸板类别
                String paperTypeName = getPaperTypeName(paperQuality);
                if (StringUtils.hasText(paperTypeName)) {
                    // 如果当前paperBoardCategory与paperQuality相同，说明可能是从销售订单引用时直接复制的值，需要更新
                    if (!StringUtils.hasText(itemDTO.getPaperBoardCategory()) ||
                        itemDTO.getPaperBoardCategory().equals(paperQuality)) {
                        itemDTO.setPaperBoardCategory(paperTypeName);
                        log.debug("Set paper board category: {} for purchase order item: {}", paperTypeName, itemDTO.getId());
                    }
                }
            }
        }

        // 如果采购订单明细中已有纸质，则根据纸质获取纸板类别（无论paperBoardCategory是否已有值）
        if (StringUtils.hasText(itemDTO.getPaperQuality())) {
            // 根据纸质名称获取纸板类别
            String paperTypeName = getPaperTypeName(itemDTO.getPaperQuality());
            if (StringUtils.hasText(paperTypeName)) {
                // 如果当前paperBoardCategory与paperQuality相同，说明可能是从销售订单引用时直接复制的值，需要更新
                if (!StringUtils.hasText(itemDTO.getPaperBoardCategory()) ||
                    itemDTO.getPaperBoardCategory().equals(itemDTO.getPaperQuality())) {
                    itemDTO.setPaperBoardCategory(paperTypeName);
                    log.debug("Set paper board category: {} for purchase order item: {}", paperTypeName, itemDTO.getId());
                }
            }
        }
    }

    /**
     * 格式化规格字符串
     * @param length 长
     * @param width 宽
     * @param height 高
     * @param sizeUnit 尺寸单位
     * @return 格式化后的规格字符串
     */
    private String formatSpecification(BigDecimal length, BigDecimal width, BigDecimal height, String sizeUnit) {
        // 检查null值
        if (length == null || width == null || height == null) {
            return "";
        }

        // 检查是否所有值都为0
        boolean allZero = length.compareTo(BigDecimal.ZERO) == 0 &&
                          width.compareTo(BigDecimal.ZERO) == 0 &&
                          height.compareTo(BigDecimal.ZERO) == 0;

        if (allZero) {
            return "";
        }

        return length + " x " + width + " x " + height + " " + (sizeUnit != null ? sizeUnit : "");
    }

    /**
     * 根据纸质名称获取纸板类别名称
     * @param paperQuality 纸质名称
     * @return 纸板类别名称
     */
    private String getPaperTypeName(String paperQuality) {
        if (!StringUtils.hasText(paperQuality)) {
            return null;
        }

        log.debug("Getting paper type name for paper quality: {}", paperQuality);

        // 查询纸质资料
        Optional<PaperMaterial> paperMaterialOpt = paperMaterialRepository.findByPaperName(paperQuality);
        if (paperMaterialOpt.isEmpty()) {
            log.debug("Paper material not found for paper quality: {}", paperQuality);
            return null;
        }

        PaperMaterial paperMaterial = paperMaterialOpt.get();
        if (paperMaterial.getPaperType() == null) {
            log.debug("Paper type is null for paper material: {}", paperMaterial.getPaperName());
            return null;
        }

        String paperTypeName = paperMaterial.getPaperType().getPaperTypeName();
        log.debug("Found paper type name: {} for paper quality: {}", paperTypeName, paperQuality);
        return paperTypeName;
    }
}
