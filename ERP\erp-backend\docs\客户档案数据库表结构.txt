mysql> SHOW FULL COLUMNS FROM czerp_web.cus_customer;
+---------------------------------+---------------+--------------------+------+-----+-------------------+-----------------------------+---------------------------------+-------------+
| Field                           | Type          | Collation          | Null | Key | Default           | Extra                       | Privileges                      | Comment     |
+---------------------------------+---------------+--------------------+------+-----+-------------------+-----------------------------+---------------------------------+-------------+
| id                              | varchar(36)   | utf8mb4_0900_ai_ci | NO   | PRI | NULL              |                             | select,insert,update,references | ???ID       |
| customer_code                   | varchar(50)   | utf8mb4_0900_ai_ci | NO   | UNI | NULL              |                             | select,insert,update,references | ??????      |
| customer_name                   | varchar(100)  | utf8mb4_0900_ai_ci | NO   | MUL | NULL              |                             | select,insert,update,references | ????        |
| category_id                     | varchar(36)   | utf8mb4_0900_ai_ci | YES  | MUL | NULL              |                             | select,insert,update,references | ???????ID   |
| full_name                       | varchar(200)  | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ?????       |
| phone                           | varchar(20)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ?绰         |
| fax                             | varchar(20)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ???         |
| contact_person                  | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ????        |
| mobile                          | varchar(20)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ??          |
| address                         | varchar(200)  | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ???         |
| shipping_address                | varchar(200)  | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ??????      |
| payment_method                  | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ?????       |
| tax_rate                        | decimal(5,2)  | NULL               | YES  |     | NULL              |                             | select,insert,update,references | ???%        |
| last_order_date                 | date          | NULL               | YES  |     | NULL              |                             | select,insert,update,references | ??????????  |
| current_month_order_amount      | decimal(15,2) | NULL               | YES  |     | 0.00              |                             | select,insert,update,references | ???????     |
| parent_company                  | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ????????    |
| region                          | varchar(50)   | utf8mb4_0900_ai_ci | YES  | MUL | NULL              |                             | select,insert,update,references | ???         |
| sales_person                    | varchar(50)   | utf8mb4_0900_ai_ci | YES  | MUL | NULL              |                             | select,insert,update,references | ?????       |
| order_tracker                   | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ?????       |
| postal_code                     | varchar(20)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ??          |
| receiver                        | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ??          |
| currency                        | varchar(20)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ??          |
| default_quote_unit              | varchar(20)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ???????λ   |
| order_spare_ratio               | decimal(5,2)  | NULL               | YES  |     | NULL              |                             | select,insert,update,references | ??????????  |
| price_decimal_places            | int           | NULL               | YES  |     | 2                 |                             | select,insert,update,references | ????С?     |
| amount_decimal_places           | int           | NULL               | YES  |     | 2                 |                             | select,insert,update,references | ????С?     |
| unit_weight_decimal_places      | int           | NULL               | YES  |     | 3                 |                             | select,insert,update,references | ????С?     |
| total_weight_decimal_places     | int           | NULL               | YES  |     | 3                 |                             | select,insert,update,references | ????С?     |
| unit_area_decimal_places        | int           | NULL               | YES  |     | 3                 |                             | select,insert,update,references | ??λ????С? |
| total_area_decimal_places       | int           | NULL               | YES  |     | 3                 |                             | select,insert,update,references | ??????С?   |
| special_monthly_settlement_date | int           | NULL               | YES  |     | NULL              |                             | select,insert,update,references | ??????????  |
| available_credit_amount         | decimal(15,2) | NULL               | YES  |     | 0.00              |                             | select,insert,update,references | ???????     |
| last_delivery_date              | date          | NULL               | YES  |     | NULL              |                             | select,insert,update,references | ??????????  |
| days_without_order              | int           | NULL               | YES  |     | NULL              |                             | select,insert,update,references | δ??????    |
| days_without_delivery           | int           | NULL               | YES  |     | NULL              |                             | select,insert,update,references | δ??????    |
| first_order_date                | date          | NULL               | YES  |     | NULL              |                             | select,insert,update,references | ??ζ??????  |
| first_delivery_date             | date          | NULL               | YES  |     | NULL              |                             | select,insert,update,references | ?????????   |
| print_header                    | varchar(200)  | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ?????       |
| disabled                        | tinyint(1)    | NULL               | NO   | MUL | 0                 |                             | select,insert,update,references | ??          |
| created_by                      | varchar(50)   | utf8mb4_0900_ai_ci | NO   |     | NULL              |                             | select,insert,update,references |             |
| created_time                    | datetime      | NULL               | NO   | MUL | CURRENT_TIMESTAMP | DEFAULT_GENERATED           | select,insert,update,references | ??????      |
| updated_by                      | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references |             |
| updated_time                    | datetime      | NULL               | YES  |     | NULL              | on update CURRENT_TIMESTAMP | select,insert,update,references | ??????      |
| industry                        | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ???         |
| is_deleted                      | tinyint(1)    | NULL               | NO   |     | 0                 |                             | select,insert,update,references | ?????       |
| version                         | int           | NULL               | YES  |     | NULL              |                             | select,insert,update,references |             |
| CreatedBy                       | varchar(50)   | utf8mb4_0900_ai_ci | NO   |     | NULL              |                             | select,insert,update,references |             |
| UpdatedBy                       | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references |             |
+---------------------------------+---------------+--------------------+------+-----+-------------------+-----------------------------+---------------------------------+-------------+
48 <USER> <GROUP> set (0.00 sec)


mysql> SHOW FULL COLUMNS FROM czerp_web.cus_customer_category;
+---------------+--------------+--------------------+------+-----+-------------------+-----------------------------+---------------------------------+------------------------------+
| Field         | Type         | Collation          | Null | Key | Default           | Extra                       | Privileges                      | Comment                      |
+---------------+--------------+--------------------+------+-----+-------------------+-----------------------------+---------------------------------+------------------------------+
| id            | varchar(36)  | utf8mb4_0900_ai_ci | NO   | PRI | NULL              |                             | select,insert,update,references | ????ID                       |
| category_code | varchar(50)  | utf8mb4_0900_ai_ci | NO   | UNI | NULL              |                             | select,insert,update,references | ???????                      |
| category_name | varchar(100) | utf8mb4_0900_ai_ci | NO   | MUL | NULL              |                             | select,insert,update,references | ?????                        |
| sort_order    | int          | NULL               | NO   | MUL | 0                 |                             | select,insert,update,references | ???                          |
| remark        | varchar(255) | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references | ???                          |
| status        | varchar(20)  | utf8mb4_0900_ai_ci | NO   | MUL | active            |                             | select,insert,update,references | ??(active-????,inactive-???) |
| created_by    | varchar(50)  | utf8mb4_0900_ai_ci | NO   |     | NULL              |                             | select,insert,update,references |                              |
| created_time  | datetime     | NULL               | NO   |     | CURRENT_TIMESTAMP | DEFAULT_GENERATED           | select,insert,update,references | ??????                       |
| updated_by    | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references |                              |
| updated_time  | datetime     | NULL               | YES  |     | NULL              | on update CURRENT_TIMESTAMP | select,insert,update,references | ??????                       |
| is_default    | tinyint(1)   | NULL               | NO   | MUL | 0                 |                             | select,insert,update,references | ?????????????????            |
| is_deleted    | tinyint(1)   | NULL               | NO   |     | 0                 |                             | select,insert,update,references | ?????                        |
| version       | int          | NULL               | YES  |     | NULL              |                             | select,insert,update,references |                              |
| CreatedBy     | varchar(50)  | utf8mb4_0900_ai_ci | NO   |     | NULL              |                             | select,insert,update,references |                              |
| UpdatedBy     | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL              |                             | select,insert,update,references |                              |
+---------------+--------------+--------------------+------+-----+-------------------+-----------------------------+---------------------------------+------------------------------+
15 <USER> <GROUP> set (0.00 sec)