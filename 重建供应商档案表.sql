-- 删除现有的供应商档案表
DROP TABLE IF EXISTS pur_supplier;

-- 重新创建供应商档案表，使用小写下划线风格命名字段
CREATE TABLE pur_supplier (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '供应商ID',
    supplier_code VARCHAR(50) NOT NULL UNIQUE COMMENT '供应商编码',
    supplier_name VARCHAR(100) NOT NULL COMMENT '供应商名称',
    full_name VARCHAR(200) COMMENT '供应商全称',
    category_id VARCHAR(36) COMMENT '供应商分类ID',
    industry VARCHAR(100) COMMENT '行业',
    phone VARCHAR(20) COMMENT '电话',
    fax VARCHAR(20) COMMENT '传真',
    contact_person VARCHAR(50) COMMENT '联系人',
    mobile VARCHAR(20) COMMENT '手机',
    address VARCHAR(200) COMMENT '地址',
    region VARCHAR(100) COMMENT '地区',
    last_order_date DATE COMMENT '最近下单日期',
    last_receive_date DATE COMMENT '最近收货日期',
    price_decimal_places INT NOT NULL DEFAULT 2 COMMENT '单价小数位',
    amount_decimal_places INT NOT NULL DEFAULT 2 COMMENT '金额小数位',
    unit_weight_decimal_places INT NOT NULL DEFAULT 3 COMMENT '单重小数位',
    total_weight_decimal_places INT NOT NULL DEFAULT 3 COMMENT '总重小数位',
    days_without_order INT COMMENT '未下单天数',
    days_without_receive INT COMMENT '未收货天数',
    tax_number VARCHAR(50) COMMENT '税号',
    bank_name VARCHAR(100) COMMENT '开户行',
    bank_account VARCHAR(50) COMMENT '银行账号',
    payment_terms VARCHAR(50) COMMENT '付款条件',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态(active-启用,inactive-停用)',
    remark TEXT COMMENT '备注',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 0 COMMENT '版本号（乐观锁）',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（逻辑删除）',
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_supplier_name (supplier_name),
    INDEX idx_category_id (category_id),
    INDEX idx_last_order_date (last_order_date),
    INDEX idx_last_receive_date (last_receive_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商档案表';
