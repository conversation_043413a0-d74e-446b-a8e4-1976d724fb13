package com.czerp.erpbackend.system.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建计量单位请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateMeasurementUnitRequest {

    /**
     * 单位名称
     */
    @NotBlank(message = "单位名称不能为空")
    @Size(max = 50, message = "单位名称长度不能超过50个字符")
    private String unitName;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    private Integer sortOrder;

    /**
     * 新建物料时默认此单位
     */
    private Boolean isDefaultForNewMaterial;

    /**
     * 纸箱/纸板尺寸单位
     */
    private Boolean isDimensionUnit;

    /**
     * 默认纸箱尺寸单位
     */
    private Boolean isDefaultDimensionUnit;

    /**
     * 默认纸度单位
     */
    private Boolean isDefaultThicknessUnit;

    /**
     * 默认纸长单位
     */
    private Boolean isDefaultLengthUnit;

    /**
     * 状态(active-启用,inactive-停用)
     */
    private String status = "active";
}
