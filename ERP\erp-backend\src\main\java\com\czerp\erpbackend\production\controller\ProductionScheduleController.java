package com.czerp.erpbackend.production.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.production.dto.*;
import com.czerp.erpbackend.production.service.ProductionScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产排程单控制器
 */
@RestController
@RequestMapping("/production-schedules")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "生产排程单管理", description = "生产排程单相关接口")
public class ProductionScheduleController {

    private final ProductionScheduleService productionScheduleService;

    /**
     * 分页查询生产排程单
     * @param request 查询请求
     * @return 生产排程单分页列表
     */
    @GetMapping
    @Operation(summary = "分页查询生产排程单", description = "分页查询生产排程单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('production:schedule:read')")
    public ResponseEntity<ApiResponse<PageResponse<ProductionScheduleDTO>>> findProductionSchedules(ProductionScheduleQueryRequest request) {
        log.info("Finding production schedules with request: {}", request);
        PageResponse<ProductionScheduleDTO> schedules = productionScheduleService.findProductionSchedules(request);
        return ResponseEntity.ok(ApiResponse.success(schedules));
    }

    /**
     * 分页查询生产排程单明细（按明细行级别分页）
     * @param request 查询请求
     * @return 生产排程单明细分页列表
     */
    @GetMapping("/items")
    @Operation(summary = "分页查询生产排程单明细", description = "按明细行级别分页查询生产排程单明细")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('production:schedule:read')")
    public ResponseEntity<ApiResponse<PageResponse<ProductionScheduleItemDTO>>> findProductionScheduleItems(ProductionScheduleQueryRequest request) {
        log.info("Finding production schedule items with request: {}", request);
        PageResponse<ProductionScheduleItemDTO> items = productionScheduleService.findProductionScheduleItems(request);
        return ResponseEntity.ok(ApiResponse.success(items));
    }

    /**
     * 根据ID查询生产排程单
     * @param id 排程单ID
     * @return 生产排程单
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询生产排程单", description = "根据ID查询生产排程单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('production:schedule:read')")
    public ResponseEntity<ApiResponse<ProductionScheduleDTO>> findProductionScheduleById(@PathVariable String id) {
        log.info("Finding production schedule by id: {}", id);
        ProductionScheduleDTO schedule = productionScheduleService.findProductionScheduleById(id);
        return ResponseEntity.ok(ApiResponse.success(schedule));
    }

    /**
     * 创建生产排程单
     * @param request 创建请求
     * @return 生产排程单
     */
    @PostMapping
    @Operation(summary = "创建生产排程单", description = "创建生产排程单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('production:schedule:create')")
    public ResponseEntity<ApiResponse<ProductionScheduleDTO>> createProductionSchedule(@Valid @RequestBody CreateProductionScheduleRequest request) {
        log.info("Creating production schedule with request: {}", request);
        ProductionScheduleDTO schedule = productionScheduleService.createProductionSchedule(request);
        return ResponseEntity.ok(ApiResponse.success(schedule));
    }

    /**
     * 更新生产排程单
     * @param id 排程单ID
     * @param request 更新请求
     * @return 生产排程单
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新生产排程单", description = "更新生产排程单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('production:schedule:update')")
    public ResponseEntity<ApiResponse<ProductionScheduleDTO>> updateProductionSchedule(
            @PathVariable String id,
            @Valid @RequestBody UpdateProductionScheduleRequest request) {
        log.info("Updating production schedule with id: {} and request: {}", id, request);
        ProductionScheduleDTO schedule = productionScheduleService.updateProductionSchedule(id, request);
        return ResponseEntity.ok(ApiResponse.success(schedule));
    }

    /**
     * 删除生产排程单
     * @param id 排程单ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除生产排程单", description = "删除生产排程单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('production:schedule:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteProductionSchedule(@PathVariable String id) {
        log.info("Deleting production schedule with id: {}", id);
        productionScheduleService.deleteProductionSchedule(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 生成排程单号
     * @return 排程单号
     */
    @GetMapping("/generate-schedule-no")
    @Operation(summary = "生成排程单号", description = "生成排程单号")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('production:schedule:create')")
    public ResponseEntity<ApiResponse<String>> generateScheduleNo() {
        log.info("Generating schedule no");
        String scheduleNo = productionScheduleService.generateScheduleNo();
        return ResponseEntity.ok(ApiResponse.success(scheduleNo));
    }

    /**
     * 批量更新打印状态
     * @param itemIds 明细ID列表
     * @param isPrinted 是否已打印
     * @return 更新结果
     */
    @PutMapping("/items/print-status")
    @Operation(summary = "批量更新打印状态", description = "批量更新生产排程单明细的打印状态")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('production:schedule:update')")
    public ResponseEntity<ApiResponse<Void>> updatePrintStatus(
            @RequestParam List<String> itemIds,
            @RequestParam Boolean isPrinted) {
        log.info("Updating print status for items: {}, isPrinted: {}", itemIds, isPrinted);
        productionScheduleService.updatePrintStatus(itemIds, isPrinted);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 查询可引用的销售订单明细列表（用于生产排程单引用销售订单）
     * @param request 查询请求
     * @return 销售订单明细分页列表（包含完整的引用数据）
     */
    @GetMapping("/sales-order-items-for-reference")
    @Operation(summary = "查询可引用的销售订单明细", description = "查询可引用的销售订单明细列表，用于生产排程单引用销售订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('production:schedule:read')")
    public ResponseEntity<ApiResponse<PageResponse<ProductionScheduleItemDTO>>> findSalesOrderItemsForReference(ProductionScheduleQueryRequest request) {
        log.info("Finding sales order items for reference with request: {}", request);
        PageResponse<ProductionScheduleItemDTO> items = productionScheduleService.findSalesOrderItemsForReference(request);
        return ResponseEntity.ok(ApiResponse.success(items));
    }
}
