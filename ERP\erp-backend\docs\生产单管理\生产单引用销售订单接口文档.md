# 生产单引用销售订单接口文档

## 接口概述

本接口用于生产排程单引用销售订单数据，提供完整的销售订单明细信息，包括采购、库存、工序等相关数据，满足生产排程的业务需求。

## API接口

### 查询可引用的销售订单明细

**接口地址：** `GET /api/production-schedules/sales-order-items-for-reference`

**接口描述：** 查询可引用的销售订单明细列表，返回包含完整引用数据的分页结果

**权限要求：** `production:schedule:read`

#### 请求参数

使用 `ProductionScheduleQueryRequest` 作为查询参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页大小，默认20 |
| keyword | String | 否 | 关键字搜索（支持生产单号、客户名称、产品名称、销售订单号、客户订单号） |
| customerName | String | 否 | 客户名称筛选 |
| productionOrderNo | String | 否 | 生产单号筛选 |
| salesOrderNo | String | 否 | 销售订单号筛选 |
| productName | String | 否 | 产品名称筛选 |
| scheduleDateStart | LocalDate | 否 | 订单日期开始 |
| scheduleDateEnd | LocalDate | 否 | 订单日期结束 |

#### 响应数据

返回 `PageResponse<ProductionScheduleItemDTO>` 格式的分页数据，其中 `ProductionScheduleItemDTO` 包含以下字段：

##### 基础字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| salesOrderItemId | String | 销售订单明细ID |
| salesOrderNo | String | 销售订单号 |
| productionOrderNo | String | 生产单号 |
| customerName | String | 客户名称 |
| customerCode | String | 客户编码 |
| productName | String | 产品名称 |
| processRequirements | String | 工艺要求（直接从sales_order_item表获取） |

##### 引用销售订单专用字段
| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| currentScheduleQuantity | Integer | 本次排程数 | 动态计算：订单数 - 已排程数，最小值为0 |
| scheduledQuantity | Integer | 已排程数 | 聚合计算：查询production_schedule_item表中该销售订单明细ID的所有schedule_quantity字段之和 |
| customerOrderNo | String | 客户订单号 | sales_order_item表 |
| customerProductNo | String | 客方货号 | sales_order_item表customerProductCode字段 |
| productName2 | String | 品名 | sales_order_item表 |
| boxType | String | 盒式 | sales_order_item表 |
| paperType | String | 纸质 | sales_order_item表 |
| productionPaperType | String | 生产纸质 | sales_order_item表 |
| specification | String | 规格 | 动态字段：长x宽x高 单位 |
| orderDate | LocalDate | 订单日期 | sales_order表 |
| deliveryDate | LocalDate | 交期 | sales_order_item表 |
| orderType | String | 订单类型 | 默认"销售订单" |
| isTaxed | Boolean | 是否含税 | sales_order_item表 |
| paperBoardType | String | 纸板类型 | 纸质对应的纸质类别 |

##### 尺寸字段
| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| length | BigDecimal | 长 | sales_order_item表 |
| width | BigDecimal | 宽 | sales_order_item表 |
| height | BigDecimal | 高 | sales_order_item表 |
| productionLength | BigDecimal | 生产长 | sales_order_item表 |
| productionWidth | BigDecimal | 生产宽 | sales_order_item表 |
| productionHeight | BigDecimal | 生产高 | sales_order_item表 |

##### 数量字段
| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| orderQuantity | Integer | 订单数 | sales_order_item表 |
| spareQuantity | Integer | 备品数 | sales_order_item表 |
| purchasedQuantity | Integer | 已采购数 | purchase_order_item表quantity字段 |
| arrivedQuantity | Integer | 已到料数 | stock_inbound_item表quantity字段聚合 |
| undeliveredQuantity | Integer | 未送货数 | 暂时默认0 |
| finishedStockQuantity | Integer | 成品库存数 | 暂时默认0 |

##### 材料字段
| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| paperWidth | String | 纸度 | sales_order_material表 |
| paperLength | BigDecimal | 纸长 | sales_order_material表 |
| actualMaterialWidth | BigDecimal | 实际用料宽 | sales_order_material表 |
| actualMaterialLength | BigDecimal | 实际用料长 | sales_order_material表 |
| actualMaterialUnit | String | 实际用料单位 | sales_order_material表unit字段 |
| materialSpecification | String | 原料规格 | 纸宽 x 纸长 单位格式（使用unit字段） |

##### 其他字段
| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| materialArrivalDate | LocalDate | 到料日期 | stock_inbound表inbound_date字段 |
| processSequence | String | 工序 | 格式化后的工序序列，如"开槽→打角→打钉" |
| productionRemark | String | 生产备注 | sales_order_item表production_remark字段 |

#### 请求示例

```bash
GET /api/production-schedules/sales-order-items-for-reference?page=1&size=20&keyword=客户A&scheduleDateStart=2024-01-01&scheduleDateEnd=2024-12-31
```

#### 响应示例

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "content": [
      {
        "salesOrderItemId": "item-001",
        "salesOrderNo": "SO20240001",
        "productionOrderNo": "000001",
        "customerName": "客户A",
        "customerCode": "CUST001",
        "productName": "产品A",
        "processRequirements": "开槽→打角→打钉",
        "currentScheduleQuantity": 500,
        "scheduledQuantity": 500,
        "customerOrderNo": "PO20240001",
        "customerProductNo": "PROD001",
        "boxType": "0201",
        "paperType": "3K",
        "productionPaperType": "3K",
        "specification": "100x200x300 mm",
        "orderDate": "2024-01-15",
        "deliveryDate": "2024-02-15",
        "orderType": "销售订单",
        "isTaxed": true,
        "paperBoardType": "瓦楞纸",
        "length": 100.00,
        "width": 200.00,
        "height": 300.00,
        "productionLength": 105.00,
        "productionWidth": 205.00,
        "productionHeight": 305.00,
        "orderQuantity": 1000,
        "spareQuantity": 50,
        "purchasedQuantity": 800,
        "arrivedQuantity": 600,
        "undeliveredQuantity": 0,
        "finishedStockQuantity": 0,
        "paperWidth": "51",
        "paperLength": 26.00,
        "actualMaterialWidth": 52.00,
        "actualMaterialLength": 27.00,
        "actualMaterialUnit": "inch",
        "materialSpecification": "51 x 26 inch",
        "materialArrivalDate": "2024-01-20",
        "processSequence": "开槽→打角→打钉",
        "productionRemark": "注意质量要求"
      }
    ],
    "page": 1,
    "size": 20,
    "totalElements": 1,
    "totalPages": 1,
    "first": true,
    "last": true,
    "empty": false
  }
}
```

## 数据处理逻辑

### 1. 工序字段处理
- 直接从 `sales_order_item.process_requirements` 字段获取，不进行特殊格式化
- 同时提供 `processSequence` 字段，格式为"开槽→打角→打钉"的工序序列

### 2. 规格字段处理
- `specification`: 动态组合长x宽x高 单位
- `materialSpecification`: 动态组合纸宽 x 纸长 单位

### 3. 数量计算逻辑
- `scheduledQuantity`: 通过 `ProductionScheduleItemRepository.findBySalesOrderItemIdIn()` 查询该销售订单明细ID在 `production_schedule_item` 表中的所有记录，然后对 `schedule_quantity` 字段进行求和聚合
- `currentScheduleQuantity`: 动态计算 = `Math.max(0, 订单数 - 已排程数)`，确保不会出现负数
- `arrivedQuantity`: 通过采购订单明细关联入库明细，聚合已到料数量

### 4. 采购相关数据
- 通过 `purchase_order_item.source_sales_order_item_id` 关联获取采购数据
- 通过 `stock_inbound_item` 关联获取入库数据

## 使用场景

1. **生产排程创建**: 前端可以通过此接口查询可用的销售订单明细，选择后创建生产排程
2. **数据展示**: 提供完整的销售订单相关信息，支持生产计划决策
3. **库存管理**: 显示采购和到料情况，帮助安排生产时间

## 注意事项

1. 接口返回的是销售订单明细数据，不是生产排程单明细数据
2. `currentScheduleQuantity` 字段默认为0，需要前端根据业务需求设置
3. 暂时忽略的字段（已送货数、成品入库）设置为默认值0
4. 工序字段提供两种格式：原始的 `processRequirements` 和格式化的 `processSequence`
