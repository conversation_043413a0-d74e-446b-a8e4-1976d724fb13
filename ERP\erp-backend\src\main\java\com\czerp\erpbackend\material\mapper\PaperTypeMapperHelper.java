package com.czerp.erpbackend.material.mapper;

import com.czerp.erpbackend.material.dto.PaperTypeDTO;
import com.czerp.erpbackend.material.entity.PaperType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 纸质类别Mapper辅助类
 * 用于解决MapStruct生成的映射器缺少addMarginMm字段映射的问题
 */
@Component
public class PaperTypeMapperHelper {

    /**
     * 手动实现实体转DTO，确保所有字段都被正确映射
     * @param entity 实体
     * @return DTO
     */
    public PaperTypeDTO manualToDto(PaperType entity) {
        if (entity == null) {
            return null;
        }

        return PaperTypeDTO.builder()
                .id(entity.getId())
                .paperTypeName(entity.getPaperTypeName())
                .staplingFlapInch(entity.getStaplingFlapInch())
                .staplingFlapCm(entity.getStaplingFlapCm())
                .gluingFlapInch(entity.getGluingFlapInch())
                .gluingFlapCm(entity.getGluingFlapCm())
                .addMarginMm(entity.getAddMarginMm())  // 确保这个字段被映射
                .reduceMarginMm(entity.getReduceMarginMm())
                .thicknessMm(entity.getThicknessMm())
                .layerCount(entity.getLayerCount())
                .sheetsPerBoard(entity.getSheetsPerBoard())
                .build();
    }

    /**
     * 手动实现实体列表转DTO列表
     * @param entities 实体列表
     * @return DTO列表
     */
    public List<PaperTypeDTO> manualToDtoList(List<PaperType> entities) {
        if (entities == null) {
            return null;
        }

        List<PaperTypeDTO> dtoList = new ArrayList<>(entities.size());
        for (PaperType entity : entities) {
            dtoList.add(manualToDto(entity));
        }
        return dtoList;
    }
}
