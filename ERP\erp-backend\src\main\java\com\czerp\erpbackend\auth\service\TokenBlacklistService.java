package com.czerp.erpbackend.auth.service;

/**
 * 令牌黑名单服务接口
 */
public interface TokenBlacklistService {
    
    /**
     * 将令牌添加到黑名单
     * @param token 令牌
     * @param expirationTime 过期时间（毫秒）
     */
    void addToBlacklist(String token, long expirationTime);
    
    /**
     * 检查令牌是否在黑名单中
     * @param token 令牌
     * @return 是否在黑名单中
     */
    boolean isBlacklisted(String token);
    
    /**
     * 从黑名单中移除令牌
     * @param token 令牌
     */
    void removeFromBlacklist(String token);
    
    /**
     * 清理过期的令牌
     */
    void cleanupExpiredTokens();
}
