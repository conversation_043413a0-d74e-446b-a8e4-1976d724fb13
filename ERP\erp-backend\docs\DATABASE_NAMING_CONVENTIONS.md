# 数据库命名规范和审计字段使用规则

## 数据库命名规范

为了保持一致性和可维护性，我们采用以下数据库命名规范：

### 表命名

- 表名使用小写字母，单词之间使用下划线分隔（snake_case）
- 表名应该使用有意义的前缀，表示表所属的模块，例如：
  - `prd_` 前缀表示产品模块
  - `cus_` 前缀表示客户模块
  - `pur_` 前缀表示采购模块
  - `sys_` 前缀表示系统模块

### 字段命名

- 字段名使用小写字母，单词之间使用下划线分隔（snake_case）
- 字段名应该清晰表达其含义，避免使用缩写（除非是广泛接受的缩写）
- 主键字段统一命名为 `id`
- 外键字段命名为 `表名_id` 或 `实体名_id`，例如 `category_id`、`customer_id`

### 审计字段

所有实体类都应该继承 `BaseEntity` 类，该类包含以下审计字段：

- `created_by`：创建人，字符串类型，非空
- `created_time`：创建时间，日期时间类型，非空
- `updated_by`：更新人，字符串类型，可空
- `updated_time`：更新时间，日期时间类型，可空
- `version`：版本号（乐观锁），整数类型，可空
- `is_deleted`：是否删除（逻辑删除），布尔类型，非空，默认为 false

## 审计字段使用规则

### 在实体类中使用审计字段

所有实体类都应该继承 `BaseEntity` 类，以自动获取审计字段：

```java
@Entity
@Table(name = "prd_product")
public class Product extends BaseEntity {
    // 实体类字段
}
```

### 在 DTO 中使用审计字段

DTO 类可以根据需要包含部分审计字段，通常包括创建时间和更新时间：

```java
@Data
public class ProductDTO {
    // DTO 字段
    
    // 创建时间，命名为 createTime 以符合前端命名规范
    private LocalDateTime createTime;
    
    // 更新时间，命名为 updateTime 以符合前端命名规范
    private LocalDateTime updateTime;
}
```

### 在 Mapper 中映射审计字段

使用 MapStruct 映射实体类和 DTO 之间的审计字段：

```java
@Mapper(componentModel = "spring")
public interface ProductMapper {
    
    @Mapping(source = "createdTime", target = "createTime")
    @Mapping(source = "updatedTime", target = "updateTime")
    ProductDTO toDto(Product product);
    
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", constant = "false")
    Product toEntity(CreateProductRequest request);
}
```

### 在导入功能中手动设置审计字段

在导入功能中，需要手动设置审计字段，确保使用小写下划线命名的字段：

```java
// 获取当前用户名
String currentUsername = SecurityUtils.getCurrentUsername();

// 创建实体并手动设置审计字段
Product product = new Product();
product.setId(UUID.randomUUID().toString());
product.setCreatedBy(currentUsername);
product.setUpdatedBy(currentUsername);
product.setCreatedTime(LocalDateTime.now());
product.setUpdatedTime(LocalDateTime.now());
product.setVersion(0);
product.setIsDeleted(false);

// 保存实体
productRepository.save(product);
```

## 注意事项

1. 不要在 `@Column` 注解中使用大写字母或驼峰命名，应该统一使用小写下划线命名
2. 不要在数据库中创建重复的审计字段，例如同时存在 `created_by` 和 `CreatedBy`
3. 如果需要修改字段命名规范，应该同时修改实体类和数据库表结构，确保一致性
4. 在导入功能中，应该手动设置审计字段，确保数据完整性

## 历史问题修复

我们曾经遇到过数据库中同时存在大写驼峰命名（CreatedBy, UpdatedBy, IsDeleted）和小写下划线命名（created_by, updated_by, is_deleted）的字段，导致导入功能失败。

为了解决这个问题，我们：

1. 修改了 `BaseEntity` 类中的 `@Column` 注解，统一使用小写下划线命名
2. 创建了数据库迁移脚本，删除重复的大写字段，并确保数据迁移到小写字段
3. 修改了导入功能，确保正确设置审计字段

请确保遵循本文档中的命名规范和审计字段使用规则，以避免类似问题再次发生。
