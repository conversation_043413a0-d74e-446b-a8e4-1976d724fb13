-- 安全的字符集修复方案 - 第一阶段
-- 优先修复核心问题表，降低风险

-- 设置安全模式
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';

-- ========================================
-- 第一阶段：修复核心问题表（最小风险）
-- ========================================

-- 1. 检查磁盘空间
SELECT 
    table_schema as '数据库',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as '当前大小(MB)',
    ROUND(SUM(data_length + index_length) / 1024 / 1024 * 2, 2) as '预计需要空间(MB)'
FROM information_schema.tables 
WHERE table_schema = 'czerp_web'
GROUP BY table_schema;

-- 2. 检查表大小（按大小排序，先处理小表）
SELECT 
    table_name as '表名',
    ROUND((data_length + index_length) / 1024 / 1024, 2) as '大小(MB)',
    table_rows as '行数',
    table_collation as '当前字符集'
FROM information_schema.tables 
WHERE table_schema = 'czerp_web' 
    AND table_collation = 'utf8mb4_unicode_ci'
ORDER BY (data_length + index_length);

-- ========================================
-- 第一阶段执行：小表优先
-- ========================================

-- 修复最小的系统表
ALTER TABLE sys_role_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE sys_user_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE sys_permission_backup CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复小的业务表
ALTER TABLE example_entity CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE paper_size_setting CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- ========================================
-- 验证第一阶段结果
-- ========================================
SELECT 
    '第一阶段修复验证' as '检查项目',
    table_name as '表名',
    table_collation as '当前字符集',
    CASE 
        WHEN table_collation = 'utf8mb4_0900_ai_ci' THEN '✓ 修复成功'
        ELSE '✗ 需要重试'
    END as '状态'
FROM information_schema.tables 
WHERE table_schema = 'czerp_web' 
    AND table_name IN ('sys_role_permission', 'sys_user_role', 'sys_permission_backup', 'example_entity', 'paper_size_setting')
ORDER BY table_name;

-- 恢复安全模式
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 第一阶段执行说明
-- ========================================
/*
第一阶段特点：
1. 只修复最小的表，风险最低
2. 验证修复流程是否正常
3. 为后续阶段积累经验

执行后检查：
1. 确认所有表都显示 "✓ 修复成功"
2. 重启应用程序测试基本功能
3. 如果一切正常，继续执行第二阶段

如果出现问题：
1. 立即停止，不要继续后续阶段
2. 恢复备份
3. 分析问题原因
*/
