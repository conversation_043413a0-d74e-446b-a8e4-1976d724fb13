package com.czerp.erpbackend.system.service.impl;

import com.czerp.erpbackend.common.constant.CommonStatus;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.system.dto.CreateRoleRequest;
import com.czerp.erpbackend.system.dto.RoleDTO;
import com.czerp.erpbackend.system.dto.RoleQueryRequest;
import com.czerp.erpbackend.system.dto.UpdateRoleRequest;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.mapper.RoleMapper;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import com.czerp.erpbackend.system.repository.UserRoleRepository;
import com.czerp.erpbackend.system.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RoleServiceImpl implements RoleService {

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final RolePermissionRepository rolePermissionRepository;
    private final UserRoleRepository userRoleRepository;
    private final RoleMapper roleMapper;

    /**
     * 分页查询角色列表
     * @param request 查询请求
     * @return 角色分页列表
     */
    @Override
    public PageResponse<RoleDTO> findRoles(RoleQueryRequest request) {
        int page = request.getPage() > 0 ? request.getPage() - 1 : 0;
        Pageable pageable = PageRequest.of(page, request.getSize());

        Page<Role> rolePage;
        if (StringUtils.hasText(request.getKeyword())) {
            rolePage = roleRepository.findByNameContainingOrCodeContainingOrDescriptionContaining(
                    request.getKeyword(), request.getKeyword(), request.getKeyword(), pageable);
        } else {
            rolePage = roleRepository.findAll(pageable);
        }

        List<RoleDTO> roleDTOs = rolePage.getContent().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());

        return new PageResponse<>(
                roleDTOs,
                rolePage.getTotalElements(),
                rolePage.getTotalPages(),
                rolePage.getNumber() + 1,
                rolePage.getSize()
        );
    }

    /**
     * 查询所有角色
     * @return 角色列表
     */
    @Override
    public List<RoleDTO> findAllRoles() {
        List<Role> roles = roleRepository.findAll();
        return roles.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID查询角色
     * @param id 角色ID
     * @return 角色信息
     */
    @Override
    public RoleDTO findRoleById(String id) {
        Role role = getRoleById(id);
        return convertToDto(role);
    }

    /**
     * 创建角色
     * @param request 创建角色请求
     * @return 角色信息
     */
    @Override
    @Transactional
    public RoleDTO createRole(CreateRoleRequest request) {
        // 检查角色编码是否已存在
        if (roleRepository.existsByCode(request.getCode())) {
            throw new BusinessException("角色编码已存在");
        }

        // 创建角色
        Role role = new Role();
        role.setId(UUID.randomUUID().toString());
        role.setName(request.getName());
        role.setCode(request.getCode());
        role.setDescription(request.getDescription());
        role.setStatus(CommonStatus.ENABLED.getValue());

        // 保存角色
        role = roleRepository.save(role);

        // 处理权限
        if (request.getPermissions() != null && !request.getPermissions().isEmpty()) {
            createRolePermissions(role, request.getPermissions());
        }

        return convertToDto(role);
    }

    /**
     * 更新角色
     * @param id 角色ID
     * @param request 更新角色请求
     * @return 角色信息
     */
    @Override
    @Transactional
    public RoleDTO updateRole(String id, UpdateRoleRequest request) {
        Role role = getRoleById(id);

        // 检查角色编码是否已被其他角色使用
        if (StringUtils.hasText(request.getCode()) && !request.getCode().equals(role.getCode())) {
            if (roleRepository.existsByCode(request.getCode())) {
                throw new BusinessException("角色编码已存在");
            }
            role.setCode(request.getCode());
        }

        // 更新角色基本信息
        if (StringUtils.hasText(request.getName())) {
            role.setName(request.getName());
        }
        if (StringUtils.hasText(request.getDescription())) {
            role.setDescription(request.getDescription());
        }

        // 保存角色
        role = roleRepository.save(role);

        // 处理权限
        if (request.getPermissions() != null) {
            // 删除原有权限
            rolePermissionRepository.deleteByRoleId(id);

            // 添加新权限
            if (!request.getPermissions().isEmpty()) {
                createRolePermissions(role, request.getPermissions());
            }
        }

        return convertToDto(role);
    }

    /**
     * 删除角色
     * @param id 角色ID
     */
    @Override
    @Transactional
    public void deleteRole(String id) {
        Role role = getRoleById(id);

        // 检查角色是否被用户使用
        long userCount = userRoleRepository.countByRoleId(id);
        if (userCount > 0) {
            throw new BusinessException("角色已被用户使用，无法删除");
        }

        // 删除角色权限关联
        rolePermissionRepository.deleteByRoleId(id);

        // 删除角色
        roleRepository.delete(role);
    }

    /**
     * 更改角色状态
     * @param id 角色ID
     * @param status 状态
     * @return 角色信息
     */
    @Override
    public RoleDTO changeStatus(String id, String status) {
        Role role = getRoleById(id);

        // 检查状态是否有效
        try {
            CommonStatus.fromValue(status);
        } catch (IllegalArgumentException e) {
            throw new BusinessException("无效的状态");
        }

        role.setStatus(status);
        role = roleRepository.save(role);

        return convertToDto(role);
    }

    /**
     * 根据用户ID查询角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<RoleDTO> findRolesByUserId(String userId) {
        List<String> roleIds = userRoleRepository.findRoleIdsByUserId(userId);
        if (roleIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<Role> roles = roleRepository.findAllById(roleIds);
        return roles.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 创建角色权限关联
     * @param role 角色
     * @param permissionIds 权限ID列表
     */
    private void createRolePermissions(Role role, List<String> permissionIds) {
        List<RolePermission> rolePermissions = new ArrayList<>();

        for (String permissionId : permissionIds) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setId(UUID.randomUUID().toString());
            rolePermission.setRoleId(role.getId());
            rolePermission.setPermissionId(permissionId);
            rolePermissions.add(rolePermission);
        }

        rolePermissionRepository.saveAll(rolePermissions);
    }

    /**
     * 根据ID获取角色
     * @param id 角色ID
     * @return 角色
     */
    private Role getRoleById(String id) {
        return roleRepository.findById(id)
                .orElseThrow(() -> new BusinessException("角色不存在"));
    }

    /**
     * 将角色实体转换为DTO
     * @param role 角色实体
     * @return 角色DTO
     */
    private RoleDTO convertToDto(Role role) {
        RoleDTO dto = roleMapper.toDto(role);

        // 获取角色权限
        List<String> permissionIds = rolePermissionRepository.findPermissionIdsByRoleId(role.getId());
        dto.setPermissions(permissionIds);

        return dto;
    }

    /**
     * 分页查询角色
     * @param page 页码
     * @param size 每页大小
     * @return 角色分页列表
     */
    @Override
    public PageResponse<RoleDTO> findRoles(int page, int size) {
        RoleQueryRequest request = new RoleQueryRequest();
        request.setPage(page);
        request.setSize(size);
        return findRoles(request);
    }
}
