package com.czerp.erpbackend.purchase.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 采购订单明细实体
 */
@Entity
@Table(name = "purchase_order_item")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderItem extends BaseEntity {

    /**
     * 采购订单明细ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 采购订单
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "purchase_order_id", nullable = false)
    private PurchaseOrder purchaseOrder;

    /**
     * 纸质
     */
    @Column(name = "paper_quality", length = 100)
    private String paperQuality;

    /**
     * 纸板类别
     */
    @Column(name = "paper_board_category", length = 100)
    private String paperBoardCategory;

    /**
     * 楞别
     */
    @Column(name = "corrugation_type", length = 50)
    private String corrugationType;

    /**
     * 纸度
     */
    @Column(name = "paper_width", precision = 10, scale = 2)
    private BigDecimal paperWidth;

    /**
     * 纸长
     */
    @Column(name = "paper_length", precision = 10, scale = 2)
    private BigDecimal paperLength;

    /**
     * 合订
     */
    @Column(name = "binding_method")
    private Boolean bindingMethod;

    /**
     * 合订规格
     */
    @Column(name = "binding_specification", length = 100)
    private String bindingSpecification;

    /**
     * 用料异动
     */
    @Column(name = "material_change")
    private Boolean materialChange;

    /**
     * 报价特价
     */
    @Column(name = "special_quotation", length = 100)
    private String specialQuotation;

    /**
     * 纸质报价
     */
    @Column(name = "paper_quotation", precision = 10, scale = 2)
    private BigDecimal paperQuotation;

    /**
     * 折扣
     */
    @Column(name = "discount", precision = 10, scale = 2)
    private BigDecimal discount;

    /**
     * 数量
     */
    @Column(name = "quantity")
    private Integer quantity;

    /**
     * 价格
     */
    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price;

    /**
     * 金额
     */
    @Column(name = "amount", precision = 10, scale = 2)
    private BigDecimal amount;

    /**
     * 压线尺寸(纸度)
     */
    @Column(name = "creasing_size", length = 100)
    private String creasingSize;

    /**
     * 压线方式
     */
    @Column(name = "creasing_method", length = 100)
    private String creasingMethod;

    /**
     * 备注
     */
    @Column(name = "remarks", length = 500)
    private String remarks;

    /**
     * 折度规格
     */
    @Column(name = "folding_specification", length = 100)
    private String foldingSpecification;

    /**
     * 长度(米)
     */
    @Column(name = "length_meters", precision = 10, scale = 2)
    private BigDecimal lengthMeters;

    /**
     * 面积(平米)
     */
    @Column(name = "area_square_meters", precision = 10, scale = 2)
    private BigDecimal areaSquareMeters;

    /**
     * 体积(立方米)
     */
    @Column(name = "volume_cubic_meters", precision = 10, scale = 2)
    private BigDecimal volumeCubicMeters;

    /**
     * 单重
     */
    @Column(name = "unit_weight", precision = 10, scale = 2)
    private BigDecimal unitWeight;

    /**
     * 总重(KG)
     */
    @Column(name = "total_weight_kg", precision = 10, scale = 2)
    private BigDecimal totalWeightKg;

    /**
     * 加工费
     */
    @Column(name = "processing_fee", precision = 10, scale = 2)
    private BigDecimal processingFee;

    /**
     * 币种
     */
    @Column(name = "currency", length = 20)
    private String currency;

    /**
     * 交期
     */
    @Column(name = "delivery_date")
    private LocalDate deliveryDate;

    /**
     * 来源销售订单明细ID
     */
    @Column(name = "source_sales_order_item_id", length = 36)
    private String sourceSalesOrderItemId;

    /**
     * 已入库数（存储字段，将逐步废弃）
     * @deprecated 使用 {@link #getReceivedQuantityDynamic()} 进行动态计算
     */
    @Deprecated
    @Column(name = "received_quantity")
    private Integer receivedQuantity;

    /**
     * 动态计算已入库数量
     * 通过聚合入库单明细表来获取实际的已入库数量
     * @return 已入库数量，如果没有入库记录则返回0
     */
    @Transient
    public Integer getReceivedQuantityDynamic() {
        // 注意：这个方法需要在Service层调用Repository来实现
        // 在实体类中无法直接注入Repository
        // 实际的动态计算逻辑将在Service层实现
        return this.receivedQuantity != null ? this.receivedQuantity : 0;
    }

    /**
     * 计算未入库数量
     * @param receivedQty 已入库数量
     * @param returnedQty 已退货数量（暂时为0）
     * @return 未入库数量
     */
    @Transient
    public Integer calculateUnreceivedQuantity(Integer receivedQty, Integer returnedQty) {
        if (this.quantity == null) {
            return 0;
        }
        int received = receivedQty != null ? receivedQty : 0;
        int returned = returnedQty != null ? returnedQty : 0;
        return Math.max(0, this.quantity - received + returned);
    }

    /**
     * 检查是否可用于入库
     * @param receivedQty 已入库数量
     * @return true表示可用于入库，false表示已完全入库
     */
    @Transient
    public boolean isAvailableForInbound(Integer receivedQty) {
        if (this.quantity == null) {
            return false;
        }
        return receivedQty == null || receivedQty < this.quantity;
    }
}
