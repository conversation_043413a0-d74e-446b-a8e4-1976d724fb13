package com.czerp.erpbackend.purchase.mapper;

import com.czerp.erpbackend.purchase.dto.CreateSupplierRequest;
import com.czerp.erpbackend.purchase.dto.SupplierDTO;
import com.czerp.erpbackend.purchase.dto.UpdateSupplierRequest;
import com.czerp.erpbackend.purchase.entity.Supplier;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 供应商Mapper
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SupplierMapper {
    
    /**
     * 实体转DTO
     * @param supplier 实体
     * @return DTO
     */
    @Mapping(source = "category.categoryName", target = "categoryName")
    @Mapping(source = "createdTime", target = "createTime")
    @Mapping(source = "updatedTime", target = "updateTime")
    SupplierDTO toDto(Supplier supplier);
    
    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "lastOrderDate", ignore = true)
    @Mapping(target = "lastReceiveDate", ignore = true)
    @Mapping(target = "daysWithoutOrder", ignore = true)
    @Mapping(target = "daysWithoutReceive", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    Supplier toEntity(CreateSupplierRequest request);
    
    /**
     * 更新实体
     * @param request 更新请求
     * @param supplier 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "supplierCode", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "lastOrderDate", ignore = true)
    @Mapping(target = "lastReceiveDate", ignore = true)
    @Mapping(target = "daysWithoutOrder", ignore = true)
    @Mapping(target = "daysWithoutReceive", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateSupplierRequest request, @MappingTarget Supplier supplier);
}
