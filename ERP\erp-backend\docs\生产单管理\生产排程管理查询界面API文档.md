# 生产排程管理查询界面API文档

## 接口概述

**接口路径：** `GET /api/production-schedules/items`

**接口描述：** 查询生产排程明细列表，支持分页、排序和多条件筛选

**权限要求：** 需要生产管理相关权限

## 请求参数

### Query Parameters

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| page | Integer | 否 | 页码，从0开始 | 0 |
| size | Integer | 否 | 每页大小，默认20 | 20 |
| sort | String | 否 | 排序字段和方向 | createdTime,desc |
| keyword | String | 否 | 关键字搜索（排程单号、生产单号、客户名称、产品名称） | 客户A |
| scheduleDateStart | String | 否 | 排程日期开始（yyyy-MM-dd） | 2024-01-01 |
| scheduleDateEnd | String | 否 | 排程日期结束（yyyy-MM-dd） | 2024-12-31 |
| deliveryDateStart | String | 否 | 交期开始（yyyy-MM-dd） | 2024-01-01 |
| deliveryDateEnd | String | 否 | 交期结束（yyyy-MM-dd） | 2024-12-31 |
| isUrgent | Boolean | 否 | 是否急单 | true |
| isPrinted | Boolean | 否 | 是否已打印 | false |

## 响应结果

### 响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        // 生产排程明细对象
      }
    ],
    "pageable": {
      "sort": {
        "sorted": true,
        "unsorted": false
      },
      "pageNumber": 0,
      "pageSize": 20
    },
    "totalElements": 100,
    "totalPages": 5,
    "last": false,
    "first": true,
    "numberOfElements": 20
  }
}
```

## 响应字段详细说明

### ProductionScheduleItemDTO 字段列表

#### 基础信息字段

| 字段名 | 类型 | 描述 | 数据来源 | 示例值 |
|--------|------|------|----------|--------|
| id | String | 排程明细ID | production_schedule_item.id | "abc123" |
| scheduleId | String | 排程单ID | production_schedule_item.schedule_id | "def456" |
| scheduleNo | String | 排程单号 | production_schedule.schedule_no | "PS202401001" |
| lastPrintTime | String | 最近打印时间 | production_schedule.last_print_time | "2024-01-15T10:30:00" |

#### 订单相关字段

| 字段名 | 类型 | 描述 | 数据来源 | 示例值 |
|--------|------|------|----------|--------|
| salesOrderNo | String | 销售订单号 | sales_order.order_no | "SO202401001" |
| salesOrderItemId | String | 销售订单明细ID | production_schedule_item.sales_order_item_id | "item123" |
| productionOrderNo | String | 生产单号 | sales_order_item.production_order_no | "PO202401001" |
| orderDate | String | 订单日期 | sales_order.order_date | "2024-01-01" |
| customerCategory | String | 客户分类 | 固定值 | "订单客户" |
| orderType | String | 订单类型 | 固定值 | "销售订单" |

#### 客户和产品信息

| 字段名 | 类型 | 描述 | 数据来源 | 示例值 |
|--------|------|------|----------|--------|
| customerName | String | 客户名称 | sales_order.customer_name | "客户A" |
| productName | String | 产品名称 | sales_order_item.product_name | "纸箱A型" |
| specification | String | 规格 | 计算字段：length×width×height | "300×200×150" |
| productionSpecification | String | 生产规格 | 计算字段：production_length×production_width×production_height | "305×205×155" |

#### 数量和金额信息

| 字段名 | 类型 | 描述 | 数据来源 | 示例值 |
|--------|------|------|----------|--------|
| salesQuantity | Integer | 销售数量 | sales_order_item.quantity | 1000 |
| scheduleQuantity | Integer | 排程数量 | production_schedule_item.schedule_quantity | 1000 |
| packageCount | Integer | 包装数 | production_schedule_item.package_count | 10 |
| unitPrice | BigDecimal | 单价 | sales_order_item.price | 1.50 |
| totalAmount | BigDecimal | 总金额 | sales_order_item.amount | 1500.00 |
| isSample | Boolean | 样品 | sales_order_item.is_sample | false |

#### 日期信息

| 字段名 | 类型 | 描述 | 数据来源 | 示例值 |
|--------|------|------|----------|--------|
| deliveryDate | String | 交期 | sales_order_item.delivery_date | "2024-01-20" |
| plannedCompletionDate | String | 计划完成日期 | production_schedule_item.planned_completion_date | "2024-01-18" |
| materialArrivalDate | String | 到料日期 | stock_inbound.inbound_date（最新） | "2024-01-10" |

#### 材料信息

| 字段名 | 类型 | 描述 | 数据来源 | 示例值 |
|--------|------|------|----------|--------|
| materialPaperWidth | BigDecimal | 纸度 | sales_order_material.paper_width | 1200.00 |
| materialPaperLength | BigDecimal | 纸长 | sales_order_material.paper_length | 800.00 |
| widthOpen | BigDecimal | 度开 | sales_order_material.width_open | 4.00 |
| lengthOpen | BigDecimal | 长开 | sales_order_material.length_open | 3.00 |

#### 采购和入库信息

| 字段名 | 类型 | 描述 | 数据来源 | 示例值 |
|--------|------|------|----------|--------|
| purchaseQuantity | Integer | 采购数量 | purchase_order_item.quantity（汇总） | 1200 |
| arrivedMaterialQuantity | Integer | 到料数量 | stock_inbound_item.quantity（汇总） | 1000 |

#### 状态和控制字段

| 字段名 | 类型 | 描述 | 数据来源 | 示例值 |
|--------|------|------|----------|--------|
| isUrgent | Boolean | 急单 | production_schedule_item.is_urgent | false |
| urgentSequence | Integer | 急单序号 | production_schedule_item.urgent_sequence | null |
| isPrinted | Boolean | 已打印 | production_schedule_item.is_printed | true |

#### 工艺和备注信息

| 字段名 | 类型 | 描述 | 数据来源 | 示例值 |
|--------|------|------|----------|--------|
| processRequirements | String | 工艺要求 | sales_order_item.process_requirements | "四色印刷+覆膜" |
| remark | String | 备注 | production_schedule_item.remark | "注意质量" |

#### 审计字段

| 字段名 | 类型 | 描述 | 数据来源 | 示例值 |
|--------|------|------|----------|--------|
| createdBy | String | 创建人 | production_schedule_item.created_by | "admin" |
| createdTime | String | 创建时间 | production_schedule_item.created_time | "2024-01-01T09:00:00" |
| updatedBy | String | 更新人 | production_schedule_item.updated_by | "admin" |
| updatedTime | String | 更新时间 | production_schedule_item.updated_time | "2024-01-01T10:00:00" |
| version | Long | 版本号 | production_schedule_item.version | 1 |

## 数据获取逻辑

### 主查询
- 基于 `production_schedule_item` 表进行分页查询
- 支持多字段动态条件筛选
- 支持多字段排序

### 数据丰富化
1. **销售订单数据丰富化**：批量查询 `sales_order_item` 和 `sales_order` 表
2. **材料数据丰富化**：批量查询 `sales_order_material` 表
3. **采购数据丰富化**：批量查询 `purchase_order_item` 表
4. **入库数据丰富化**：批量查询 `stock_inbound_item` 表

### 性能优化
- 使用批量查询避免N+1问题
- 异常隔离：单个模块数据获取失败不影响主流程
- 懒加载关联对象减少内存占用

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 检查登录状态 |
| 403 | 权限不足 | 联系管理员分配权限 |
| 500 | 服务器内部错误 | 联系技术支持 |

## 使用示例

### 请求示例
```http
GET /api/production-schedules/items?page=0&size=20&keyword=客户A&isUrgent=false&sort=createdTime,desc
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": "item123",
        "scheduleId": "schedule456",
        "scheduleNo": "PS202401001",
        "lastPrintTime": "2024-01-15T10:30:00",
        "salesOrderNo": "SO202401001",
        "salesOrderItemId": "orderitem789",
        "productionOrderNo": "PO202401001",
        "orderDate": "2024-01-01",
        "customerCategory": "订单客户",
        "orderType": "销售订单",
        "customerName": "客户A",
        "productName": "纸箱A型",
        "specification": "300×200×150",
        "productionSpecification": "305×205×155",
        "salesQuantity": 1000,
        "scheduleQuantity": 1000,
        "packageCount": 10,
        "unitPrice": 1.50,
        "totalAmount": 1500.00,
        "isSample": false,
        "deliveryDate": "2024-01-20",
        "plannedCompletionDate": "2024-01-18",
        "materialArrivalDate": "2024-01-10",
        "materialPaperWidth": 1200.00,
        "materialPaperLength": 800.00,
        "widthOpen": 4.00,
        "lengthOpen": 3.00,
        "purchaseQuantity": 1200,
        "arrivedMaterialQuantity": 1000,
        "isUrgent": false,
        "urgentSequence": null,
        "isPrinted": true,
        "processRequirements": "四色印刷+覆膜",
        "remark": "注意质量",
        "createdBy": "admin",
        "createdTime": "2024-01-01T09:00:00",
        "updatedBy": "admin",
        "updatedTime": "2024-01-01T10:00:00",
        "version": 1
      }
    ],
    "pageable": {
      "sort": {
        "sorted": true,
        "unsorted": false
      },
      "pageNumber": 0,
      "pageSize": 20
    },
    "totalElements": 1,
    "totalPages": 1,
    "last": true,
    "first": true,
    "numberOfElements": 1
  }
}
```

## 更新日志

### v1.1.0 (2024-01-15)
- ✅ 新增 `lastPrintTime` 字段 - 最近打印时间
- ✅ 新增 `orderDate` 字段 - 订单日期
- ✅ 新增 `customerCategory` 字段 - 客户分类（固定值）
- ✅ 新增 `unitPrice` 字段 - 单价
- ✅ 新增 `totalAmount` 字段 - 总金额
- ✅ 新增 `isSample` 字段 - 样品标识
- ✅ 新增 `orderType` 字段 - 订单类型（固定值）
- ✅ 新增材料相关字段：`materialPaperWidth`、`materialPaperLength`、`widthOpen`、`lengthOpen`
- ✅ 新增采购相关字段：`purchaseQuantity`
- ✅ 新增入库相关字段：`arrivedMaterialQuantity`、`materialArrivalDate`
- ✅ 优化数据获取性能，使用批量查询避免N+1问题
- ✅ 增强异常处理，提高系统稳定性

### v1.0.0 (2024-01-01)
- 初始版本，基础查询功能

## 技术实现说明

### 数据库表关联关系

```
production_schedule_item (主表)
├── production_schedule (1:N) - 获取排程单信息
├── sales_order_item (1:1) - 获取销售订单明细信息
│   ├── sales_order (1:N) - 获取销售订单信息
│   └── sales_order_material (1:N) - 获取材料信息
├── purchase_order_item (1:N) - 通过source_sales_order_item_id关联
└── stock_inbound_item (1:N) - 通过purchase_order_item_id关联
```

### 查询性能优化策略

1. **分页查询优化**
   - 使用Spring Data JPA的Pageable进行分页
   - 支持多字段排序，默认按创建时间倒序

2. **批量数据加载**
   - 主查询获取基础数据后，收集所有关联ID
   - 使用`findByIdIn`等批量查询方法
   - 避免N+1查询问题

3. **异常隔离机制**
   - 每个数据丰富化模块独立异常处理
   - 单个模块失败不影响其他模块和主流程
   - 记录详细的警告日志便于问题诊断

### 字段计算逻辑

1. **规格字段计算**
   ```java
   // 产品规格 = 长×宽×高
   String specification = String.format("%.0f×%.0f×%.0f",
       length, width, height);
   ```

2. **采购数量汇总**
   ```java
   // 按销售订单明细ID汇总所有采购数量
   Map<String, Integer> purchaseQuantityMap = purchaseItems.stream()
       .collect(Collectors.groupingBy(
           PurchaseOrderItem::getSourceSalesOrderItemId,
           Collectors.summingInt(PurchaseOrderItem::getQuantity)
       ));
   ```

3. **到料数量和日期计算**
   ```java
   // 汇总到料数量
   int totalArrivedQuantity = inbounds.stream()
       .mapToInt(item -> item.getQuantity() != null ? item.getQuantity() : 0)
       .sum();

   // 获取最新到料日期
   Optional<LocalDate> latestDate = inbounds.stream()
       .map(item -> item.getStockInbound().getInboundDate())
       .filter(Objects::nonNull)
       .max(LocalDate::compareTo);
   ```

### 注意事项

1. **数据一致性**
   - 所有金额字段使用BigDecimal类型避免精度丢失
   - 日期字段统一使用LocalDate/LocalDateTime
   - 布尔字段处理null值情况

2. **性能考虑**
   - 大数据量查询时建议使用合适的分页大小
   - 复杂查询条件可能影响性能，建议添加数据库索引
   - 材料、采购、入库数据为扩展信息，获取失败不影响核心功能

3. **扩展性**
   - 新增字段时需要同时更新DTO、Mapper、Service层
   - 数据丰富化采用模块化设计，便于后续扩展
   - 支持通过配置控制是否启用某些数据丰富化模块

4. **安全性**
   - 所有查询都包含逻辑删除过滤条件
   - 支持基于用户权限的数据过滤
   - 敏感信息字段可配置脱敏处理
