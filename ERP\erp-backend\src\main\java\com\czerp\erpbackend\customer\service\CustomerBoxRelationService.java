package com.czerp.erpbackend.customer.service;

import com.czerp.erpbackend.customer.dto.CreateCustomerBoxRelationRequest;
import com.czerp.erpbackend.customer.dto.CustomerBoxRelationDTO;

import java.util.List;

/**
 * 客户盒式关联服务接口
 */
public interface CustomerBoxRelationService {

    /**
     * 查询客户关联的盒式信息列表
     * @param customerId 客户ID
     * @return 盒式信息列表
     */
    List<CustomerBoxRelationDTO> findCustomerBoxRelations(String customerId);

    /**
     * 查询盒式信息关联的客户列表
     * @param boxInfoId 盒式信息ID
     * @return 客户列表
     */
    List<CustomerBoxRelationDTO> findBoxInfoCustomerRelations(String boxInfoId);

    /**
     * 创建客户盒式关联
     * @param customerId 客户ID
     * @param request 创建请求
     * @return 关联信息
     */
    CustomerBoxRelationDTO createCustomerBoxRelation(String customerId, CreateCustomerBoxRelationRequest request);

    /**
     * 批量创建客户盒式关联
     * @param customerId 客户ID
     * @param requests 创建请求列表
     * @return 关联信息列表
     */
    List<CustomerBoxRelationDTO> batchCreateCustomerBoxRelations(String customerId, List<CreateCustomerBoxRelationRequest> requests);

    /**
     * 删除客户盒式关联
     * @param customerId 客户ID
     * @param boxInfoId 盒式信息ID
     */
    void deleteCustomerBoxRelation(String customerId, String boxInfoId);

    /**
     * 批量删除客户盒式关联
     * @param customerId 客户ID
     * @param boxInfoIds 盒式信息ID列表
     */
    void batchDeleteCustomerBoxRelations(String customerId, List<String> boxInfoIds);

    /**
     * 删除客户的所有盒式关联
     * @param customerId 客户ID
     */
    void deleteAllCustomerBoxRelations(String customerId);
}
