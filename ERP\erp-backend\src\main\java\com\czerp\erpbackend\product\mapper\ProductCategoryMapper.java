package com.czerp.erpbackend.product.mapper;

import com.czerp.erpbackend.product.dto.CreateCategoryRequest;
import com.czerp.erpbackend.product.dto.ProductCategoryDTO;
import com.czerp.erpbackend.product.dto.UpdateCategoryRequest;
import com.czerp.erpbackend.product.entity.ProductCategory;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 产品分类Mapper
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ProductCategoryMapper {
    
    /**
     * 实体转DTO
     * @param category 实体
     * @return DTO
     */
    @Mapping(source = "createdTime", target = "createTime")
    @Mapping(source = "updatedTime", target = "updateTime")
    ProductCategoryDTO toDto(ProductCategory category);
    
    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "level", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "isDeleted", constant = "false")
    ProductCategory toEntity(CreateCategoryRequest request);
    
    /**
     * 更新实体
     * @param request 更新请求
     * @param category 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "level", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateCategoryRequest request, @MappingTarget ProductCategory category);
}
