package com.czerp.erpbackend.sales.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import com.czerp.erpbackend.sales.dto.FilterOptionDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryParamDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryResponseDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryResultDTO;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import com.czerp.erpbackend.sales.entity.SalesOrderProcess;
import com.czerp.erpbackend.material.entity.PaperMaterial;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderMaterialRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderProcessRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderRepository;
import com.czerp.erpbackend.material.repository.PaperMaterialRepository;
import com.czerp.erpbackend.sales.service.SalesOrderQueryService;
import com.czerp.erpbackend.sales.enums.FilterField;
import com.czerp.erpbackend.sales.entity.SalesOrder_;
import com.czerp.erpbackend.sales.entity.SalesOrderItem_;
// import com.czerp.erpbackend.sales.adapter.SalesOrderFilterAdapter; // 暂时不使用新架构
import com.czerp.erpbackend.system.entity.User;
import com.czerp.erpbackend.system.repository.UserRepository;
import com.czerp.erpbackend.system.service.DictionaryService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Tuple;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 销售订单查询Service实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SalesOrderQueryServiceImpl implements SalesOrderQueryService {

    private final SalesOrderRepository salesOrderRepository;
    private final SalesOrderItemRepository salesOrderItemRepository;
    private final SalesOrderProcessRepository salesOrderProcessRepository;
    private final SalesOrderMaterialRepository salesOrderMaterialRepository;
    private final PaperMaterialRepository paperMaterialRepository;
    private final UserRepository userRepository;
    private final PurchaseOrderItemRepository purchaseOrderItemRepository;
    private final DictionaryService dictionaryService;
    // private final SalesOrderFilterAdapter salesOrderFilterAdapter; // 暂时不使用新架构

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    @Transactional(readOnly = true)
    public PageResponse<SalesOrderQueryResultDTO> querySalesOrders(SalesOrderQueryParamDTO queryParam) {
        log.info("Querying sales orders with params: {}", queryParam);

        // 构建分页
        int pageIndex = Math.max(0, queryParam.getPage() - 1); // 确保页码不小于0
        Pageable pageable = PageRequest.of(pageIndex, queryParam.getPageSize());

        // 使用Criteria API构建查询
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        // 由于需要处理specification字段和createdByName字段，我们不能直接使用构造函数
        // 而是需要查询原始数据，然后在Java代码中进行处理
        CriteriaQuery<Tuple> query = cb.createTupleQuery();

        // 定义FROM子句
        Root<SalesOrder> orderRoot = query.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> itemJoin = orderRoot.join("items", JoinType.INNER);

        // 构建查询条件
        List<Predicate> predicates = buildPredicates(cb, orderRoot, itemJoin, queryParam);

        // 应用查询条件
        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 构建SELECT子句，选择所有需要的字段
        List<Selection<?>> selections = new ArrayList<>();

        // 添加销售订单头字段
        addSelection(selections, orderRoot, "id", "orderId");
        addSelection(selections, orderRoot, "orderNo", "orderNo");
        addSelection(selections, orderRoot, "orderDate", "orderDate");
        addSelection(selections, orderRoot, "customerName", "customerName");
        addSelection(selections, orderRoot, "salesPerson", "salesPerson");
        addSelection(selections, orderRoot, "receivingUnit", "receivingUnit");
        addSelection(selections, orderRoot, "receiver", "receiver");
        addSelection(selections, orderRoot, "receiverPhone", "receiverPhone");
        addSelection(selections, orderRoot, "receivingAddress", "receivingAddress");
        addSelection(selections, orderRoot, "createdBy", "createdBy");
        addSelection(selections, orderRoot, "createdTime", "createdTime");

        // 添加销售订单行字段
        addSelection(selections, itemJoin, "id", "itemId");
        addSelection(selections, itemJoin, "productionOrderNo", "productionOrderNo");
        addSelection(selections, itemJoin, "customerOrderNo", "customerOrderNo");
        addSelection(selections, itemJoin, "customerProductCode", "customerProductCode");
        addSelection(selections, itemJoin, "productName", "productName");
        addSelection(selections, itemJoin, "processRequirements", "processRequirements");
        addSelection(selections, itemJoin, "boxType", "boxType");
        addSelection(selections, itemJoin, "paperType", "paperType");
        addSelection(selections, itemJoin, "productionPaperType", "productionPaperType");
        addSelection(selections, itemJoin, "length", "length");
        addSelection(selections, itemJoin, "width", "width");
        addSelection(selections, itemJoin, "height", "height");
        addSelection(selections, itemJoin, "sizeUnit", "sizeUnit");
        addSelection(selections, itemJoin, "productionLength", "productionLength");
        addSelection(selections, itemJoin, "productionWidth", "productionWidth");
        addSelection(selections, itemJoin, "productionHeight", "productionHeight");
        addSelection(selections, itemJoin, "quantity", "quantity");
        addSelection(selections, itemJoin, "spareQuantity", "spareQuantity");
        addSelection(selections, itemJoin, "price", "price");
        addSelection(selections, itemJoin, "amount", "amount");
        addSelection(selections, itemJoin, "isSpecialPrice", "isSpecialPrice");
        addSelection(selections, itemJoin, "productionRemark", "productionRemark");
        addSelection(selections, itemJoin, "remark", "remark");
        addSelection(selections, itemJoin, "connectionMethod", "connectionMethod");
        addSelection(selections, itemJoin, "unit", "unit");
        addSelection(selections, itemJoin, "paperQuotation", "paperQuotation");
        addSelection(selections, itemJoin, "currency", "currency");
        addSelection(selections, itemJoin, "unitWeight", "unitWeight");
        addSelection(selections, itemJoin, "totalWeight", "totalWeight");
        addSelection(selections, itemJoin, "productArea", "productArea");
        addSelection(selections, itemJoin, "totalArea", "totalArea");
        addSelection(selections, itemJoin, "productVolume", "productVolume");
        addSelection(selections, itemJoin, "totalVolume", "totalVolume");
        addSelection(selections, itemJoin, "taxRate", "taxRate");
        addSelection(selections, itemJoin, "isTaxed", "isTaxed");
        addSelection(selections, itemJoin, "corrugationType", "corrugationType");
        addSelection(selections, itemJoin, "deliveryDate", "deliveryDate");

        // 应用选择字段
        if (!selections.isEmpty()) {
            query.multiselect(selections.toArray(new Selection<?>[0]));
        } else {
            log.warn("没有有效的选择字段，查询可能返回空结果");
        }

        // 添加排序
        if (StringUtils.hasText(queryParam.getSortField())) {
            Path<?> sortPath;
            // 判断排序字段属于哪个实体
            if (isSalesOrderField(queryParam.getSortField())) {
                sortPath = orderRoot.get(queryParam.getSortField());
            } else {
                sortPath = itemJoin.get(queryParam.getSortField());
            }

            if ("desc".equalsIgnoreCase(queryParam.getSortOrder())) {
                query.orderBy(cb.desc(sortPath));
            } else {
                query.orderBy(cb.asc(sortPath));
            }
        } else {
            // 默认按创建时间降序排序
            query.orderBy(cb.desc(orderRoot.get("createdTime")));
        }

        // 去重
        query.distinct(true);

        // 执行查询
        TypedQuery<Tuple> typedQuery = entityManager.createQuery(query);

        // 获取总记录数
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<SalesOrder> countRoot = countQuery.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> countItemJoin = countRoot.join("items", JoinType.INNER);

        List<Predicate> countPredicates = buildPredicates(cb, countRoot, countItemJoin, queryParam);
        if (!countPredicates.isEmpty()) {
            countQuery.where(cb.and(countPredicates.toArray(new Predicate[0])));
        }

        // 修复：统计明细数量而不是订单数量，与分页查询逻辑保持一致
        countQuery.select(cb.count(countItemJoin));
        Long totalCount = entityManager.createQuery(countQuery).getSingleResult();

        // 设置分页
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        // 执行查询并获取结果
        List<Tuple> tuples = typedQuery.getResultList();

        // 获取所有创建人ID
        List<String> createdByIds = tuples.stream()
            .map(tuple -> tuple.get("createdBy", String.class))
            .filter(StringUtils::hasText)
            .distinct()
            .toList();

        // 查询所有创建人的用户名
        Map<String, String> userMap = new HashMap<>();
        if (!createdByIds.isEmpty()) {
            List<User> users = userRepository.findAllById(createdByIds);
            users.forEach(user -> userMap.put(user.getId(), user.getUsername()));
        }

        // 将Tuple转换为DTO
        List<SalesOrderQueryResultDTO> results = tuples.stream()
            .map(tuple -> {
                SalesOrderQueryResultDTO dto = new SalesOrderQueryResultDTO();

                // 设置销售订单头信息
                dto.setId(tuple.get("orderId", String.class));
                dto.setOrderNo(tuple.get("orderNo", String.class));
                dto.setOrderDate(tuple.get("orderDate", java.time.LocalDate.class));
                dto.setCustomerName(tuple.get("customerName", String.class));
                dto.setSalesPerson(tuple.get("salesPerson", String.class));
                dto.setReceivingUnit(tuple.get("receivingUnit", String.class));
                dto.setReceiver(tuple.get("receiver", String.class));
                dto.setReceiverPhone(tuple.get("receiverPhone", String.class));
                dto.setReceivingAddress(tuple.get("receivingAddress", String.class));

                // 设置创建人信息
                String createdBy = tuple.get("createdBy", String.class);
                dto.setCreatedBy(createdBy);
                dto.setCreatedByName(userMap.getOrDefault(createdBy, ""));
                dto.setCreatedTime(tuple.get("createdTime", java.time.LocalDateTime.class));

                // 设置销售订单行信息
                dto.setItemId(tuple.get("itemId", String.class));
                dto.setProductionOrderNo(tuple.get("productionOrderNo", String.class));
                dto.setCustomerOrderNo(tuple.get("customerOrderNo", String.class));
                dto.setCustomerProductCode(tuple.get("customerProductCode", String.class));
                dto.setProductName(tuple.get("productName", String.class));
                dto.setProcessRequirements(tuple.get("processRequirements", String.class));
                dto.setBoxType(tuple.get("boxType", String.class));
                dto.setPaperType(tuple.get("paperType", String.class));
                dto.setProductionPaperType(tuple.get("productionPaperType", String.class));

                // 获取尺寸相关字段
                BigDecimal length = tuple.get("length", BigDecimal.class);
                BigDecimal width = tuple.get("width", BigDecimal.class);
                BigDecimal height = tuple.get("height", BigDecimal.class);
                String sizeUnit = tuple.get("sizeUnit", String.class);
                BigDecimal productionLength = tuple.get("productionLength", BigDecimal.class);
                BigDecimal productionWidth = tuple.get("productionWidth", BigDecimal.class);
                BigDecimal productionHeight = tuple.get("productionHeight", BigDecimal.class);

                // 处理specification字段 - 组合长宽高 + 单位
                String specification = formatSpecification(length, width, height, sizeUnit);
                dto.setSpecification(specification);

                // 处理productionSpecification字段 - 优先使用生产尺寸，为空时使用产品尺寸
                String productionSpecification = formatSpecification(productionLength, productionWidth, productionHeight, sizeUnit);
                if (productionSpecification.isEmpty()) {
                    productionSpecification = specification;
                }
                dto.setProductionSpecification(productionSpecification);

                // 设置其他字段
                dto.setQuantity(tuple.get("quantity", Integer.class));
                dto.setSpareQuantity(tuple.get("spareQuantity", Integer.class));
                dto.setPrice(tuple.get("price", BigDecimal.class));
                dto.setAmount(tuple.get("amount", BigDecimal.class));
                dto.setIsSpecialPrice(tuple.get("isSpecialPrice", Boolean.class));
                dto.setProductionRemark(tuple.get("productionRemark", String.class));
                dto.setRemark(tuple.get("remark", String.class));
                dto.setConnectionMethod(tuple.get("connectionMethod", String.class));
                dto.setUnit(tuple.get("unit", String.class));
                dto.setPaperQuotation(tuple.get("paperQuotation", BigDecimal.class));
                dto.setCurrency(tuple.get("currency", String.class));
                dto.setUnitWeight(tuple.get("unitWeight", BigDecimal.class));
                dto.setTotalWeight(tuple.get("totalWeight", BigDecimal.class));
                dto.setProductArea(tuple.get("productArea", BigDecimal.class));
                dto.setTotalArea(tuple.get("totalArea", BigDecimal.class));
                dto.setProductVolume(tuple.get("productVolume", BigDecimal.class));
                dto.setTotalVolume(tuple.get("totalVolume", BigDecimal.class));
                dto.setTaxRate(tuple.get("taxRate", BigDecimal.class));
                dto.setIsTaxed(tuple.get("isTaxed", Boolean.class));
                dto.setCorrugationType(tuple.get("corrugationType", String.class));
                dto.setDeliveryDate(tuple.get("deliveryDate", java.time.LocalDate.class));

                return dto;
            })
            .toList();

        // 添加采购订单数据
        enrichWithPurchaseOrderData(results);

        // 添加工序数据
        enrichWithProcessData(results);

        // 添加用料数据
        enrichWithMaterialData(results);

        // 添加纸质类别数据
        enrichWithPaperTypeData(results);

        // 创建分页结果
        Page<SalesOrderQueryResultDTO> page = new PageImpl<>(results, pageable, totalCount);

        // 计算总页数
        int totalPages = totalCount > 0 ? (int) Math.ceil((double) totalCount / pageable.getPageSize()) : 0;

        // 转换为PageResponse
        return new PageResponse<>(
            page.getContent(),
            page.getTotalElements(),
            totalPages,
            page.getNumber() + 1,
            page.getSize()
        );
    }

    /**
     * 构建查询条件（增强版，支持新的列筛选功能）
     */
    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<SalesOrder> orderRoot,
                                           Join<SalesOrder, SalesOrderItem> itemJoin,
                                           SalesOrderQueryParamDTO queryParam) {
        List<Predicate> predicates = new ArrayList<>();

        // ========== 传统搜索条件（保持向后兼容） ==========

        // 关键字查询
        if (StringUtils.hasText(queryParam.getKeyword())) {
            String keyword = "%" + queryParam.getKeyword() + "%";
            predicates.add(cb.or(
                cb.like(orderRoot.get("orderNo"), keyword),
                cb.like(orderRoot.get("customerName"), keyword),
                cb.like(itemJoin.get("productionOrderNo"), keyword),
                cb.like(itemJoin.get("customerOrderNo"), keyword),
                cb.like(itemJoin.get("customerProductCode"), keyword),
                cb.like(itemJoin.get("productName"), keyword)
            ));
        }

        // 销售单号查询
        if (StringUtils.hasText(queryParam.getOrderNo())) {
            predicates.add(cb.equal(orderRoot.get("orderNo"), queryParam.getOrderNo()));
        }

        // 生产单号查询
        if (StringUtils.hasText(queryParam.getProductionOrderNo())) {
            predicates.add(cb.equal(itemJoin.get("productionOrderNo"), queryParam.getProductionOrderNo()));
        }

        // 客户名称查询
        if (StringUtils.hasText(queryParam.getCustomerName())) {
            predicates.add(cb.like(orderRoot.get("customerName"), "%" + queryParam.getCustomerName() + "%"));
        }

        // 客户订单号查询
        if (StringUtils.hasText(queryParam.getCustomerOrderNo())) {
            predicates.add(cb.equal(itemJoin.get("customerOrderNo"), queryParam.getCustomerOrderNo()));
        }

        // 客方货号查询
        if (StringUtils.hasText(queryParam.getCustomerProductCode())) {
            predicates.add(cb.equal(itemJoin.get("customerProductCode"), queryParam.getCustomerProductCode()));
        }

        // 品名查询
        if (StringUtils.hasText(queryParam.getProductName())) {
            predicates.add(cb.like(itemJoin.get("productName"), "%" + queryParam.getProductName() + "%"));
        }

        // 日期范围查询
        if (queryParam.getStartDate() != null) {
            predicates.add(cb.greaterThanOrEqualTo(orderRoot.get("orderDate"), queryParam.getStartDate()));
        }
        if (queryParam.getEndDate() != null) {
            predicates.add(cb.lessThanOrEqualTo(orderRoot.get("orderDate"), queryParam.getEndDate()));
        }

        // 销售员查询
        if (StringUtils.hasText(queryParam.getSalesPerson())) {
            predicates.add(cb.equal(orderRoot.get("salesPerson"), queryParam.getSalesPerson()));
        }

        // ========== 第一阶段：新增的列筛选条件 ==========

        // 文本筛选
        if (StringUtils.hasText(queryParam.getFilterProductionOrderNo())) {
            predicates.add(cb.like(itemJoin.get("productionOrderNo"), "%" + queryParam.getFilterProductionOrderNo() + "%"));
        }

        if (StringUtils.hasText(queryParam.getFilterOrderNo())) {
            predicates.add(cb.like(orderRoot.get("orderNo"), "%" + queryParam.getFilterOrderNo() + "%"));
        }

        if (StringUtils.hasText(queryParam.getFilterCustomerOrderNo())) {
            predicates.add(cb.like(itemJoin.get("customerOrderNo"), "%" + queryParam.getFilterCustomerOrderNo() + "%"));
        }

        if (StringUtils.hasText(queryParam.getFilterCustomerProductCode())) {
            predicates.add(cb.like(itemJoin.get("customerProductCode"), "%" + queryParam.getFilterCustomerProductCode() + "%"));
        }

        // 选择筛选（多值）
        if (queryParam.getFilterCustomerNames() != null && !queryParam.getFilterCustomerNames().isEmpty()) {
            predicates.add(orderRoot.get("customerName").in(queryParam.getFilterCustomerNames()));
        }

        if (queryParam.getFilterProductNames() != null && !queryParam.getFilterProductNames().isEmpty()) {
            predicates.add(itemJoin.get("productName").in(queryParam.getFilterProductNames()));
        }

        if (queryParam.getFilterSalesPersons() != null && !queryParam.getFilterSalesPersons().isEmpty()) {
            predicates.add(orderRoot.get("salesPerson").in(queryParam.getFilterSalesPersons()));
        }

        // 🔥 新增：单号类字段的多值筛选（列头筛选）
        if (queryParam.getFilterProductionOrderNos() != null && !queryParam.getFilterProductionOrderNos().isEmpty()) {
            predicates.add(itemJoin.get("productionOrderNo").in(queryParam.getFilterProductionOrderNos()));
        }

        if (queryParam.getFilterOrderNos() != null && !queryParam.getFilterOrderNos().isEmpty()) {
            predicates.add(orderRoot.get("orderNo").in(queryParam.getFilterOrderNos()));
        }

        if (queryParam.getFilterCustomerOrderNos() != null && !queryParam.getFilterCustomerOrderNos().isEmpty()) {
            predicates.add(itemJoin.get("customerOrderNo").in(queryParam.getFilterCustomerOrderNos()));
        }

        if (queryParam.getFilterCustomerProductCodes() != null && !queryParam.getFilterCustomerProductCodes().isEmpty()) {
            predicates.add(itemJoin.get("customerProductCode").in(queryParam.getFilterCustomerProductCodes()));
        }

        // 🔥 新增：采购单号筛选（需要通过子查询实现）
        if (queryParam.getFilterPurchaseOrderNos() != null && !queryParam.getFilterPurchaseOrderNos().isEmpty()) {
            // 创建子查询来查找关联的采购订单
            Subquery<String> subquery = cb.createQuery().subquery(String.class);
            Root<PurchaseOrderItem> purchaseItemRoot = subquery.from(PurchaseOrderItem.class);
            Join<PurchaseOrderItem, PurchaseOrder> purchaseOrderJoin = purchaseItemRoot.join("purchaseOrder", JoinType.INNER);

            // 🔧 简化字符集处理：移除COLLATE函数，让Hibernate自动处理
            subquery.select(purchaseItemRoot.get("sourceSalesOrderItemId"))
                   .where(purchaseOrderJoin.get("purchaseOrderNo").in(queryParam.getFilterPurchaseOrderNos()));

            predicates.add(itemJoin.get("id").in(subquery));
        }

        // 🔥 新增：采购供应商筛选（需要通过子查询实现）
        if (queryParam.getFilterSupplierNames() != null && !queryParam.getFilterSupplierNames().isEmpty()) {
            // 创建子查询来查找关联的采购订单供应商
            Subquery<String> subquery = cb.createQuery().subquery(String.class);
            Root<PurchaseOrderItem> purchaseItemRoot = subquery.from(PurchaseOrderItem.class);
            Join<PurchaseOrderItem, PurchaseOrder> purchaseOrderJoin = purchaseItemRoot.join("purchaseOrder", JoinType.INNER);

            // 🔧 简化字符集处理：移除COLLATE函数，让Hibernate自动处理
            subquery.select(purchaseItemRoot.get("sourceSalesOrderItemId"))
                   .where(purchaseOrderJoin.get("supplierName").in(queryParam.getFilterSupplierNames()));

            predicates.add(itemJoin.get("id").in(subquery));
        }

        // 🔥 新增：纸板类别筛选（需要通过子查询实现）
        if (queryParam.getFilterPaperTypeNames() != null && !queryParam.getFilterPaperTypeNames().isEmpty()) {
            // 创建子查询来查找关联的采购订单纸板类别
            Subquery<String> subquery = cb.createQuery().subquery(String.class);
            Root<PurchaseOrderItem> purchaseItemRoot = subquery.from(PurchaseOrderItem.class);

            // 🔧 简化字符集处理：移除COLLATE函数，让Hibernate自动处理
            subquery.select(purchaseItemRoot.get("sourceSalesOrderItemId"))
                   .where(purchaseItemRoot.get("paperBoardCategory").in(queryParam.getFilterPaperTypeNames()));

            predicates.add(itemJoin.get("id").in(subquery));
        }

        // 🔥 新增：规格类字段的多值筛选
        if (queryParam.getFilterBindingSpecifications() != null && !queryParam.getFilterBindingSpecifications().isEmpty()) {
            // 合订规格需要通过采购订单明细表查询
            Subquery<String> subquery = cb.createQuery().subquery(String.class);
            Root<PurchaseOrderItem> purchaseItemRoot = subquery.from(PurchaseOrderItem.class);

            // 🔧 简化字符集处理：移除COLLATE函数，让Hibernate自动处理
            subquery.select(purchaseItemRoot.get("sourceSalesOrderItemId"))
                   .where(purchaseItemRoot.get("bindingSpecification").in(queryParam.getFilterBindingSpecifications()));

            predicates.add(itemJoin.get("id").in(subquery));
        }

        if (queryParam.getFilterSpecifications() != null && !queryParam.getFilterSpecifications().isEmpty()) {
            // 规格字段需要动态构建（长×宽×高 单位格式）
            List<Predicate> specPredicates = new ArrayList<>();
            for (String spec : queryParam.getFilterSpecifications()) {
                specPredicates.add(cb.like(
                    cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
                        cb.coalesce(itemJoin.get("length"), cb.literal(BigDecimal.ZERO)).as(String.class),
                        cb.literal("×")),
                        cb.coalesce(itemJoin.get("width"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                        cb.literal("×")),
                        cb.coalesce(itemJoin.get("height"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                        cb.literal(" ")),
                        cb.coalesce(itemJoin.get("sizeUnit"), cb.literal(""))),
                    "%" + spec + "%"
                ));
            }
            if (!specPredicates.isEmpty()) {
                predicates.add(cb.or(specPredicates.toArray(new Predicate[0])));
            }
        }

        if (queryParam.getFilterProductionSpecifications() != null && !queryParam.getFilterProductionSpecifications().isEmpty()) {
            // 🔥 修复：生产规格字段需要实现与查询结果一致的回退机制
            List<Predicate> prodSpecPredicates = new ArrayList<>();
            for (String spec : queryParam.getFilterProductionSpecifications()) {
                // 构建生产规格表达式
                Expression<String> productionSpecExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
                    cb.coalesce(itemJoin.get("productionLength"), cb.literal(BigDecimal.ZERO)).as(String.class),
                    cb.literal("×")),
                    cb.coalesce(itemJoin.get("productionWidth"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                    cb.literal("×")),
                    cb.coalesce(itemJoin.get("productionHeight"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                    cb.literal(" ")),
                    cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

                // 构建产品规格表达式（回退选项）
                Expression<String> productSpecExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
                    cb.coalesce(itemJoin.get("length"), cb.literal(BigDecimal.ZERO)).as(String.class),
                    cb.literal("×")),
                    cb.coalesce(itemJoin.get("width"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                    cb.literal("×")),
                    cb.coalesce(itemJoin.get("height"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                    cb.literal(" ")),
                    cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

                // 🔥 修复：判断生产规格是否有效（基于原始字段值而非表达式）
                Predicate hasValidProductionSpec = cb.and(
                    cb.isNotNull(itemJoin.get("productionLength")),
                    cb.isNotNull(itemJoin.get("productionWidth")),
                    cb.isNotNull(itemJoin.get("productionHeight")),
                    cb.or(
                        cb.notEqual(itemJoin.get("productionLength"), BigDecimal.ZERO),
                        cb.notEqual(itemJoin.get("productionWidth"), BigDecimal.ZERO),
                        cb.notEqual(itemJoin.get("productionHeight"), BigDecimal.ZERO)
                    )
                );

                // 实现回退机制：优先使用生产规格，为空时使用产品规格
                Expression<String> finalSpecExpr = cb.<String>selectCase()
                    .when(hasValidProductionSpec, productionSpecExpr)
                    .otherwise(productSpecExpr);

                prodSpecPredicates.add(cb.like(finalSpecExpr, "%" + spec + "%"));
            }
            if (!prodSpecPredicates.isEmpty()) {
                predicates.add(cb.or(prodSpecPredicates.toArray(new Predicate[0])));
            }
        }

        // 🔥 新增：压线尺寸筛选（需要通过子查询实现）
        if (queryParam.getFilterPressSizeWidths() != null && !queryParam.getFilterPressSizeWidths().isEmpty()) {
            // 创建子查询来查找关联的用料信息
            Subquery<String> subquery = cb.createQuery().subquery(String.class);
            Root<SalesOrderMaterial> materialRoot = subquery.from(SalesOrderMaterial.class);
            Join<SalesOrderMaterial, SalesOrderItem> materialItemJoin = materialRoot.join("orderItem", JoinType.INNER);

            // 🔧 简化字符集处理：移除COLLATE函数，让Hibernate自动处理
            subquery.select(materialItemJoin.get("id"))
                   .where(materialRoot.get("pressSizeWidth").in(queryParam.getFilterPressSizeWidths()));

            predicates.add(itemJoin.get("id").in(subquery));
        }

        // 🔥 新增：工艺和人员字段的多值筛选
        if (queryParam.getFilterPaperTypes() != null && !queryParam.getFilterPaperTypes().isEmpty()) {
            predicates.add(itemJoin.get("paperType").in(queryParam.getFilterPaperTypes()));
        }

        if (queryParam.getFilterProcessRequirements() != null && !queryParam.getFilterProcessRequirements().isEmpty()) {
            predicates.add(itemJoin.get("processRequirements").in(queryParam.getFilterProcessRequirements()));
        }

        // 🔥 新增：工序筛选（需要通过子查询实现）
        if (queryParam.getFilterProcesses() != null && !queryParam.getFilterProcesses().isEmpty()) {
            // 创建子查询来查找关联的工序信息
            Subquery<String> subquery = cb.createQuery().subquery(String.class);
            Root<SalesOrderProcess> processRoot = subquery.from(SalesOrderProcess.class);

            // 🔧 简化字符集处理：移除COLLATE函数，让Hibernate自动处理
            subquery.select(processRoot.get("orderItemId"))
                   .where(cb.and(
                       processRoot.get("processName").in(queryParam.getFilterProcesses()),
                       cb.equal(processRoot.get("isDeleted"), false)
                   ));

            predicates.add(itemJoin.get("id").in(subquery));
        }

        if (queryParam.getFilterReceivingUnits() != null && !queryParam.getFilterReceivingUnits().isEmpty()) {
            predicates.add(orderRoot.get("receivingUnit").in(queryParam.getFilterReceivingUnits()));
        }

        if (queryParam.getFilterReceivingPersons() != null && !queryParam.getFilterReceivingPersons().isEmpty()) {
            predicates.add(orderRoot.get("receiver").in(queryParam.getFilterReceivingPersons()));
        }

        // 🔥 新增：字典类字段的多值筛选
        if (queryParam.getFilterBoxTypes() != null && !queryParam.getFilterBoxTypes().isEmpty()) {
            predicates.add(itemJoin.get("boxType").in(queryParam.getFilterBoxTypes()));
        }

        if (queryParam.getFilterCorrugationTypes() != null && !queryParam.getFilterCorrugationTypes().isEmpty()) {
            predicates.add(itemJoin.get("corrugationType").in(queryParam.getFilterCorrugationTypes()));
        }

        // 🔥 新增：生产和工艺相关字段的多值筛选
        if (queryParam.getFilterProductionPaperTypes() != null && !queryParam.getFilterProductionPaperTypes().isEmpty()) {
            predicates.add(itemJoin.get("productionPaperType").in(queryParam.getFilterProductionPaperTypes()));
        }

        if (queryParam.getFilterConnectionMethods() != null && !queryParam.getFilterConnectionMethods().isEmpty()) {
            predicates.add(itemJoin.get("connectionMethod").in(queryParam.getFilterConnectionMethods()));
        }

        if (queryParam.getFilterUnits() != null && !queryParam.getFilterUnits().isEmpty()) {
            predicates.add(itemJoin.get("unit").in(queryParam.getFilterUnits()));
        }

        if (queryParam.getFilterCurrencies() != null && !queryParam.getFilterCurrencies().isEmpty()) {
            predicates.add(itemJoin.get("currency").in(queryParam.getFilterCurrencies()));
        }

        if (queryParam.getFilterTaxRates() != null && !queryParam.getFilterTaxRates().isEmpty()) {
            // 税率需要转换为字符串进行比较
            List<String> taxRateStrings = queryParam.getFilterTaxRates().stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
            predicates.add(cb.function("CAST", String.class, itemJoin.get("taxRate"), cb.literal("CHAR")).in(taxRateStrings));
        }

        // 🔥 新增：人员和地址相关字段的多值筛选
        if (queryParam.getFilterReceiverPhones() != null && !queryParam.getFilterReceiverPhones().isEmpty()) {
            predicates.add(orderRoot.get("receiverPhone").in(queryParam.getFilterReceiverPhones()));
        }

        if (queryParam.getFilterCreatedBys() != null && !queryParam.getFilterCreatedBys().isEmpty()) {
            predicates.add(orderRoot.get("createdBy").in(queryParam.getFilterCreatedBys()));
        }

        // 🔥 新增：文本字段的筛选
        if (queryParam.getFilterProductionRemarks() != null && !queryParam.getFilterProductionRemarks().isEmpty()) {
            List<Predicate> remarkPredicates = new ArrayList<>();
            for (String remark : queryParam.getFilterProductionRemarks()) {
                remarkPredicates.add(cb.like(itemJoin.get("productionRemark"), "%" + remark + "%"));
            }
            if (!remarkPredicates.isEmpty()) {
                predicates.add(cb.or(remarkPredicates.toArray(new Predicate[0])));
            }
        }

        if (queryParam.getFilterRemarks() != null && !queryParam.getFilterRemarks().isEmpty()) {
            List<Predicate> remarkPredicates = new ArrayList<>();
            for (String remark : queryParam.getFilterRemarks()) {
                remarkPredicates.add(cb.like(itemJoin.get("remark"), "%" + remark + "%"));
            }
            if (!remarkPredicates.isEmpty()) {
                predicates.add(cb.or(remarkPredicates.toArray(new Predicate[0])));
            }
        }

        if (queryParam.getFilterReceivingAddresses() != null && !queryParam.getFilterReceivingAddresses().isEmpty()) {
            List<Predicate> addressPredicates = new ArrayList<>();
            for (String address : queryParam.getFilterReceivingAddresses()) {
                addressPredicates.add(cb.like(orderRoot.get("receivingAddress"), "%" + address + "%"));
            }
            if (!addressPredicates.isEmpty()) {
                predicates.add(cb.or(addressPredicates.toArray(new Predicate[0])));
            }
        }

        // 🔥 新增：特殊标识字段的筛选
        if (queryParam.getFilterIsSpecialPrices() != null && !queryParam.getFilterIsSpecialPrices().isEmpty()) {
            predicates.add(itemJoin.get("isSpecialPrice").in(queryParam.getFilterIsSpecialPrices()));
        }

        // 数字范围筛选
        if (queryParam.getFilterQuantityMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("quantity"), queryParam.getFilterQuantityMin()));
        }
        if (queryParam.getFilterQuantityMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("quantity"), queryParam.getFilterQuantityMax()));
        }

        if (queryParam.getFilterTotalAmountMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("amount"), queryParam.getFilterTotalAmountMin()));
        }
        if (queryParam.getFilterTotalAmountMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("amount"), queryParam.getFilterTotalAmountMax()));
        }

        // 🔥 新增：数字范围筛选字段
        if (queryParam.getFilterSpareQuantityMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("spareQuantity"), queryParam.getFilterSpareQuantityMin()));
        }
        if (queryParam.getFilterSpareQuantityMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("spareQuantity"), queryParam.getFilterSpareQuantityMax()));
        }

        if (queryParam.getFilterPriceMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("price"), queryParam.getFilterPriceMin()));
        }
        if (queryParam.getFilterPriceMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("price"), queryParam.getFilterPriceMax()));
        }

        if (queryParam.getFilterPurchasedQuantityMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("purchasedQuantity"), queryParam.getFilterPurchasedQuantityMin()));
        }
        if (queryParam.getFilterPurchasedQuantityMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("purchasedQuantity"), queryParam.getFilterPurchasedQuantityMax()));
        }

        if (queryParam.getFilterPaperQuotationMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("paperQuotation"), queryParam.getFilterPaperQuotationMin()));
        }
        if (queryParam.getFilterPaperQuotationMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("paperQuotation"), queryParam.getFilterPaperQuotationMax()));
        }

        if (queryParam.getFilterUnitWeightMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("unitWeight"), queryParam.getFilterUnitWeightMin()));
        }
        if (queryParam.getFilterUnitWeightMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("unitWeight"), queryParam.getFilterUnitWeightMax()));
        }

        if (queryParam.getFilterTotalWeightMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("totalWeight"), queryParam.getFilterTotalWeightMin()));
        }
        if (queryParam.getFilterTotalWeightMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("totalWeight"), queryParam.getFilterTotalWeightMax()));
        }

        if (queryParam.getFilterProductAreaMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("productArea"), queryParam.getFilterProductAreaMin()));
        }
        if (queryParam.getFilterProductAreaMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("productArea"), queryParam.getFilterProductAreaMax()));
        }

        if (queryParam.getFilterTotalAreaMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("totalArea"), queryParam.getFilterTotalAreaMin()));
        }
        if (queryParam.getFilterTotalAreaMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("totalArea"), queryParam.getFilterTotalAreaMax()));
        }

        if (queryParam.getFilterProductVolumeMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("productVolume"), queryParam.getFilterProductVolumeMin()));
        }
        if (queryParam.getFilterProductVolumeMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("productVolume"), queryParam.getFilterProductVolumeMax()));
        }

        if (queryParam.getFilterTotalVolumeMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("totalVolume"), queryParam.getFilterTotalVolumeMin()));
        }
        if (queryParam.getFilterTotalVolumeMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("totalVolume"), queryParam.getFilterTotalVolumeMax()));
        }

        // 日期范围筛选
        if (queryParam.getFilterOrderDateStart() != null) {
            predicates.add(cb.greaterThanOrEqualTo(orderRoot.get("orderDate"), queryParam.getFilterOrderDateStart()));
        }
        if (queryParam.getFilterOrderDateEnd() != null) {
            predicates.add(cb.lessThanOrEqualTo(orderRoot.get("orderDate"), queryParam.getFilterOrderDateEnd()));
        }

        if (queryParam.getFilterDeliveryDateStart() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("deliveryDate"), queryParam.getFilterDeliveryDateStart()));
        }
        if (queryParam.getFilterDeliveryDateEnd() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("deliveryDate"), queryParam.getFilterDeliveryDateEnd()));
        }

        // 🔥 新增：日期范围筛选字段
        if (queryParam.getFilterCreatedTimeStart() != null) {
            predicates.add(cb.greaterThanOrEqualTo(orderRoot.get("createdTime").as(LocalDate.class), queryParam.getFilterCreatedTimeStart()));
        }
        if (queryParam.getFilterCreatedTimeEnd() != null) {
            predicates.add(cb.lessThanOrEqualTo(orderRoot.get("createdTime").as(LocalDate.class), queryParam.getFilterCreatedTimeEnd()));
        }

        return predicates;
    }

    /**
     * 判断字段是否属于SalesOrder实体
     */
    private boolean isSalesOrderField(String fieldName) {
        // 定义SalesOrder实体的字段列表
        List<String> salesOrderFields = List.of(
            "id", "orderNo", "orderDate", "customerName", "salesPerson",
            "receivingUnit", "receiver", "receiverPhone", "receivingAddress",
            "createdBy", "createdByName", "createdTime", "updatedBy", "updatedByName", "updatedTime"
        );

        return salesOrderFields.contains(fieldName);
    }

    /**
     * 安全地添加选择字段
     *
     * @param selections 选择字段列表
     * @param root 根对象
     * @param fieldName 字段名
     * @param alias 别名
     */
    private void addSelection(List<Selection<?>> selections, From<?, ?> root, String fieldName, String alias) {
        try {
            Path<?> path = root.get(fieldName);
            if (path != null) {
                Selection<?> selection = path.alias(alias);
                if (selection != null) {
                    selections.add(selection);
                } else {
                    log.warn("字段 {} 的别名 {} 返回null，已跳过", fieldName, alias);
                }
            } else {
                log.warn("字段 {} 在实体中不存在，已跳过", fieldName);
            }
        } catch (IllegalArgumentException e) {
            log.warn("获取字段 {} 时出错: {}", fieldName, e.getMessage());
        }
    }

    /**
     * 为销售订单查询结果添加采购订单数据
     * @param results 销售订单查询结果列表
     */
    private void enrichWithPurchaseOrderData(List<SalesOrderQueryResultDTO> results) {
        log.debug("开始为销售订单查询结果添加采购订单数据，共 {} 条记录", results.size());

        // 收集所有销售订单明细ID
        List<String> salesOrderItemIds = results.stream()
            .map(SalesOrderQueryResultDTO::getItemId)
            .filter(StringUtils::hasText)
            .distinct()
            .collect(Collectors.toList());

        if (salesOrderItemIds.isEmpty()) {
            log.debug("没有有效的销售订单明细ID，跳过采购数据查询");
            return;
        }

        // 查询采购订单明细数据
        log.debug("查询 {} 个销售订单明细的采购信息", salesOrderItemIds.size());
        List<Object[]> purchaseData = purchaseOrderItemRepository.findWithPurchaseOrderInfoBySourceSalesOrderItemIdIn(salesOrderItemIds);
        log.debug("查询到 {} 条采购订单明细记录", purchaseData.size());

        // 构建映射：销售订单明细ID -> 采购信息
        Map<String, List<PurchaseInfo>> purchaseInfoMap = new HashMap<>();

        for (Object[] row : purchaseData) {
            PurchaseOrderItem purchaseOrderItem = (PurchaseOrderItem) row[0];
            String purchaseOrderNo = (String) row[1];
            String supplierName = (String) row[2];
            String salesOrderItemId = purchaseOrderItem.getSourceSalesOrderItemId();
            Integer quantity = purchaseOrderItem.getQuantity();
            String bindingSpecification = purchaseOrderItem.getBindingSpecification();

            PurchaseInfo info = new PurchaseInfo(purchaseOrderNo, supplierName, quantity, bindingSpecification);

            purchaseInfoMap.computeIfAbsent(salesOrderItemId, k -> new ArrayList<>())
                .add(info);
        }

        // 为每个销售订单明细设置采购信息
        for (SalesOrderQueryResultDTO dto : results) {
            String itemId = dto.getItemId();
            List<PurchaseInfo> purchaseInfoList = purchaseInfoMap.get(itemId);

            if (purchaseInfoList != null && !purchaseInfoList.isEmpty()) {
                // 如果有多个采购订单，我们取第一个（或者可以用逗号分隔显示多个）
                PurchaseInfo firstPurchase = purchaseInfoList.get(0);
                dto.setPurchaseOrderNo(firstPurchase.getPurchaseOrderNo());
                dto.setSupplierName(firstPurchase.getSupplierName());
                dto.setBindingSpecification(firstPurchase.getBindingSpecification());

                // 计算总采购数量
                int totalPurchasedQuantity = purchaseInfoList.stream()
                    .mapToInt(PurchaseInfo::getQuantity)
                    .sum();
                dto.setPurchasedQuantity(totalPurchasedQuantity);

                log.debug("为销售订单明细 {} 设置采购信息：采购单号={}, 供应商={}, 已采购数量={}, 合订规格={}",
                    itemId, dto.getPurchaseOrderNo(), dto.getSupplierName(), dto.getPurchasedQuantity(), dto.getBindingSpecification());
            } else {
                // 没有采购信息
                dto.setPurchasedQuantity(0);
                log.debug("销售订单明细 {} 没有关联的采购信息", itemId);
            }
        }

        log.debug("完成销售订单查询结果的采购数据填充");
    }

    /**
     * 采购信息辅助类
     */
    @Data
    @AllArgsConstructor
    private static class PurchaseInfo {
        private String purchaseOrderNo;
        private String supplierName;
        private Integer quantity;
        private String bindingSpecification; // 合订规格
    }

    // ========== 第一阶段：新增的列筛选功能方法 ==========

    @Override
    @Transactional(readOnly = true)
    public SalesOrderQueryResponseDTO querySalesOrdersEnhanced(SalesOrderQueryParamDTO queryParam) {
        log.info("Enhanced querying sales orders with params: {}", queryParam);

        // 执行基础查询
        PageResponse<SalesOrderQueryResultDTO> pageData = querySalesOrders(queryParam);

        // 如果不需要筛选选项，返回简单响应
        if (queryParam.getIncludeFilterOptions() == null || !queryParam.getIncludeFilterOptions()) {
            return SalesOrderQueryResponseDTO.simple(pageData);
        }

        // 生成筛选选项
        Map<String, List<FilterOptionDTO>> filterOptions = generateFilterOptions(queryParam);

        // 生成筛选统计信息
        SalesOrderQueryResponseDTO.FilterStatsDTO filterStats = generateFilterStats(queryParam, pageData);

        return SalesOrderQueryResponseDTO.full(pageData, filterOptions, filterStats);
    }

    @Override
    @Transactional(readOnly = true)
    public List<FilterOptionDTO> getFilterOptions(String fieldName, String searchText, SalesOrderQueryParamDTO currentFilters) {
        log.info("Getting filter options for field: {}, searchText: {} (using stable legacy implementation)", fieldName, searchText);

        // 🔥 策略调整：销售订单继续使用稳定的旧架构
        // 新架构留给其他模块使用，确保销售订单功能的稳定性
        return getLegacyFilterOptions(fieldName, searchText, currentFilters);
    }

    /**
     * 原有的筛选选项获取逻辑（作为回退方案保留）
     */
    private List<FilterOptionDTO> getLegacyFilterOptions(String fieldName, String searchText, SalesOrderQueryParamDTO currentFilters) {
        log.debug("Using legacy filter options implementation for field: {}", fieldName);

        // 使用元数据驱动的方式获取筛选选项
        FilterField filterField = FilterField.findByFieldName(fieldName);
        if (filterField == null) {
            log.warn("Unsupported filter field: {}", fieldName);
            return List.of();
        }

        // 根据筛选类型选择不同的处理方式
        if (filterField.isStaticDictionary()) {
            return getStaticDictionaryOptions(filterField, searchText);
        } else if (filterField.isDynamicQuery()) {
            return getDynamicQueryOptions(filterField, searchText, currentFilters);
        } else {
            log.warn("Unknown filter type for field: {}", fieldName);
            return List.of();
        }
    }

    /**
     * 获取静态字典选项（已废弃，保留用于未来扩展）
     */
    private List<FilterOptionDTO> getStaticDictionaryOptions(FilterField filterField, String searchText) {
        log.debug("Getting static dictionary options for field: {}, dictionary: {}",
                 filterField.getFieldName(), filterField.getDictionaryCode());

        List<String> options = dictionaryService.getOptionsByCode(filterField.getDictionaryCode(), searchText);

        return options.stream()
                .map(FilterOptionDTO::of) // 不包含计数
                .collect(Collectors.toList());
    }

    /**
     * 获取动态查询选项（使用 JPA Metamodel 实现类型安全）
     * 不包含计数统计，仅返回去重的选项值
     */
    private List<FilterOptionDTO> getDynamicQueryOptions(FilterField filterField, String searchText, SalesOrderQueryParamDTO currentFilters) {
        log.debug("Getting dynamic query options for field: {}, entity field: {}, join source: {}",
                 filterField.getFieldName(), filterField.getEntityFieldName(), filterField.getJoinSource());

        // 🔥 新增：处理子查询类型的字段
        if (filterField.getJoinSource() == FilterField.JoinSource.SUBQUERY) {
            return getSubqueryFilterOptions(filterField, searchText, currentFilters);
        }

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<SalesOrder> orderRoot = query.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> itemJoin = orderRoot.join(SalesOrder_.items, JoinType.INNER);

        // 使用 JPA Metamodel 获取类型安全的字段路径
        Path<String> fieldPath = getMetamodelFieldPath(orderRoot, itemJoin, filterField);

        // 只选择字段值，不计算数量
        query.select(fieldPath).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(searchText)) {
            predicates.add(cb.like(fieldPath, "%" + searchText + "%"));
        }

        // 🔥 重构：使用统一级联筛选架构（排除当前字段）
        List<Predicate> cascadePredicates = CascadeFilterManager.buildCascadePredicates(
            cb, orderRoot, itemJoin, currentFilters, filterField.getFieldName(), entityManager);
        predicates.addAll(cascadePredicates);

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(fieldPath));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(50) // 限制选项数量
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of) // 不包含计数的构造方法
                .collect(Collectors.toList());
    }

    /**
     * 🔥 新增：获取子查询字段的筛选选项
     */
    private List<FilterOptionDTO> getSubqueryFilterOptions(FilterField filterField, String searchText, SalesOrderQueryParamDTO currentFilters) {
        log.debug("Getting subquery filter options for field: {}", filterField.getFieldName());

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        switch (filterField) {
            case SUPPLIER_NAME:
                return getSupplierNameOptions(cb, searchText, currentFilters);
            case PAPER_TYPE_NAME:
                return getPaperTypeNameOptions(cb, searchText, currentFilters);
            case PURCHASE_ORDER_NO:
                return getPurchaseOrderNoOptions(cb, searchText, currentFilters);
            case BINDING_SPECIFICATION:
                return getBindingSpecificationOptions(cb, searchText, currentFilters);
            case PRESS_SIZE_WIDTH:
                return getPressSizeWidthOptions(cb, searchText, currentFilters);
            case PROCESSES:
                return getProcessesOptions(cb, searchText, currentFilters);
            case SPECIFICATION:
                return getSpecificationOptions(cb, searchText, currentFilters);
            case PRODUCTION_SPECIFICATION:
                return getProductionSpecificationOptions(cb, searchText, currentFilters);
            default:
                log.warn("Unsupported subquery field: {}", filterField.getFieldName());
                return List.of();
        }
    }

    /**
     * 🔥 新增：获取采购供应商筛选选项
     */
    private List<FilterOptionDTO> getSupplierNameOptions(CriteriaBuilder cb, String searchText, SalesOrderQueryParamDTO currentFilters) {
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<PurchaseOrderItem> purchaseItemRoot = query.from(PurchaseOrderItem.class);
        Join<PurchaseOrderItem, PurchaseOrder> purchaseOrderJoin = purchaseItemRoot.join("purchaseOrder", JoinType.INNER);

        // 选择供应商名称，去重
        query.select(purchaseOrderJoin.get("supplierName")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(searchText)) {
            predicates.add(cb.like(purchaseOrderJoin.get("supplierName"), "%" + searchText + "%"));
        }

        // 过滤空值
        predicates.add(cb.isNotNull(purchaseOrderJoin.get("supplierName")));
        predicates.add(cb.notEqual(purchaseOrderJoin.get("supplierName"), ""));

        // 只查询有关联销售订单明细的采购订单
        predicates.add(cb.isNotNull(purchaseItemRoot.get("sourceSalesOrderItemId")));

        // 🔥 修复：添加级联筛选条件（排除当前字段）
        addSubqueryFilterConditions(cb, purchaseItemRoot, currentFilters, predicates, "supplierName");

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(purchaseOrderJoin.get("supplierName")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(50) // 限制选项数量
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 🔥 新增：获取纸板类别筛选选项
     */
    private List<FilterOptionDTO> getPaperTypeNameOptions(CriteriaBuilder cb, String searchText, SalesOrderQueryParamDTO currentFilters) {
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<PurchaseOrderItem> purchaseItemRoot = query.from(PurchaseOrderItem.class);

        // 选择纸板类别，去重
        query.select(purchaseItemRoot.get("paperBoardCategory")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(searchText)) {
            predicates.add(cb.like(purchaseItemRoot.get("paperBoardCategory"), "%" + searchText + "%"));
        }

        // 过滤空值
        predicates.add(cb.isNotNull(purchaseItemRoot.get("paperBoardCategory")));
        predicates.add(cb.notEqual(purchaseItemRoot.get("paperBoardCategory"), ""));

        // 只查询有关联销售订单明细的采购订单
        predicates.add(cb.isNotNull(purchaseItemRoot.get("sourceSalesOrderItemId")));

        // 🔥 修复：添加级联筛选条件（排除当前字段）
        addSubqueryFilterConditions(cb, purchaseItemRoot, currentFilters, predicates, "paperTypeName");

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(purchaseItemRoot.get("paperBoardCategory")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(50) // 限制选项数量
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 🔥 新增：获取采购单号筛选选项
     */
    private List<FilterOptionDTO> getPurchaseOrderNoOptions(CriteriaBuilder cb, String searchText, SalesOrderQueryParamDTO currentFilters) {
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<PurchaseOrderItem> purchaseItemRoot = query.from(PurchaseOrderItem.class);
        Join<PurchaseOrderItem, PurchaseOrder> purchaseOrderJoin = purchaseItemRoot.join("purchaseOrder", JoinType.INNER);

        // 选择采购单号，去重
        query.select(purchaseOrderJoin.get("purchaseOrderNo")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(searchText)) {
            predicates.add(cb.like(purchaseOrderJoin.get("purchaseOrderNo"), "%" + searchText + "%"));
        }

        // 过滤空值
        predicates.add(cb.isNotNull(purchaseOrderJoin.get("purchaseOrderNo")));
        predicates.add(cb.notEqual(purchaseOrderJoin.get("purchaseOrderNo"), ""));

        // 只查询有关联销售订单明细的采购订单
        predicates.add(cb.isNotNull(purchaseItemRoot.get("sourceSalesOrderItemId")));

        // 🔥 修复：添加级联筛选条件（排除当前字段）
        addSubqueryFilterConditions(cb, purchaseItemRoot, currentFilters, predicates, "purchaseOrderNo");

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(purchaseOrderJoin.get("purchaseOrderNo")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(50) // 限制选项数量
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 🔥 新增：获取合订规格筛选选项
     */
    private List<FilterOptionDTO> getBindingSpecificationOptions(CriteriaBuilder cb, String searchText, SalesOrderQueryParamDTO currentFilters) {
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<PurchaseOrderItem> purchaseItemRoot = query.from(PurchaseOrderItem.class);

        // 选择合订规格，去重
        query.select(purchaseItemRoot.get("bindingSpecification")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(searchText)) {
            predicates.add(cb.like(purchaseItemRoot.get("bindingSpecification"), "%" + searchText + "%"));
        }

        // 过滤空值
        predicates.add(cb.isNotNull(purchaseItemRoot.get("bindingSpecification")));
        predicates.add(cb.notEqual(purchaseItemRoot.get("bindingSpecification"), ""));

        // 只查询有关联销售订单明细的采购订单
        predicates.add(cb.isNotNull(purchaseItemRoot.get("sourceSalesOrderItemId")));

        // 🔥 修复：添加级联筛选条件（排除当前字段）
        addSubqueryFilterConditions(cb, purchaseItemRoot, currentFilters, predicates, "bindingSpecification");

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(purchaseItemRoot.get("bindingSpecification")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(50) // 限制选项数量
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 🔥 新增：获取压线尺寸筛选选项
     */
    private List<FilterOptionDTO> getPressSizeWidthOptions(CriteriaBuilder cb, String searchText, SalesOrderQueryParamDTO currentFilters) {
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<SalesOrderMaterial> materialRoot = query.from(SalesOrderMaterial.class);
        Join<SalesOrderMaterial, SalesOrderItem> materialItemJoin = materialRoot.join("orderItem", JoinType.INNER);

        // 选择压线尺寸，去重
        query.select(materialRoot.get("pressSizeWidth")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(searchText)) {
            predicates.add(cb.like(materialRoot.get("pressSizeWidth"), "%" + searchText + "%"));
        }

        // 过滤空值
        predicates.add(cb.isNotNull(materialRoot.get("pressSizeWidth")));
        predicates.add(cb.notEqual(materialRoot.get("pressSizeWidth"), ""));

        // 🔥 修复：添加级联筛选条件（排除当前字段）
        addMaterialSubqueryFilterConditions(cb, materialRoot, materialItemJoin, currentFilters, predicates, "pressSizeWidth");

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(materialRoot.get("pressSizeWidth")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(50) // 限制选项数量
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 🔥 新增：获取工序筛选选项
     */
    private List<FilterOptionDTO> getProcessesOptions(CriteriaBuilder cb, String searchText, SalesOrderQueryParamDTO currentFilters) {
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<SalesOrderProcess> processRoot = query.from(SalesOrderProcess.class);

        // 选择工序名称，去重
        query.select(processRoot.get("processName")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(searchText)) {
            predicates.add(cb.like(processRoot.get("processName"), "%" + searchText + "%"));
        }

        // 过滤空值和已删除的记录
        predicates.add(cb.isNotNull(processRoot.get("processName")));
        predicates.add(cb.notEqual(processRoot.get("processName"), ""));
        predicates.add(cb.equal(processRoot.get("isDeleted"), false));

        // 只查询有关联销售订单明细的工序
        predicates.add(cb.isNotNull(processRoot.get("orderItemId")));

        // 🔥 修复：添加级联筛选条件（排除当前字段）
        addProcessSubqueryFilterConditions(cb, processRoot, currentFilters, predicates, "processes");

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(processRoot.get("processName")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(50) // 限制选项数量
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 🔥 新增：获取规格筛选选项（动态构建字段）
     */
    private List<FilterOptionDTO> getSpecificationOptions(CriteriaBuilder cb, String searchText, SalesOrderQueryParamDTO currentFilters) {
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<SalesOrder> orderRoot = query.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> itemJoin = orderRoot.join("items", JoinType.INNER);

        // 动态构建规格字段：长×宽×高 单位
        Expression<String> specificationExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
            cb.coalesce(itemJoin.get("length"), cb.literal(BigDecimal.ZERO)).as(String.class),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("width"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("height"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal(" ")),
            cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

        query.select(specificationExpr).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(searchText)) {
            predicates.add(cb.like(specificationExpr, "%" + searchText + "%"));
        }

        // 过滤空值和零值
        predicates.add(cb.isNotNull(itemJoin.get("length")));
        predicates.add(cb.isNotNull(itemJoin.get("width")));
        predicates.add(cb.isNotNull(itemJoin.get("height")));
        predicates.add(cb.or(
            cb.notEqual(itemJoin.get("length"), BigDecimal.ZERO),
            cb.notEqual(itemJoin.get("width"), BigDecimal.ZERO),
            cb.notEqual(itemJoin.get("height"), BigDecimal.ZERO)
        ));

        // 🔥 重构：使用统一级联筛选架构（排除当前字段）
        List<Predicate> cascadePredicates = CascadeFilterManager.buildCascadePredicates(
            cb, orderRoot, itemJoin, currentFilters, "specification", entityManager);
        predicates.addAll(cascadePredicates);

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(specificationExpr));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(50) // 限制选项数量
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .filter(spec -> !spec.trim().equals("0×0×0") && !spec.trim().equals("0×0×0 "))
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 🔥 新增：获取生产规格筛选选项（动态构建字段）
     * 🔥 修复：实现与查询结果一致的回退机制逻辑
     */
    private List<FilterOptionDTO> getProductionSpecificationOptions(CriteriaBuilder cb, String searchText, SalesOrderQueryParamDTO currentFilters) {
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<SalesOrder> orderRoot = query.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> itemJoin = orderRoot.join("items", JoinType.INNER);

        // 🔥 修复：实现与查询结果一致的回退机制
        // 构建生产规格表达式：优先使用生产尺寸，为空时使用产品尺寸
        Expression<String> productionSpecExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
            cb.coalesce(itemJoin.get("productionLength"), cb.literal(BigDecimal.ZERO)).as(String.class),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("productionWidth"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("productionHeight"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal(" ")),
            cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

        // 构建产品规格表达式（回退选项）
        Expression<String> productSpecExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
            cb.coalesce(itemJoin.get("length"), cb.literal(BigDecimal.ZERO)).as(String.class),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("width"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal("×")),
            cb.coalesce(itemJoin.get("height"), cb.literal(BigDecimal.ZERO)).as(String.class)),
            cb.literal(" ")),
            cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

        // 🔥 关键修复：实现回退机制 - 优先使用生产规格，为空时使用产品规格
        // 判断生产规格是否有效（排除各种无效格式）
        Predicate hasValidProductionSpec = cb.and(
            cb.isNotNull(itemJoin.get("productionLength")),
            cb.isNotNull(itemJoin.get("productionWidth")),
            cb.isNotNull(itemJoin.get("productionHeight")),
            cb.or(
                cb.notEqual(itemJoin.get("productionLength"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("productionWidth"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("productionHeight"), BigDecimal.ZERO)
            )
        );

        // 使用CASE WHEN实现回退逻辑
        Expression<String> finalSpecExpr = cb.<String>selectCase()
            .when(hasValidProductionSpec, productionSpecExpr)
            .otherwise(productSpecExpr);

        query.select(finalSpecExpr).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 🔥 修复：搜索文本条件应该基于最终的规格表达式
        if (StringUtils.hasText(searchText)) {
            predicates.add(cb.like(finalSpecExpr, "%" + searchText + "%"));
        }

        // 🔥 修复：优化过滤条件，实现与查询结果一致的逻辑
        // 至少有生产尺寸或产品尺寸其中之一有效
        Predicate hasValidProductionDimensions = cb.and(
            cb.isNotNull(itemJoin.get("productionLength")),
            cb.isNotNull(itemJoin.get("productionWidth")),
            cb.isNotNull(itemJoin.get("productionHeight")),
            cb.or(
                cb.notEqual(itemJoin.get("productionLength"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("productionWidth"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("productionHeight"), BigDecimal.ZERO)
            )
        );

        Predicate hasValidProductDimensions = cb.and(
            cb.isNotNull(itemJoin.get("length")),
            cb.isNotNull(itemJoin.get("width")),
            cb.isNotNull(itemJoin.get("height")),
            cb.or(
                cb.notEqual(itemJoin.get("length"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("width"), BigDecimal.ZERO),
                cb.notEqual(itemJoin.get("height"), BigDecimal.ZERO)
            )
        );

        // 至少有生产尺寸或产品尺寸其中之一有效
        predicates.add(cb.or(hasValidProductionDimensions, hasValidProductDimensions));

        // 🔥 重构：使用统一级联筛选架构（排除当前字段）
        List<Predicate> cascadePredicates = CascadeFilterManager.buildCascadePredicates(
            cb, orderRoot, itemJoin, currentFilters, "productionSpecification", entityManager);
        predicates.addAll(cascadePredicates);

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 🔥 修复：按最终规格表达式排序
        query.orderBy(cb.asc(finalSpecExpr));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(50) // 限制选项数量
                .getResultList();

        // 🔥 修复：过滤逻辑，排除无效的规格值（包括各种零值格式）
        return results.stream()
                .filter(StringUtils::hasText)
                .filter(spec -> !spec.trim().isEmpty()) // 排除空字符串
                .filter(spec -> {
                    // 排除各种零值格式：0×0×0, 0.0×0.0×0.0, 0.00×0.00×0.00 等
                    String trimmed = spec.trim();
                    return !trimmed.matches("^0(\\.0+)?×0(\\.0+)?×0(\\.0+)?( .*)?$");
                })
                .distinct() // 确保去重
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 使用 JPA Metamodel 获取类型安全的字段路径
     */
    private Path<String> getMetamodelFieldPath(Root<SalesOrder> orderRoot, Join<SalesOrder, SalesOrderItem> itemJoin, FilterField filterField) {
        // 根据字段名称和连接源返回对应的 Metamodel 路径
        return switch (filterField) {
            // 主表字段
            case CUSTOMER_NAME -> orderRoot.get(SalesOrder_.customerName);
            case SALES_PERSON -> orderRoot.get(SalesOrder_.salesPerson);
            case ORDER_NO -> orderRoot.get(SalesOrder_.orderNo);

            // 明细表字段
            case PRODUCT_NAME -> itemJoin.get(SalesOrderItem_.productName);
            case PRODUCTION_ORDER_NO -> itemJoin.get(SalesOrderItem_.productionOrderNo);
            case CUSTOMER_ORDER_NO -> itemJoin.get(SalesOrderItem_.customerOrderNo);
            case CUSTOMER_PRODUCT_CODE -> itemJoin.get(SalesOrderItem_.customerProductCode);
            case PAPER_TYPE -> itemJoin.get(SalesOrderItem_.paperType);
            case PRODUCTION_PAPER_TYPE -> itemJoin.get(SalesOrderItem_.productionPaperType);
            case PROCESS_REQUIREMENTS -> itemJoin.get(SalesOrderItem_.processRequirements);
            case BOX_TYPE -> itemJoin.get(SalesOrderItem_.boxType);
            case CORRUGATION_TYPE -> itemJoin.get(SalesOrderItem_.corrugationType);
            case CONNECTION_METHOD -> itemJoin.get(SalesOrderItem_.connectionMethod);
            case STAPLE_POSITION -> itemJoin.get(SalesOrderItem_.staplePosition);
            case UNIT -> itemJoin.get(SalesOrderItem_.unit);
            case CURRENCY -> itemJoin.get(SalesOrderItem_.currency);

            // 如果没有匹配的 Metamodel 字段，回退到字符串方式
            default -> {
                log.warn("No metamodel mapping found for field: {}, falling back to string-based approach", filterField.getFieldName());
                yield filterField.getJoinSource() == FilterField.JoinSource.ROOT
                    ? orderRoot.get(filterField.getEntityFieldName())
                    : itemJoin.get(filterField.getEntityFieldName());
            }
        };
    }

    /**
     * 生成筛选选项
     */
    private Map<String, List<FilterOptionDTO>> generateFilterOptions(SalesOrderQueryParamDTO queryParam) {
        Map<String, List<FilterOptionDTO>> filterOptions = new HashMap<>();

        List<String> requestedFields = queryParam.getFilterOptionFields();
        if (requestedFields == null || requestedFields.isEmpty()) {
            // 如果没有指定字段，返回默认的核心字段选项
            requestedFields = List.of("customerName", "productName", "salesPerson");
        }

        for (String fieldName : requestedFields) {
            List<FilterOptionDTO> options = getFilterOptions(fieldName, null, queryParam);
            if (!options.isEmpty()) {
                filterOptions.put(fieldName, options);
            }
        }

        return filterOptions;
    }

    /**
     * 生成筛选统计信息
     */
    private SalesOrderQueryResponseDTO.FilterStatsDTO generateFilterStats(
            SalesOrderQueryParamDTO queryParam, PageResponse<SalesOrderQueryResultDTO> pageData) {

        // 计算活跃筛选条件数
        int activeFilterCount = countActiveFilters(queryParam);

        // 获取总记录数（无筛选条件）
        long totalRecords = getTotalRecordsCount();

        return SalesOrderQueryResponseDTO.FilterStatsDTO.builder()
                .totalRecords(totalRecords)
                .filteredRecords(pageData.getTotalElements())
                .activeFilterCount(activeFilterCount)
                .build();
    }

    /**
     * 计算活跃筛选条件数
     */
    private int countActiveFilters(SalesOrderQueryParamDTO queryParam) {
        int count = 0;

        // 文本筛选（单值）
        if (StringUtils.hasText(queryParam.getFilterProductionOrderNo())) count++;
        if (StringUtils.hasText(queryParam.getFilterOrderNo())) count++;
        if (StringUtils.hasText(queryParam.getFilterCustomerOrderNo())) count++;
        if (StringUtils.hasText(queryParam.getFilterCustomerProductCode())) count++;

        // 选择筛选（多值）
        if (queryParam.getFilterCustomerNames() != null && !queryParam.getFilterCustomerNames().isEmpty()) count++;
        if (queryParam.getFilterProductNames() != null && !queryParam.getFilterProductNames().isEmpty()) count++;
        if (queryParam.getFilterSalesPersons() != null && !queryParam.getFilterSalesPersons().isEmpty()) count++;

        // 🔥 新增：单号类字段的多值筛选
        if (queryParam.getFilterProductionOrderNos() != null && !queryParam.getFilterProductionOrderNos().isEmpty()) count++;
        if (queryParam.getFilterOrderNos() != null && !queryParam.getFilterOrderNos().isEmpty()) count++;
        if (queryParam.getFilterCustomerOrderNos() != null && !queryParam.getFilterCustomerOrderNos().isEmpty()) count++;
        if (queryParam.getFilterCustomerProductCodes() != null && !queryParam.getFilterCustomerProductCodes().isEmpty()) count++;
        if (queryParam.getFilterPurchaseOrderNos() != null && !queryParam.getFilterPurchaseOrderNos().isEmpty()) count++;

        // 🔥 新增：规格类字段的多值筛选
        if (queryParam.getFilterBindingSpecifications() != null && !queryParam.getFilterBindingSpecifications().isEmpty()) count++;
        if (queryParam.getFilterSpecifications() != null && !queryParam.getFilterSpecifications().isEmpty()) count++;
        if (queryParam.getFilterProductionSpecifications() != null && !queryParam.getFilterProductionSpecifications().isEmpty()) count++;
        if (queryParam.getFilterPressSizeWidths() != null && !queryParam.getFilterPressSizeWidths().isEmpty()) count++;

        // 🔥 新增：工艺和人员字段的多值筛选
        if (queryParam.getFilterPaperTypes() != null && !queryParam.getFilterPaperTypes().isEmpty()) count++;
        if (queryParam.getFilterProcessRequirements() != null && !queryParam.getFilterProcessRequirements().isEmpty()) count++;
        if (queryParam.getFilterProcesses() != null && !queryParam.getFilterProcesses().isEmpty()) count++;
        if (queryParam.getFilterReceivingUnits() != null && !queryParam.getFilterReceivingUnits().isEmpty()) count++;
        if (queryParam.getFilterReceivingPersons() != null && !queryParam.getFilterReceivingPersons().isEmpty()) count++;

        // 🔥 新增：采购相关字段的多值筛选
        if (queryParam.getFilterSupplierNames() != null && !queryParam.getFilterSupplierNames().isEmpty()) count++;
        if (queryParam.getFilterPaperTypeNames() != null && !queryParam.getFilterPaperTypeNames().isEmpty()) count++;

        // 🔥 新增：字典类字段的多值筛选
        if (queryParam.getFilterBoxTypes() != null && !queryParam.getFilterBoxTypes().isEmpty()) count++;
        if (queryParam.getFilterCorrugationTypes() != null && !queryParam.getFilterCorrugationTypes().isEmpty()) count++;

        // 🔥 新增：生产和工艺相关字段的多值筛选
        if (queryParam.getFilterProductionPaperTypes() != null && !queryParam.getFilterProductionPaperTypes().isEmpty()) count++;
        if (queryParam.getFilterConnectionMethods() != null && !queryParam.getFilterConnectionMethods().isEmpty()) count++;
        if (queryParam.getFilterUnits() != null && !queryParam.getFilterUnits().isEmpty()) count++;
        if (queryParam.getFilterCurrencies() != null && !queryParam.getFilterCurrencies().isEmpty()) count++;
        if (queryParam.getFilterTaxRates() != null && !queryParam.getFilterTaxRates().isEmpty()) count++;

        // 🔥 新增：人员和地址相关字段的多值筛选
        if (queryParam.getFilterReceiverPhones() != null && !queryParam.getFilterReceiverPhones().isEmpty()) count++;
        if (queryParam.getFilterCreatedBys() != null && !queryParam.getFilterCreatedBys().isEmpty()) count++;

        // 🔥 新增：文本字段的筛选
        if (queryParam.getFilterProductionRemarks() != null && !queryParam.getFilterProductionRemarks().isEmpty()) count++;
        if (queryParam.getFilterRemarks() != null && !queryParam.getFilterRemarks().isEmpty()) count++;
        if (queryParam.getFilterReceivingAddresses() != null && !queryParam.getFilterReceivingAddresses().isEmpty()) count++;

        // 🔥 新增：特殊标识字段的筛选
        if (queryParam.getFilterIsSpecialPrices() != null && !queryParam.getFilterIsSpecialPrices().isEmpty()) count++;

        // 数字范围筛选
        if (queryParam.getFilterQuantityMin() != null || queryParam.getFilterQuantityMax() != null) count++;
        if (queryParam.getFilterTotalAmountMin() != null || queryParam.getFilterTotalAmountMax() != null) count++;

        // 🔥 新增：数字范围筛选字段
        if (queryParam.getFilterSpareQuantityMin() != null || queryParam.getFilterSpareQuantityMax() != null) count++;
        if (queryParam.getFilterPriceMin() != null || queryParam.getFilterPriceMax() != null) count++;
        if (queryParam.getFilterPurchasedQuantityMin() != null || queryParam.getFilterPurchasedQuantityMax() != null) count++;
        if (queryParam.getFilterPaperQuotationMin() != null || queryParam.getFilterPaperQuotationMax() != null) count++;
        if (queryParam.getFilterUnitWeightMin() != null || queryParam.getFilterUnitWeightMax() != null) count++;
        if (queryParam.getFilterTotalWeightMin() != null || queryParam.getFilterTotalWeightMax() != null) count++;
        if (queryParam.getFilterProductAreaMin() != null || queryParam.getFilterProductAreaMax() != null) count++;
        if (queryParam.getFilterTotalAreaMin() != null || queryParam.getFilterTotalAreaMax() != null) count++;
        if (queryParam.getFilterProductVolumeMin() != null || queryParam.getFilterProductVolumeMax() != null) count++;
        if (queryParam.getFilterTotalVolumeMin() != null || queryParam.getFilterTotalVolumeMax() != null) count++;

        // 日期范围筛选
        if (queryParam.getFilterOrderDateStart() != null || queryParam.getFilterOrderDateEnd() != null) count++;
        if (queryParam.getFilterDeliveryDateStart() != null || queryParam.getFilterDeliveryDateEnd() != null) count++;

        // 🔥 新增：日期范围筛选字段
        if (queryParam.getFilterCreatedTimeStart() != null || queryParam.getFilterCreatedTimeEnd() != null) count++;

        return count;
    }

    /**
     * 获取总记录数（无筛选条件）
     */
    private long getTotalRecordsCount() {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<SalesOrder> orderRoot = countQuery.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> itemJoin = orderRoot.join("items", JoinType.INNER);

        countQuery.select(cb.count(itemJoin));
        return entityManager.createQuery(countQuery).getSingleResult();
    }







    /**
     * 🔥 新增：统一级联筛选管理器
     * 负责处理所有字段间的级联筛选关系
     */
    private static class CascadeFilterManager {

        /**
         * 字段类别枚举
         */
        public enum FieldCategory {
            MAIN_TABLE,        // 主表字段：customerName, salesPerson, orderNo
            ITEM_TABLE,        // 明细表字段：productName, productionOrderNo
            PURCHASE_SUBQUERY, // 采购子查询：purchaseOrderNo, supplierName, paperTypeName
            MATERIAL_SUBQUERY, // 用料子查询：pressSizeWidth
            PROCESS_SUBQUERY,  // 工序子查询：processes
            DYNAMIC_FIELD      // 动态字段：specification, productionSpecification
        }

        /**
         * 字段分类映射
         */
        private static final Map<String, FieldCategory> FIELD_CATEGORIES;

        static {
            Map<String, FieldCategory> categories = new HashMap<>();

            // 主表字段
            categories.put("customerName", FieldCategory.MAIN_TABLE);
            categories.put("salesPerson", FieldCategory.MAIN_TABLE);
            categories.put("orderNo", FieldCategory.MAIN_TABLE);

            // 明细表字段
            categories.put("productName", FieldCategory.ITEM_TABLE);
            categories.put("productionOrderNo", FieldCategory.ITEM_TABLE);
            categories.put("customerOrderNo", FieldCategory.ITEM_TABLE);
            categories.put("customerProductCode", FieldCategory.ITEM_TABLE);
            categories.put("paperType", FieldCategory.ITEM_TABLE);
            categories.put("productionPaperType", FieldCategory.ITEM_TABLE);

            // 采购子查询字段
            categories.put("purchaseOrderNo", FieldCategory.PURCHASE_SUBQUERY);
            categories.put("supplierName", FieldCategory.PURCHASE_SUBQUERY);
            categories.put("paperTypeName", FieldCategory.PURCHASE_SUBQUERY);
            categories.put("bindingSpecification", FieldCategory.PURCHASE_SUBQUERY);

            // 用料子查询字段
            categories.put("pressSizeWidth", FieldCategory.MATERIAL_SUBQUERY);

            // 工序子查询字段
            categories.put("processes", FieldCategory.PROCESS_SUBQUERY);

            // 动态字段
            categories.put("specification", FieldCategory.DYNAMIC_FIELD);
            categories.put("productionSpecification", FieldCategory.DYNAMIC_FIELD);

            FIELD_CATEGORIES = Map.copyOf(categories);
        }

        /**
         * 获取字段类别
         */
        public static FieldCategory getFieldCategory(String fieldName) {
            return FIELD_CATEGORIES.getOrDefault(fieldName, FieldCategory.ITEM_TABLE);
        }

        /**
         * 检查是否有采购相关筛选条件
         */
        public static boolean hasPurchaseFilters(SalesOrderQueryParamDTO filters) {
            if (filters == null) return false;
            return (filters.getFilterPurchaseOrderNos() != null && !filters.getFilterPurchaseOrderNos().isEmpty()) ||
                   (filters.getFilterSupplierNames() != null && !filters.getFilterSupplierNames().isEmpty()) ||
                   (filters.getFilterPaperTypeNames() != null && !filters.getFilterPaperTypeNames().isEmpty()) ||
                   (filters.getFilterBindingSpecifications() != null && !filters.getFilterBindingSpecifications().isEmpty());
        }

        /**
         * 检查是否有工序相关筛选条件
         */
        public static boolean hasProcessFilters(SalesOrderQueryParamDTO filters) {
            if (filters == null) return false;
            return (filters.getFilterProcesses() != null && !filters.getFilterProcesses().isEmpty());
        }

        /**
         * 检查是否有用料相关筛选条件
         */
        public static boolean hasMaterialFilters(SalesOrderQueryParamDTO filters) {
            if (filters == null) return false;
            return (filters.getFilterPressSizeWidths() != null && !filters.getFilterPressSizeWidths().isEmpty());
        }

        /**
         * 检查是否有主表筛选条件
         */
        public static boolean hasMainTableFilters(SalesOrderQueryParamDTO filters) {
            if (filters == null) return false;
            return (filters.getFilterCustomerNames() != null && !filters.getFilterCustomerNames().isEmpty()) ||
                   (filters.getFilterSalesPersons() != null && !filters.getFilterSalesPersons().isEmpty()) ||
                   (filters.getFilterOrderNos() != null && !filters.getFilterOrderNos().isEmpty());
        }

        /**
         * 检查是否有明细表筛选条件
         */
        public static boolean hasItemTableFilters(SalesOrderQueryParamDTO filters) {
            if (filters == null) return false;
            return (filters.getFilterProductNames() != null && !filters.getFilterProductNames().isEmpty()) ||
                   (filters.getFilterProductionOrderNos() != null && !filters.getFilterProductionOrderNos().isEmpty()) ||
                   (filters.getFilterCustomerOrderNos() != null && !filters.getFilterCustomerOrderNos().isEmpty()) ||
                   (filters.getFilterCustomerProductCodes() != null && !filters.getFilterCustomerProductCodes().isEmpty()) ||
                   (filters.getFilterPaperTypes() != null && !filters.getFilterPaperTypes().isEmpty()) ||
                   (filters.getFilterProductionPaperTypes() != null && !filters.getFilterProductionPaperTypes().isEmpty());
        }

        /**
         * 🔥 新增：检查是否有动态字段筛选条件
         */
        public static boolean hasDynamicFieldFilters(SalesOrderQueryParamDTO filters) {
            if (filters == null) return false;
            return (filters.getFilterSpecifications() != null && !filters.getFilterSpecifications().isEmpty()) ||
                   (filters.getFilterProductionSpecifications() != null && !filters.getFilterProductionSpecifications().isEmpty());
        }

        /**
         * 🔥 核心方法：统一构建级联筛选条件
         * 根据目标字段类别，智能应用所有相关的筛选条件
         */
        public static List<Predicate> buildCascadePredicates(
                CriteriaBuilder cb,
                Root<SalesOrder> orderRoot,
                Join<SalesOrder, SalesOrderItem> itemJoin,
                SalesOrderQueryParamDTO filters,
                String excludeField,
                EntityManager entityManager) {

            List<Predicate> predicates = new ArrayList<>();

            if (filters == null) {
                return predicates;
            }

            FieldCategory targetCategory = getFieldCategory(excludeField);

            // 🔥 修复：总是应用主表筛选条件，让内部方法处理字段级排除
            // 原逻辑错误：当目标字段是主表字段时，完全跳过主表筛选条件
            // 修复逻辑：总是应用主表筛选条件，addMainTablePredicates内部会排除当前字段
            if (hasMainTableFilters(filters)) {
                addMainTablePredicates(cb, orderRoot, filters, predicates, excludeField);
            }

            // 🔥 修复：总是应用明细表筛选条件，让内部方法处理字段级排除
            // 原逻辑错误：当目标字段是明细表字段时，完全跳过明细表筛选条件
            // 修复逻辑：总是应用明细表筛选条件，addItemTablePredicates内部会排除当前字段
            if (hasItemTableFilters(filters)) {
                addItemTablePredicates(cb, itemJoin, filters, predicates, excludeField);
            }

            // 3. 应用采购子查询筛选条件（总是应用，但排除当前字段）
            if (hasPurchaseFilters(filters)) {
                addPurchaseSubqueryPredicates(cb, itemJoin, filters, predicates, excludeField);
            }

            // 4. 应用工序子查询筛选条件（总是应用，但排除当前字段）
            if (hasProcessFilters(filters)) {
                addProcessSubqueryPredicates(cb, itemJoin, filters, predicates, excludeField);
            }

            // 5. 应用用料子查询筛选条件（总是应用，但排除当前字段）
            if (hasMaterialFilters(filters)) {
                addMaterialSubqueryPredicates(cb, itemJoin, filters, predicates, excludeField);
            }

            // 🔥 新增：6. 应用动态字段筛选条件（总是应用，但排除当前字段）
            if (hasDynamicFieldFilters(filters)) {
                addDynamicFieldPredicates(cb, itemJoin, filters, predicates, excludeField);
            }

            return predicates;
        }

        /**
         * 添加主表筛选条件
         */
        private static void addMainTablePredicates(
                CriteriaBuilder cb,
                Root<SalesOrder> orderRoot,
                SalesOrderQueryParamDTO filters,
                List<Predicate> predicates,
                String excludeField) {

            if (!excludeField.equals("customerName") && filters.getFilterCustomerNames() != null && !filters.getFilterCustomerNames().isEmpty()) {
                predicates.add(orderRoot.get("customerName").in(filters.getFilterCustomerNames()));
            }

            if (!excludeField.equals("salesPerson") && filters.getFilterSalesPersons() != null && !filters.getFilterSalesPersons().isEmpty()) {
                predicates.add(orderRoot.get("salesPerson").in(filters.getFilterSalesPersons()));
            }

            if (!excludeField.equals("orderNo") && filters.getFilterOrderNos() != null && !filters.getFilterOrderNos().isEmpty()) {
                predicates.add(orderRoot.get("orderNo").in(filters.getFilterOrderNos()));
            }
        }

        /**
         * 添加明细表筛选条件
         */
        private static void addItemTablePredicates(
                CriteriaBuilder cb,
                Join<SalesOrder, SalesOrderItem> itemJoin,
                SalesOrderQueryParamDTO filters,
                List<Predicate> predicates,
                String excludeField) {

            if (!excludeField.equals("productName") && filters.getFilterProductNames() != null && !filters.getFilterProductNames().isEmpty()) {
                predicates.add(itemJoin.get("productName").in(filters.getFilterProductNames()));
            }

            if (!excludeField.equals("productionOrderNo") && filters.getFilterProductionOrderNos() != null && !filters.getFilterProductionOrderNos().isEmpty()) {
                predicates.add(itemJoin.get("productionOrderNo").in(filters.getFilterProductionOrderNos()));
            }

            if (!excludeField.equals("customerOrderNo") && filters.getFilterCustomerOrderNos() != null && !filters.getFilterCustomerOrderNos().isEmpty()) {
                predicates.add(itemJoin.get("customerOrderNo").in(filters.getFilterCustomerOrderNos()));
            }

            if (!excludeField.equals("customerProductCode") && filters.getFilterCustomerProductCodes() != null && !filters.getFilterCustomerProductCodes().isEmpty()) {
                predicates.add(itemJoin.get("customerProductCode").in(filters.getFilterCustomerProductCodes()));
            }

            if (!excludeField.equals("paperType") && filters.getFilterPaperTypes() != null && !filters.getFilterPaperTypes().isEmpty()) {
                predicates.add(itemJoin.get("paperType").in(filters.getFilterPaperTypes()));
            }

            if (!excludeField.equals("productionPaperType") && filters.getFilterProductionPaperTypes() != null && !filters.getFilterProductionPaperTypes().isEmpty()) {
                predicates.add(itemJoin.get("productionPaperType").in(filters.getFilterProductionPaperTypes()));
            }
        }

        /**
         * 添加采购子查询筛选条件
         */
        private static void addPurchaseSubqueryPredicates(
                CriteriaBuilder cb,
                Join<SalesOrder, SalesOrderItem> itemJoin,
                SalesOrderQueryParamDTO filters,
                List<Predicate> predicates,
                String excludeField) {

            // 创建采购订单子查询
            Subquery<String> purchaseSubquery = cb.createQuery().subquery(String.class);
            Root<PurchaseOrderItem> purchaseItemRoot = purchaseSubquery.from(PurchaseOrderItem.class);
            Join<PurchaseOrderItem, PurchaseOrder> purchaseOrderJoin = purchaseItemRoot.join("purchaseOrder", JoinType.INNER);

            purchaseSubquery.select(purchaseItemRoot.get("sourceSalesOrderItemId"));

            List<Predicate> purchasePredicates = new ArrayList<>();

            // 只查询有关联销售订单明细的采购订单
            purchasePredicates.add(cb.isNotNull(purchaseItemRoot.get("sourceSalesOrderItemId")));

            if (!excludeField.equals("purchaseOrderNo") && filters.getFilterPurchaseOrderNos() != null && !filters.getFilterPurchaseOrderNos().isEmpty()) {
                purchasePredicates.add(purchaseOrderJoin.get("purchaseOrderNo").in(filters.getFilterPurchaseOrderNos()));
            }

            if (!excludeField.equals("supplierName") && filters.getFilterSupplierNames() != null && !filters.getFilterSupplierNames().isEmpty()) {
                purchasePredicates.add(purchaseOrderJoin.get("supplierName").in(filters.getFilterSupplierNames()));
            }

            if (!excludeField.equals("paperTypeName") && filters.getFilterPaperTypeNames() != null && !filters.getFilterPaperTypeNames().isEmpty()) {
                purchasePredicates.add(purchaseItemRoot.get("paperBoardCategory").in(filters.getFilterPaperTypeNames()));
            }

            if (!excludeField.equals("bindingSpecification") && filters.getFilterBindingSpecifications() != null && !filters.getFilterBindingSpecifications().isEmpty()) {
                purchasePredicates.add(purchaseItemRoot.get("bindingSpecification").in(filters.getFilterBindingSpecifications()));
            }

            if (!purchasePredicates.isEmpty()) {
                purchaseSubquery.where(cb.and(purchasePredicates.toArray(new Predicate[0])));
                predicates.add(itemJoin.get("id").in(purchaseSubquery));
            }
        }

        /**
         * 添加工序子查询筛选条件
         */
        private static void addProcessSubqueryPredicates(
                CriteriaBuilder cb,
                Join<SalesOrder, SalesOrderItem> itemJoin,
                SalesOrderQueryParamDTO filters,
                List<Predicate> predicates,
                String excludeField) {

            if (!excludeField.equals("processes") && filters.getFilterProcesses() != null && !filters.getFilterProcesses().isEmpty()) {
                // 创建工序子查询
                Subquery<String> processSubquery = cb.createQuery().subquery(String.class);
                Root<SalesOrderProcess> processRoot = processSubquery.from(SalesOrderProcess.class);

                processSubquery.select(processRoot.get("orderItemId"));

                List<Predicate> processPredicates = new ArrayList<>();
                processPredicates.add(cb.isNotNull(processRoot.get("orderItemId")));
                processPredicates.add(cb.equal(processRoot.get("isDeleted"), false));
                processPredicates.add(processRoot.get("processName").in(filters.getFilterProcesses()));

                processSubquery.where(cb.and(processPredicates.toArray(new Predicate[0])));
                predicates.add(itemJoin.get("id").in(processSubquery));
            }
        }

        /**
         * 添加用料子查询筛选条件
         */
        private static void addMaterialSubqueryPredicates(
                CriteriaBuilder cb,
                Join<SalesOrder, SalesOrderItem> itemJoin,
                SalesOrderQueryParamDTO filters,
                List<Predicate> predicates,
                String excludeField) {

            if (!excludeField.equals("pressSizeWidth") && filters.getFilterPressSizeWidths() != null && !filters.getFilterPressSizeWidths().isEmpty()) {
                // 创建用料子查询
                Subquery<String> materialSubquery = cb.createQuery().subquery(String.class);
                Root<SalesOrderMaterial> materialRoot = materialSubquery.from(SalesOrderMaterial.class);
                Join<SalesOrderMaterial, SalesOrderItem> materialItemJoin = materialRoot.join("orderItem", JoinType.INNER);

                materialSubquery.select(materialItemJoin.get("id"));

                List<Predicate> materialPredicates = new ArrayList<>();
                materialPredicates.add(cb.isNotNull(materialRoot.get("pressSizeWidth")));
                materialPredicates.add(materialRoot.get("pressSizeWidth").in(filters.getFilterPressSizeWidths()));

                materialSubquery.where(cb.and(materialPredicates.toArray(new Predicate[0])));
                predicates.add(itemJoin.get("id").in(materialSubquery));
            }
        }

        /**
         * 🔥 新增：添加动态字段筛选条件
         * 处理specification和productionSpecification等动态构建字段的筛选
         */
        private static void addDynamicFieldPredicates(
                CriteriaBuilder cb,
                Join<SalesOrder, SalesOrderItem> itemJoin,
                SalesOrderQueryParamDTO filters,
                List<Predicate> predicates,
                String excludeField) {

            // 处理规格字段筛选（排除当前字段）
            if (!excludeField.equals("specification") && filters.getFilterSpecifications() != null && !filters.getFilterSpecifications().isEmpty()) {
                // 规格字段需要动态构建（长×宽×高 单位格式）
                List<Predicate> specPredicates = new ArrayList<>();
                for (String spec : filters.getFilterSpecifications()) {
                    Expression<String> specExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
                        cb.coalesce(itemJoin.get("length"), cb.literal(BigDecimal.ZERO)).as(String.class),
                        cb.literal("×")),
                        cb.coalesce(itemJoin.get("width"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                        cb.literal("×")),
                        cb.coalesce(itemJoin.get("height"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                        cb.literal(" ")),
                        cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

                    specPredicates.add(cb.like(specExpr, "%" + spec + "%"));
                }
                if (!specPredicates.isEmpty()) {
                    predicates.add(cb.or(specPredicates.toArray(new Predicate[0])));
                }
            }

            // 🔥 修复：处理生产规格字段筛选（排除当前字段）- 实现回退机制
            if (!excludeField.equals("productionSpecification") && filters.getFilterProductionSpecifications() != null && !filters.getFilterProductionSpecifications().isEmpty()) {
                // 生产规格字段需要实现与查询结果一致的回退机制
                List<Predicate> prodSpecPredicates = new ArrayList<>();
                for (String spec : filters.getFilterProductionSpecifications()) {
                    // 构建生产规格表达式
                    Expression<String> productionSpecExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
                        cb.coalesce(itemJoin.get("productionLength"), cb.literal(BigDecimal.ZERO)).as(String.class),
                        cb.literal("×")),
                        cb.coalesce(itemJoin.get("productionWidth"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                        cb.literal("×")),
                        cb.coalesce(itemJoin.get("productionHeight"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                        cb.literal(" ")),
                        cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

                    // 构建产品规格表达式（回退选项）
                    Expression<String> productSpecExpr = cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(cb.concat(
                        cb.coalesce(itemJoin.get("length"), cb.literal(BigDecimal.ZERO)).as(String.class),
                        cb.literal("×")),
                        cb.coalesce(itemJoin.get("width"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                        cb.literal("×")),
                        cb.coalesce(itemJoin.get("height"), cb.literal(BigDecimal.ZERO)).as(String.class)),
                        cb.literal(" ")),
                        cb.coalesce(itemJoin.get("sizeUnit"), cb.literal("")));

                    // 🔥 修复：判断生产规格是否有效（基于原始字段值而非表达式）
                    Predicate hasValidProductionSpec = cb.and(
                        cb.isNotNull(itemJoin.get("productionLength")),
                        cb.isNotNull(itemJoin.get("productionWidth")),
                        cb.isNotNull(itemJoin.get("productionHeight")),
                        cb.or(
                            cb.notEqual(itemJoin.get("productionLength"), BigDecimal.ZERO),
                            cb.notEqual(itemJoin.get("productionWidth"), BigDecimal.ZERO),
                            cb.notEqual(itemJoin.get("productionHeight"), BigDecimal.ZERO)
                        )
                    );

                    // 实现回退机制：优先使用生产规格，为空时使用产品规格
                    Expression<String> finalSpecExpr = cb.<String>selectCase()
                        .when(hasValidProductionSpec, productionSpecExpr)
                        .otherwise(productSpecExpr);

                    prodSpecPredicates.add(cb.like(finalSpecExpr, "%" + spec + "%"));
                }
                if (!prodSpecPredicates.isEmpty()) {
                    predicates.add(cb.or(prodSpecPredicates.toArray(new Predicate[0])));
                }
            }
        }
    }

    /**
     * 🔥 新增：为子查询添加级联筛选条件
     * 通过销售订单明细ID关联来应用其他筛选条件
     */
    private void addSubqueryFilterConditions(CriteriaBuilder cb, Root<PurchaseOrderItem> purchaseItemRoot,
                                           SalesOrderQueryParamDTO currentFilters,
                                           List<Predicate> predicates, String excludeField) {
        // 如果currentFilters为null，直接返回（用于筛选选项接口的独立调用）
        if (currentFilters == null) {
            return;
        }

        // 创建子查询来获取符合条件的销售订单明细ID
        CriteriaQuery<String> mainQuery = cb.createQuery(String.class);
        Root<SalesOrder> orderRoot = mainQuery.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> itemJoin = orderRoot.join("items", JoinType.INNER);

        mainQuery.select(itemJoin.get("id"));

        List<Predicate> mainPredicates = new ArrayList<>();

        // 🔥 重构：使用统一级联筛选架构（排除当前字段相关的条件）
        List<Predicate> cascadePredicates = CascadeFilterManager.buildCascadePredicates(
            cb, orderRoot, itemJoin, currentFilters, excludeField, entityManager);
        mainPredicates.addAll(cascadePredicates);

        if (!mainPredicates.isEmpty()) {
            mainQuery.where(cb.and(mainPredicates.toArray(new Predicate[0])));

            // 将符合条件的销售订单明细ID作为筛选条件
            Subquery<String> subquery = cb.createQuery().subquery(String.class);
            Root<SalesOrder> subOrderRoot = subquery.from(SalesOrder.class);
            Join<SalesOrder, SalesOrderItem> subItemJoin = subOrderRoot.join("items", JoinType.INNER);

            subquery.select(subItemJoin.get("id"));

            List<Predicate> subPredicates = new ArrayList<>();
            // 🔥 重构：使用统一级联筛选架构
            List<Predicate> subCascadePredicates = CascadeFilterManager.buildCascadePredicates(
                cb, subOrderRoot, subItemJoin, currentFilters, excludeField, entityManager);
            subPredicates.addAll(subCascadePredicates);

            if (!subPredicates.isEmpty()) {
                subquery.where(cb.and(subPredicates.toArray(new Predicate[0])));
                predicates.add(purchaseItemRoot.get("sourceSalesOrderItemId").in(subquery));
            }
        }
    }

    /**
     * 🔥 新增：为用料表子查询添加级联筛选条件
     */
    private void addMaterialSubqueryFilterConditions(CriteriaBuilder cb, Root<SalesOrderMaterial> materialRoot,
                                                   Join<SalesOrderMaterial, SalesOrderItem> materialItemJoin,
                                                   SalesOrderQueryParamDTO currentFilters,
                                                   List<Predicate> predicates, String excludeField) {
        if (currentFilters == null) {
            return;
        }

        // 创建子查询来获取符合条件的销售订单明细ID
        Subquery<String> subquery = cb.createQuery().subquery(String.class);
        Root<SalesOrder> subOrderRoot = subquery.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> subItemJoin = subOrderRoot.join("items", JoinType.INNER);

        subquery.select(subItemJoin.get("id"));

        List<Predicate> subPredicates = new ArrayList<>();
        // 🔥 重构：使用统一级联筛选架构
        List<Predicate> cascadePredicates = CascadeFilterManager.buildCascadePredicates(
            cb, subOrderRoot, subItemJoin, currentFilters, excludeField, entityManager);
        subPredicates.addAll(cascadePredicates);

        if (!subPredicates.isEmpty()) {
            subquery.where(cb.and(subPredicates.toArray(new Predicate[0])));
            predicates.add(materialItemJoin.get("id").in(subquery));
        }
    }

    /**
     * 🔥 新增：为工序表子查询添加级联筛选条件
     */
    private void addProcessSubqueryFilterConditions(CriteriaBuilder cb, Root<SalesOrderProcess> processRoot,
                                                  SalesOrderQueryParamDTO currentFilters,
                                                  List<Predicate> predicates, String excludeField) {
        if (currentFilters == null) {
            return;
        }

        // 创建子查询来获取符合条件的销售订单明细ID
        Subquery<String> subquery = cb.createQuery().subquery(String.class);
        Root<SalesOrder> subOrderRoot = subquery.from(SalesOrder.class);
        Join<SalesOrder, SalesOrderItem> subItemJoin = subOrderRoot.join("items", JoinType.INNER);

        subquery.select(subItemJoin.get("id"));

        List<Predicate> subPredicates = new ArrayList<>();
        // 🔥 重构：使用统一级联筛选架构
        List<Predicate> cascadePredicates = CascadeFilterManager.buildCascadePredicates(
            cb, subOrderRoot, subItemJoin, currentFilters, excludeField, entityManager);
        subPredicates.addAll(cascadePredicates);

        if (!subPredicates.isEmpty()) {
            subquery.where(cb.and(subPredicates.toArray(new Predicate[0])));
            predicates.add(processRoot.get("orderItemId").in(subquery));
        }
    }

    /**
     * 添加其他筛选条件（不包含指定的排除字段）- 仅主表字段
     */
    private void addOtherFilterConditions(CriteriaBuilder cb, Root<SalesOrder> orderRoot,
                                        SalesOrderQueryParamDTO currentFilters,
                                        List<Predicate> predicates, String excludeField) {
        // 如果currentFilters为null，直接返回（用于筛选选项接口的独立调用）
        if (currentFilters == null) {
            return;
        }

        // 传统搜索条件
        if (StringUtils.hasText(currentFilters.getKeyword())) {
            String keyword = "%" + currentFilters.getKeyword() + "%";
            predicates.add(cb.or(
                cb.like(orderRoot.get("orderNo"), keyword),
                cb.like(orderRoot.get("customerName"), keyword)
            ));
        }

        // 销售单号
        if (StringUtils.hasText(currentFilters.getOrderNo())) {
            predicates.add(cb.equal(orderRoot.get("orderNo"), currentFilters.getOrderNo()));
        }

        // 客户名称（排除当前字段）
        if (!"customerName".equals(excludeField) && StringUtils.hasText(currentFilters.getCustomerName())) {
            predicates.add(cb.like(orderRoot.get("customerName"), "%" + currentFilters.getCustomerName() + "%"));
        }

        // 销售员（排除当前字段）
        if (!"salesPerson".equals(excludeField) && StringUtils.hasText(currentFilters.getSalesPerson())) {
            predicates.add(cb.equal(orderRoot.get("salesPerson"), currentFilters.getSalesPerson()));
        }

        // 日期范围
        if (currentFilters.getStartDate() != null) {
            predicates.add(cb.greaterThanOrEqualTo(orderRoot.get("orderDate"), currentFilters.getStartDate()));
        }
        if (currentFilters.getEndDate() != null) {
            predicates.add(cb.lessThanOrEqualTo(orderRoot.get("orderDate"), currentFilters.getEndDate()));
        }

        // 新的筛选条件
        if (StringUtils.hasText(currentFilters.getFilterOrderNo())) {
            predicates.add(cb.like(orderRoot.get("orderNo"), "%" + currentFilters.getFilterOrderNo() + "%"));
        }

        if (!"customerName".equals(excludeField) && currentFilters.getFilterCustomerNames() != null && !currentFilters.getFilterCustomerNames().isEmpty()) {
            predicates.add(orderRoot.get("customerName").in(currentFilters.getFilterCustomerNames()));
        }

        if (!"salesPerson".equals(excludeField) && currentFilters.getFilterSalesPersons() != null && !currentFilters.getFilterSalesPersons().isEmpty()) {
            predicates.add(orderRoot.get("salesPerson").in(currentFilters.getFilterSalesPersons()));
        }

        // 日期范围筛选
        if (currentFilters.getFilterOrderDateStart() != null) {
            predicates.add(cb.greaterThanOrEqualTo(orderRoot.get("orderDate"), currentFilters.getFilterOrderDateStart()));
        }
        if (currentFilters.getFilterOrderDateEnd() != null) {
            predicates.add(cb.lessThanOrEqualTo(orderRoot.get("orderDate"), currentFilters.getFilterOrderDateEnd()));
        }
    }

    /**
     * 添加其他筛选条件（不包含指定的排除字段）- 包含明细表字段
     */
    private void addOtherFilterConditions(CriteriaBuilder cb, Root<SalesOrder> orderRoot,
                                        Join<SalesOrder, SalesOrderItem> itemJoin,
                                        SalesOrderQueryParamDTO currentFilters,
                                        List<Predicate> predicates, String excludeField) {
        // 先添加主表条件
        addOtherFilterConditions(cb, orderRoot, currentFilters, predicates, excludeField);

        // 如果currentFilters为null，直接返回（用于筛选选项接口的独立调用）
        if (currentFilters == null) {
            return;
        }

        // 明细表条件
        if (StringUtils.hasText(currentFilters.getProductionOrderNo())) {
            predicates.add(cb.equal(itemJoin.get("productionOrderNo"), currentFilters.getProductionOrderNo()));
        }

        if (StringUtils.hasText(currentFilters.getCustomerOrderNo())) {
            predicates.add(cb.equal(itemJoin.get("customerOrderNo"), currentFilters.getCustomerOrderNo()));
        }

        if (StringUtils.hasText(currentFilters.getCustomerProductCode())) {
            predicates.add(cb.equal(itemJoin.get("customerProductCode"), currentFilters.getCustomerProductCode()));
        }

        if (!"productName".equals(excludeField) && StringUtils.hasText(currentFilters.getProductName())) {
            predicates.add(cb.like(itemJoin.get("productName"), "%" + currentFilters.getProductName() + "%"));
        }

        // 新的筛选条件
        if (StringUtils.hasText(currentFilters.getFilterProductionOrderNo())) {
            predicates.add(cb.like(itemJoin.get("productionOrderNo"), "%" + currentFilters.getFilterProductionOrderNo() + "%"));
        }

        if (StringUtils.hasText(currentFilters.getFilterCustomerOrderNo())) {
            predicates.add(cb.like(itemJoin.get("customerOrderNo"), "%" + currentFilters.getFilterCustomerOrderNo() + "%"));
        }

        if (StringUtils.hasText(currentFilters.getFilterCustomerProductCode())) {
            predicates.add(cb.like(itemJoin.get("customerProductCode"), "%" + currentFilters.getFilterCustomerProductCode() + "%"));
        }

        if (!"productName".equals(excludeField) && currentFilters.getFilterProductNames() != null && !currentFilters.getFilterProductNames().isEmpty()) {
            predicates.add(itemJoin.get("productName").in(currentFilters.getFilterProductNames()));
        }

        // 数字范围筛选
        if (currentFilters.getFilterQuantityMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("quantity"), currentFilters.getFilterQuantityMin()));
        }
        if (currentFilters.getFilterQuantityMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("quantity"), currentFilters.getFilterQuantityMax()));
        }

        if (currentFilters.getFilterTotalAmountMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("amount"), currentFilters.getFilterTotalAmountMin()));
        }
        if (currentFilters.getFilterTotalAmountMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("amount"), currentFilters.getFilterTotalAmountMax()));
        }

        // 交期筛选
        if (currentFilters.getFilterDeliveryDateStart() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get("deliveryDate"), currentFilters.getFilterDeliveryDateStart()));
        }
        if (currentFilters.getFilterDeliveryDateEnd() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get("deliveryDate"), currentFilters.getFilterDeliveryDateEnd()));
        }
    }

    /**
     * 格式化规格字符串
     * @param length 长
     * @param width 宽
     * @param height 高
     * @param sizeUnit 尺寸单位
     * @return 格式化后的规格字符串，格式：长×宽×高 单位
     */
    private String formatSpecification(BigDecimal length, BigDecimal width, BigDecimal height, String sizeUnit) {
        // 检查null值
        if (length == null || width == null || height == null) {
            return "";
        }

        // 检查是否所有值都为0
        boolean allZero = length.compareTo(BigDecimal.ZERO) == 0 &&
                          width.compareTo(BigDecimal.ZERO) == 0 &&
                          height.compareTo(BigDecimal.ZERO) == 0;

        if (allZero) {
            return "";
        }

        // 构建规格字符串：长×宽×高 单位
        StringBuilder sb = new StringBuilder();
        sb.append(length);
        sb.append("×");
        sb.append(width);
        sb.append("×");
        sb.append(height);

        // 添加单位（如果有的话）
        if (sizeUnit != null && !sizeUnit.trim().isEmpty()) {
            sb.append(" ");
            sb.append(sizeUnit);
        }

        return sb.toString();
    }

    /**
     * 为销售订单查询结果添加工序数据
     * @param results 销售订单查询结果列表
     */
    private void enrichWithProcessData(List<SalesOrderQueryResultDTO> results) {
        log.debug("开始为销售订单查询结果添加工序数据，共 {} 条记录", results.size());

        // 收集所有销售订单明细ID
        List<String> salesOrderItemIds = results.stream()
            .map(SalesOrderQueryResultDTO::getItemId)
            .filter(StringUtils::hasText)
            .distinct()
            .collect(Collectors.toList());

        if (salesOrderItemIds.isEmpty()) {
            log.debug("没有有效的销售订单明细ID，跳过工序数据查询");
            return;
        }

        // 批量查询工序信息
        log.debug("查询 {} 个销售订单明细的工序信息", salesOrderItemIds.size());
        List<SalesOrderProcess> processes = salesOrderProcessRepository
            .findByOrderItemIdInAndIsDeletedFalseOrderByOrderItemIdAscSequenceAsc(salesOrderItemIds);
        log.debug("查询到 {} 条工序记录", processes.size());

        // 按明细ID分组
        Map<String, List<SalesOrderProcess>> processMap = processes.stream()
            .collect(Collectors.groupingBy(SalesOrderProcess::getOrderItemId));

        // 为每个销售订单明细设置工序信息
        for (SalesOrderQueryResultDTO dto : results) {
            String itemId = dto.getItemId();
            List<SalesOrderProcess> itemProcesses = processMap.get(itemId);

            if (itemProcesses != null && !itemProcesses.isEmpty()) {
                // 按sequence排序并用"→"连接工序名称
                String processNames = itemProcesses.stream()
                    .sorted((p1, p2) -> Integer.compare(p1.getSequence(), p2.getSequence()))
                    .map(SalesOrderProcess::getProcessName)
                    .filter(StringUtils::hasText)
                    .collect(Collectors.joining("→"));
                dto.setProcesses(processNames);

                log.debug("为销售订单明细 {} 设置工序信息：{}", itemId, processNames);
            } else {
                // 没有工序信息
                dto.setProcesses("");
                log.debug("销售订单明细 {} 没有关联的工序信息", itemId);
            }
        }

        log.debug("完成销售订单查询结果的工序数据填充");
    }

    /**
     * 为销售订单查询结果添加用料数据
     * @param results 销售订单查询结果列表
     */
    private void enrichWithMaterialData(List<SalesOrderQueryResultDTO> results) {
        log.debug("开始为销售订单查询结果添加用料数据，共 {} 条记录", results.size());

        // 收集所有销售订单明细ID
        List<String> salesOrderItemIds = results.stream()
            .map(SalesOrderQueryResultDTO::getItemId)
            .filter(StringUtils::hasText)
            .distinct()
            .collect(Collectors.toList());

        if (salesOrderItemIds.isEmpty()) {
            log.debug("没有有效的销售订单明细ID，跳过用料数据查询");
            return;
        }

        // 批量查询用料信息
        log.debug("查询 {} 个销售订单明细的用料信息", salesOrderItemIds.size());
        List<SalesOrderMaterial> materials = salesOrderMaterialRepository
            .findByOrderItemIdIn(salesOrderItemIds);
        log.debug("查询到 {} 条用料记录", materials.size());

        // 按明细ID分组，并获取第一个用料的压线尺寸
        Map<String, String> materialMap = materials.stream()
            .filter(material -> StringUtils.hasText(material.getPressSizeWidth()))
            .collect(Collectors.toMap(
                material -> material.getOrderItem().getId(),
                SalesOrderMaterial::getPressSizeWidth,
                (existing, replacement) -> existing // 如果有多个用料，取第一个
            ));

        // 为每个销售订单明细设置用料信息
        for (SalesOrderQueryResultDTO dto : results) {
            String itemId = dto.getItemId();
            String pressSizeWidth = materialMap.get(itemId);

            if (StringUtils.hasText(pressSizeWidth)) {
                dto.setPressSizeWidth(pressSizeWidth);
                log.debug("为销售订单明细 {} 设置压线尺寸：{}", itemId, pressSizeWidth);
            } else {
                // 没有用料信息或压线尺寸为空
                dto.setPressSizeWidth("");
                log.debug("销售订单明细 {} 没有关联的压线尺寸信息", itemId);
            }
        }

        log.debug("完成销售订单查询结果的用料数据填充");
    }

    /**
     * 为销售订单查询结果添加纸质类别数据
     * @param results 销售订单查询结果列表
     */
    private void enrichWithPaperTypeData(List<SalesOrderQueryResultDTO> results) {
        log.debug("开始为销售订单查询结果添加纸质类别数据，共 {} 条记录", results.size());

        // 收集所有不重复的纸质名称
        List<String> paperTypeNames = results.stream()
            .map(SalesOrderQueryResultDTO::getPaperType)
            .filter(StringUtils::hasText)
            .distinct()
            .collect(Collectors.toList());

        if (paperTypeNames.isEmpty()) {
            log.debug("没有有效的纸质名称，跳过纸质类别数据查询");
            return;
        }

        // 批量查询纸质资料信息
        log.debug("查询 {} 个纸质名称对应的纸质类别信息", paperTypeNames.size());
        List<PaperMaterial> paperMaterials = paperMaterialRepository.findByPaperNameIn(paperTypeNames);
        log.debug("查询到 {} 条纸质资料记录", paperMaterials.size());

        // 构建纸质名称到纸质类别名称的映射
        Map<String, String> paperTypeNameMap = paperMaterials.stream()
            .filter(material -> material.getPaperType() != null &&
                               StringUtils.hasText(material.getPaperType().getPaperTypeName()))
            .collect(Collectors.toMap(
                PaperMaterial::getPaperName,
                material -> material.getPaperType().getPaperTypeName(),
                (existing, replacement) -> existing // 如果有重复，保留第一个
            ));

        // 为每个销售订单明细设置纸质类别信息
        for (SalesOrderQueryResultDTO dto : results) {
            String paperType = dto.getPaperType();
            String paperTypeName = paperTypeNameMap.get(paperType);

            if (StringUtils.hasText(paperTypeName)) {
                dto.setPaperTypeName(paperTypeName);
                log.debug("为纸质 {} 设置纸质类别：{}", paperType, paperTypeName);
            } else {
                // 没有找到对应的纸质类别
                dto.setPaperTypeName("");
                log.debug("纸质 {} 没有找到对应的纸质类别信息", paperType);
            }
        }

        log.debug("完成销售订单查询结果的纸质类别数据填充");
    }
}
