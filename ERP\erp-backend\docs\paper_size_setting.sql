-- 创建纸度设置表
CREATE TABLE IF NOT EXISTS paper_size_setting (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    paper_size_inch DECIMAL(10,2) COMMENT '纸度(inch)',
    paper_size_cm DECIMAL(10,2) COMMENT '纸度(cm)',
    max_loss_inch DECIMAL(10,2) COMMENT '最大损耗(inch)',
    max_loss_cm DECIMAL(10,2) COMMENT '最大损耗(cm)',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME COMMENT '更新时间',
    version INT COMMENT '版本号',
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除'
);

-- 插入纸度设置数据
INSERT INTO paper_size_setting (paper_size_inch, paper_size_cm, max_loss_inch, max_loss_cm, created_by, created_time, deleted) VALUES
(29, 74, 2, 5, 'system', NOW(), 0),
(31, 80, 2, 5, 'system', NOW(), 0),
(33, 84, 2, 5, 'system', NOW(), 0),
(35, 89, 2, 5, 'system', NOW(), 0),
(37, 94, 2, 5, 'system', NOW(), 0),
(39, 99, 2, 5, 'system', NOW(), 0),
(41, 104, 2, 5, 'system', NOW(), 0),
(43, 109, 2, 5, 'system', NOW(), 0),
(45, 114, 2, 5, 'system', NOW(), 0),
(47, 119, 2, 5, 'system', NOW(), 0),
(49, 124, 2, 5, 'system', NOW(), 0),
(51, 130, 2, 5, 'system', NOW(), 0),
(53, 135, 2, 5, 'system', NOW(), 0),
(55, 140, 2, 5, 'system', NOW(), 0),
(58, 147, 3, 8, 'system', NOW(), 0),
(62, 157, 4, 10, 'system', NOW(), 0),
(66, 168, 4, 10, 'system', NOW(), 0),
(70, 178, 4, 10, 'system', NOW(), 0),
(74, 188, 4, 10, 'system', NOW(), 0),
(78, 198, 4, 10, 'system', NOW(), 0),
(82, 208, 4, 10, 'system', NOW(), 0),
(86, 218, 4, 10, 'system', NOW(), 0),
(90, 229, 4, 10, 'system', NOW(), 0),
(94, 239, 4, 10, 'system', NOW(), 0),
(98, 249, 4, 10, 'system', NOW(), 0),
(99, 251, 4, 10, 'system', NOW(), 0),
(102, 259, 4, 10, 'system', NOW(), 0),
(104, 264, 4, 10, 'system', NOW(), 0),
(106, 269, 4, 10, 'system', NOW(), 0),
(110, 279, 4, 10, 'system', NOW(), 0);
