package com.czerp.erpbackend.purchase.mapper;

import com.czerp.erpbackend.purchase.dto.CreatePurchaseOrderRequest;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderDTO;
import com.czerp.erpbackend.purchase.dto.UpdatePurchaseOrderRequest;
import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 采购订单Mapper
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {PurchaseOrderItemMapper.class})
public interface PurchaseOrderMapper {

    /**
     * 实体转DTO
     * @param entity 实体
     * @return DTO
     */
    PurchaseOrderDTO toDto(PurchaseOrder entity);

    /**
     * 实体列表转DTO列表
     * @param entities 实体列表
     * @return DTO列表
     */
    List<PurchaseOrderDTO> toDto(List<PurchaseOrder> entities);

    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "purchaseOrderNo", ignore = true)
    @Mapping(target = "items", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    PurchaseOrder toEntity(CreatePurchaseOrderRequest request);

    /**
     * 更新实体
     * @param request 创建请求
     * @param entity 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "purchaseOrderNo", ignore = true)
    @Mapping(target = "items", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateEntity(CreatePurchaseOrderRequest request, @MappingTarget PurchaseOrder entity);

    /**
     * 更新请求转实体
     * @param request 更新请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "purchaseOrderNo", ignore = true)
    @Mapping(target = "items", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    PurchaseOrder toEntity(UpdatePurchaseOrderRequest request);

    /**
     * 更新实体
     * @param request 更新请求
     * @param entity 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "purchaseOrderNo", ignore = true)
    @Mapping(target = "items", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateEntity(UpdatePurchaseOrderRequest request, @MappingTarget PurchaseOrder entity);
}
