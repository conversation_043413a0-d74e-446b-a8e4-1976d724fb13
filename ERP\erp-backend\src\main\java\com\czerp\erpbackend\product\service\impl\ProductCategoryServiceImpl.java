package com.czerp.erpbackend.product.service.impl;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.product.dto.CreateCategoryRequest;
import com.czerp.erpbackend.product.dto.ProductCategoryDTO;
import com.czerp.erpbackend.product.dto.UpdateCategoryRequest;
import com.czerp.erpbackend.product.entity.ProductCategory;
import com.czerp.erpbackend.product.mapper.ProductCategoryMapper;
import com.czerp.erpbackend.product.repository.ProductCategoryRepository;
import com.czerp.erpbackend.product.repository.ProductRepository;
import com.czerp.erpbackend.product.service.ProductCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 产品分类服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductCategoryServiceImpl implements ProductCategoryService {
    
    private final ProductCategoryRepository categoryRepository;
    private final ProductRepository productRepository;
    private final ProductCategoryMapper categoryMapper;
    
    /**
     * 查询所有分类
     * @return 分类列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<ProductCategoryDTO> findAllCategories() {
        log.debug("Finding all categories");
        
        List<ProductCategory> categories = categoryRepository.findAll();
        
        return categories.stream()
                .map(categoryMapper::toDto)
                .collect(Collectors.toList());
    }
    
    /**
     * 查询分类树
     * @return 分类树
     */
    @Override
    @Transactional(readOnly = true)
    public List<ProductCategoryDTO> findCategoryTree() {
        log.debug("Finding category tree");
        
        // 查询所有分类
        List<ProductCategory> allCategories = categoryRepository.findAll();
        
        // 转换为DTO
        List<ProductCategoryDTO> categoryDTOs = allCategories.stream()
                .map(categoryMapper::toDto)
                .collect(Collectors.toList());
        
        // 构建分类树
        Map<String, List<ProductCategoryDTO>> childrenMap = categoryDTOs.stream()
                .filter(dto -> dto.getParentId() != null)
                .collect(Collectors.groupingBy(ProductCategoryDTO::getParentId));
        
        categoryDTOs.forEach(dto -> dto.setChildren(childrenMap.getOrDefault(dto.getId(), new ArrayList<>())));
        
        // 返回根分类
        return categoryDTOs.stream()
                .filter(dto -> dto.getParentId() == null)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据ID查询分类
     * @param id 分类ID
     * @return 分类信息
     */
    @Override
    @Transactional(readOnly = true)
    public ProductCategoryDTO findCategoryById(String id) {
        log.debug("Finding category by id: {}", id);
        
        ProductCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new BusinessException("分类不存在"));
        
        return categoryMapper.toDto(category);
    }
    
    /**
     * 创建分类
     * @param request 创建请求
     * @return 分类信息
     */
    @Override
    @Transactional
    public ProductCategoryDTO createCategory(CreateCategoryRequest request) {
        log.debug("Creating category with request: {}", request);
        
        // 检查编码是否已存在
        if (categoryRepository.existsByCode(request.getCode())) {
            throw new BusinessException("分类编码已存在");
        }
        
        // 创建分类
        ProductCategory category = categoryMapper.toEntity(request);
        category.setId(UUID.randomUUID().toString());
        
        // 设置层级
        if (request.getParentId() != null) {
            ProductCategory parentCategory = categoryRepository.findById(request.getParentId())
                    .orElseThrow(() -> new BusinessException("父分类不存在"));
            
            category.setLevel(parentCategory.getLevel() + 1);
        } else {
            category.setLevel(1);
        }
        
        // 保存分类
        category = categoryRepository.save(category);
        
        return categoryMapper.toDto(category);
    }
    
    /**
     * 更新分类
     * @param id 分类ID
     * @param request 更新请求
     * @return 分类信息
     */
    @Override
    @Transactional
    public ProductCategoryDTO updateCategory(String id, UpdateCategoryRequest request) {
        log.debug("Updating category with id: {} and request: {}", id, request);
        
        // 查询分类
        ProductCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new BusinessException("分类不存在"));
        
        // 检查父分类是否存在
        if (request.getParentId() != null && !request.getParentId().equals(category.getParentId())) {
            // 检查是否是自己的子分类
            if (request.getParentId().equals(id)) {
                throw new BusinessException("不能将自己设为父分类");
            }
            
            ProductCategory parentCategory = categoryRepository.findById(request.getParentId())
                    .orElseThrow(() -> new BusinessException("父分类不存在"));
            
            // 更新层级
            category.setLevel(parentCategory.getLevel() + 1);
        }
        
        // 更新分类
        categoryMapper.updateEntity(request, category);
        
        // 保存分类
        category = categoryRepository.save(category);
        
        return categoryMapper.toDto(category);
    }
    
    /**
     * 删除分类
     * @param id 分类ID
     */
    @Override
    @Transactional
    public void deleteCategory(String id) {
        log.debug("Deleting category with id: {}", id);
        
        // 查询分类
        ProductCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new BusinessException("分类不存在"));
        
        // 检查是否有子分类
        long childCount = categoryRepository.countByParentId(id);
        if (childCount > 0) {
            throw new BusinessException("该分类下有子分类，不能删除");
        }
        
        // 检查是否有关联的货品
        long productCount = productRepository.findByCategoryId(id).size();
        if (productCount > 0) {
            throw new BusinessException("该分类下有货品，不能删除");
        }
        
        // 删除分类
        categoryRepository.delete(category);
    }
}
