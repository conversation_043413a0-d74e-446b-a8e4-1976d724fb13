# 纸质资料 API 文档

## 数据模型

### PaperType（纸质类别）

| 字段名 | 类型 | 描述 | 备注 |
| ----- | ---- | ---- | ---- |
| id | Integer | 主键ID | 自增长 |
| paperTypeName | String | 纸板类别名称 | 最大长度50，不能为空 |
| staplingFlapInch | BigDecimal | 打钉钉口(inch) | 精度(10,2) |
| staplingFlapCm | BigDecimal | 打钉钉口(cm) | 精度(10,2) |
| gluingFlapInch | BigDecimal | 粘箱钉口(inch) | 精度(10,2) |
| gluingFlapCm | BigDecimal | 粘箱钉口(cm) | 精度(10,2) |
| addMarginMm | BigDecimal | 加分(mm) | 精度(10,2) |
| reduceMarginMm | BigDecimal | 缩分(mm) | 精度(10,2) |
| thicknessMm | BigDecimal | 厚度(mm) | 精度(10,2) |
| layerCount | Integer | 层数 | 整数 |
| sheetsPerBoard | Integer | 每板数 | 整数 |
| createdBy | String | 创建人 | 系统字段 |
| createdTime | DateTime | 创建时间 | 系统字段 |
| updatedBy | String | 更新人 | 系统字段 |
| updatedTime | DateTime | 更新时间 | 系统字段 |
| isDeleted | Boolean | 是否删除 | 0-未删除，1-已删除 |

### PaperMaterial（纸质资料）

| 字段名 | 类型 | 描述 | 备注 |
| ----- | ---- | ---- | ---- |
| id | Long | 主键ID | 自增长 |
| paperCode | String | 纸质编码 | 最大长度50 |
| paperName | String | 纸质 | 最大长度50 |
| paperType | PaperType | 纸类 | 关联纸质类别表 |
| fluteType | String | 楞别 | 最大长度50 |
| isStandard | Boolean | 标准纸质 | true-是，false-否 |
| productionCode | String | 生产代号 | 最大长度50 |
| facePaper | String | 面纸 | 最大长度100 |
| corePaper1 | String | 芯纸1 | 最大长度100 |
| middlePartition1 | String | 中隔1 | 最大长度100 |
| corePaper2 | String | 芯纸2 | 最大长度100 |
| middlePartition2 | String | 中隔2 | 最大长度100 |
| corePaper3 | String | 芯纸3 | 最大长度100 |
| linerPaper | String | 里纸 | 最大长度100 |
| layerCount | Integer | 层数 | 整数 |
| weightKgPerKsi | BigDecimal | 重量(千克/千平方英寸) | 精度(10,6) |
| weightKgPerSqm | BigDecimal | 重量(千克/平方米) | 精度(10,6) |
| edgeCrushStrength | String | 边压强度 | 最大长度50 |
| burstingStrength | String | 纸板耐破度 | 最大长度50 |
| correspondingStandard | String | 对应标准纸质 | 最大长度100 |
| defaultSupplier | String | 默认供应商名称 | 最大长度100 |
| remarks | String | 备注 | 最大长度500 |
| isDisabled | Boolean | 停用 | true-是，false-否，默认false |
| createdBy | String | 创建人 | 系统字段 |
| createdTime | DateTime | 创建时间 | 系统字段 |
| updatedBy | String | 更新人 | 系统字段 |
| updatedTime | DateTime | 更新时间 | 系统字段 |

## API 接口

### 纸质类别API

#### 1. 查询所有纸质类别

- **URL**: `/material/paper-types`
- **Method**: GET
- **权限**: `material:paper-type:list`
- **请求参数**: 无
- **响应结果**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "paperTypeName": "三坑",
      "staplingFlapInch": 1.5,
      "staplingFlapCm": 3.81,
      "gluingFlapInch": 1.5,
      "gluingFlapCm": 3.81,
      "addMarginMm": 3.0,
      "reduceMarginMm": null,
      "thicknessMm": 9.0,
      "layerCount": 7,
      "sheetsPerBoard": null
    },
    {
      "id": 2,
      "paperTypeName": "双坑",
      "staplingFlapInch": 1.18,
      "staplingFlapCm": 3.0,
      "gluingFlapInch": 1.18,
      "gluingFlapCm": 3.0,
      "addMarginMm": 2.0,
      "reduceMarginMm": null,
      "thicknessMm": 6.0,
      "layerCount": 5,
      "sheetsPerBoard": null
    }
  ]
}
```

#### 2. 根据ID查询纸质类别

- **URL**: `/material/paper-types/{id}`
- **Method**: GET
- **权限**: `material:paper-type:read`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| id | Integer | 是 | 纸质类别ID | 1 |

- **响应结果**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "paperTypeName": "三坑",
    "staplingFlapInch": 1.5,
    "staplingFlapCm": 3.81,
    "gluingFlapInch": 1.5,
    "gluingFlapCm": 3.81,
    "addMarginMm": 3.0,
    "reduceMarginMm": null,
    "thicknessMm": 9.0,
    "layerCount": 7,
    "sheetsPerBoard": null
  }
}
```

#### 3. 公共接口 - 查询所有纸质类别

- **URL**: `/public/material/paper-types`
- **Method**: GET
- **权限**: 无需权限
- **请求参数**: 无
- **响应结果**: 同上

### 纸质资料API

#### 1. 分页查询纸质资料列表

- **URL**: `/material/paper-materials`
- **Method**: GET
- **权限**: `material:paper-material:list`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| page | Integer | 否 | 页码，从1开始 | 1 |
| size | Integer | 否 | 每页大小 | 10 |
| paperCode | String | 否 | 纸质编码（模糊查询） | A3 |
| paperName | String | 否 | 纸质（模糊查询） | A3A |
| paperType | String | 否 | 纸类名称（模糊查询） | 单坑 |
| fluteType | String | 否 | 楞别（模糊查询） | C瓦 |
| isStandard | Boolean | 否 | 标准纸质 | true |
| isDisabled | Boolean | 否 | 停用 | false |

- **响应结果**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": 1,
        "paperCode": "A3A",
        "paperName": "A3A",
        "paperType": {
          "id": 1,
          "paperTypeName": "三坑",
          "staplingFlapInch": 1.5,
          "staplingFlapCm": 3.81,
          "gluingFlapInch": 1.5,
          "gluingFlapCm": 3.81,
          "addMarginMm": 3.0,
          "reduceMarginMm": null,
          "thicknessMm": 9.0,
          "layerCount": 7,
          "sheetsPerBoard": null
        },
        "paperTypeName": "三坑",
        "fluteType": "C瓦",
        "isStandard": true,
        "productionCode": "P001",
        "facePaper": "面纸A",
        "corePaper1": "芯纸1A",
        "middlePartition1": "中隔1A",
        "corePaper2": "芯纸2A",
        "middlePartition2": "中隔2A",
        "corePaper3": "芯纸3A",
        "linerPaper": "里纸A",
        "layerCount": 3,
        "weightKgPerKsi": 0.25,
        "weightKgPerSqm": 0.387501,
        "edgeCrushStrength": "5.5",
        "burstingStrength": "8.0",
        "correspondingStandard": "标准A",
        "defaultSupplier": "供应商A",
        "remarks": "备注信息",
        "isDisabled": false,
        "createdBy": "admin",
        "createdTime": "2023-06-01T10:00:00",
        "updatedBy": "admin",
        "updatedTime": "2023-06-01T10:00:00"
      }
    ],
    "page": 1,
    "size": 10,
    "totalElements": 1,
    "totalPages": 1
  }
}
```

#### 2. 查询所有纸质资料

- **URL**: `/material/paper-materials/all`
- **Method**: GET
- **权限**: `material:paper-material:list`
- **请求参数**: 无
- **响应结果**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "paperCode": "A3A",
      "paperName": "A3A",
      "paperType": {
        "id": 1,
        "paperTypeName": "三坑",
        "staplingFlapInch": 1.5,
        "staplingFlapCm": 3.81,
        "gluingFlapInch": 1.5,
        "gluingFlapCm": 3.81,
        "addMarginMm": 3.0,
        "reduceMarginMm": null,
        "thicknessMm": 9.0,
        "layerCount": 7,
        "sheetsPerBoard": null
      },
      "paperTypeName": "三坑",
      "fluteType": "C瓦",
      "isStandard": true,
      "productionCode": "P001",
      "facePaper": "面纸A",
      "corePaper1": "芯纸1A",
      "middlePartition1": "中隔1A",
      "corePaper2": "芯纸2A",
      "middlePartition2": "中隔2A",
      "corePaper3": "芯纸3A",
      "linerPaper": "里纸A",
      "layerCount": 3,
      "weightKgPerKsi": 0.25,
      "weightKgPerSqm": 0.387501,
      "edgeCrushStrength": "5.5",
      "burstingStrength": "8.0",
      "correspondingStandard": "标准A",
      "defaultSupplier": "供应商A",
      "remarks": "备注信息",
      "isDisabled": false,
      "createdBy": "admin",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-06-01T10:00:00"
    }
  ]
}
```

#### 3. 查询所有未停用的纸质资料

- **URL**: `/material/paper-materials/active`
- **Method**: GET
- **权限**: `material:paper-material:list`
- **请求参数**: 无
- **响应结果**: 同上，但只返回 `isDisabled` 为 `false` 的记录

#### 4. 查询所有标准纸质资料

- **URL**: `/material/paper-materials/standard`
- **Method**: GET
- **权限**: `material:paper-material:list`
- **请求参数**: 无
- **响应结果**: 同上，但只返回 `isStandard` 为 `true` 且 `isDisabled` 为 `false` 的记录

#### 5. 根据ID查询纸质资料

- **URL**: `/material/paper-materials/{id}`
- **Method**: GET
- **权限**: `material:paper-material:read`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| id | Long | 是 | 纸质资料ID | 1 |

- **响应结果**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "paperCode": "A3A",
    "paperName": "A3A",
    "paperType": {
      "id": 1,
      "paperTypeName": "三坑",
      "staplingFlapInch": 1.5,
      "staplingFlapCm": 3.81,
      "gluingFlapInch": 1.5,
      "gluingFlapCm": 3.81,
      "addMarginMm": 3.0,
      "reduceMarginMm": null,
      "thicknessMm": 9.0,
      "layerCount": 7,
      "sheetsPerBoard": null
    },
    "paperTypeName": "三坑",
    "fluteType": "C瓦",
    "isStandard": true,
    "productionCode": "P001",
    "facePaper": "面纸A",
    "corePaper1": "芯纸1A",
    "middlePartition1": "中隔1A",
    "corePaper2": "芯纸2A",
    "middlePartition2": "中隔2A",
    "corePaper3": "芯纸3A",
    "linerPaper": "里纸A",
    "layerCount": 3,
    "weightKgPerKsi": 0.25,
    "weightKgPerSqm": 0.387501,
    "edgeCrushStrength": "5.5",
    "burstingStrength": "8.0",
    "correspondingStandard": "标准A",
    "defaultSupplier": "供应商A",
    "remarks": "备注信息",
    "isDisabled": false,
    "createdBy": "admin",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-06-01T10:00:00"
  }
}
```

#### 6. 创建纸质资料

- **URL**: `/material/paper-materials`
- **Method**: POST
- **权限**: `material:paper-material:create`
- **请求参数**:

```json
{
  "paperCode": "A3A",
  "paperName": "A3A",
  "paperTypeId": 1,
  "fluteType": "C瓦",
  "isStandard": true,
  "productionCode": "P001",
  "facePaper": "面纸A",
  "corePaper1": "芯纸1A",
  "middlePartition1": "中隔1A",
  "corePaper2": "芯纸2A",
  "middlePartition2": "中隔2A",
  "corePaper3": "芯纸3A",
  "linerPaper": "里纸A",
  "layerCount": 3,
  "weightKgPerKsi": 0.25,
  "weightKgPerSqm": 0.387501,
  "edgeCrushStrength": "5.5",
  "burstingStrength": "8.0",
  "correspondingStandard": "标准A",
  "defaultSupplier": "供应商A",
  "remarks": "备注信息",
  "isDisabled": false
}
```

- **响应结果**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "paperCode": "A3A",
    "paperName": "A3A",
    "paperType": {
      "id": 1,
      "paperTypeName": "三坑",
      "staplingFlapInch": 1.5,
      "staplingFlapCm": 3.81,
      "gluingFlapInch": 1.5,
      "gluingFlapCm": 3.81,
      "addMarginMm": 3.0,
      "reduceMarginMm": null,
      "thicknessMm": 9.0,
      "layerCount": 7,
      "sheetsPerBoard": null
    },
    "paperTypeName": "三坑",
    "fluteType": "C瓦",
    "isStandard": true,
    "productionCode": "P001",
    "facePaper": "面纸A",
    "corePaper1": "芯纸1A",
    "middlePartition1": "中隔1A",
    "corePaper2": "芯纸2A",
    "middlePartition2": "中隔2A",
    "corePaper3": "芯纸3A",
    "linerPaper": "里纸A",
    "layerCount": 3,
    "weightKgPerKsi": 0.25,
    "weightKgPerSqm": 0.387501,
    "edgeCrushStrength": "5.5",
    "burstingStrength": "8.0",
    "correspondingStandard": "标准A",
    "defaultSupplier": "供应商A",
    "remarks": "备注信息",
    "isDisabled": false,
    "createdBy": "admin",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-06-01T10:00:00"
  }
}
```

#### 7. 更新纸质资料

- **URL**: `/material/paper-materials/{id}`
- **Method**: PUT
- **权限**: `material:paper-material:update`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| id | Long | 是 | 纸质资料ID | 1 |

```json
{
  "paperCode": "A3A",
  "paperName": "A3A",
  "paperTypeId": 1,
  "fluteType": "C瓦",
  "isStandard": true,
  "productionCode": "P001",
  "facePaper": "面纸A",
  "corePaper1": "芯纸1A",
  "middlePartition1": "中隔1A",
  "corePaper2": "芯纸2A",
  "middlePartition2": "中隔2A",
  "corePaper3": "芯纸3A",
  "linerPaper": "里纸A",
  "layerCount": 3,
  "weightKgPerKsi": 0.25,
  "weightKgPerSqm": 0.387501,
  "edgeCrushStrength": "5.5",
  "burstingStrength": "8.0",
  "correspondingStandard": "标准A",
  "defaultSupplier": "供应商A",
  "remarks": "备注信息",
  "isDisabled": false
}
```

- **响应结果**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "paperCode": "A3A",
    "paperName": "A3A",
    "paperType": {
      "id": 1,
      "paperTypeName": "三坑",
      "staplingFlapInch": 1.5,
      "staplingFlapCm": 3.81,
      "gluingFlapInch": 1.5,
      "gluingFlapCm": 3.81,
      "addMarginMm": 3.0,
      "reduceMarginMm": null,
      "thicknessMm": 9.0,
      "layerCount": 7,
      "sheetsPerBoard": null
    },
    "paperTypeName": "三坑",
    "fluteType": "C瓦",
    "isStandard": true,
    "productionCode": "P001",
    "facePaper": "面纸A",
    "corePaper1": "芯纸1A",
    "middlePartition1": "中隔1A",
    "corePaper2": "芯纸2A",
    "middlePartition2": "中隔2A",
    "corePaper3": "芯纸3A",
    "linerPaper": "里纸A",
    "layerCount": 3,
    "weightKgPerKsi": 0.25,
    "weightKgPerSqm": 0.387501,
    "edgeCrushStrength": "5.5",
    "burstingStrength": "8.0",
    "correspondingStandard": "标准A",
    "defaultSupplier": "供应商A",
    "remarks": "备注信息",
    "isDisabled": false,
    "createdBy": "admin",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-06-01T10:00:00"
  }
}
```

#### 8. 删除纸质资料

- **URL**: `/material/paper-materials/{id}`
- **Method**: DELETE
- **权限**: `material:paper-material:delete`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| id | Long | 是 | 纸质资料ID | 1 |

- **响应结果**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

#### 9. 批量删除纸质资料

- **URL**: `/material/paper-materials/batch`
- **Method**: DELETE
- **权限**: `material:paper-material:delete`
- **请求参数**:

```json
[1, 2, 3]
```

- **响应结果**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

#### 10. 导入纸质资料

- **URL**: `/material/paper-materials/import`
- **Method**: POST
- **权限**: `material:paper-material:import`
- **Content-Type**: `multipart/form-data`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| file | File | 是 | Excel文件 | - |

- **响应结果**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "success": 5,
    "fail": 2,
    "errors": [
      "导入失败: A001 - 测试纸质, 原因: 纸质编码已存在: A001",
      "导入失败: A002 - , 原因: 纸质名称不能为空"
    ]
  }
}
```

#### 11. 下载纸质资料导入模板

- **URL**: `/material/paper-materials/import/template`
- **Method**: GET
- **权限**: `material:paper-material:import`
- **请求参数**: 无
- **响应结果**: Excel文件流

### 公共接口

#### 1. 查询所有纸质类别（公共接口）

- **URL**: `/public/material/paper-types`
- **Method**: GET
- **权限**: 无需权限
- **请求参数**: 无
- **响应结果**: 同纸质类别API的查询所有纸质类别接口

#### 2. 查询所有未停用的纸质资料（公共接口）

- **URL**: `/public/material/paper-materials`
- **Method**: GET
- **权限**: 无需权限
- **请求参数**: 无
- **响应结果**: 同上，但只返回 `isDisabled` 为 `false` 的记录

#### 3. 查询所有标准纸质资料（公共接口）

- **URL**: `/public/material/paper-materials/standard`
- **Method**: GET
- **权限**: 无需权限
- **请求参数**: 无
- **响应结果**: 同上，但只返回 `isStandard` 为 `true` 且 `isDisabled` 为 `false` 的记录

## 业务规则

1. 纸质编码（paperCode）和纸质名称（paperName）必须唯一
2. 创建纸质资料时，如果未指定 isStandard 和 isDisabled，则默认为 false
3. 删除纸质资料为逻辑删除，不会从数据库中物理删除记录
4. 公共接口不需要权限验证，可以直接访问
5. 导入Excel文件时，纸质编码和纸质名称为必填字段
6. 导入时会自动检查纸质编码和纸质名称的唯一性
7. 纸质资料与纸质类别是多对一的关系，一个纸质资料只能属于一个纸质类别

## 前端开发建议

1. 前端字段名称应与后端保持一致，特别是驼峰命名法
2. 布尔类型字段（如 isStandard、isDisabled）在前端应使用 Boolean 类型，而不是字符串
3. 日期时间字段（如 createdTime、updatedTime）在前端可以使用 ISO 8601 格式或自定义格式展示
4. 数值类型字段（如 weightKgPerKsi、weightKgPerSqm）在前端应使用 Number 类型，并注意精度处理
5. 表单验证应与后端验证规则保持一致，如字段长度限制等
6. 导入功能应提供下载模板的入口，并在导入后显示成功和失败的数量及原因
7. 导入Excel文件时，应提示用户必填字段和字段格式要求
8. 在纸质资料表单中，纸类字段应改为下拉选择框，从纸质类别API获取选项
9. 在纸质资料列表中，应显示纸类名称而不是纸类ID

## 前端适配指南

### TypeScript类型定义

需要更新前端的类型定义，以适配新的API响应格式：

```typescript
// 纸质类别
export interface PaperType {
  id: number;
  paperTypeName: string;
  staplingFlapInch: number | null;
  staplingFlapCm: number | null;
  gluingFlapInch: number | null;
  gluingFlapCm: number | null;
  addMarginMm: number | null;
  reduceMarginMm: number | null;
  thicknessMm: number | null;
  layerCount: number | null;
  sheetsPerBoard: number | null;
}

// 纸质资料
export interface PaperMaterial {
  id: number;
  paperCode: string;
  paperName: string;
  paperType: PaperType;
  paperTypeName: string; // 为了兼容前端，后端额外提供的字段
  fluteType: string;
  isStandard: boolean;
  productionCode?: string;
  facePaper?: string;
  corePaper1?: string;
  middlePartition1?: string;
  corePaper2?: string;
  middlePartition2?: string;
  corePaper3?: string;
  linerPaper?: string;
  layerCount?: number;
  weightKgPerKsi?: number;
  weightKgPerSqm?: number;
  edgeCrushStrength?: string;
  burstingStrength?: string;
  correspondingStandard?: string;
  defaultSupplier?: string;
  remarks?: string;
  isDisabled: boolean;
  createdBy?: string;
  createdTime?: string;
  updatedBy?: string;
  updatedTime?: string;
}

// 创建纸质资料
export interface CreatePaperMaterial {
  paperCode: string;
  paperName: string;
  paperTypeId: number; // 注意这里是ID，不是对象
  fluteType: string;
  isStandard?: boolean;
  productionCode?: string;
  facePaper?: string;
  corePaper1?: string;
  middlePartition1?: string;
  corePaper2?: string;
  middlePartition2?: string;
  corePaper3?: string;
  linerPaper?: string;
  layerCount?: number;
  weightKgPerKsi?: number;
  weightKgPerSqm?: number;
  edgeCrushStrength?: string;
  burstingStrength?: string;
  correspondingStandard?: string;
  defaultSupplier?: string;
  remarks?: string;
  isDisabled?: boolean;
}

// 更新纸质资料
export interface UpdatePaperMaterial extends Partial<CreatePaperMaterial> {}

// 纸质资料查询
export interface PaperMaterialQuery {
  page?: number;
  size?: number;
  paperCode?: string;
  paperName?: string;
  paperType?: string;
  fluteType?: string;
  isStandard?: boolean;
  isDisabled?: boolean;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
}
```

### 纸质类别服务

添加纸质类别服务：

```typescript
// 在 src/services/paperTypeService.ts 中
import { get } from './request';
import type { PaperType } from '@/types/paperType';
import type { ApiResponse } from '@/types/api';

const BASE_URL = '/api/material/paper-types';
const PUBLIC_BASE_URL = '/api/public/material/paper-types';

/**
 * 查询所有纸质类别
 */
export function getAllPaperTypes() {
  return get<PaperType[]>(BASE_URL);
}

/**
 * 根据ID查询纸质类别
 */
export function getPaperTypeById(id: number) {
  return get<PaperType>(`${BASE_URL}/${id}`);
}

/**
 * 查询所有纸质类别（公共接口）
 */
export function getAllPaperTypesPublic() {
  return get<PaperType[]>(PUBLIC_BASE_URL);
}
```

### 纸质资料表单适配

在纸质资料的创建和编辑表单中，需要将原来的纸类输入框改为下拉选择框：

```vue
<template>
  <a-form-item label="纸类" name="paperTypeId" :rules="[{ required: true, message: '请选择纸类' }]">
    <a-select v-model:value="formState.paperTypeId" placeholder="请选择纸类">
      <a-select-option v-for="type in paperTypes" :key="type.id" :value="type.id">
        {{ type.paperTypeName }}
      </a-select-option>
    </a-select>
  </a-form-item>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getAllPaperTypesPublic } from '@/services/paperTypeService';
import type { PaperType } from '@/types/paperType';

const paperTypes = ref<PaperType[]>([]);

// 加载纸质类别
const loadPaperTypes = async () => {
  try {
    const response = await getAllPaperTypesPublic();
    if (response && response.success) {
      paperTypes.value = response.data || [];
    }
  } catch (error) {
    console.error('获取纸质类别失败', error);
  }
};

// 在组件挂载时加载纸质类别
onMounted(() => {
  loadPaperTypes();
});
</script>
```

### 纸质资料列表适配

在纸质资料列表中，需要显示纸类名称而不是纸类ID：

```vue
<template>
  <a-table-column title="纸类" data-index="paperTypeName" />
</template>
```

或者，如果需要显示更多纸类信息：

```vue
<template>
  <a-table-column title="纸类" data-index="paperType">
    <template #default="{ record }">
      {{ record.paperType?.paperTypeName }}
    </template>
  </a-table-column>
</template>
```
