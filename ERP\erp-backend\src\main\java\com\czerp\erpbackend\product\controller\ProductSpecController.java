package com.czerp.erpbackend.product.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.product.dto.CreateSpecRequest;
import com.czerp.erpbackend.product.dto.ProductSpecDTO;
import com.czerp.erpbackend.product.dto.UpdateSpecRequest;
import com.czerp.erpbackend.product.service.ProductSpecService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品规格控制器
 */
@RestController
@RequestMapping("/product-specs")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Product Spec Management", description = "产品规格管理相关接口")
public class ProductSpecController {
    
    private final ProductSpecService specService;
    
    @GetMapping
    @Operation(summary = "获取规格列表", description = "获取所有规格列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-spec:list')")
    public ResponseEntity<ApiResponse<List<ProductSpecDTO>>> getAllSpecs() {
        log.info("Getting all specs");
        List<ProductSpecDTO> specs = specService.findAllSpecs();
        return ResponseEntity.ok(ApiResponse.success(specs));
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取规格详情", description = "根据ID获取规格详细信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-spec:read')")
    public ResponseEntity<ApiResponse<ProductSpecDTO>> getSpec(@PathVariable String id) {
        log.info("Getting spec with id: {}", id);
        ProductSpecDTO spec = specService.findSpecById(id);
        return ResponseEntity.ok(ApiResponse.success(spec));
    }
    
    @PostMapping
    @Operation(summary = "创建规格", description = "创建新规格")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-spec:create')")
    public ResponseEntity<ApiResponse<ProductSpecDTO>> createSpec(@Valid @RequestBody CreateSpecRequest request) {
        log.info("Creating spec with request: {}", request);
        ProductSpecDTO spec = specService.createSpec(request);
        return ResponseEntity.ok(ApiResponse.success(spec));
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新规格", description = "更新规格信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-spec:update')")
    public ResponseEntity<ApiResponse<ProductSpecDTO>> updateSpec(
            @PathVariable String id,
            @Valid @RequestBody UpdateSpecRequest request) {
        log.info("Updating spec with id: {} and request: {}", id, request);
        ProductSpecDTO spec = specService.updateSpec(id, request);
        return ResponseEntity.ok(ApiResponse.success(spec));
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除规格", description = "删除规格")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('product-spec:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteSpec(@PathVariable String id) {
        log.info("Deleting spec with id: {}", id);
        specService.deleteSpec(id);
        return ResponseEntity.ok(ApiResponse.success());
    }
}
