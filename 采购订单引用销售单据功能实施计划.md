# 采购订单引用销售单据功能实施计划

## 功能概述

在采购订单新建页面添加"引用销售单据"按钮，点击后弹出销售订单明细列表（按生产单号维度展示），支持多选，选择后将销售订单明细数据填充到采购订单明细中。

## 实施阶段

### 阶段一：后端API开发

#### 任务1.1：创建销售订单明细查询API

- **目标**：开发一个API，用于查询可用于采购的销售订单明细列表
- **步骤**：
  1. 在`SalesOrderController`中添加新的接口方法
  2. 实现分页、搜索和筛选功能
  3. 返回适合采购订单使用的数据结构
- **预期输出**：
  ```
  GET /api/sales-orders/items-for-purchase
  ```
- **不影响现有逻辑的方式**：
  - 创建新的API端点，不修改现有API
  - 复用现有的服务方法，添加新的查询参数

#### 任务1.2：扩展SalesOrderService

- **目标**：在服务层添加查询销售订单明细的方法
- **步骤**：
  1. 在`SalesOrderService`中添加新的方法
  2. 实现查询逻辑，支持各种筛选条件
  3. 优化查询性能
- **预期输出**：
  ```java
  List<SalesOrderItemDTO> findSalesOrderItemsForPurchase(SalesOrderItemQueryRequest request);
  ```
- **不影响现有逻辑的方式**：
  - 添加新的服务方法，不修改现有方法
  - 使用现有的数据访问层代码

#### 任务1.3：创建数据转换服务（可选）

- **目标**：实现销售订单明细到采购订单明细的转换逻辑
- **步骤**：
  1. 创建`OrderConversionService`
  2. 实现转换方法
  3. 处理特殊业务规则
- **预期输出**：
  ```java
  PurchaseOrderItemDTO convertToPurchaseOrderItem(SalesOrderItemDTO salesOrderItem);
  ```
- **不影响现有逻辑的方式**：
  - 创建新的服务类，不修改现有服务
  - 使用工厂模式或策略模式实现灵活的转换逻辑

### 阶段二：前端组件开发

#### 任务2.1：创建销售订单选择弹窗组件

- **目标**：开发一个弹窗组件，用于展示和选择销售订单明细
- **步骤**：
  1. 创建`SalesOrderSelectionModal.vue`组件
  2. 实现表格展示、搜索、筛选和多选功能
  3. 添加确认和取消按钮
- **预期输出**：
  ```
  src/components/purchase/SalesOrderSelectionModal.vue
  ```
- **不影响现有逻辑的方式**：
  - 创建独立的组件，不修改现有组件
  - 使用props和events进行组件通信

#### 任务2.2：添加销售订单服务

- **目标**：开发前端服务，用于调用销售订单明细查询API
- **步骤**：
  1. 在`salesOrderService.ts`中添加新的方法
  2. 实现API调用和错误处理
- **预期输出**：
  ```typescript
  function getSalesOrderItemsForPurchase(params: SalesOrderItemQueryParams): Promise<ApiResponse<PaginationResult<SalesOrderItem>>>;
  ```
- **不影响现有逻辑的方式**：
  - 添加新的服务方法，不修改现有方法
  - 使用现有的HTTP客户端和错误处理机制

#### 任务2.3：创建数据转换函数

- **目标**：实现前端的销售订单明细到采购订单明细的转换逻辑
- **步骤**：
  1. 创建`orderConverter.ts`
  2. 实现转换函数
  3. 处理字段映射和默认值
- **预期输出**：
  ```typescript
  function convertSalesOrderItemToPurchaseOrderItem(salesItem: SalesOrderItem): PurchaseOrderItem;
  ```
- **不影响现有逻辑的方式**：
  - 创建新的工具函数，不修改现有函数
  - 使用纯函数设计，避免副作用

### 阶段三：集成到采购订单页面

#### 任务3.1：修改采购订单编辑页面

- **目标**：在采购订单编辑页面添加"引用销售单据"按钮和相关逻辑
- **步骤**：
  1. 在`PurchaseOrderEdit.vue`中添加按钮
  2. 添加打开弹窗的方法
  3. 添加处理选中销售订单明细的方法
- **预期输出**：
  - 更新后的`PurchaseOrderEdit.vue`
- **不影响现有逻辑的方式**：
  - 添加新的UI元素和方法，不修改现有逻辑
  - 使用条件渲染，确保新功能不影响现有功能

#### 任务3.2：实现数据填充逻辑

- **目标**：将选中的销售订单明细数据填充到采购订单明细表格中
- **步骤**：
  1. 实现数据转换和填充逻辑
  2. 处理重复数据和冲突
  3. 更新表单状态
- **预期输出**：
  - 更新后的`PurchaseOrderEdit.vue`中的数据处理逻辑
- **不影响现有逻辑的方式**：
  - 在现有数据处理逻辑基础上添加新的逻辑
  - 使用不可变数据模式，避免直接修改现有数据

#### 任务3.3：添加关联信息展示

- **目标**：在采购订单明细中显示关联的销售订单信息
- **步骤**：
  1. 修改明细表格列定义
  2. 添加关联信息的展示逻辑
  3. 优化UI布局
- **预期输出**：
  - 更新后的明细表格，包含销售订单关联信息
- **不影响现有逻辑的方式**：
  - 添加新的表格列，不修改现有列
  - 使用条件渲染，只在有关联信息时显示

