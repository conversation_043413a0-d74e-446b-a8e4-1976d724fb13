# 纸度设置 API 文档

本文档描述了纸度设置模块的API接口定义，供前端开发人员参考。

## 数据结构

### PaperSizeSettingDTO

纸度设置数据传输对象

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| id | Long | 主键ID |
| paperSizeInch | BigDecimal | 纸度(inch) |
| paperSizeCm | BigDecimal | 纸度(cm) |
| maxLossInch | BigDecimal | 最大损耗(inch) |
| maxLossCm | BigDecimal | 最大损耗(cm) |
| createdBy | String | 创建人 |
| createdTime | LocalDateTime | 创建时间 |
| updatedBy | String | 更新人 |
| updatedTime | LocalDateTime | 更新时间 |

### CreatePaperSizeSettingRequest

创建纸度设置请求

| 字段名 | 类型 | 描述 | 验证规则 |
| --- | --- | --- | --- |
| paperSizeInch | BigDecimal | 纸度(inch) | 不能为空，必须大于0，最多8位整数，2位小数 |
| paperSizeCm | BigDecimal | 纸度(cm) | 不能为空，必须大于0，最多8位整数，2位小数 |
| maxLossInch | BigDecimal | 最大损耗(inch) | 不能为空，必须大于等于0，最多8位整数，2位小数 |
| maxLossCm | BigDecimal | 最大损耗(cm) | 不能为空，必须大于等于0，最多8位整数，2位小数 |

### UpdatePaperSizeSettingRequest

更新纸度设置请求

| 字段名 | 类型 | 描述 | 验证规则 |
| --- | --- | --- | --- |
| paperSizeInch | BigDecimal | 纸度(inch) | 不能为空，必须大于0，最多8位整数，2位小数 |
| paperSizeCm | BigDecimal | 纸度(cm) | 不能为空，必须大于0，最多8位整数，2位小数 |
| maxLossInch | BigDecimal | 最大损耗(inch) | 不能为空，必须大于等于0，最多8位整数，2位小数 |
| maxLossCm | BigDecimal | 最大损耗(cm) | 不能为空，必须大于等于0，最多8位整数，2位小数 |

### PaperSizeSettingQueryRequest

查询纸度设置请求

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| keyword | String | 关键字（纸度inch或cm） |
| page | Integer | 页码，从1开始 |
| size | Integer | 每页记录数 |

## API 接口

### 1. 获取纸度设置列表（分页）

- **URL**: `/system/paper-size-settings`
- **Method**: GET
- **权限**: `system:paper-size-setting:list`
- **请求参数**:
  - `keyword`: 关键字（可选）
  - `page`: 页码，默认1
  - `size`: 每页记录数，默认10
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "content": [
        {
          "id": 1,
          "paperSizeInch": 29.00,
          "paperSizeCm": 74.00,
          "maxLossInch": 2.00,
          "maxLossCm": 5.00,
          "createdBy": "admin",
          "createdTime": "2023-01-01T12:00:00",
          "updatedBy": null,
          "updatedTime": null
        },
        // ...更多记录
      ],
      "page": 1,
      "size": 10,
      "totalElements": 30,
      "totalPages": 3
    }
  }
  ```

### 2. 获取所有纸度设置

- **URL**: `/system/paper-size-settings/all`
- **Method**: GET
- **权限**: `system:paper-size-setting:list`
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": [
      {
        "id": 1,
        "paperSizeInch": 29.00,
        "paperSizeCm": 74.00,
        "maxLossInch": 2.00,
        "maxLossCm": 5.00,
        "createdBy": "admin",
        "createdTime": "2023-01-01T12:00:00",
        "updatedBy": null,
        "updatedTime": null
      },
      // ...更多记录
    ]
  }
  ```

### 3. 获取纸度设置详情

- **URL**: `/system/paper-size-settings/{id}`
- **Method**: GET
- **权限**: `system:paper-size-setting:read`
- **路径参数**:
  - `id`: 纸度设置ID
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "paperSizeInch": 29.00,
      "paperSizeCm": 74.00,
      "maxLossInch": 2.00,
      "maxLossCm": 5.00,
      "createdBy": "admin",
      "createdTime": "2023-01-01T12:00:00",
      "updatedBy": null,
      "updatedTime": null
    }
  }
  ```

### 4. 创建纸度设置

- **URL**: `/system/paper-size-settings`
- **Method**: POST
- **权限**: `system:paper-size-setting:create`
- **请求体**:
  ```json
  {
    "paperSizeInch": 29.00,
    "paperSizeCm": 74.00,
    "maxLossInch": 2.00,
    "maxLossCm": 5.00
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "paperSizeInch": 29.00,
      "paperSizeCm": 74.00,
      "maxLossInch": 2.00,
      "maxLossCm": 5.00,
      "createdBy": "admin",
      "createdTime": "2023-01-01T12:00:00",
      "updatedBy": null,
      "updatedTime": null
    }
  }
  ```

### 5. 更新纸度设置

- **URL**: `/system/paper-size-settings/{id}`
- **Method**: PUT
- **权限**: `system:paper-size-setting:update`
- **路径参数**:
  - `id`: 纸度设置ID
- **请求体**:
  ```json
  {
    "paperSizeInch": 29.00,
    "paperSizeCm": 74.00,
    "maxLossInch": 2.00,
    "maxLossCm": 5.00
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "paperSizeInch": 29.00,
      "paperSizeCm": 74.00,
      "maxLossInch": 2.00,
      "maxLossCm": 5.00,
      "createdBy": "admin",
      "createdTime": "2023-01-01T12:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-01-02T12:00:00"
    }
  }
  ```

### 6. 删除纸度设置

- **URL**: `/system/paper-size-settings/{id}`
- **Method**: DELETE
- **权限**: `system:paper-size-setting:delete`
- **路径参数**:
  - `id`: 纸度设置ID
- **响应**:
  ```json
  {
    "code": 200,
    "message": "删除成功",
    "data": null
  }
  ```

### 7. 公共接口 - 获取所有纸度设置

- **URL**: `/public/paper-size-settings`
- **Method**: GET
- **权限**: 无需权限
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": [
      {
        "id": 1,
        "paperSizeInch": 29.00,
        "paperSizeCm": 74.00,
        "maxLossInch": 2.00,
        "maxLossCm": 5.00,
        "createdBy": "admin",
        "createdTime": "2023-01-01T12:00:00",
        "updatedBy": null,
        "updatedTime": null
      },
      // ...更多记录
    ]
  }
  ```

## 错误码

| 错误码 | 描述 |
| --- | --- |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 业务错误

| 错误信息 | 描述 |
| --- | --- |
| "纸度(inch)已存在" | 创建或更新时，纸度(inch)已存在 |
| "纸度设置不存在: {id}" | 查询、更新或删除时，指定ID的纸度设置不存在 |
