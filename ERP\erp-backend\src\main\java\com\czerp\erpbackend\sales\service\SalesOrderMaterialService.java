package com.czerp.erpbackend.sales.service;

import com.czerp.erpbackend.sales.dto.SalesOrderMaterialDTO;

import java.util.List;

/**
 * 销售订单材料信息Service接口
 */
public interface SalesOrderMaterialService {

    /**
     * 根据订单行项目ID查询材料信息
     * @param orderItemId 订单行项目ID
     * @return 材料信息列表
     */
    List<SalesOrderMaterialDTO> getMaterialsByOrderItemId(String orderItemId);

    /**
     * 根据订单ID查询所有行项目的材料信息
     * @param orderId 订单ID
     * @return 材料信息列表
     */
    List<SalesOrderMaterialDTO> getMaterialsByOrderId(String orderId);

    /**
     * 保存材料信息
     * @param materialDTO 材料信息DTO
     * @return 保存后的材料信息
     */
    SalesOrderMaterialDTO saveMaterial(SalesOrderMaterialDTO materialDTO);

    /**
     * 批量保存材料信息
     * @param materialDTOs 材料信息DTO列表
     * @return 保存后的材料信息列表
     */
    List<SalesOrderMaterialDTO> saveMaterials(List<SalesOrderMaterialDTO> materialDTOs);

    /**
     * 更新材料信息
     * @param id 材料信息ID
     * @param materialDTO 材料信息DTO
     * @return 更新后的材料信息
     */
    SalesOrderMaterialDTO updateMaterial(String id, SalesOrderMaterialDTO materialDTO);

    /**
     * 删除材料信息
     * @param id 材料信息ID
     */
    void deleteMaterial(String id);

    /**
     * 根据订单行项目ID删除材料信息
     * @param orderItemId 订单行项目ID
     */
    void deleteMaterialsByOrderItemId(String orderItemId);
}
