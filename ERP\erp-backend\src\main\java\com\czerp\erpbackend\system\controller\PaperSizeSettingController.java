package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.system.dto.CreatePaperSizeSettingRequest;
import com.czerp.erpbackend.system.dto.PaperSizeSettingDTO;
import com.czerp.erpbackend.system.dto.PaperSizeSettingQueryRequest;
import com.czerp.erpbackend.system.dto.UpdatePaperSizeSettingRequest;
import com.czerp.erpbackend.system.entity.PaperSizeSetting;
import com.czerp.erpbackend.system.mapper.PaperSizeSettingMapper;
import com.czerp.erpbackend.system.repository.PaperSizeSettingRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 纸度设置控制器
 */
@RestController
@RequestMapping("/system/paper-size-settings")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Paper Size Setting Management", description = "纸度设置管理相关接口")
public class PaperSizeSettingController {

    private final PaperSizeSettingRepository paperSizeSettingRepository;
    private final PaperSizeSettingMapper paperSizeSettingMapper;

    @GetMapping
    @Operation(summary = "获取纸度设置列表", description = "分页查询纸度设置列表，支持关键字搜索")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:paper-size-setting:list')")
    public ResponseEntity<ApiResponse<PageResponse<PaperSizeSettingDTO>>> getPaperSizeSettings(PaperSizeSettingQueryRequest request) {
        log.info("Getting paper size settings with request: {}", request);

        Pageable pageable = PageRequest.of(
                request.getPage() - 1,
                request.getSize(),
                Sort.by(Sort.Direction.ASC, "paperSizeInch")
        );

        Page<PaperSizeSetting> page = paperSizeSettingRepository.search(request.getKeyword(), pageable);

        List<PaperSizeSettingDTO> content = page.getContent().stream()
                .map(paperSizeSettingMapper::toDto)
                .collect(Collectors.toList());

        PageResponse<PaperSizeSettingDTO> response = new PageResponse<PaperSizeSettingDTO>(
                content,
                page.getTotalElements(),
                page.getTotalPages(),
                page.getNumber() + 1,
                page.getSize()
        );

        return ResponseEntity.ok(ApiResponse.success(response));
    }

    @GetMapping("/all")
    @Operation(summary = "获取所有纸度设置", description = "获取所有未删除的纸度设置列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:paper-size-setting:list')")
    public ResponseEntity<ApiResponse<List<PaperSizeSettingDTO>>> getAllPaperSizeSettings() {
        log.info("Getting all paper size settings");

        List<PaperSizeSetting> settings = paperSizeSettingRepository.findByIsDeletedFalseOrderByPaperSizeInchAsc();
        List<PaperSizeSettingDTO> dtos = settings.stream()
                .map(paperSizeSettingMapper::toDto)
                .collect(Collectors.toList());

        return ResponseEntity.ok(ApiResponse.success(dtos));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取纸度设置详情", description = "根据ID获取纸度设置详情")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:paper-size-setting:read')")
    public ResponseEntity<ApiResponse<PaperSizeSettingDTO>> getPaperSizeSetting(@PathVariable Long id) {
        log.info("Getting paper size setting with id: {}", id);

        PaperSizeSetting setting = paperSizeSettingRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("纸度设置不存在: " + id));

        return ResponseEntity.ok(ApiResponse.success(paperSizeSettingMapper.toDto(setting)));
    }

    @PostMapping
    @Operation(summary = "创建纸度设置", description = "创建新的纸度设置")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:paper-size-setting:create')")
    public ResponseEntity<ApiResponse<PaperSizeSettingDTO>> createPaperSizeSetting(
            @Valid @RequestBody CreatePaperSizeSettingRequest request) {
        log.info("Creating paper size setting with request: {}", request);

        // 检查纸度是否已存在
        if (paperSizeSettingRepository.existsByPaperSizeInch(request.getPaperSizeInch())) {
            return ResponseEntity.badRequest().body(ApiResponse.error("纸度(inch)已存在"));
        }

        PaperSizeSetting setting = paperSizeSettingMapper.toEntity(request);
        setting = paperSizeSettingRepository.save(setting);

        return ResponseEntity.ok(ApiResponse.success(paperSizeSettingMapper.toDto(setting)));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新纸度设置", description = "更新纸度设置信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:paper-size-setting:update')")
    public ResponseEntity<ApiResponse<PaperSizeSettingDTO>> updatePaperSizeSetting(
            @PathVariable Long id,
            @Valid @RequestBody UpdatePaperSizeSettingRequest request) {
        log.info("Updating paper size setting with id: {} and request: {}", id, request);

        PaperSizeSetting setting = paperSizeSettingRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("纸度设置不存在: " + id));

        // 检查纸度是否已存在（排除当前记录）
        if (paperSizeSettingRepository.existsByPaperSizeInchAndIdNot(request.getPaperSizeInch(), id)) {
            return ResponseEntity.badRequest().body(ApiResponse.error("纸度(inch)已存在"));
        }

        paperSizeSettingMapper.updateEntityFromRequest(request, setting);
        setting = paperSizeSettingRepository.save(setting);

        return ResponseEntity.ok(ApiResponse.success(paperSizeSettingMapper.toDto(setting)));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除纸度设置", description = "根据ID删除纸度设置")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:paper-size-setting:delete')")
    public ResponseEntity<ApiResponse<Void>> deletePaperSizeSetting(@PathVariable Long id) {
        log.info("Deleting paper size setting with id: {}", id);

        PaperSizeSetting setting = paperSizeSettingRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("纸度设置不存在: " + id));

        setting.setIsDeleted(true);
        paperSizeSettingRepository.save(setting);

        return ResponseEntity.ok(ApiResponse.<Void>builder()
                .success(true)
                .code("200")
                .message("删除成功")
                .build());
    }
}
