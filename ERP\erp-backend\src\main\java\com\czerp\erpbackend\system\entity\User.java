package com.czerp.erpbackend.system.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 系统用户实体
 */
@Entity
@Table(name = "sys_user")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class User extends BaseEntity {
    
    /**
     * 用户ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;
    
    /**
     * 用户名
     */
    @Column(name = "username", length = 50, nullable = false, unique = true)
    private String username;
    
    /**
     * 密码
     */
    @Column(name = "password", length = 100, nullable = false)
    private String password;
    
    /**
     * 姓名
     */
    @Column(name = "nickname", length = 50, nullable = false)
    private String nickname;
    
    /**
     * 头像URL
     */
    @Column(name = "avatar", length = 255)
    private String avatar;
    
    /**
     * 邮箱
     */
    @Column(name = "email", length = 100, nullable = false, unique = true)
    private String email;
    
    /**
     * 手机号
     */
    @Column(name = "phone", length = 20)
    private String phone;
    
    /**
     * 状态(active-正常,inactive-停用,locked-锁定)
     */
    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";
    
    /**
     * 部门ID
     */
    @Column(name = "department_id", length = 36)
    private String departmentId;
    
    /**
     * 部门
     */
    @ManyToOne
    @JoinColumn(name = "department_id", insertable = false, updatable = false)
    private Department department;
    
    /**
     * 职位
     */
    @Column(name = "position", length = 50)
    private String position;
    
    /**
     * 最后登录时间
     */
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;
} 