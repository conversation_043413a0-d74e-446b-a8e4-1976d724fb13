package com.czerp.erpbackend.customer.dto;

import lombok.Data;

/**
 * 客户盒式信息查询请求
 */
@Data
public class CustomerBoxInfoQueryRequest {

    /**
     * 关键字（盒式编码或盒式名称）
     */
    private String keyword;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否默认盒式
     */
    private Boolean isDefault;

    /**
     * 页码（从1开始）
     */
    private int page = 1;

    /**
     * 每页大小
     */
    private int size = 10;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向（asc/desc）
     */
    private String sortDirection;
}
