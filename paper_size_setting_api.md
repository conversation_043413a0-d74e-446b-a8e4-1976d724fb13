# 纸度设置 API 文档

本文档描述了纸度设置模块的API接口定义，供前端开发人员参考。

## 基本信息

- 基础路径: `/api/system/paper-size-settings`
- 权限前缀: `system:paper-size-setting`

## 数据结构

### PaperSizeSettingDTO

纸度设置数据传输对象

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| id | Long | 主键ID |
| paperSizeInch | BigDecimal | 纸度(inch) |
| paperSizeCm | BigDecimal | 纸度(cm) |
| maxLossInch | BigDecimal | 最大损耗(inch) |
| maxLossCm | BigDecimal | 最大损耗(cm) |
| createdBy | String | 创建人 |
| createdTime | LocalDateTime | 创建时间 |
| updatedBy | String | 更新人 |
| updatedTime | LocalDateTime | 更新时间 |

### CreatePaperSizeSettingRequest

创建纸度设置请求

| 字段名 | 类型 | 描述 | 验证规则 |
| --- | --- | --- | --- |
| paperSizeInch | BigDecimal | 纸度(inch) | 不能为空，必须大于0，最多8位整数，2位小数 |
| paperSizeCm | BigDecimal | 纸度(cm) | 不能为空，必须大于0，最多8位整数，2位小数 |
| maxLossInch | BigDecimal | 最大损耗(inch) | 不能为空，必须大于等于0，最多8位整数，2位小数 |
| maxLossCm | BigDecimal | 最大损耗(cm) | 不能为空，必须大于等于0，最多8位整数，2位小数 |

### UpdatePaperSizeSettingRequest

更新纸度设置请求

| 字段名 | 类型 | 描述 | 验证规则 |
| --- | --- | --- | --- |
| paperSizeInch | BigDecimal | 纸度(inch) | 不能为空，必须大于0，最多8位整数，2位小数 |
| paperSizeCm | BigDecimal | 纸度(cm) | 不能为空，必须大于0，最多8位整数，2位小数 |
| maxLossInch | BigDecimal | 最大损耗(inch) | 不能为空，必须大于等于0，最多8位整数，2位小数 |
| maxLossCm | BigDecimal | 最大损耗(cm) | 不能为空，必须大于等于0，最多8位整数，2位小数 |

### PaperSizeSettingQueryRequest

查询纸度设置请求

| 字段名 | 类型 | 描述 | 默认值 |
| --- | --- | --- | --- |
| keyword | String | 关键字（纸度inch或cm） | 无 |
| page | Integer | 页码 | 1 |
| size | Integer | 每页大小 | 10 |

### PageResponse<PaperSizeSettingDTO>

分页响应

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| content | List<PaperSizeSettingDTO> | 数据列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |
| page | Integer | 当前页码 |
| size | Integer | 每页大小 |

## API 接口

### 1. 获取纸度设置列表

分页查询纸度设置列表，支持关键字搜索。

- **URL**: `/api/system/paper-size-settings`
- **方法**: `GET`
- **权限**: `system:paper-size-setting:list`
- **请求参数**:

| 参数名 | 类型 | 位置 | 必填 | 描述 | 默认值 |
| --- | --- | --- | --- | --- | --- |
| keyword | String | query | 否 | 关键字（纸度inch或cm） | 无 |
| page | Integer | query | 否 | 页码 | 1 |
| size | Integer | query | 否 | 每页大小 | 10 |

- **响应示例**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": 1,
        "paperSizeInch": 12.00,
        "paperSizeCm": 30.48,
        "maxLossInch": 0.50,
        "maxLossCm": 1.27,
        "createdBy": "admin",
        "createdTime": "2023-01-01T12:00:00",
        "updatedBy": "admin",
        "updatedTime": "2023-01-01T12:00:00"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "page": 1,
    "size": 10
  }
}
```

### 2. 获取纸度设置详情

根据ID获取纸度设置详情。

- **URL**: `/api/system/paper-size-settings/{id}`
- **方法**: `GET`
- **权限**: `system:paper-size-setting:read`
- **路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| id | Long | 是 | 纸度设置ID |

- **响应示例**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "paperSizeInch": 12.00,
    "paperSizeCm": 30.48,
    "maxLossInch": 0.50,
    "maxLossCm": 1.27,
    "createdBy": "admin",
    "createdTime": "2023-01-01T12:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-01-01T12:00:00"
  }
}
```

### 3. 创建纸度设置

创建新的纸度设置。

- **URL**: `/api/system/paper-size-settings`
- **方法**: `POST`
- **权限**: `system:paper-size-setting:create`
- **请求体**:

```json
{
  "paperSizeInch": 12.00,
  "paperSizeCm": 30.48,
  "maxLossInch": 0.50,
  "maxLossCm": 1.27
}
```

- **响应示例**:

```json
{
  "success": true,
  "code": "200",
  "message": "创建成功",
  "data": {
    "id": 1,
    "paperSizeInch": 12.00,
    "paperSizeCm": 30.48,
    "maxLossInch": 0.50,
    "maxLossCm": 1.27,
    "createdBy": "admin",
    "createdTime": "2023-01-01T12:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-01-01T12:00:00"
  }
}
```

### 4. 更新纸度设置

更新纸度设置信息。

- **URL**: `/api/system/paper-size-settings/{id}`
- **方法**: `PUT`
- **权限**: `system:paper-size-setting:update`
- **路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| id | Long | 是 | 纸度设置ID |

- **请求体**:

```json
{
  "paperSizeInch": 12.00,
  "paperSizeCm": 30.48,
  "maxLossInch": 0.50,
  "maxLossCm": 1.27
}
```

- **响应示例**:

```json
{
  "success": true,
  "code": "200",
  "message": "更新成功",
  "data": {
    "id": 1,
    "paperSizeInch": 12.00,
    "paperSizeCm": 30.48,
    "maxLossInch": 0.50,
    "maxLossCm": 1.27,
    "createdBy": "admin",
    "createdTime": "2023-01-01T12:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-01-01T12:00:00"
  }
}
```

### 5. 删除纸度设置

根据ID删除纸度设置。

- **URL**: `/api/system/paper-size-settings/{id}`
- **方法**: `DELETE`
- **权限**: `system:paper-size-setting:delete`
- **路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| id | Long | 是 | 纸度设置ID |

- **响应示例**:

```json
{
  "success": true,
  "code": "200",
  "message": "删除成功"
}
```

### 6. 批量删除纸度设置

批量删除纸度设置。

- **URL**: `/api/system/paper-size-settings/batch`
- **方法**: `DELETE`
- **权限**: `system:paper-size-setting:delete`
- **请求体**:

```json
[1, 2, 3]
```

- **响应示例**:

```json
{
  "success": true,
  "code": "200",
  "message": "批量删除成功"
}
```

## 公共接口

### 1. 获取所有纸度设置

获取所有未删除的纸度设置列表，无需权限验证。

- **URL**: `/api/public/paper-size-settings`
- **方法**: `GET`
- **权限**: 无需权限
- **响应示例**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "paperSizeInch": 12.00,
      "paperSizeCm": 30.48,
      "maxLossInch": 0.50,
      "maxLossCm": 1.27,
      "createdBy": "admin",
      "createdTime": "2023-01-01T12:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-01-01T12:00:00"
    }
  ]
}
```

## 错误码

| 错误码 | 描述 |
| --- | --- |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如纸度已存在） |
| 500 | 服务器内部错误 |

## 注意事项

1. 纸度(inch)在系统中是唯一的，不能重复
2. 创建和更新时，纸度(inch)和纸度(cm)必须大于0
3. 最大损耗(inch)和最大损耗(cm)必须大于等于0
4. 所有数值类型字段最多支持8位整数，2位小数
