package com.czerp.erpbackend.customer.dto;

import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 更新客户请求
 */
@Data
public class UpdateCustomerRequest {
    
    /**
     * 客户名称
     */
    @Size(max = 100, message = "客户名称长度不能超过100个字符")
    private String customerName;
    
    /**
     * 客户分类ID
     */
    private String categoryId;
    
    /**
     * 客户全称
     */
    @Size(max = 200, message = "客户全称长度不能超过200个字符")
    private String fullName;
    
    /**
     * 电话
     */
    @Size(max = 20, message = "电话长度不能超过20个字符")
    private String phone;
    
    /**
     * 传真
     */
    @Size(max = 20, message = "传真长度不能超过20个字符")
    private String fax;
    
    /**
     * 联系人
     */
    @Size(max = 50, message = "联系人长度不能超过50个字符")
    private String contactPerson;
    
    /**
     * 手机
     */
    @Size(max = 20, message = "手机长度不能超过20个字符")
    private String mobile;
    
    /**
     * 地址
     */
    @Size(max = 200, message = "地址长度不能超过200个字符")
    private String address;
    
    /**
     * 收货地址
     */
    @Size(max = 200, message = "收货地址长度不能超过200个字符")
    private String shippingAddress;
    
    /**
     * 付款方式
     */
    @Size(max = 50, message = "付款方式长度不能超过50个字符")
    private String paymentMethod;
    
    /**
     * 税率%
     */
    private BigDecimal taxRate;
    
    /**
     * 所属总公司
     */
    @Size(max = 100, message = "所属总公司长度不能超过100个字符")
    private String parentCompany;
    
    /**
     * 地区
     */
    @Size(max = 50, message = "地区长度不能超过50个字符")
    private String region;
    
    /**
     * 销售员
     */
    @Size(max = 50, message = "销售员长度不能超过50个字符")
    private String salesPerson;
    
    /**
     * 跟单员
     */
    @Size(max = 50, message = "跟单员长度不能超过50个字符")
    private String orderTracker;
    
    /**
     * 邮编
     */
    @Size(max = 20, message = "邮编长度不能超过20个字符")
    private String postalCode;
    
    /**
     * 收货人
     */
    @Size(max = 50, message = "收货人长度不能超过50个字符")
    private String receiver;
    
    /**
     * 币别
     */
    @Size(max = 20, message = "币别长度不能超过20个字符")
    private String currency;
    
    /**
     * 默认报价单位
     */
    @Size(max = 20, message = "默认报价单位长度不能超过20个字符")
    private String defaultQuoteUnit;
    
    /**
     * 订单备品比例%
     */
    private BigDecimal orderSpareRatio;
    
    /**
     * 单价小数位
     */
    private Integer priceDecimalPlaces;
    
    /**
     * 金额小数位
     */
    private Integer amountDecimalPlaces;
    
    /**
     * 单重小数位
     */
    private Integer unitWeightDecimalPlaces;
    
    /**
     * 总重小数位
     */
    private Integer totalWeightDecimalPlaces;
    
    /**
     * 单位面积小数位
     */
    private Integer unitAreaDecimalPlaces;
    
    /**
     * 总面积小数位
     */
    private Integer totalAreaDecimalPlaces;
    
    /**
     * 特殊月结日期
     */
    private Integer specialMonthlySettlementDate;
    
    /**
     * 可用额度金额
     */
    private BigDecimal availableCreditAmount;
    
    /**
     * 打印抬头
     */
    @Size(max = 200, message = "打印抬头长度不能超过200个字符")
    private String printHeader;
    
    /**
     * 行业
     */
    @Size(max = 100, message = "行业长度不能超过100个字符")
    private String industry;
    
    /**
     * 停用
     */
    private Boolean disabled;
}
