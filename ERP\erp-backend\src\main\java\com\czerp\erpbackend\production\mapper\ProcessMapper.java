package com.czerp.erpbackend.production.mapper;

import com.czerp.erpbackend.production.dto.CreateProcessRequest;
import com.czerp.erpbackend.production.dto.ProcessDTO;
import com.czerp.erpbackend.production.dto.UpdateProcessRequest;
import com.czerp.erpbackend.production.entity.Process;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 工序Mapper
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProcessMapper {
    
    /**
     * 实体转DTO
     * @param entity 实体
     * @return DTO
     */
    @Mapping(target = "createTime", source = "createdTime")
    @Mapping(target = "updateTime", source = "updatedTime")
    ProcessDTO toDto(Process entity);
    
    /**
     * 实体列表转DTO列表
     * @param entities 实体列表
     * @return DTO列表
     */
    List<ProcessDTO> toDtoList(List<Process> entities);
    
    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdByName", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedByName", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    Process toEntity(CreateProcessRequest request);
    
    /**
     * 更新实体
     * @param request 更新请求
     * @param entity 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdByName", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedByName", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateProcessRequest request, @MappingTarget Process entity);
}
