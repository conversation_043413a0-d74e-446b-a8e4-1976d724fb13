package com.czerp.erpbackend.material.mapper;

import com.czerp.erpbackend.material.dto.PaperMaterialDTO;
import com.czerp.erpbackend.material.dto.PaperTypeDTO;
import com.czerp.erpbackend.material.entity.PaperMaterial;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 纸质资料Mapper辅助类
 * 用于解决MapStruct生成的映射器在处理paperType属性时没有正确映射addMarginMm字段的问题
 */
@Component
public class PaperMaterialMapperHelper {

    private final PaperTypeMapperHelper paperTypeMapperHelper;

    public PaperMaterialMapperHelper(PaperTypeMapperHelper paperTypeMapperHelper) {
        this.paperTypeMapperHelper = paperTypeMapperHelper;
    }

    /**
     * 手动实现实体转DTO，确保paperType属性中的所有字段都被正确映射
     * @param entity 实体
     * @return DTO
     */
    public PaperMaterialDTO manualToDto(PaperMaterial entity) {
        if (entity == null) {
            return null;
        }

        // 使用PaperTypeMapperHelper处理paperType属性
        PaperTypeDTO paperTypeDTO = null;
        if (entity.getPaperType() != null) {
            paperTypeDTO = paperTypeMapperHelper.manualToDto(entity.getPaperType());
        }

        return PaperMaterialDTO.builder()
                .id(entity.getId())
                .paperCode(entity.getPaperCode())
                .paperName(entity.getPaperName())
                .paperType(paperTypeDTO)
                .paperTypeName(entity.getPaperType() != null ? entity.getPaperType().getPaperTypeName() : null)
                .fluteType(entity.getFluteType())
                .isStandard(entity.getIsStandard())
                .productionCode(entity.getProductionCode())
                .facePaper(entity.getFacePaper())
                .corePaper1(entity.getCorePaper1())
                .middlePartition1(entity.getMiddlePartition1())
                .corePaper2(entity.getCorePaper2())
                .middlePartition2(entity.getMiddlePartition2())
                .corePaper3(entity.getCorePaper3())
                .linerPaper(entity.getLinerPaper())
                .layerCount(entity.getLayerCount())
                .weightKgPerKsi(entity.getWeightKgPerKsi())
                .weightKgPerSqm(entity.getWeightKgPerSqm())
                .edgeCrushStrength(entity.getEdgeCrushStrength())
                .burstingStrength(entity.getBurstingStrength())
                .correspondingStandard(entity.getCorrespondingStandard())
                .defaultSupplier(entity.getDefaultSupplier())
                .remarks(entity.getRemarks())
                .isDisabled(entity.getIsDisabled())
                .createdBy(entity.getCreatedBy())
                .createdTime(entity.getCreatedTime())
                .updatedBy(entity.getUpdatedBy())
                .updatedTime(entity.getUpdatedTime())
                .build();
    }

    /**
     * 手动实现实体列表转DTO列表
     * @param entities 实体列表
     * @return DTO列表
     */
    public List<PaperMaterialDTO> manualToDtoList(List<PaperMaterial> entities) {
        if (entities == null) {
            return null;
        }

        List<PaperMaterialDTO> dtoList = new ArrayList<>(entities.size());
        for (PaperMaterial entity : entities) {
            dtoList.add(manualToDto(entity));
        }
        return dtoList;
    }
}
