package com.czerp.erpbackend.purchase.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 供应商查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierQueryRequest {
    
    /**
     * 关键字（编码/名称）
     */
    private String keyword;
    
    /**
     * 供应商分类ID
     */
    private String categoryId;
    
    /**
     * 状态(active-启用,inactive-停用)
     */
    private String status;
    
    /**
     * 行业
     */
    private String industry;
    
    /**
     * 地区
     */
    private String region;
    
    /**
     * 最近下单开始日期
     */
    private LocalDate lastOrderStartDate;
    
    /**
     * 最近下单结束日期
     */
    private LocalDate lastOrderEndDate;
    
    /**
     * 最近收货开始日期
     */
    private LocalDate lastReceiveStartDate;
    
    /**
     * 最近收货结束日期
     */
    private LocalDate lastReceiveEndDate;
    
    /**
     * 当前页码
     */
    private Integer page;
    
    /**
     * 每页条数
     */
    private Integer size;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方向(asc-升序,desc-降序)
     */
    private String sortDirection;
}
