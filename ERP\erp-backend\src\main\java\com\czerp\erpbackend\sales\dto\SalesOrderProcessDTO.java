package com.czerp.erpbackend.sales.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 销售订单工序DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderProcessDTO {

    /**
     * 工序ID
     */
    private String id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单明细项ID
     */
    private String orderItemId;

    /**
     * 工序顺序
     */
    private Integer sequence;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 工艺要求
     */
    private String processRequirements;

    /**
     * 印版编号
     */
    private String plateNumber;

    /**
     * 水墨编号
     */
    private String inkNumber;

    /**
     * 水墨名称
     */
    private String inkName;

    /**
     * 颜色数
     */
    private Integer colorCount;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
