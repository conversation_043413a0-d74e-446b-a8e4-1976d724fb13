-- 销售订单底部区域数据表结构
CREATE TABLE sales_order_material (
    id VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    order_item_id VARCHAR(36) NOT NULL COMMENT '销售订单行项目ID',
    paper_type VARCHAR(100) COMMENT '纸质',
    paper_width DECIMAL(10,2) COMMENT '纸度',
    paper_length DECIMAL(10,2) COMMENT '纸长',
    width_opening DECIMAL(10,2) COMMENT '度开',
    length_opening DECIMAL(10,2) COMMENT '长开',
    board_count INT COMMENT '纸板数',
    board_loss DECIMAL(10,2) COMMENT '纸板损耗',
    material_usage DECIMAL(10,2) COMMENT '原料用量',
    `usage` DECIMAL(10,2) COMMENT '用量',
    method VARCHAR(50) COMMENT '方式',
    creasing_size_width DECIMAL(10,2) COMMENT '压线尺寸(纸度)',
    creasing_method VARCHAR(50) COMMENT '压线方式',
    actual_material_width DECIMAL(10,2) COMMENT '实际用料宽',
    actual_material_length DECIMAL(10,2) COMMENT '实际用料长',
    die_cutting_model VARCHAR(100) COMMENT '啤模',
    die_cutting_model_no VARCHAR(50) COMMENT '啤模编号',
    die_cutting_count INT COMMENT '模开数',
    die_cutting_position VARCHAR(100) COMMENT '啤模位置',
    unit VARCHAR(20) COMMENT '单位',
    current_inventory INT COMMENT '当前库存',
    available_inventory INT COMMENT '可用库存',
    use_inventory_count INT COMMENT '使用库存数',
    actual_material_length_converted DECIMAL(10,2) COMMENT '实际用料长(转换)',
    actual_material_width_converted DECIMAL(10,2) COMMENT '实际用料宽(转换)',
    supplier VARCHAR(100) COMMENT '供应商',
    material_remark VARCHAR(500) COMMENT '材料备注',
    purchased_count INT COMMENT '已采购数',
    die_press_line VARCHAR(100) COMMENT '啤模压线',
    creasing_size_length DECIMAL(10,2) COMMENT '压线尺寸(纸长)',
    received_count INT COMMENT '已入库数',
    material_issued_count INT COMMENT '已领料数',
    INDEX idx_order_item_id (order_item_id),
    INDEX idx_paper_type (paper_type),
    INDEX idx_die_cutting_model_no (die_cutting_model_no),
    INDEX idx_supplier (supplier),
    FOREIGN KEY (order_item_id) REFERENCES sales_order_item(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单材料信息表';