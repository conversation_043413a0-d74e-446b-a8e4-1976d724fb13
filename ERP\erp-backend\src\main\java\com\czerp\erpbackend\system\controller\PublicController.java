package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.system.dto.PermissionDTO;
import com.czerp.erpbackend.system.dto.RoleDTO;
import com.czerp.erpbackend.system.entity.Department;
import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.User;
import com.czerp.erpbackend.system.repository.DepartmentRepository;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import com.czerp.erpbackend.system.repository.UserRepository;
import com.czerp.erpbackend.system.repository.UserRoleRepository;
import com.czerp.erpbackend.system.service.PermissionService;
import com.czerp.erpbackend.system.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 公共接口控制器
 */
@RestController
@RequestMapping("/public")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Public API", description = "公共接口")
public class PublicController {

    private final PermissionService permissionService;
    private final RoleService roleService;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final UserRoleRepository userRoleRepository;
    private final RolePermissionRepository rolePermissionRepository;
    private final DepartmentRepository departmentRepository;

    @GetMapping("/permissions")
    @Operation(summary = "获取所有权限", description = "获取系统中所有权限列表")
    public ResponseEntity<ApiResponse<List<PermissionDTO>>> getAllPermissions() {
        log.debug("Getting all permissions (public)");
        List<PermissionDTO> permissions = permissionService.findAllPermissions();
        return ResponseEntity.ok(ApiResponse.success(permissions));
    }

    @GetMapping("/roles")
    @Operation(summary = "获取所有角色", description = "获取系统中所有角色列表")
    public ResponseEntity<ApiResponse<List<RoleDTO>>> getAllRoles() {
        log.debug("Getting all roles (public)");
        List<RoleDTO> roles = roleService.findAllRoles();
        return ResponseEntity.ok(ApiResponse.success(roles));
    }

    @GetMapping("/departments")
    @Operation(summary = "获取部门列表", description = "获取系统中所有部门列表")
    public ResponseEntity<ApiResponse<List<Department>>> getAllDepartments() {
        log.debug("Getting all departments (public)");
        List<Department> departments = departmentRepository.findAll();
        return ResponseEntity.ok(ApiResponse.success(departments));
    }

    @GetMapping("/system-info")
    @Operation(summary = "获取系统信息", description = "获取系统基本信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemInfo() {
        log.debug("Getting system info");

        Map<String, Object> info = new HashMap<>();
        info.put("userCount", userRepository.count());
        info.put("roleCount", roleRepository.count());
        info.put("permissionCount", permissionRepository.count());

        // 检查管理员用户
        Optional<User> adminUser = userRepository.findByUsername("admin");
        if (adminUser.isPresent()) {
            User admin = adminUser.get();
            Map<String, Object> adminInfo = new HashMap<>();
            adminInfo.put("id", admin.getId());
            adminInfo.put("username", admin.getUsername());
            adminInfo.put("name", admin.getNickname());
            adminInfo.put("status", admin.getStatus());

            // 获取管理员角色
            List<String> roleIds = userRoleRepository.findRoleIdsByUserId(admin.getId());
            List<Role> roles = roleRepository.findAllById(roleIds);
            adminInfo.put("roles", roles.stream().map(Role::getCode).toList());

            // 获取管理员权限
            List<String> permissionCodes = permissionService.findPermissionCodesByUserId(admin.getId());
            adminInfo.put("permissions", permissionCodes);

            info.put("adminUser", adminInfo);
        }

        return ResponseEntity.ok(ApiResponse.success(info));
    }
}
