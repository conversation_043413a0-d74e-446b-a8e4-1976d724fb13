package com.czerp.erpbackend.system.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计量单位查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeasurementUnitQueryRequest {

    /**
     * 关键词（单位名称）
     */
    private String keyword;

    /**
     * 状态(active-启用,inactive-停用)
     */
    private String status;

    /**
     * 是否为尺寸单位
     */
    private Boolean isDimensionUnit;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向
     */
    private String sortDirection;

    /**
     * 页码（从1开始）
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;
}
