package com.czerp.erpbackend.system.mapper;

import com.czerp.erpbackend.system.dto.CreateUserRequest;
import com.czerp.erpbackend.system.dto.UpdateUserRequest;
import com.czerp.erpbackend.system.dto.UserDTO;
import com.czerp.erpbackend.system.entity.User;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 用户映射器
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        uses = {DepartmentMapper.class, RoleMapper.class})
public interface UserMapper {

    /**
     * 用户实体转DTO
     * @param user 用户实体
     * @return 用户DTO
     */
    @Mapping(target = "department", source = "departmentId")
    @Mapping(target = "createTime", source = "createdTime")
    @Mapping(target = "updateTime", source = "updatedTime")
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "permissions", ignore = true)
    @Mapping(target = "departmentName", expression = "java(user.getDepartment() != null ? user.getDepartment().getName() : null)")
    UserDTO toDto(User user);

    /**
     * 创建用户请求转用户实体
     * @param request 创建用户请求
     * @return 用户实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "departmentId", source = "department")
    @Mapping(target = "department", ignore = true)
    @Mapping(target = "avatar", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "lastLoginTime", ignore = true)
    User toEntity(CreateUserRequest request);

    /**
     * 更新用户
     * @param request 更新用户请求
     * @param user 用户实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "username", ignore = true)
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "departmentId", source = "department")
    @Mapping(target = "department", ignore = true)
    @Mapping(target = "avatar", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "lastLoginTime", ignore = true)
    void updateEntity(UpdateUserRequest request, @MappingTarget User user);
}