# 供应商档案 API 接口文档

## 基础信息

- **基础路径**: `/api`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **响应格式**: 所有API响应都遵循以下格式：

```json
{
  "success": true,  // 是否成功
  "code": "200",    // 状态码
  "message": "操作成功", // 响应消息
  "data": {}        // 响应数据
}
```

## 1. 供应商管理接口

### 1.1 分页查询供应商列表

- **接口URL**: `GET /api/suppliers`
- **请求方式**: GET
- **权限要求**: `supplier:read`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| keyword | String | 否 | 关键字（编码/名称） | "供应商1" |
| categoryId | String | 否 | 供应商分类ID | "1001" |
| status | String | 否 | 状态(active-启用,inactive-停用) | "active" |
| industry | String | 否 | 行业 | "制造业" |
| region | String | 否 | 地区 | "上海" |
| lastOrderStartDate | Date | 否 | 最近下单开始日期 | "2023-01-01" |
| lastOrderEndDate | Date | 否 | 最近下单结束日期 | "2023-12-31" |
| lastReceiveStartDate | Date | 否 | 最近收货开始日期 | "2023-01-01" |
| lastReceiveEndDate | Date | 否 | 最近收货结束日期 | "2023-12-31" |
| page | Integer | 否 | 当前页码(从1开始) | 1 |
| size | Integer | 否 | 每页条数 | 10 |
| sortField | String | 否 | 排序字段 | "supplierCode" |
| sortDirection | String | 否 | 排序方向(asc-升序,desc-降序) | "asc" |

- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": 1,
        "supplierCode": "S001",
        "supplierName": "供应商1",
        "fullName": "供应商1有限公司",
        "categoryId": "1001",
        "categoryName": "原材料供应商",
        "industry": "制造业",
        "phone": "021-********",
        "fax": "021-********",
        "contactPerson": "张三",
        "mobile": "***********",
        "address": "上海市浦东新区XX路XX号",
        "region": "上海",
        "lastOrderDate": "2023-06-15",
        "lastReceiveDate": "2023-06-20",
        "priceDecimalPlaces": 2,
        "amountDecimalPlaces": 2,
        "unitWeightDecimalPlaces": 3,
        "totalWeightDecimalPlaces": 3,
        "daysWithoutOrder": 30,
        "daysWithoutReceive": 25,
        "taxNumber": "91310000XXXXXXXX",
        "bankName": "中国银行",
        "bankAccount": "****************",
        "paymentTerms": "月结30天",
        "status": "active",
        "remark": "优质供应商",
        "createdBy": "admin",
        "createTime": "2023-01-01T10:00:00",
        "updatedBy": "admin",
        "updateTime": "2023-06-01T15:30:00"
      }
    ],
    "totalElements": 100,
    "totalPages": 10,
    "page": 1,
    "size": 10,
    "first": true,
    "last": false,
    "empty": false
  }
}
```

### 1.2 查询所有供应商

- **接口URL**: `GET /api/suppliers/all`
- **请求方式**: GET
- **权限要求**: `supplier:read`
- **请求参数**: 无
- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "supplierCode": "S001",
      "supplierName": "供应商1",
      // 其他字段同上
    }
  ]
}
```

### 1.3 根据ID查询供应商

- **接口URL**: `GET /api/suppliers/{id}`
- **请求方式**: GET
- **权限要求**: `supplier:read`
- **路径参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| id | Long | 是 | 供应商ID | 1 |

- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "supplierCode": "S001",
    "supplierName": "供应商1",
    // 其他字段同上
  }
}
```

### 1.4 创建供应商

- **接口URL**: `POST /api/suppliers`
- **请求方式**: POST
- **权限要求**: `supplier:create`
- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| supplierCode | String | 是 | 供应商编码 | "S001" |
| supplierName | String | 是 | 供应商名称 | "供应商1" |
| fullName | String | 否 | 供应商全称 | "供应商1有限公司" |
| categoryId | String | 否 | 供应商分类ID | "1001" |
| industry | String | 否 | 行业 | "制造业" |
| phone | String | 否 | 电话 | "021-********" |
| fax | String | 否 | 传真 | "021-********" |
| contactPerson | String | 否 | 联系人 | "张三" |
| mobile | String | 否 | 手机 | "***********" |
| address | String | 否 | 地址 | "上海市浦东新区XX路XX号" |
| region | String | 否 | 地区 | "上海" |
| priceDecimalPlaces | Integer | 否 | 单价小数位 | 2 |
| amountDecimalPlaces | Integer | 否 | 金额小数位 | 2 |
| unitWeightDecimalPlaces | Integer | 否 | 单重小数位 | 3 |
| totalWeightDecimalPlaces | Integer | 否 | 总重小数位 | 3 |
| taxNumber | String | 否 | 税号 | "91310000XXXXXXXX" |
| bankName | String | 否 | 开户行 | "中国银行" |
| bankAccount | String | 否 | 银行账号 | "****************" |
| paymentTerms | String | 否 | 付款条件 | "月结30天" |
| status | String | 否 | 状态(active-启用,inactive-停用) | "active" |
| remark | String | 否 | 备注 | "优质供应商" |

- **请求示例**:

```json
{
  "supplierCode": "S001",
  "supplierName": "供应商1",
  "fullName": "供应商1有限公司",
  "categoryId": "1001",
  "industry": "制造业",
  "phone": "021-********",
  "fax": "021-********",
  "contactPerson": "张三",
  "mobile": "***********",
  "address": "上海市浦东新区XX路XX号",
  "region": "上海",
  "priceDecimalPlaces": 2,
  "amountDecimalPlaces": 2,
  "unitWeightDecimalPlaces": 3,
  "totalWeightDecimalPlaces": 3,
  "taxNumber": "91310000XXXXXXXX",
  "bankName": "中国银行",
  "bankAccount": "****************",
  "paymentTerms": "月结30天",
  "status": "active",
  "remark": "优质供应商"
}
```

- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "supplierCode": "S001",
    "supplierName": "供应商1",
    // 其他字段同上
  }
}
```

### 1.5 更新供应商

- **接口URL**: `PUT /api/suppliers/{id}`
- **请求方式**: PUT
- **权限要求**: `supplier:update`
- **路径参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| id | Long | 是 | 供应商ID | 1 |

- **请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| supplierName | String | 否 | 供应商名称 | "供应商1" |
| fullName | String | 否 | 供应商全称 | "供应商1有限公司" |
| categoryId | String | 否 | 供应商分类ID | "1001" |
| industry | String | 否 | 行业 | "制造业" |
| phone | String | 否 | 电话 | "021-********" |
| fax | String | 否 | 传真 | "021-********" |
| contactPerson | String | 否 | 联系人 | "张三" |
| mobile | String | 否 | 手机 | "***********" |
| address | String | 否 | 地址 | "上海市浦东新区XX路XX号" |
| region | String | 否 | 地区 | "上海" |
| priceDecimalPlaces | Integer | 否 | 单价小数位 | 2 |
| amountDecimalPlaces | Integer | 否 | 金额小数位 | 2 |
| unitWeightDecimalPlaces | Integer | 否 | 单重小数位 | 3 |
| totalWeightDecimalPlaces | Integer | 否 | 总重小数位 | 3 |
| taxNumber | String | 否 | 税号 | "91310000XXXXXXXX" |
| bankName | String | 否 | 开户行 | "中国银行" |
| bankAccount | String | 否 | 银行账号 | "****************" |
| paymentTerms | String | 否 | 付款条件 | "月结30天" |
| status | String | 否 | 状态(active-启用,inactive-停用) | "active" |
| remark | String | 否 | 备注 | "优质供应商" |

- **请求示例**:

```json
{
  "supplierName": "供应商1(更新)",
  "contactPerson": "李四",
  "mobile": "***********",
  "address": "上海市浦东新区YY路YY号",
  "remark": "优质供应商，合作良好"
}
```

- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "supplierCode": "S001",
    "supplierName": "供应商1(更新)",
    // 其他字段同上，包含更新后的值
  }
}
```

### 1.6 删除供应商

- **接口URL**: `DELETE /api/suppliers/{id}`
- **请求方式**: DELETE
- **权限要求**: `supplier:delete`
- **路径参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| id | Long | 是 | 供应商ID | 1 |

- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

### 1.7 批量删除供应商

- **接口URL**: `DELETE /api/suppliers/batch`
- **请求方式**: DELETE
- **权限要求**: `supplier:delete`
- **请求参数**:

```json
[1, 2, 3]  // 供应商ID列表
```

- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

### 1.8 切换供应商状态

- **接口URL**: `PUT /api/suppliers/{id}/status`
- **请求方式**: PUT
- **权限要求**: `supplier:update`
- **路径参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| id | Long | 是 | 供应商ID | 1 |

- **查询参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| status | String | 是 | 状态(active-启用,inactive-停用) | "inactive" |

- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "supplierCode": "S001",
    "supplierName": "供应商1",
    "status": "inactive",
    // 其他字段同上
  }
}
```

## 2. 供应商分类管理接口

### 2.1 查询所有供应商分类

- **接口URL**: `GET /api/supplier-categories`
- **请求方式**: GET
- **权限要求**: `supplier:read`
- **请求参数**: 无
- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "1001",
      "categoryCode": "C001",
      "categoryName": "原材料供应商",
      "sortOrder": 1,
      "status": "active",
      "remark": "提供原材料的供应商",
      "createdBy": "admin",
      "createTime": "2023-01-01T10:00:00",
      "updatedBy": "admin",
      "updateTime": "2023-01-01T10:00:00"
    },
    {
      "id": "1002",
      "categoryCode": "C002",
      "categoryName": "设备供应商",
      "sortOrder": 2,
      "status": "active",
      "remark": "提供设备的供应商",
      "createdBy": "admin",
      "createTime": "2023-01-01T10:00:00",
      "updatedBy": "admin",
      "updateTime": "2023-01-01T10:00:00"
    }
  ]
}
```

### 2.2 查询所有启用的供应商分类

- **接口URL**: `GET /api/supplier-categories/active`
- **请求方式**: GET
- **权限要求**: `supplier:read`
- **请求参数**: 无
- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "1001",
      "categoryCode": "C001",
      "categoryName": "原材料供应商",
      "sortOrder": 1,
      "status": "active",
      "remark": "提供原材料的供应商",
      "createdBy": "admin",
      "createTime": "2023-01-01T10:00:00",
      "updatedBy": "admin",
      "updateTime": "2023-01-01T10:00:00"
    },
    {
      "id": "1002",
      "categoryCode": "C002",
      "categoryName": "设备供应商",
      "sortOrder": 2,
      "status": "active",
      "remark": "提供设备的供应商",
      "createdBy": "admin",
      "createTime": "2023-01-01T10:00:00",
      "updatedBy": "admin",
      "updateTime": "2023-01-01T10:00:00"
    }
  ]
}
```

### 2.3 根据ID查询供应商分类

- **接口URL**: `GET /api/supplier-categories/{id}`
- **请求方式**: GET
- **权限要求**: `supplier:read`
- **路径参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| id | String | 是 | 供应商分类ID | "1001" |

- **响应参数**:

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": "1001",
    "categoryCode": "C001",
    "categoryName": "原材料供应商",
    "sortOrder": 1,
    "status": "active",
    "remark": "提供原材料的供应商",
    "createdBy": "admin",
    "createTime": "2023-01-01T10:00:00",
    "updatedBy": "admin",
    "updateTime": "2023-01-01T10:00:00"
  }
}
```

## 3. 错误码说明

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 200 | 操作成功 | - |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 401 | 未授权 | 检查认证信息是否正确 |
| 403 | 禁止访问 | 检查是否有相应的权限 |
| 404 | 资源不存在 | 检查请求的资源是否存在 |
| 500 | 服务器内部错误 | 联系后端开发人员 |

## 4. 前端开发建议

1. **分页处理**：
   - 后端分页从1开始，前端需要注意页码转换
   - 分页响应中包含了totalElements（总记录数）和totalPages（总页数）

2. **状态管理**：
   - 供应商状态有两种：active（启用）和inactive（停用）
   - 可以使用不同的颜色或图标来区分不同状态

3. **表单验证**：
   - 供应商编码和名称为必填项
   - 各字段长度限制参考请求参数说明

4. **数据缓存**：
   - 供应商分类数据可以缓存在前端，减少请求次数
   - 供应商列表数据应该实时获取，确保数据最新

5. **错误处理**：
   - 对接口返回的错误信息进行友好展示
   - 针对不同的错误码采取不同的处理策略

## 5. 示例代码

### 5.1 获取供应商列表

```typescript
// 使用axios示例
import axios from 'axios';

interface SupplierQueryParams {
  keyword?: string;
  categoryId?: string;
  status?: string;
  page?: number;
  size?: number;
  sortField?: string;
  sortDirection?: string;
}

async function getSuppliers(params: SupplierQueryParams) {
  try {
    const response = await axios.get('/api/suppliers', { params });
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('获取供应商列表失败:', error);
    throw error;
  }
}
```

### 5.2 创建供应商

```typescript
// 使用axios示例
import axios from 'axios';

interface CreateSupplierRequest {
  supplierCode: string;
  supplierName: string;
  fullName?: string;
  categoryId?: string;
  // 其他字段...
}

async function createSupplier(supplier: CreateSupplierRequest) {
  try {
    const response = await axios.post('/api/suppliers', supplier);
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('创建供应商失败:', error);
    throw error;
  }
}
```

### 5.3 获取供应商分类

```typescript
// 使用axios示例
import axios from 'axios';

async function getActiveCategories() {
  try {
    const response = await axios.get('/api/supplier-categories/active');
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('获取供应商分类失败:', error);
    throw error;
  }
}
```
