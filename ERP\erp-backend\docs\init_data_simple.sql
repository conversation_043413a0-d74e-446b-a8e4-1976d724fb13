-- 简化版初始化数据脚本
-- 只包含必要的数据，用于快速初始化系统

-- 获取管理员用户ID
SET @admin_id = (SELECT id FROM sys_user WHERE username = 'admin' LIMIT 1);
SET @now = NOW();

-- 初始化权限数据
-- 用户管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, code, component, icon, name, parent_id, path, sort, status, type) VALUES
('p001', @admin_id, @now, 0, 'user', 'system/user/index', 'user', '用户管理', NULL, '/system/users', 10, 'active', 'menu'),
('p002', @admin_id, @now, 0, 'user:list', NULL, NULL, '用户列表', 'p001', NULL, 11, 'active', 'button'),
('p003', @admin_id, @now, 0, 'user:read', NULL, NULL, '用户详情', 'p001', NULL, 12, 'active', 'button'),
('p004', @admin_id, @now, 0, 'user:create', NULL, NULL, '创建用户', 'p001', NULL, 13, 'active', 'button'),
('p005', @admin_id, @now, 0, 'user:update', NULL, NULL, '更新用户', 'p001', NULL, 14, 'active', 'button'),
('p006', @admin_id, @now, 0, 'user:delete', NULL, NULL, '删除用户', 'p001', NULL, 15, 'active', 'button');

-- 角色管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, code, component, icon, name, parent_id, path, sort, status, type) VALUES
('p007', @admin_id, @now, 0, 'role', 'system/role/index', 'team', '角色管理', NULL, '/system/roles', 20, 'active', 'menu'),
('p008', @admin_id, @now, 0, 'role:list', NULL, NULL, '角色列表', 'p007', NULL, 21, 'active', 'button'),
('p009', @admin_id, @now, 0, 'role:read', NULL, NULL, '角色详情', 'p007', NULL, 22, 'active', 'button'),
('p010', @admin_id, @now, 0, 'role:create', NULL, NULL, '创建角色', 'p007', NULL, 23, 'active', 'button'),
('p011', @admin_id, @now, 0, 'role:update', NULL, NULL, '更新角色', 'p007', NULL, 24, 'active', 'button'),
('p012', @admin_id, @now, 0, 'role:delete', NULL, NULL, '删除角色', 'p007', NULL, 25, 'active', 'button');

-- 权限管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, code, component, icon, name, parent_id, path, sort, status, type) VALUES
('p013', @admin_id, @now, 0, 'permission', 'system/permission/index', 'safety', '权限管理', NULL, '/system/permissions', 30, 'active', 'menu'),
('p014', @admin_id, @now, 0, 'permission:list', NULL, NULL, '权限列表', 'p013', NULL, 31, 'active', 'button'),
('p015', @admin_id, @now, 0, 'permission:read', NULL, NULL, '权限详情', 'p013', NULL, 32, 'active', 'button');

-- 部门管理权限
INSERT INTO sys_permission (id, created_by, created_time, is_deleted, code, component, icon, name, parent_id, path, sort, status, type) VALUES
('p016', @admin_id, @now, 0, 'department', 'system/department/index', 'apartment', '部门管理', NULL, '/system/departments', 40, 'active', 'menu'),
('p017', @admin_id, @now, 0, 'department:list', NULL, NULL, '部门列表', 'p016', NULL, 41, 'active', 'button');

-- 初始化角色数据
INSERT INTO sys_role (id, created_by, created_time, is_deleted, code, description, name, status) VALUES
('r001', @admin_id, @now, 0, 'admin', '系统管理员，拥有所有权限', '管理员', 'active');

-- 为管理员角色分配所有权限
INSERT INTO sys_role_permission (id, create_by, create_time, permission_id, role_id) VALUES
('rp001', @admin_id, @now, 'p001', 'r001'),
('rp002', @admin_id, @now, 'p002', 'r001'),
('rp003', @admin_id, @now, 'p003', 'r001'),
('rp004', @admin_id, @now, 'p004', 'r001'),
('rp005', @admin_id, @now, 'p005', 'r001'),
('rp006', @admin_id, @now, 'p006', 'r001'),
('rp007', @admin_id, @now, 'p007', 'r001'),
('rp008', @admin_id, @now, 'p008', 'r001'),
('rp009', @admin_id, @now, 'p009', 'r001'),
('rp010', @admin_id, @now, 'p010', 'r001'),
('rp011', @admin_id, @now, 'p011', 'r001'),
('rp012', @admin_id, @now, 'p012', 'r001'),
('rp013', @admin_id, @now, 'p013', 'r001'),
('rp014', @admin_id, @now, 'p014', 'r001'),
('rp015', @admin_id, @now, 'p015', 'r001'),
('rp016', @admin_id, @now, 'p016', 'r001'),
('rp017', @admin_id, @now, 'p017', 'r001');

-- 为管理员用户分配管理员角色
INSERT INTO sys_user_role (id, create_by, create_time, role_id, user_id) VALUES
('ur001', @admin_id, @now, 'r001', @admin_id);

-- 初始化部门数据
INSERT INTO sys_department (id, created_by, created_time, is_deleted, code, level, manager_id, name, parent_id, sort, status) VALUES
('d001', @admin_id, @now, 0, 'HQ', 1, @admin_id, '总部', NULL, 1, 'active'),
('d002', @admin_id, @now, 0, 'RD', 2, @admin_id, '研发部', 'd001', 1, 'active');
