package com.czerp.erpbackend.purchase.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建供应商请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateSupplierRequest {
    
    /**
     * 供应商编码
     */
    @NotBlank(message = "供应商编码不能为空")
    @Size(max = 50, message = "供应商编码长度不能超过50个字符")
    private String supplierCode;
    
    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空")
    @Size(max = 100, message = "供应商名称长度不能超过100个字符")
    private String supplierName;
    
    /**
     * 供应商全称
     */
    @Size(max = 200, message = "供应商全称长度不能超过200个字符")
    private String fullName;
    
    /**
     * 供应商分类ID
     */
    private String categoryId;
    
    /**
     * 行业
     */
    @Size(max = 100, message = "行业长度不能超过100个字符")
    private String industry;
    
    /**
     * 电话
     */
    @Size(max = 20, message = "电话长度不能超过20个字符")
    private String phone;
    
    /**
     * 传真
     */
    @Size(max = 20, message = "传真长度不能超过20个字符")
    private String fax;
    
    /**
     * 联系人
     */
    @Size(max = 50, message = "联系人长度不能超过50个字符")
    private String contactPerson;
    
    /**
     * 手机
     */
    @Size(max = 20, message = "手机长度不能超过20个字符")
    private String mobile;
    
    /**
     * 地址
     */
    @Size(max = 200, message = "地址长度不能超过200个字符")
    private String address;
    
    /**
     * 地区
     */
    @Size(max = 100, message = "地区长度不能超过100个字符")
    private String region;
    
    /**
     * 单价小数位
     */
    private Integer priceDecimalPlaces;
    
    /**
     * 金额小数位
     */
    private Integer amountDecimalPlaces;
    
    /**
     * 单重小数位
     */
    private Integer unitWeightDecimalPlaces;
    
    /**
     * 总重小数位
     */
    private Integer totalWeightDecimalPlaces;
    
    /**
     * 税号
     */
    @Size(max = 50, message = "税号长度不能超过50个字符")
    private String taxNumber;
    
    /**
     * 开户行
     */
    @Size(max = 100, message = "开户行长度不能超过100个字符")
    private String bankName;
    
    /**
     * 银行账号
     */
    @Size(max = 50, message = "银行账号长度不能超过50个字符")
    private String bankAccount;
    
    /**
     * 付款条件
     */
    @Size(max = 50, message = "付款条件长度不能超过50个字符")
    private String paymentTerms;
    
    /**
     * 状态(active-启用,inactive-停用)
     */
    private String status;
    
    /**
     * 备注
     */
    private String remark;
}
