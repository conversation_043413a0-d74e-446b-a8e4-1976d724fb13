package com.czerp.erpbackend.common.constant;

/**
 * 通用状态枚举
 */
public enum CommonStatus {
    /**
     * 启用
     */
    ENABLED("active"),

    /**
     * 停用
     */
    DISABLED("inactive");

    private final String value;

    CommonStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }

    /**
     * 根据值获取枚举
     * @param value 值
     * @return 枚举
     */
    public static CommonStatus fromValue(String value) {
        for (CommonStatus status : CommonStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown CommonStatus value: " + value);
    }
}