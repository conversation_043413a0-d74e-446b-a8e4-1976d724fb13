package com.czerp.erpbackend.system.repository;

import com.czerp.erpbackend.system.entity.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 角色存储库
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, String> {

    /**
     * 根据编码查找角色
     * @param code 编码
     * @return 角色
     */
    Optional<Role> findByCode(String code);

    /**
     * 根据编码列表查找角色列表
     * @param codes 编码列表
     * @return 角色列表
     */
    List<Role> findByCodeIn(List<String> codes);

    /**
     * 判断编码是否存在
     * @param code 编码
     * @return 是否存在
     */
    boolean existsByCode(String code);

    /**
     * 搜索角色列表
     * @param keyword 关键字
     * @param status 状态
     * @param pageable 分页参数
     * @return 角色分页列表
     */
    @Query("SELECT r FROM Role r WHERE r.isDeleted = false " +
            "AND (:keyword IS NULL OR r.name LIKE %:keyword% OR r.code LIKE %:keyword%) " +
            "AND (:status IS NULL OR r.status = :status)")
    Page<Role> search(
            @Param("keyword") String keyword,
            @Param("status") String status,
            Pageable pageable);

    /**
     * 查询所有未删除的角色
     * @return 角色列表
     */
    List<Role> findByIsDeletedFalse();

    /**
     * 根据名称、编码或描述模糊查询
     * @param name 名称
     * @param code 编码
     * @param description 描述
     * @param pageable 分页参数
     * @return 角色分页列表
     */
    Page<Role> findByNameContainingOrCodeContainingOrDescriptionContaining(
            String name, String code, String description, Pageable pageable);
}