package com.czerp.erpbackend.production.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 生产管理模块权限初始化器
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(10) // 在系统权限初始化之后执行
public class ProductionPermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing production module permissions...");

        // 初始化生产管理模块权限
        initProductionPermissions();

        // 为管理员角色分配权限
        assignPermissionsToAdminRole();

        log.info("Production module permissions initialized successfully.");
    }

    /**
     * 初始化生产管理模块权限
     */
    private void initProductionPermissions() {
        log.info("Initializing production module permissions...");

        // 确保生产管理模块存在
        String productionModuleId = findOrCreateProductionModule();

        // 检查生产排程管理子模块是否已存在
        if (permissionRepository.existsByCode("production:schedule")) {
            log.info("Production schedule module already exists, skipping...");
            return;
        }

        // 创建生产排程管理子模块
        String scheduleModuleId = createPermission("生产排程", "production:schedule", "MENU", productionModuleId,
                "/production/schedule", "production/schedule/index", "schedule", 10);

        // 创建生产排程管理操作权限
        createPermission("生产排程列表", "production:schedule:list", "BUTTON", scheduleModuleId, null, null, null, 11);
        createPermission("生产排程详情", "production:schedule:read", "BUTTON", scheduleModuleId, null, null, null, 12);
        createPermission("创建生产排程", "production:schedule:create", "BUTTON", scheduleModuleId, null, null, null, 13);
        createPermission("更新生产排程", "production:schedule:update", "BUTTON", scheduleModuleId, null, null, null, 14);
        createPermission("删除生产排程", "production:schedule:delete", "BUTTON", scheduleModuleId, null, null, null, 15);

        log.info("Production schedule permissions created successfully.");
    }

    /**
     * 查找或创建生产管理模块
     */
    private String findOrCreateProductionModule() {
        Optional<Permission> existingPermission = permissionRepository.findByCode("production");
        if (existingPermission.isPresent()) {
            log.info("Production module already exists, using existing one.");
            return existingPermission.get().getId();
        }

        // 创建生产管理模块菜单
        log.info("Creating production module...");
        return createPermission("生产管理", "production", "MENU", null,
                "/production", "production/index", "build", 50);
    }

    /**
     * 为管理员角色分配生产管理模块权限
     */
    private void assignPermissionsToAdminRole() {
        log.info("Assigning production permissions to admin role...");

        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping production permissions assignment");
            return;
        }

        // 获取所有生产排程相关权限
        List<String> permissionCodes = Arrays.asList(
                // 生产排程管理
                "production:schedule", "production:schedule:list", "production:schedule:read",
                "production:schedule:create", "production:schedule:update", "production:schedule:delete"
        );

        List<Permission> permissions = permissionRepository.findByCodeIn(permissionCodes);

        // 为管理员角色分配权限
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : permissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString());
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermission.setCreateBy("system");
                rolePermission.setCreateTime(LocalDateTime.now());
                rolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }

        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} production permissions to admin role", rolePermissions.size());
        } else {
            log.info("All production permissions already assigned to admin role");
        }
    }

    /**
     * 创建权限
     */
    private String createPermission(String name, String code, String type, String parentId,
                                    String path, String component, String icon, Integer sort) {
        // 检查权限是否已存在
        Optional<Permission> existingPermission = permissionRepository.findByCode(code);
        if (existingPermission.isPresent()) {
            log.debug("Permission {} already exists, skipping...", code);
            return existingPermission.get().getId();
        }

        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setName(name);
        permission.setCode(code);
        permission.setType(type);
        permission.setParentId(parentId);
        permission.setPath(path);
        permission.setComponent(component);
        permission.setIcon(icon);
        permission.setSort(sort);
        permission.setStatus("active");

        permission = permissionRepository.save(permission);
        log.debug("Created permission: {} ({})", name, code);

        return permission.getId();
    }
}
