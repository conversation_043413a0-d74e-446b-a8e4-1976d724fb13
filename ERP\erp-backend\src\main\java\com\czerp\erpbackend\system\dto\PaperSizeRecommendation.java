package com.czerp.erpbackend.system.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 纸度推荐
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaperSizeRecommendation {
    
    /**
     * 纸度(CM)
     */
    private BigDecimal paperSizeCm;
    
    /**
     * 纸度(Inch)
     */
    private BigDecimal paperSizeInch;
    
    /**
     * 纸度开数
     */
    private Integer degreeCount;
    
    /**
     * 实际用料(Inch)
     */
    private BigDecimal actualUsageInch;
    
    /**
     * 纸度损耗(Inch)
     */
    private BigDecimal wastageInch;
}
