package com.czerp.erpbackend.system.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 更新用户请求
 */
@Data
public class UpdateUserRequest {
    
    /**
     * 姓名
     */
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String nickname;
    
    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;
    
    /**
     * 手机号
     */
    @Pattern(regexp = "^(1[3-9]\\d{9})?$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 部门ID
     */
    private String department;
    
    /**
     * 职位
     */
    @Size(max = 50, message = "职位长度不能超过50个字符")
    private String position;
    
    /**
     * 角色ID列表
     */
    private List<String> roles;
    
    /**
     * 状态
     */
    private String status;
} 