package com.czerp.erpbackend.sales.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.common.util.IdGenerator;
import com.czerp.erpbackend.customer.entity.Customer;
import com.czerp.erpbackend.customer.repository.CustomerRepository;
import com.czerp.erpbackend.sales.dto.SalesOrderDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderDetailDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderItemDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderMaterialDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderProcessDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryDTO;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderMaterialRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderRepository;
import com.czerp.erpbackend.sales.service.ProductionOrderNumberService;
import com.czerp.erpbackend.sales.service.SalesOrderMaterialService;
import com.czerp.erpbackend.sales.service.SalesOrderProcessService;
import com.czerp.erpbackend.sales.service.SalesOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;

/**
 * 销售订单Service实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SalesOrderServiceImpl implements SalesOrderService {

    private final SalesOrderRepository salesOrderRepository;
    private final SalesOrderItemRepository salesOrderItemRepository;
    private final SalesOrderMaterialRepository salesOrderMaterialRepository;
    private final CustomerRepository customerRepository;
    private final ProductionOrderNumberService productionOrderNumberService;
    private final SalesOrderProcessService salesOrderProcessService;
    private final SalesOrderMaterialService salesOrderMaterialService;

    /**
     * 创建销售订单
     * @param orderDTO 销售订单DTO
     * @return 销售订单DTO
     */
    @Override
    @Transactional
    public SalesOrderDetailDTO createOrder(SalesOrderDetailDTO orderDTO) {
        log.info("Creating sales order: {}", orderDTO);

        // 1. 验证客户信息
        Customer customer = null;
        if (StringUtils.hasText(orderDTO.getCustomerCode())) {
            customer = customerRepository.findByCustomerCode(orderDTO.getCustomerCode())
                    .orElseThrow(() -> new BusinessException("客户不存在"));
        } else {
            throw new BusinessException("客户编码不能为空");
        }

        // 2. 生成订单号（如果未提供）
        if (!StringUtils.hasText(orderDTO.getOrderNo())) {
            orderDTO.setOrderNo(generateOrderNo());
        }

        // 3. 创建订单实体
        SalesOrder order = new SalesOrder();

        // 设置订单ID
        order.setId(IdGenerator.generateId());

        // 设置基本信息
        order.setOrderNo(orderDTO.getOrderNo());
        order.setOrderDate(orderDTO.getOrderDate());
        order.setPaymentMethod(orderDTO.getPaymentMethod());
        order.setCustomerCode(customer.getCustomerCode());
        order.setCustomerName(customer.getCustomerName());
        order.setSalesPerson(orderDTO.getSalesPerson());
        order.setCustomerPurchaser(orderDTO.getCustomerPurchaser());
        order.setReceivingUnit(orderDTO.getReceivingUnit());
        order.setReceiver(orderDTO.getReceiver());
        order.setReceiverPhone(orderDTO.getReceiverPhone());
        order.setReceivingAddress(orderDTO.getReceivingAddress());
        order.setRemark(orderDTO.getRemark());
        order.setOrderType(orderDTO.getOrderType());

        // 设置审计字段
        LocalDateTime now = LocalDateTime.now();
        order.setCreatedTime(now);
        order.setUpdatedTime(now);

        // 4. 处理订单明细，并记录前端ID与后端ID的映射关系
        Map<String, String> itemIdMapping = new HashMap<>();
        if (orderDTO.getItems() != null && !orderDTO.getItems().isEmpty()) {
            List<SalesOrderItem> items = new ArrayList<>();
            for (SalesOrderItemDTO itemDTO : orderDTO.getItems()) {
                // 记录前端传入的ID
                String frontendItemId = itemDTO.getId();

                // 创建订单行项目
                SalesOrderItem item = createOrderItem(order, itemDTO);

                // 记录ID映射关系
                itemIdMapping.put(frontendItemId, item.getId());
                log.info("Item ID mapping: frontend {} -> backend {}", frontendItemId, item.getId());

                items.add(item);
            }

            // 5. 为需要生产单号的订单项生成生产单号
            if (!items.isEmpty()) {
                Map<String, String> productionOrderNumbers = productionOrderNumberService.batchGenerateProductionOrderNumbers(items);

                // 设置生产单号
                for (SalesOrderItem item : items) {
                    String productionOrderNo = productionOrderNumbers.get(item.getId());
                    if (StringUtils.hasText(productionOrderNo)) {
                        item.setProductionOrderNo(productionOrderNo);
                        log.info("Generated production order number for item {}: {}", item.getId(), productionOrderNo);
                    }
                }
            }

            // 设置订单明细 - 在保存订单前设置关联关系
            order.setItems(items);
        }

        // 6. 保存订单（包括明细，因为有级联关系）
        SalesOrder savedOrder = salesOrderRepository.save(order);

        // 7. 更新客户最近订单信息
        updateCustomerLastOrderInfo(customer, savedOrder);

        // 8. 处理订单工序
        if (orderDTO.getProcesses() != null && !orderDTO.getProcesses().isEmpty()) {
            // 设置订单ID和订单明细项ID
            for (SalesOrderProcessDTO process : orderDTO.getProcesses()) {
                process.setOrderId(savedOrder.getId());

                // 如果前端传入了orderItemId，需要将其映射到后端实际的ID
                if (StringUtils.hasText(process.getOrderItemId()) && itemIdMapping.containsKey(process.getOrderItemId())) {
                    process.setOrderItemId(itemIdMapping.get(process.getOrderItemId()));
                }
            }

            // 批量添加工序
            salesOrderProcessService.addProcesses(orderDTO.getProcesses());
        }

        // 9. 处理订单材料信息
        // 9.1 处理items中的材料信息
        if (orderDTO.getItems() != null) {
            for (SalesOrderItemDTO itemDTO : orderDTO.getItems()) {
                if (itemDTO.getMaterials() != null && !itemDTO.getMaterials().isEmpty()) {
                    log.info("Processing {} materials from item {}", itemDTO.getMaterials().size(), itemDTO.getId());

                    // 获取后端实际保存的订单行项目ID
                    String backendItemId = itemIdMapping.get(itemDTO.getId());
                    if (backendItemId != null) {
                        // 设置订单行项目ID为后端实际保存的ID
                        for (SalesOrderMaterialDTO materialDTO : itemDTO.getMaterials()) {
                            materialDTO.setOrderItemId(backendItemId);
                        }
                        // 批量保存材料信息
                        salesOrderMaterialService.saveMaterials(itemDTO.getMaterials());
                    } else {
                        log.error("Cannot find backend item ID for frontend item ID: {}", itemDTO.getId());
                    }
                }
            }
        }

        // 9.2 处理materials字段中的材料信息
        if (orderDTO.getMaterials() != null && !orderDTO.getMaterials().isEmpty()) {
            log.info("Processing materials map with {} entries", orderDTO.getMaterials().size());
            for (Map.Entry<String, List<SalesOrderMaterialDTO>> entry : orderDTO.getMaterials().entrySet()) {
                String frontendItemId = entry.getKey();
                List<SalesOrderMaterialDTO> materials = entry.getValue();

                if (materials != null && !materials.isEmpty()) {
                    log.info("Processing {} materials for order item {}", materials.size(), frontendItemId);

                    // 获取后端实际保存的订单行项目ID
                    String backendItemId = itemIdMapping.get(frontendItemId);
                    if (backendItemId != null) {
                        // 设置订单行项目ID为后端实际保存的ID
                        for (SalesOrderMaterialDTO materialDTO : materials) {
                            materialDTO.setOrderItemId(backendItemId);
                        }
                        // 批量保存材料信息
                        salesOrderMaterialService.saveMaterials(materials);
                    } else {
                        log.error("Cannot find backend item ID for frontend item ID: {}", frontendItemId);
                    }
                }
            }
        }

        // 10. 转换为DTO
        SalesOrderDetailDTO resultDTO = convertToDetailDTO(savedOrder);

        // 11. 加载订单工序
        List<SalesOrderProcessDTO> processes = salesOrderProcessService.getProcessesByOrderId(savedOrder.getId());
        resultDTO.setProcesses(processes);

        // 12. 加载订单材料信息
        loadMaterialsForOrder(resultDTO);

        return resultDTO;
    }

    /**
     * 创建订单明细
     * @param order 订单
     * @param itemDTO 明细DTO
     * @return 明细实体
     */
    private SalesOrderItem createOrderItem(SalesOrder order, SalesOrderItemDTO itemDTO) {
        SalesOrderItem item = new SalesOrderItem();

        // 设置明细ID
        item.setId(IdGenerator.generateId());

        // 设置订单关联
        item.setOrder(order);

        // 设置基本信息
        item.setProductionOrderNo(itemDTO.getProductionOrderNo());
        item.setCustomerOrderNo(itemDTO.getCustomerOrderNo());
        item.setCustomerProductCode(itemDTO.getCustomerProductCode());
        item.setProductName(itemDTO.getProductName());
        item.setProcessRequirements(itemDTO.getProcessRequirements());
        item.setIsTaxed(itemDTO.getIsTaxed());
        item.setBoxType(itemDTO.getBoxType());
        item.setPaperType(itemDTO.getPaperType());
        item.setCorrugationType(itemDTO.getCorrugationType());
        item.setProductionPaperType(itemDTO.getProductionPaperType());
        item.setLength(itemDTO.getLength());
        item.setWidth(itemDTO.getWidth());
        item.setHeight(itemDTO.getHeight());
        item.setSizeUnit(itemDTO.getSizeUnit());
        item.setQuantity(itemDTO.getQuantity());
        item.setSpareQuantity(itemDTO.getSpareQuantity());
        item.setPrice(itemDTO.getPrice());
        item.setAmount(itemDTO.getAmount());
        item.setDeliveryDate(itemDTO.getDeliveryDate());
        item.setIsSpecialPrice(itemDTO.getIsSpecialPrice());
        item.setPaperQuotation(itemDTO.getPaperQuotation());
        item.setConnectionMethod(itemDTO.getConnectionMethod());
        item.setStaplePosition(itemDTO.getStaplePosition());
        item.setPackagingCount(itemDTO.getPackagingCount());
        item.setProductionRemark(itemDTO.getProductionRemark());
        item.setRemark(itemDTO.getRemark());
        item.setProductionLength(itemDTO.getProductionLength());
        item.setProductionWidth(itemDTO.getProductionWidth());
        item.setProductionHeight(itemDTO.getProductionHeight());
        item.setSpareRatio(itemDTO.getSpareRatio());
        item.setSpareQuantityTotal(itemDTO.getSpareQuantityTotal());
        item.setCurrentInventory(itemDTO.getCurrentInventory());
        item.setAvailableInventory(itemDTO.getAvailableInventory());
        item.setUseInventory(itemDTO.getUseInventory());
        item.setIsSpecialSpecification(itemDTO.getIsSpecialSpecification());
        item.setUnitWeight(itemDTO.getUnitWeight());
        item.setTotalWeight(itemDTO.getTotalWeight());
        item.setProductArea(itemDTO.getProductArea());
        item.setTotalArea(itemDTO.getTotalArea());
        item.setProductVolume(itemDTO.getProductVolume());
        item.setTotalVolume(itemDTO.getTotalVolume());
        item.setComponent(itemDTO.getComponent());
        item.setIsSample(itemDTO.getIsSample());
        item.setDeliveredQuantity(itemDTO.getDeliveredQuantity());
        item.setDeliveredSpareQuantity(itemDTO.getDeliveredSpareQuantity());
        item.setReturnedQuantity(itemDTO.getReturnedQuantity());
        item.setSafetyStock(itemDTO.getSafetyStock());
        item.setReconciliationQuantity(itemDTO.getReconciliationQuantity());
        item.setUnit(itemDTO.getUnit());
        item.setCurrency(itemDTO.getCurrency());
        item.setTaxRate(itemDTO.getTaxRate());

        // 设置审计字段
        LocalDateTime now = LocalDateTime.now();
        item.setCreatedTime(now);
        item.setUpdatedTime(now);

        // 设置创建人和更新人信息
        if (order.getCreatedBy() != null) {
            item.setCreatedBy(order.getCreatedBy());
            if (order.getCreatedByName() != null) {
                item.setCreatedByName(order.getCreatedByName());
            }
        }
        if (order.getUpdatedBy() != null) {
            item.setUpdatedBy(order.getUpdatedBy());
            if (order.getUpdatedByName() != null) {
                item.setUpdatedByName(order.getUpdatedByName());
            }
        }

        return item;
    }



    /**
     * 更新客户最近订单信息
     * @param customer 客户
     * @param order 订单
     */
    private void updateCustomerLastOrderInfo(Customer customer, SalesOrder order) {
        customer.setLastOrderDate(order.getOrderDate());
        customerRepository.save(customer);
    }

    /**
     * 生成订单号
     * @return 订单号
     */
    @Override
    public String generateOrderNo() {
        // 生成格式：SO + 年月日 + 4位序号，例如：SO2023060100001
        LocalDate today = LocalDate.now();
        String datePrefix = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String orderNoPrefix = "SO" + datePrefix;

        // 查询当天最大订单号
        String maxOrderNo = salesOrderRepository.findMaxOrderNoByPrefix(orderNoPrefix);

        int sequence = 1;
        if (maxOrderNo != null && maxOrderNo.length() >= orderNoPrefix.length() + 5) {
            String sequenceStr = maxOrderNo.substring(orderNoPrefix.length());
            try {
                sequence = Integer.parseInt(sequenceStr) + 1;
            } catch (NumberFormatException e) {
                log.warn("Failed to parse sequence from order no: {}", maxOrderNo);
            }
        }

        // 格式化序号为5位数字
        return orderNoPrefix + String.format("%05d", sequence);
    }

    /**
     * 将实体转换为详细DTO
     * @param order 订单实体
     * @return 订单详细DTO
     */
    private SalesOrderDetailDTO convertToDetailDTO(SalesOrder order) {
        SalesOrderDetailDTO dto = new SalesOrderDetailDTO();

        // 设置基本信息
        dto.setId(order.getId());
        dto.setOrderNo(order.getOrderNo());
        dto.setOrderDate(order.getOrderDate());
        dto.setPaymentMethod(order.getPaymentMethod());
        dto.setCustomerCode(order.getCustomerCode());
        dto.setCustomerName(order.getCustomerName());
        dto.setSalesPerson(order.getSalesPerson());
        dto.setCustomerPurchaser(order.getCustomerPurchaser());
        dto.setReceivingUnit(order.getReceivingUnit());
        dto.setReceiver(order.getReceiver());
        dto.setReceiverPhone(order.getReceiverPhone());
        dto.setReceivingAddress(order.getReceivingAddress());
        dto.setRemark(order.getRemark());
        dto.setOrderType(order.getOrderType());
        dto.setCreatedBy(order.getCreatedBy());
        dto.setCreatedByName(order.getCreatedByName());
        dto.setCreatedTime(order.getCreatedTime());
        dto.setUpdatedBy(order.getUpdatedBy());
        dto.setUpdatedByName(order.getUpdatedByName());
        dto.setUpdatedTime(order.getUpdatedTime());

        // 设置明细
        if (order.getItems() != null && !order.getItems().isEmpty()) {
            List<SalesOrderItemDTO> itemDTOs = order.getItems().stream()
                    .map(this::convertToItemDTO)
                    .collect(Collectors.toList());
            dto.setItems(itemDTOs);
        }

        return dto;
    }

    /**
     * 将明细实体转换为DTO
     * @param item 明细实体
     * @return 明细DTO
     */
    private SalesOrderItemDTO convertToItemDTO(SalesOrderItem item) {
        SalesOrderItemDTO dto = new SalesOrderItemDTO();

        dto.setId(item.getId());
        dto.setOrderId(item.getOrder().getId());
        dto.setProductionOrderNo(item.getProductionOrderNo());
        dto.setCustomerOrderNo(item.getCustomerOrderNo());
        dto.setCustomerProductCode(item.getCustomerProductCode());
        dto.setProductName(item.getProductName());
        dto.setProcessRequirements(item.getProcessRequirements());
        dto.setIsTaxed(item.getIsTaxed());
        dto.setBoxType(item.getBoxType());
        dto.setPaperType(item.getPaperType());
        dto.setCorrugationType(item.getCorrugationType());
        dto.setProductionPaperType(item.getProductionPaperType());
        dto.setLength(item.getLength());
        dto.setWidth(item.getWidth());
        dto.setHeight(item.getHeight());
        dto.setSizeUnit(item.getSizeUnit());
        dto.setQuantity(item.getQuantity());
        dto.setSpareQuantity(item.getSpareQuantity());
        dto.setPrice(item.getPrice());
        dto.setAmount(item.getAmount());
        dto.setDeliveryDate(item.getDeliveryDate());
        dto.setIsSpecialPrice(item.getIsSpecialPrice());
        dto.setPaperQuotation(item.getPaperQuotation());
        dto.setConnectionMethod(item.getConnectionMethod());
        dto.setStaplePosition(item.getStaplePosition());
        dto.setPackagingCount(item.getPackagingCount());
        dto.setProductionRemark(item.getProductionRemark());
        dto.setRemark(item.getRemark());
        dto.setProductionLength(item.getProductionLength());
        dto.setProductionWidth(item.getProductionWidth());
        dto.setProductionHeight(item.getProductionHeight());
        dto.setSpareRatio(item.getSpareRatio());
        dto.setSpareQuantityTotal(item.getSpareQuantityTotal());
        dto.setCurrentInventory(item.getCurrentInventory());
        dto.setAvailableInventory(item.getAvailableInventory());
        dto.setUseInventory(item.getUseInventory());
        dto.setIsSpecialSpecification(item.getIsSpecialSpecification());
        dto.setUnitWeight(item.getUnitWeight());
        dto.setTotalWeight(item.getTotalWeight());
        dto.setProductArea(item.getProductArea());
        dto.setTotalArea(item.getTotalArea());
        dto.setProductVolume(item.getProductVolume());
        dto.setTotalVolume(item.getTotalVolume());
        dto.setComponent(item.getComponent());
        dto.setIsSample(item.getIsSample());
        dto.setDeliveredQuantity(item.getDeliveredQuantity());
        dto.setDeliveredSpareQuantity(item.getDeliveredSpareQuantity());
        dto.setReturnedQuantity(item.getReturnedQuantity());
        dto.setSafetyStock(item.getSafetyStock());
        dto.setReconciliationQuantity(item.getReconciliationQuantity());
        dto.setUnit(item.getUnit());
        dto.setCurrency(item.getCurrency());
        dto.setTaxRate(item.getTaxRate());
        dto.setCreatedBy(item.getCreatedBy());
        dto.setCreatedByName(item.getCreatedByName());
        dto.setCreatedTime(item.getCreatedTime());
        dto.setUpdatedBy(item.getUpdatedBy());
        dto.setUpdatedByName(item.getUpdatedByName());
        dto.setUpdatedTime(item.getUpdatedTime());

        return dto;
    }

    /**
     * 将实体转换为DTO
     * @param order 订单实体
     * @return 订单DTO
     */
    private SalesOrderDTO convertToDTO(SalesOrder order) {
        SalesOrderDTO dto = new SalesOrderDTO();

        dto.setId(order.getId());
        dto.setOrderNo(order.getOrderNo());
        dto.setOrderDate(order.getOrderDate());
        dto.setPaymentMethod(order.getPaymentMethod());
        dto.setCustomerCode(order.getCustomerCode());
        dto.setCustomerName(order.getCustomerName());
        dto.setSalesPerson(order.getSalesPerson());
        dto.setCustomerPurchaser(order.getCustomerPurchaser());
        dto.setReceivingUnit(order.getReceivingUnit());
        dto.setReceiver(order.getReceiver());
        dto.setReceiverPhone(order.getReceiverPhone());
        dto.setReceivingAddress(order.getReceivingAddress());
        dto.setRemark(order.getRemark());
        dto.setOrderType(order.getOrderType());
        dto.setCreatedBy(order.getCreatedBy());
        dto.setCreatedByName(order.getCreatedByName());
        dto.setCreatedTime(order.getCreatedTime());
        dto.setUpdatedBy(order.getUpdatedBy());
        dto.setUpdatedByName(order.getUpdatedByName());
        dto.setUpdatedTime(order.getUpdatedTime());

        return dto;
    }

    /**
     * 更新销售订单
     * @param id 订单ID
     * @param orderDTO 销售订单DTO
     * @return 销售订单DTO
     */
    @Override
    @Transactional
    public SalesOrderDetailDTO updateOrder(String id, SalesOrderDetailDTO orderDTO) {
        log.info("Updating sales order: id={}, orderDTO={}", id, orderDTO);

        // 验证订单是否存在
        SalesOrder order = salesOrderRepository.findById(id)
                .orElseThrow(() -> new BusinessException("订单不存在"));

        // 验证客户信息
        if (StringUtils.hasText(orderDTO.getCustomerCode())) {
            Customer customer = customerRepository.findByCustomerCode(orderDTO.getCustomerCode())
                    .orElseThrow(() -> new BusinessException("客户不存在"));
            order.setCustomerCode(customer.getCustomerCode());
            order.setCustomerName(customer.getCustomerName());
        }

        // 更新基本信息
        if (orderDTO.getOrderDate() != null) {
            order.setOrderDate(orderDTO.getOrderDate());
        }
        if (StringUtils.hasText(orderDTO.getPaymentMethod())) {
            order.setPaymentMethod(orderDTO.getPaymentMethod());
        }
        if (StringUtils.hasText(orderDTO.getSalesPerson())) {
            order.setSalesPerson(orderDTO.getSalesPerson());
        }
        if (StringUtils.hasText(orderDTO.getCustomerPurchaser())) {
            order.setCustomerPurchaser(orderDTO.getCustomerPurchaser());
        }
        if (StringUtils.hasText(orderDTO.getReceivingUnit())) {
            order.setReceivingUnit(orderDTO.getReceivingUnit());
        }
        if (StringUtils.hasText(orderDTO.getReceiver())) {
            order.setReceiver(orderDTO.getReceiver());
        }
        if (StringUtils.hasText(orderDTO.getReceiverPhone())) {
            order.setReceiverPhone(orderDTO.getReceiverPhone());
        }
        if (StringUtils.hasText(orderDTO.getReceivingAddress())) {
            order.setReceivingAddress(orderDTO.getReceivingAddress());
        }
        if (StringUtils.hasText(orderDTO.getRemark())) {
            order.setRemark(orderDTO.getRemark());
        }
        if (StringUtils.hasText(orderDTO.getOrderType())) {
            order.setOrderType(orderDTO.getOrderType());
        }

        // 更新审计字段
        order.setUpdatedTime(LocalDateTime.now());

        // 处理订单明细，并记录前端ID与后端ID的映射关系
        Map<String, String> itemIdMapping = new HashMap<>();
        if (orderDTO.getItems() != null && !orderDTO.getItems().isEmpty()) {
            // 获取现有明细
            List<SalesOrderItem> existingItems = salesOrderItemRepository.findByOrderId(id);

            // 创建明细ID映射，用于判断是新增还是更新
            Map<String, SalesOrderItem> existingItemMap = existingItems.stream()
                    .collect(Collectors.toMap(SalesOrderItem::getId, item -> item));

            // 处理每个明细
            List<SalesOrderItem> updatedItems = new ArrayList<>();

            for (SalesOrderItemDTO itemDTO : orderDTO.getItems()) {
                // 记录前端传入的ID
                String frontendItemId = itemDTO.getId();

                if (StringUtils.hasText(itemDTO.getId()) && existingItemMap.containsKey(itemDTO.getId())) {
                    // 更新现有明细
                    SalesOrderItem existingItem = existingItemMap.get(itemDTO.getId());
                    updateOrderItem(existingItem, itemDTO);
                    updatedItems.add(existingItem);
                    existingItemMap.remove(itemDTO.getId());

                    // 记录ID映射关系（前端ID -> 后端ID）
                    itemIdMapping.put(frontendItemId, existingItem.getId());
                    log.info("Item ID mapping (existing): frontend {} -> backend {}", frontendItemId, existingItem.getId());
                } else {
                    // 新增明细
                    SalesOrderItem newItem = createOrderItem(order, itemDTO);
                    updatedItems.add(newItem);

                    // 记录ID映射关系（前端ID -> 后端ID）
                    itemIdMapping.put(frontendItemId, newItem.getId());
                    log.info("Item ID mapping (new): frontend {} -> backend {}", frontendItemId, newItem.getId());
                }
            }

            // 为新增的订单项生成生产单号（过滤出没有生产单号的项）
            List<SalesOrderItem> newItemsWithoutProductionOrderNo = updatedItems.stream()
                    .filter(item -> !StringUtils.hasText(item.getProductionOrderNo()))
                    .collect(Collectors.toList());

            if (!newItemsWithoutProductionOrderNo.isEmpty()) {
                Map<String, String> productionOrderNumbers = productionOrderNumberService.batchGenerateProductionOrderNumbers(newItemsWithoutProductionOrderNo);

                // 设置生产单号
                for (SalesOrderItem item : newItemsWithoutProductionOrderNo) {
                    String productionOrderNo = productionOrderNumbers.get(item.getId());
                    if (StringUtils.hasText(productionOrderNo)) {
                        item.setProductionOrderNo(productionOrderNo);
                        log.info("Generated production order number for new item {}: {}", item.getId(), productionOrderNo);
                    }
                }
            }

            // 清除现有明细并设置更新后的明细
            order.getItems().clear();
            order.getItems().addAll(updatedItems);
        } else {
            // 如果没有明细项，清空现有明细
            order.getItems().clear();
        }

        // 保存订单（包括明细，因为有级联关系）
        SalesOrder savedOrder = salesOrderRepository.save(order);

        // 处理订单工序
        // 注意：这里采用先删除再添加的方式，也可以根据需要改为更新现有工序
        salesOrderProcessService.deleteProcessesByOrderId(id);
        if (orderDTO.getProcesses() != null && !orderDTO.getProcesses().isEmpty()) {
            // 设置订单ID和订单明细项ID
            for (SalesOrderProcessDTO process : orderDTO.getProcesses()) {
                process.setOrderId(id);

                // 如果前端传入了orderItemId，需要将其映射到后端实际的ID
                if (StringUtils.hasText(process.getOrderItemId()) && itemIdMapping.containsKey(process.getOrderItemId())) {
                    process.setOrderItemId(itemIdMapping.get(process.getOrderItemId()));
                }
            }

            // 批量添加工序
            salesOrderProcessService.addProcesses(orderDTO.getProcesses());
        }

        // 处理订单材料信息
        // 获取所有订单行项目ID
        List<String> orderItemIds = savedOrder.getItems().stream()
                .map(SalesOrderItem::getId)
                .collect(Collectors.toList());

        // 处理每个订单行项目的材料信息
        // 1. 处理items中的材料信息
        if (orderDTO.getItems() != null) {
            for (SalesOrderItemDTO itemDTO : orderDTO.getItems()) {
                if (itemDTO.getMaterials() != null && !itemDTO.getMaterials().isEmpty()) {
                    log.info("Updating {} materials from item {}", itemDTO.getMaterials().size(), itemDTO.getId());

                    // 获取后端实际保存的订单行项目ID
                    String backendItemId = itemIdMapping.get(itemDTO.getId());
                    if (backendItemId != null) {
                        // 先删除该行项目的所有材料信息
                        salesOrderMaterialService.deleteMaterialsByOrderItemId(backendItemId);

                        // 设置订单行项目ID为后端实际保存的ID
                        for (SalesOrderMaterialDTO materialDTO : itemDTO.getMaterials()) {
                            materialDTO.setOrderItemId(backendItemId);
                        }

                        // 批量保存材料信息
                        salesOrderMaterialService.saveMaterials(itemDTO.getMaterials());
                    } else {
                        log.error("Cannot find backend item ID for frontend item ID: {}", itemDTO.getId());
                    }
                }
            }
        }

        // 2. 处理materials字段中的材料信息
        if (orderDTO.getMaterials() != null && !orderDTO.getMaterials().isEmpty()) {
            log.info("Updating materials map with {} entries", orderDTO.getMaterials().size());
            for (Map.Entry<String, List<SalesOrderMaterialDTO>> entry : orderDTO.getMaterials().entrySet()) {
                String frontendItemId = entry.getKey();
                List<SalesOrderMaterialDTO> materials = entry.getValue();

                if (materials != null && !materials.isEmpty()) {
                    log.info("Updating {} materials for order item {}", materials.size(), frontendItemId);

                    // 获取后端实际保存的订单行项目ID
                    String backendItemId = itemIdMapping.get(frontendItemId);
                    if (backendItemId != null) {
                        // 先删除该行项目的所有材料信息
                        salesOrderMaterialService.deleteMaterialsByOrderItemId(backendItemId);

                        // 设置订单行项目ID为后端实际保存的ID
                        for (SalesOrderMaterialDTO materialDTO : materials) {
                            materialDTO.setOrderItemId(backendItemId);
                        }

                        // 批量保存材料信息
                        salesOrderMaterialService.saveMaterials(materials);
                    } else {
                        log.error("Cannot find backend item ID for frontend item ID: {}", frontendItemId);
                    }
                }
            }
        }

        // 转换为DTO
        SalesOrderDetailDTO resultDTO = convertToDetailDTO(savedOrder);

        // 加载订单工序
        List<SalesOrderProcessDTO> processes = salesOrderProcessService.getProcessesByOrderId(id);
        resultDTO.setProcesses(processes);

        // 加载订单材料信息
        loadMaterialsForOrder(resultDTO);

        return resultDTO;
    }

    /**
     * 更新订单明细
     * @param item 明细实体
     * @param itemDTO 明细DTO
     */
    private void updateOrderItem(SalesOrderItem item, SalesOrderItemDTO itemDTO) {
        if (StringUtils.hasText(itemDTO.getCustomerOrderNo())) {
            item.setCustomerOrderNo(itemDTO.getCustomerOrderNo());
        }
        if (StringUtils.hasText(itemDTO.getCustomerProductCode())) {
            item.setCustomerProductCode(itemDTO.getCustomerProductCode());
        }
        if (StringUtils.hasText(itemDTO.getProductName())) {
            item.setProductName(itemDTO.getProductName());
        }
        if (StringUtils.hasText(itemDTO.getProcessRequirements())) {
            item.setProcessRequirements(itemDTO.getProcessRequirements());
        }
        if (itemDTO.getIsTaxed() != null) {
            item.setIsTaxed(itemDTO.getIsTaxed());
        }
        if (StringUtils.hasText(itemDTO.getBoxType())) {
            item.setBoxType(itemDTO.getBoxType());
        }
        if (StringUtils.hasText(itemDTO.getPaperType())) {
            item.setPaperType(itemDTO.getPaperType());
        }
        if (StringUtils.hasText(itemDTO.getCorrugationType())) {
            item.setCorrugationType(itemDTO.getCorrugationType());
        }
        if (StringUtils.hasText(itemDTO.getProductionPaperType())) {
            item.setProductionPaperType(itemDTO.getProductionPaperType());
        }
        if (itemDTO.getLength() != null) {
            item.setLength(itemDTO.getLength());
        }
        if (itemDTO.getWidth() != null) {
            item.setWidth(itemDTO.getWidth());
        }
        if (itemDTO.getHeight() != null) {
            item.setHeight(itemDTO.getHeight());
        }
        if (StringUtils.hasText(itemDTO.getSizeUnit())) {
            item.setSizeUnit(itemDTO.getSizeUnit());
        }
        if (itemDTO.getQuantity() != null) {
            item.setQuantity(itemDTO.getQuantity());
        }
        if (itemDTO.getSpareQuantity() != null) {
            item.setSpareQuantity(itemDTO.getSpareQuantity());
        }
        if (itemDTO.getPrice() != null) {
            item.setPrice(itemDTO.getPrice());
        }
        if (itemDTO.getAmount() != null) {
            item.setAmount(itemDTO.getAmount());
        }
        if (itemDTO.getDeliveryDate() != null) {
            item.setDeliveryDate(itemDTO.getDeliveryDate());
        }
        if (itemDTO.getIsSpecialPrice() != null) {
            item.setIsSpecialPrice(itemDTO.getIsSpecialPrice());
        }
        if (itemDTO.getPaperQuotation() != null) {
            item.setPaperQuotation(itemDTO.getPaperQuotation());
        }
        if (StringUtils.hasText(itemDTO.getConnectionMethod())) {
            item.setConnectionMethod(itemDTO.getConnectionMethod());
        }
        if (StringUtils.hasText(itemDTO.getStaplePosition())) {
            item.setStaplePosition(itemDTO.getStaplePosition());
        }
        if (itemDTO.getPackagingCount() != null) {
            item.setPackagingCount(itemDTO.getPackagingCount());
        }
        if (StringUtils.hasText(itemDTO.getProductionRemark())) {
            item.setProductionRemark(itemDTO.getProductionRemark());
        }
        if (StringUtils.hasText(itemDTO.getRemark())) {
            item.setRemark(itemDTO.getRemark());
        }
        if (itemDTO.getProductionLength() != null) {
            item.setProductionLength(itemDTO.getProductionLength());
        }
        if (itemDTO.getProductionWidth() != null) {
            item.setProductionWidth(itemDTO.getProductionWidth());
        }
        if (itemDTO.getProductionHeight() != null) {
            item.setProductionHeight(itemDTO.getProductionHeight());
        }
        if (itemDTO.getSpareRatio() != null) {
            item.setSpareRatio(itemDTO.getSpareRatio());
        }
        if (itemDTO.getSpareQuantityTotal() != null) {
            item.setSpareQuantityTotal(itemDTO.getSpareQuantityTotal());
        }
        if (itemDTO.getCurrentInventory() != null) {
            item.setCurrentInventory(itemDTO.getCurrentInventory());
        }
        if (itemDTO.getAvailableInventory() != null) {
            item.setAvailableInventory(itemDTO.getAvailableInventory());
        }
        if (itemDTO.getUseInventory() != null) {
            item.setUseInventory(itemDTO.getUseInventory());
        }
        if (itemDTO.getIsSpecialSpecification() != null) {
            item.setIsSpecialSpecification(itemDTO.getIsSpecialSpecification());
        }
        if (itemDTO.getUnitWeight() != null) {
            item.setUnitWeight(itemDTO.getUnitWeight());
        }
        if (itemDTO.getTotalWeight() != null) {
            item.setTotalWeight(itemDTO.getTotalWeight());
        }
        if (itemDTO.getProductArea() != null) {
            item.setProductArea(itemDTO.getProductArea());
        }
        if (itemDTO.getTotalArea() != null) {
            item.setTotalArea(itemDTO.getTotalArea());
        }
        if (itemDTO.getProductVolume() != null) {
            item.setProductVolume(itemDTO.getProductVolume());
        }
        if (itemDTO.getTotalVolume() != null) {
            item.setTotalVolume(itemDTO.getTotalVolume());
        }
        if (StringUtils.hasText(itemDTO.getComponent())) {
            item.setComponent(itemDTO.getComponent());
        }
        if (itemDTO.getIsSample() != null) {
            item.setIsSample(itemDTO.getIsSample());
        }
        if (itemDTO.getDeliveredQuantity() != null) {
            item.setDeliveredQuantity(itemDTO.getDeliveredQuantity());
        }
        if (itemDTO.getDeliveredSpareQuantity() != null) {
            item.setDeliveredSpareQuantity(itemDTO.getDeliveredSpareQuantity());
        }
        if (itemDTO.getReturnedQuantity() != null) {
            item.setReturnedQuantity(itemDTO.getReturnedQuantity());
        }
        if (itemDTO.getSafetyStock() != null) {
            item.setSafetyStock(itemDTO.getSafetyStock());
        }
        if (itemDTO.getReconciliationQuantity() != null) {
            item.setReconciliationQuantity(itemDTO.getReconciliationQuantity());
        }
        if (StringUtils.hasText(itemDTO.getUnit())) {
            item.setUnit(itemDTO.getUnit());
        }
        if (StringUtils.hasText(itemDTO.getCurrency())) {
            item.setCurrency(itemDTO.getCurrency());
        }
        if (itemDTO.getTaxRate() != null) {
            item.setTaxRate(itemDTO.getTaxRate());
        }

        // 不更新生产单号，生产单号由系统自动生成，不允许手动修改
        // 如果前端传入了生产单号，忽略它

        // 更新审计字段
        item.setUpdatedTime(LocalDateTime.now());

        // 更新创建人和更新人姓名
        if (StringUtils.hasText(itemDTO.getCreatedByName())) {
            item.setCreatedByName(itemDTO.getCreatedByName());
        }
        if (StringUtils.hasText(itemDTO.getUpdatedByName())) {
            item.setUpdatedByName(itemDTO.getUpdatedByName());
        }
    }

    /**
     * 根据ID查询销售订单
     * @param id 订单ID
     * @return 销售订单DTO
     */
    @Override
    public SalesOrderDetailDTO getOrderById(String id) {
        log.info("Getting sales order by id: {}", id);

        if (!StringUtils.hasText(id)) {
            throw new BusinessException("订单ID不能为空");
        }

        SalesOrder order = salesOrderRepository.findById(id)
                .orElseThrow(() -> new BusinessException("订单不存在"));

        // 加载订单明细
        List<SalesOrderItem> items = salesOrderItemRepository.findByOrderId(id);
        order.setItems(items);

        // 转换为DTO
        SalesOrderDetailDTO orderDTO = convertToDetailDTO(order);

        // 加载订单工序
        if (salesOrderProcessService != null) {
            List<SalesOrderProcessDTO> processes = salesOrderProcessService.getProcessesByOrderId(id);
            orderDTO.setProcesses(processes);
        }

        // 加载订单材料信息
        loadMaterialsForOrder(orderDTO);

        return orderDTO;
    }

    /**
     * 根据订单号查询销售订单
     * @param orderNo 订单号
     * @return 销售订单DTO
     */
    @Override
    public SalesOrderDetailDTO getOrderByOrderNo(String orderNo) {
        log.info("Getting sales order by orderNo: {}", orderNo);

        if (!StringUtils.hasText(orderNo)) {
            throw new BusinessException("订单号不能为空");
        }

        SalesOrder order = salesOrderRepository.findByOrderNo(orderNo)
                .orElseThrow(() -> new BusinessException("订单不存在"));

        // 加载订单明细
        List<SalesOrderItem> items = salesOrderItemRepository.findByOrderId(order.getId());
        order.setItems(items);

        // 转换为DTO
        SalesOrderDetailDTO orderDTO = convertToDetailDTO(order);

        // 加载订单工序
        if (salesOrderProcessService != null) {
            List<SalesOrderProcessDTO> processes = salesOrderProcessService.getProcessesByOrderId(order.getId());
            orderDTO.setProcesses(processes);
        }

        // 加载订单材料信息
        loadMaterialsForOrder(orderDTO);

        return orderDTO;
    }

    /**
     * 分页查询销售订单
     * @param queryDTO 查询条件
     * @return 销售订单分页列表
     */
    @Override
    public PageResponse<SalesOrderDTO> getOrders(SalesOrderQueryDTO queryDTO) {
        log.info("Getting sales orders with query: {}", queryDTO);

        // 构建排序
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        if (StringUtils.hasText(queryDTO.getSortField())) {
            Sort.Direction direction = Sort.Direction.ASC;
            if (queryDTO.getSortOrder() != null && queryDTO.getSortOrder().equalsIgnoreCase("desc")) {
                direction = Sort.Direction.DESC;
            }
            sort = Sort.by(direction, queryDTO.getSortField());
        }

        // 构建分页
        int pageIndex = Math.max(0, queryDTO.getPage() - 1); // 确保页码不小于0
        Pageable pageable = PageRequest.of(pageIndex, queryDTO.getPageSize(), sort);

        // 构建查询条件
        Specification<SalesOrder> spec = buildSpecification(queryDTO);

        // 执行查询
        Page<SalesOrder> orderPage = salesOrderRepository.findAll(spec, pageable);

        // 转换为DTO
        List<SalesOrderDTO> orderDTOs = orderPage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 构建分页响应
        return PageResponse.<SalesOrderDTO>builder()
                .content(orderDTOs)
                .totalElements(orderPage.getTotalElements())
                .totalPages(orderPage.getTotalPages())
                .page(queryDTO.getPage())
                .size(queryDTO.getPageSize())
                .first(queryDTO.getPage() == 1)
                .last(queryDTO.getPage() >= orderPage.getTotalPages())
                .empty(orderDTOs.isEmpty())
                .build();
    }

    /**
     * 构建查询条件
     * @param queryDTO 查询条件
     * @return 查询条件
     */
    private Specification<SalesOrder> buildSpecification(SalesOrderQueryDTO queryDTO) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 关键字查询
            if (StringUtils.hasText(queryDTO.getKeyword())) {
                String keyword = "%" + queryDTO.getKeyword() + "%";
                Predicate orderNoPredicate = criteriaBuilder.like(root.get("orderNo"), keyword);
                Predicate customerNamePredicate = criteriaBuilder.like(root.get("customerName"), keyword);
                predicates.add(criteriaBuilder.or(orderNoPredicate, customerNamePredicate));
            }

            // 订单号查询
            if (StringUtils.hasText(queryDTO.getOrderNo())) {
                predicates.add(criteriaBuilder.equal(root.get("orderNo"), queryDTO.getOrderNo()));
            }

            // 生产单号查询 - 移除，因为 productionOrderNo 在 SalesOrderItem 中，不在 SalesOrder 中

            // 客户编码查询
            if (StringUtils.hasText(queryDTO.getCustomerCode())) {
                predicates.add(criteriaBuilder.equal(root.get("customerCode"), queryDTO.getCustomerCode()));
            }

            // 客户名称查询
            if (StringUtils.hasText(queryDTO.getCustomerName())) {
                predicates.add(criteriaBuilder.like(root.get("customerName"), "%" + queryDTO.getCustomerName() + "%"));
            }

            // 客户订单号查询 - 移除，因为 customerOrderNo 在 SalesOrderItem 中，不在 SalesOrder 中

            // 销售员查询
            if (StringUtils.hasText(queryDTO.getSalesPerson())) {
                predicates.add(criteriaBuilder.equal(root.get("salesPerson"), queryDTO.getSalesPerson()));
            }

            // 客户采购员查询
            if (StringUtils.hasText(queryDTO.getCustomerPurchaser())) {
                predicates.add(criteriaBuilder.equal(root.get("customerPurchaser"), queryDTO.getCustomerPurchaser()));
            }

            // 订单日期范围查询
            if (queryDTO.getOrderDateStart() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("orderDate"), queryDTO.getOrderDateStart()));
            }
            if (queryDTO.getOrderDateEnd() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("orderDate"), queryDTO.getOrderDateEnd()));
            }

            // 创建人查询
            if (StringUtils.hasText(queryDTO.getCreatedBy())) {
                predicates.add(criteriaBuilder.equal(root.get("createdBy"), queryDTO.getCreatedBy()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 删除销售订单
     * @param id 订单ID
     */
    @Override
    @Transactional
    public void deleteOrder(String id) {
        log.info("Deleting sales order: {}", id);

        SalesOrder order = salesOrderRepository.findById(id)
                .orElseThrow(() -> new BusinessException("订单不存在"));

        // 删除订单工序
        salesOrderProcessService.deleteProcessesByOrderId(id);

        // 获取订单明细
        List<SalesOrderItem> items = salesOrderItemRepository.findByOrderId(id);
        if (!items.isEmpty()) {
            // 获取所有订单行项目ID
            List<String> orderItemIds = items.stream()
                    .map(SalesOrderItem::getId)
                    .collect(Collectors.toList());

            // 删除所有订单行项目的材料信息
            for (String orderItemId : orderItemIds) {
                salesOrderMaterialService.deleteMaterialsByOrderItemId(orderItemId);
            }

            // 删除订单明细
            salesOrderItemRepository.deleteAll(items);
        }

        // 删除订单
        salesOrderRepository.delete(order);
    }

    /**
     * 根据客户编码查询销售订单
     * @param customerCode 客户编码
     * @return 销售订单列表
     */
    @Override
    public List<SalesOrderDTO> getOrdersByCustomerCode(String customerCode) {
        log.info("Getting sales orders by customerCode: {}", customerCode);

        if (!StringUtils.hasText(customerCode)) {
            throw new BusinessException("客户编码不能为空");
        }

        // 构建排序
        Sort sort = Sort.by(Sort.Direction.DESC, "orderDate");

        // 构建分页（查询所有）
        Pageable pageable = PageRequest.of(0, Integer.MAX_VALUE, sort);

        // 执行查询
        Page<SalesOrder> orderPage = salesOrderRepository.findByCustomerCode(customerCode, pageable);

        // 转换为DTO
        return orderPage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }



    /**
     * 打印订单
     * @param id 订单ID
     * @return 销售订单DTO
     */
    @Override
    @Transactional
    public SalesOrderDTO printOrder(String id) {
        log.info("Printing sales order: id={}", id);

        // 验证订单是否存在
        SalesOrder order = salesOrderRepository.findById(id)
                .orElseThrow(() -> new BusinessException("订单不存在"));

        // 更新审计字段
        order.setUpdatedTime(LocalDateTime.now());

        // 保存订单
        SalesOrder savedOrder = salesOrderRepository.save(order);

        // 转换为DTO并返回
        return convertToDTO(savedOrder);
    }

    /**
     * 加载订单的材料信息
     * @param orderDTO 订单DTO
     */
    private void loadMaterialsForOrder(SalesOrderDetailDTO orderDTO) {
        if (orderDTO == null || orderDTO.getItems() == null || orderDTO.getItems().isEmpty()) {
            return;
        }

        // 获取所有订单行项目ID
        List<String> orderItemIds = orderDTO.getItems().stream()
                .map(SalesOrderItemDTO::getId)
                .collect(Collectors.toList());

        // 查询所有行项目的材料信息
        List<SalesOrderMaterialDTO> allMaterials = salesOrderMaterialService.getMaterialsByOrderId(orderDTO.getId());

        // 按订单行项目ID分组
        Map<String, List<SalesOrderMaterialDTO>> materialsByOrderItemId = new HashMap<>();
        for (SalesOrderMaterialDTO material : allMaterials) {
            if (!materialsByOrderItemId.containsKey(material.getOrderItemId())) {
                materialsByOrderItemId.put(material.getOrderItemId(), new ArrayList<>());
            }
            materialsByOrderItemId.get(material.getOrderItemId()).add(material);
        }

        // 设置到订单DTO中
        orderDTO.setMaterials(materialsByOrderItemId);

        // 设置到每个订单行项目DTO中
        for (SalesOrderItemDTO itemDTO : orderDTO.getItems()) {
            List<SalesOrderMaterialDTO> materials = materialsByOrderItemId.getOrDefault(itemDTO.getId(), new ArrayList<>());
            itemDTO.setMaterials(materials);
        }
    }

    /**
     * 根据客户ID查询历史订单明细
     * @param customerId 客户ID
     * @param keyword 关键词（可选，用于搜索客方货号或品名）
     * @return 历史订单明细列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<SalesOrderItemDTO> getHistoricalItemsByCustomerId(String customerId, String keyword) {
        log.info("Getting historical items by customer id: {}, keyword: {}", customerId, keyword);

        // 1. 先查询该客户是否存在
        Customer customer = customerRepository.findById(customerId)
                .orElseThrow(() -> new BusinessException("客户不存在"));

        // 2. 构建查询条件
        Specification<SalesOrderItem> spec = (root, query, cb) -> {
            Join<SalesOrderItem, SalesOrder> orderJoin = root.join("order", JoinType.INNER);

            List<Predicate> predicates = new ArrayList<>();

            // 客户编码条件
            predicates.add(cb.equal(orderJoin.get("customerCode"), customer.getCustomerCode()));

            // 关键词搜索（如果提供）
            if (StringUtils.hasText(keyword)) {
                predicates.add(cb.or(
                    cb.like(root.get("customerProductCode"), "%" + keyword + "%"),
                    cb.like(root.get("productName"), "%" + keyword + "%")
                ));
            }

            // 排除已删除的订单
            predicates.add(cb.equal(orderJoin.get("isDeleted"), false));

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 3. 构建排序（按创建时间降序，最新的在前面）
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");

        // 4. 执行查询
        List<SalesOrderItem> items = salesOrderItemRepository.findAll(spec, sort);

        // 5. 去重处理（按客方货号去重，保留最新的记录）
        Map<String, SalesOrderItem> uniqueItems = new LinkedHashMap<>();
        for (SalesOrderItem item : items) {
            if (StringUtils.hasText(item.getCustomerProductCode())) {
                uniqueItems.putIfAbsent(item.getCustomerProductCode(), item);
            }
        }

        // 6. 转换为DTO
        List<SalesOrderItemDTO> itemDTOs = uniqueItems.values().stream()
                .map(this::convertToHistoricalItemDTO)
                .collect(Collectors.toList());

        // 7. 批量加载工序信息
        if (!itemDTOs.isEmpty()) {
            // 获取所有订单ID
            Set<String> orderIds = itemDTOs.stream()
                    .map(SalesOrderItemDTO::getOrderId)
                    .collect(Collectors.toSet());

            // 批量查询工序信息
            Map<String, List<SalesOrderProcessDTO>> processesByOrderId = new HashMap<>();
            for (String orderId : orderIds) {
                List<SalesOrderProcessDTO> processes = salesOrderProcessService.getProcessesByOrderId(orderId);
                processesByOrderId.put(orderId, processes);
            }

            // 设置工序信息到每个订单明细DTO
            for (SalesOrderItemDTO itemDTO : itemDTOs) {
                List<SalesOrderProcessDTO> processes = processesByOrderId.getOrDefault(itemDTO.getOrderId(), new ArrayList<>());
                itemDTO.setProcesses(processes);
            }
        }

        // 8. 批量加载材料信息
        if (!itemDTOs.isEmpty()) {
            // 获取所有订单明细ID
            List<String> itemIds = itemDTOs.stream()
                    .map(SalesOrderItemDTO::getId)
                    .collect(Collectors.toList());

            // 批量查询材料信息
            Map<String, List<SalesOrderMaterialDTO>> materialsByItemId = new HashMap<>();
            for (String itemId : itemIds) {
                List<SalesOrderMaterialDTO> materials = salesOrderMaterialService.getMaterialsByOrderItemId(itemId);
                materialsByItemId.put(itemId, materials);
            }

            // 设置材料信息到每个订单明细DTO
            for (SalesOrderItemDTO itemDTO : itemDTOs) {
                List<SalesOrderMaterialDTO> materials = materialsByItemId.getOrDefault(itemDTO.getId(), new ArrayList<>());
                itemDTO.setMaterials(materials);
            }
        }

        return itemDTOs;
    }

    /**
     * 将订单明细实体转换为历史记录DTO
     * @param item 订单明细实体
     * @return 订单明细DTO
     */
    private SalesOrderItemDTO convertToHistoricalItemDTO(SalesOrderItem item) {
        SalesOrderItemDTO dto = new SalesOrderItemDTO();

        dto.setId(item.getId());
        dto.setOrderId(item.getOrder().getId());
        dto.setProductionOrderNo(item.getProductionOrderNo()); // 添加生产单号字段
        dto.setCustomerOrderNo(item.getCustomerOrderNo());
        dto.setCustomerProductCode(item.getCustomerProductCode());
        dto.setProductName(item.getProductName());
        dto.setProcessRequirements(item.getProcessRequirements());
        dto.setBoxType(item.getBoxType());
        dto.setPaperType(item.getPaperType());
        dto.setCorrugationType(item.getCorrugationType());
        dto.setProductionPaperType(item.getProductionPaperType());
        dto.setLength(item.getLength());
        dto.setWidth(item.getWidth());
        dto.setHeight(item.getHeight());
        dto.setSizeUnit(item.getSizeUnit());
        dto.setCreatedTime(item.getCreatedTime()); // 添加日期字段

        // 添加连接方式字段
        dto.setConnectionMethod(item.getConnectionMethod());

        // 添加钉口字段
        dto.setStaplePosition(item.getStaplePosition());

        // 处理specification字段 - 组合长宽高 + 单位
        String specification = formatSpecification(item.getLength(), item.getWidth(), item.getHeight(), item.getSizeUnit());
        dto.setSpecification(specification);

        // 处理productionSpecification字段 - 优先使用生产尺寸，为空时使用产品尺寸
        String productionSpecification = formatSpecification(item.getProductionLength(), item.getProductionWidth(), item.getProductionHeight(), item.getSizeUnit());
        if (productionSpecification.isEmpty()) {
            productionSpecification = specification;
        }
        dto.setProductionSpecification(productionSpecification);

        return dto;
    }

    /**
     * 格式化规格字符串
     * @param length 长
     * @param width 宽
     * @param height 高
     * @param sizeUnit 尺寸单位
     * @return 格式化后的规格字符串，格式：长×宽×高 单位
     */
    private String formatSpecification(BigDecimal length, BigDecimal width, BigDecimal height, String sizeUnit) {
        // 检查null值
        if (length == null || width == null || height == null) {
            return "";
        }

        // 检查是否所有值都为0
        boolean allZero = length.compareTo(BigDecimal.ZERO) == 0 &&
                          width.compareTo(BigDecimal.ZERO) == 0 &&
                          height.compareTo(BigDecimal.ZERO) == 0;

        if (allZero) {
            return "";
        }

        // 构建规格字符串：长×宽×高 单位
        StringBuilder sb = new StringBuilder();
        sb.append(length);
        sb.append("×");
        sb.append(width);
        sb.append("×");
        sb.append(height);

        // 添加单位（如果有的话）
        if (sizeUnit != null && !sizeUnit.trim().isEmpty()) {
            sb.append(" ");
            sb.append(sizeUnit);
        }

        return sb.toString();
    }
}
