package com.czerp.erpbackend.purchase.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.purchase.dto.CreateSupplierRequest;
import com.czerp.erpbackend.purchase.dto.SupplierDTO;
import com.czerp.erpbackend.purchase.dto.SupplierQueryRequest;
import com.czerp.erpbackend.purchase.dto.UpdateSupplierRequest;

import java.util.List;

/**
 * 供应商服务接口
 */
public interface SupplierService {
    
    /**
     * 分页查询供应商列表
     * @param request 查询请求
     * @return 供应商分页列表
     */
    PageResponse<SupplierDTO> findSuppliers(SupplierQueryRequest request);
    
    /**
     * 查询所有供应商
     * @return 供应商列表
     */
    List<SupplierDTO> findAllSuppliers();
    
    /**
     * 根据ID查询供应商
     * @param id 供应商ID
     * @return 供应商信息
     */
    SupplierDTO findSupplierById(Long id);
    
    /**
     * 创建供应商
     * @param request 创建请求
     * @return 供应商信息
     */
    SupplierDTO createSupplier(CreateSupplierRequest request);
    
    /**
     * 更新供应商
     * @param id 供应商ID
     * @param request 更新请求
     * @return 供应商信息
     */
    SupplierDTO updateSupplier(Long id, UpdateSupplierRequest request);
    
    /**
     * 删除供应商
     * @param id 供应商ID
     */
    void deleteSupplier(Long id);
    
    /**
     * 批量删除供应商
     * @param ids 供应商ID列表
     */
    void batchDeleteSuppliers(List<Long> ids);
    
    /**
     * 切换供应商状态
     * @param id 供应商ID
     * @param status 状态
     * @return 供应商信息
     */
    SupplierDTO toggleSupplierStatus(Long id, String status);
}
