package com.czerp.erpbackend.production.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 创建生产排程单明细请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateProductionScheduleItemRequest {

    /**
     * 急单
     */
    private Boolean isUrgent = false;

    /**
     * 急单序号
     */
    private Integer urgentSequence;

    /**
     * 已打印
     */
    private Boolean isPrinted = false;

    /**
     * 计划完成日期
     */
    private LocalDate plannedCompletionDate;

    /**
     * 排程数量
     */
    @NotNull(message = "排程数量不能为空")
    private Integer scheduleQuantity;

    /**
     * 包装数
     */
    private Integer packageCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 销售订单明细ID
     */
    private String salesOrderItemId;
}
