package com.czerp.erpbackend.system.repository;

import com.czerp.erpbackend.system.entity.Department;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 部门存储库
 */
@Repository
public interface DepartmentRepository extends JpaRepository<Department, String> {
    
    /**
     * 根据编码查找部门
     * @param code 编码
     * @return 部门
     */
    Optional<Department> findByCode(String code);
    
    /**
     * 判断编码是否存在
     * @param code 编码
     * @return 是否存在
     */
    boolean existsByCode(String code);
    
    /**
     * 查询所有未删除的部门
     * @return 部门列表
     */
    List<Department> findByIsDeletedFalseOrderBySortAsc();
    
    /**
     * 查询根部门列表
     * @return 根部门列表
     */
    List<Department> findByParentIdIsNullAndIsDeletedFalseOrderBySortAsc();
    
    /**
     * 根据父部门ID查询子部门列表
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Department> findByParentIdAndIsDeletedFalseOrderBySortAsc(String parentId);
    
    /**
     * 根据父部门ID和状态查询子部门数量
     * @param parentId 父部门ID
     * @return 子部门数量
     */
    long countByParentIdAndIsDeletedFalse(String parentId);
} 