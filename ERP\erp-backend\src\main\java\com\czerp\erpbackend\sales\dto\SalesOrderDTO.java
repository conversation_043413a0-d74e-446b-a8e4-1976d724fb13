package com.czerp.erpbackend.sales.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销售订单DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderDTO {

    /**
     * 订单ID
     */
    private String id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单日期
     */
    private LocalDate orderDate;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 销售员
     */
    private String salesPerson;

    /**
     * 客户采购员
     */
    private String customerPurchaser;

    /**
     * 收货单位
     */
    private String receivingUnit;

    /**
     * 收货人
     */
    private String receiver;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 收货地址
     */
    private String receivingAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新人姓名
     */
    private String updatedByName;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
