package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.system.dto.CreateUserRequest;
import com.czerp.erpbackend.system.dto.UpdateUserRequest;
import com.czerp.erpbackend.system.dto.UserDTO;
import com.czerp.erpbackend.system.dto.UserQueryRequest;
import com.czerp.erpbackend.system.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "User Management", description = "用户管理相关接口")
public class UserController {

    private final UserService userService;

    @GetMapping
    @Operation(summary = "获取用户列表", description = "分页查询用户列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('user:list')")
    public ResponseEntity<ApiResponse<PageResponse<UserDTO>>> getUsers(UserQueryRequest request) {
        PageResponse<UserDTO> users = userService.findUsers(request);
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取用户详情", description = "根据ID获取用户详情")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('user:read')")
    public ResponseEntity<ApiResponse<UserDTO>> getUser(@PathVariable String id) {
        UserDTO user = userService.findUserById(id);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @GetMapping("/current")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @SecurityRequirement(name = "Bearer Authentication")
    public ResponseEntity<ApiResponse<UserDTO>> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        UserDTO user = userService.findUserByUsername(username);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @GetMapping("/current/authorities")
    @Operation(summary = "获取当前用户权限", description = "获取当前登录用户的权限信息")
    @SecurityRequirement(name = "Bearer Authentication")
    public ResponseEntity<ApiResponse<Object>> getCurrentUserAuthorities() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return ResponseEntity.ok(ApiResponse.success(authentication.getAuthorities()));
    }

    @PostMapping
    @Operation(summary = "创建用户", description = "创建新用户")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('user:create')")
    public ResponseEntity<ApiResponse<UserDTO>> createUser(@Valid @RequestBody CreateUserRequest request) {
        UserDTO user = userService.createUser(request);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新用户", description = "更新用户信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('user:update')")
    public ResponseEntity<ApiResponse<UserDTO>> updateUser(
            @PathVariable String id,
            @Valid @RequestBody UpdateUserRequest request) {
        UserDTO user = userService.updateUser(id, request);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "删除用户")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('user:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable String id) {
        userService.deleteUser(id);
        return ResponseEntity.ok(ApiResponse.success());
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "修改用户状态", description = "启用或禁用用户")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('user:update')")
    public ResponseEntity<ApiResponse<UserDTO>> changeUserStatus(
            @PathVariable String id,
            @RequestBody String status) {
        UserDTO user = userService.changeStatus(id, status);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @PostMapping("/{id}/reset-password")
    @Operation(summary = "重置用户密码", description = "重置用户密码")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('user:update')")
    public ResponseEntity<ApiResponse<UserDTO>> resetPassword(@PathVariable String id) {
        UserDTO user = userService.resetPassword(id);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @PostMapping("/change-password")
    @Operation(summary = "修改密码", description = "修改当前用户密码")
    @SecurityRequirement(name = "Bearer Authentication")
    public ResponseEntity<ApiResponse<UserDTO>> changePassword(
            @RequestParam String oldPassword,
            @RequestParam String newPassword) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        UserDTO user = userService.findUserByUsername(username);
        UserDTO updatedUser = userService.changePassword(user.getId(), oldPassword, newPassword);
        return ResponseEntity.ok(ApiResponse.success(updatedUser));
    }

    @GetMapping("/by-department/{departmentId}")
    @Operation(summary = "获取部门用户", description = "获取指定部门的用户列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('user:list')")
    public ResponseEntity<ApiResponse<PageResponse<UserDTO>>> getUsersByDepartment(
            @PathVariable String departmentId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageResponse<UserDTO> users = userService.findUsersByDepartmentId(departmentId, page, size);
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    @GetMapping("/by-role/{roleId}")
    @Operation(summary = "获取角色用户", description = "获取指定角色的用户列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('user:list')")
    public ResponseEntity<ApiResponse<PageResponse<UserDTO>>> getUsersByRole(
            @PathVariable String roleId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageResponse<UserDTO> users = userService.findUsersByRoleId(roleId, page, size);
        return ResponseEntity.ok(ApiResponse.success(users));
    }
}
