package com.czerp.erpbackend.purchase.repository;

import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 采购订单Repository
 */
@Repository
public interface PurchaseOrderRepository extends JpaRepository<PurchaseOrder, Long>, JpaSpecificationExecutor<PurchaseOrder> {

    /**
     * 根据采购单号查询采购订单
     * @param purchaseOrderNo 采购单号
     * @return 采购订单
     */
    PurchaseOrder findByPurchaseOrderNo(String purchaseOrderNo);

    /**
     * 检查采购单号是否存在
     * @param purchaseOrderNo 采购单号
     * @return 是否存在
     */
    boolean existsByPurchaseOrderNo(String purchaseOrderNo);

    /**
     * 查询指定前缀的最大采购单号
     * @param prefix 前缀
     * @return 最大采购单号
     */
    @Query("SELECT MAX(p.purchaseOrderNo) FROM PurchaseOrder p WHERE p.purchaseOrderNo LIKE :prefix%")
    String findMaxPurchaseOrderNoByPrefix(@Param("prefix") String prefix);
}
