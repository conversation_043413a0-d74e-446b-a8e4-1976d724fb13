package com.czerp.erpbackend.system.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 系统部门实体
 */
@Entity
@Table(name = "sys_department")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Department extends BaseEntity {
    
    /**
     * 部门ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;
    
    /**
     * 部门名称
     */
    @Column(name = "name", length = 50, nullable = false)
    private String name;
    
    /**
     * 部门编码
     */
    @Column(name = "code", length = 50, nullable = false, unique = true)
    private String code;
    
    /**
     * 父部门ID
     */
    @Column(name = "parent_id", length = 36)
    private String parentId;
    
    /**
     * 父部门
     */
    @ManyToOne
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private Department parent;
    
    /**
     * 层级
     */
    @Column(name = "level", nullable = false)
    private Integer level = 1;
    
    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort = 0;
    
    /**
     * 部门负责人ID
     */
    @Column(name = "manager_id", length = 36)
    private String managerId;
    
    /**
     * 部门负责人
     */
    @ManyToOne
    @JoinColumn(name = "manager_id", insertable = false, updatable = false)
    private User manager;
    
    /**
     * 状态(active-启用,inactive-停用)
     */
    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";
} 