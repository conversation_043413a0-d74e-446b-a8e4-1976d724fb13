-- 修复字符集冲突问题
-- 将所有表统一为 utf8mb4_0900_ai_ci 字符集
-- 执行日期：2025-06-03
-- 问题：Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_unicode_ci,IMPLICIT) for operation '='

-- 设置安全模式
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';

-- ========================================
-- 1. 修复采购订单相关表
-- ========================================

-- 修复 purchase_order 表
ALTER TABLE purchase_order CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 purchase_order_item 表
ALTER TABLE purchase_order_item CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- ========================================
-- 2. 修复供应商相关表
-- ========================================

-- 修复 pur_supplier 表
ALTER TABLE pur_supplier CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 pur_supplier_category 表
ALTER TABLE pur_supplier_category CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- ========================================
-- 3. 修复销售订单相关表
-- ========================================

-- 修复 sales_order_item_process 表
ALTER TABLE sales_order_item_process CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 sales_order_material_detail 表
ALTER TABLE sales_order_material_detail CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 sales_order_process_step 表
ALTER TABLE sales_order_process_step CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- ========================================
-- 4. 修复系统表
-- ========================================

-- 修复 sys_department 表
ALTER TABLE sys_department CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 sys_measurement_unit 表
ALTER TABLE sys_measurement_unit CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 sys_permission 表
ALTER TABLE sys_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 sys_permission_backup 表
ALTER TABLE sys_permission_backup CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 sys_role 表
ALTER TABLE sys_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 sys_role_permission 表
ALTER TABLE sys_role_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 sys_user 表
ALTER TABLE sys_user CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 sys_user_role 表
ALTER TABLE sys_user_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- ========================================
-- 5. 修复其他表
-- ========================================

-- 修复 example_entity 表
ALTER TABLE example_entity CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 修复 paper_size_setting 表
ALTER TABLE paper_size_setting CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- ========================================
-- 6. 验证修复结果
-- ========================================

-- 查看所有表的字符集情况
SELECT 
    TABLE_NAME, 
    TABLE_COLLATION,
    CASE 
        WHEN TABLE_COLLATION = 'utf8mb4_0900_ai_ci' THEN '✓ 正确'
        ELSE '✗ 需要修复'
    END AS STATUS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'czerp_web' 
ORDER BY TABLE_NAME;

-- 恢复安全模式
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 执行说明
-- ========================================
/*
1. 此脚本将所有表的字符集统一为 utf8mb4_0900_ai_ci
2. 这是 MySQL 8.0 的默认字符集，性能更好
3. 执行前请备份数据库
4. 执行后需要重启应用程序
5. 建议在维护窗口期间执行

执行步骤：
1. 停止应用程序
2. 备份数据库：mysqldump -u root -p czerp_web > backup_before_collation_fix.sql
3. 执行此脚本
4. 验证结果
5. 重启应用程序
6. 测试功能是否正常

回滚方案：
如果出现问题，可以恢复备份：
mysql -u root -p czerp_web < backup_before_collation_fix.sql
*/
