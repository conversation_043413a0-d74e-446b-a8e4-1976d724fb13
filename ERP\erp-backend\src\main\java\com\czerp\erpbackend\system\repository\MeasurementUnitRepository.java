package com.czerp.erpbackend.system.repository;

import com.czerp.erpbackend.system.entity.MeasurementUnit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 计量单位存储库
 */
@Repository
public interface MeasurementUnitRepository extends JpaRepository<MeasurementUnit, Long> {

    /**
     * 根据单位名称查找计量单位
     * @param unitName 单位名称
     * @return 计量单位
     */
    Optional<MeasurementUnit> findByUnitName(String unitName);

    /**
     * 判断单位名称是否存在
     * @param unitName 单位名称
     * @return 是否存在
     */
    boolean existsByUnitName(String unitName);

    /**
     * 查询所有计量单位，按排序字段升序
     * @return 计量单位列表
     */
    List<MeasurementUnit> findByOrderBySortOrderAsc();

    /**
     * 查询所有启用的计量单位，按排序字段升序
     * @return 计量单位列表
     */
    List<MeasurementUnit> findByStatusEqualsOrderBySortOrderAsc(String status);

    /**
     * 查询所有尺寸单位的计量单位，按排序字段升序
     * @return 计量单位列表
     */
    List<MeasurementUnit> findByIsDimensionUnitTrueOrderBySortOrderAsc();

    /**
     * 查询默认物料单位
     * @return 计量单位
     */
    Optional<MeasurementUnit> findByIsDefaultForNewMaterialTrue();

    /**
     * 查询默认纸箱尺寸单位
     * @return 计量单位
     */
    Optional<MeasurementUnit> findByIsDefaultDimensionUnitTrue();

    /**
     * 查询默认纸度单位
     * @return 计量单位
     */
    Optional<MeasurementUnit> findByIsDefaultThicknessUnitTrue();

    /**
     * 查询默认纸长单位
     * @return 计量单位
     */
    Optional<MeasurementUnit> findByIsDefaultLengthUnitTrue();

    /**
     * 多条件查询计量单位
     * @param keyword 关键词
     * @param status 状态
     * @param isDimensionUnit 是否为尺寸单位
     * @param pageable 分页参数
     * @return 计量单位分页列表
     */
    @Query("SELECT m FROM MeasurementUnit m WHERE " +
           "(:keyword IS NULL OR :keyword = '' OR m.unitName LIKE %:keyword%) AND " +
           "(:status IS NULL OR :status = '' OR m.status = :status) AND " +
           "(:isDimensionUnit IS NULL OR m.isDimensionUnit = :isDimensionUnit) AND " +
           "m.isDeleted = false")
    Page<MeasurementUnit> search(
            @Param("keyword") String keyword,
            @Param("status") String status,
            @Param("isDimensionUnit") Boolean isDimensionUnit,
            Pageable pageable
    );
}
