前端场景：
前端 - 添加行时:
用户点击 "+ 添加" 按钮（无论是 Inline Add 还是弹框添加）。
前端在表格中添加新行。
关键区别: 此时，“生产单号” 单元格保持为空，或者显示一个占位符/提示文本，例如 "待生成"、"保存后生成" 或 "(Auto)"。
这个单元格必须是只读 (read-only) 或禁用 (disabled) 状态，明确告知用户此处无需也无法手动输入。
用户填写该行其他可编辑的单元格（客户订单号、品名、数量等）。
前端 - 点击保存单据时:
用户填写完所有表头信息和表格行项目后，点击页面上总的 "保存" 或 "提交" 按钮。
前端收集整个表单的数据，包括：
表头信息（客户、日期、付款方式等）。
表格中所有行的数据（此时这些行的“生产单号”字段值是空的或表示待生成的状态）。
将这个完整的数据包（通常是 JSON 格式）通过 API 请求发送到后端进行保存。


后端流程：
后端接收到前端发送的完整订单数据。
在将数据写入数据库之前（或者在同一个数据库事务中）：
后端逻辑识别出这是一个新建的订单（或者识别出请求数据中包含了需要生成编号的新行项目）。
后端调用其可靠的序列号生成机制（如数据库 Sequence、Redis 原子计数器、分布式ID生成服务等）。
为每一个需要编号的行项目生成一个唯一的、格式化好的生产单号（例如 "000001", "000002" ...）。
将这些新生成的编号填充到后端正在处理的数据对象的相应字段中。
后端对包含新生成编号的完整订单数据进行最终的业务逻辑校验。
如果校验通过：
将带有生产单号的完整订单数据（表头和所有行项目）保存到数据库中。确保生成编号和保存数据在同一事务中完成，保证数据一致性。
向前端返回保存成功的响应。这个响应可以包含刚刚生成的生产单号（如果前端需要在不刷新的情况下显示它们）。
如果校验失败或保存过程中出错：
回滚事务（如果使用了事务）。
向前端返回错误信息

这种方式的优点:
原子性: 编号生成和数据保存紧密耦合（最好在同一事务中），要么都成功，要么都失败，避免了只生成了编号但数据没保存成功的情况。
减少前端复杂度: 前端在添加行时无需关心编号生成，只需留空或显示占位符。
减少不必要的API调用: 不需要在每次添加行时都请求编号。
避免编号浪费: 只有在确认要保存整个有效单据时才分配编号，减少了因用户中途放弃而导致的序列号跳跃（虽然跳号通常是允许的，但这种方式更节省）。
后端权威性: 保持了编号生成的权威性、唯一性和安全性在后端。