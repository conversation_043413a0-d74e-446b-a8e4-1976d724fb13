-- MySQL兼容的动态计算已入库数量索引创建脚本
-- 解决MySQL不支持条件索引创建和INCLUDE语法的问题

-- 使用当前数据库
USE czerp;

-- 1. 检查并创建核心复合索引
-- 用于优化单个采购订单明细的已入库数量计算
DROP PROCEDURE IF EXISTS CreateIndexIfNotExists;

DELIMITER $$
CREATE PROCEDURE CreateIndexIfNotExists()
BEGIN
    -- 检查idx_stock_inbound_item_calculation索引是否存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = DATABASE() 
        AND table_name = 'stock_inbound_item' 
        AND index_name = 'idx_stock_inbound_item_calculation'
    ) THEN
        SET @sql = 'CREATE INDEX idx_stock_inbound_item_calculation ON stock_inbound_item(purchase_order_item_id, is_deleted, quantity)';
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SELECT 'Created index: idx_stock_inbound_item_calculation' as result;
    ELSE
        SELECT 'Index already exists: idx_stock_inbound_item_calculation' as result;
    END IF;

    -- 检查idx_stock_inbound_item_purchase_order_item_id索引是否存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = DATABASE() 
        AND table_name = 'stock_inbound_item' 
        AND index_name = 'idx_stock_inbound_item_purchase_order_item_id'
    ) THEN
        SET @sql = 'CREATE INDEX idx_stock_inbound_item_purchase_order_item_id ON stock_inbound_item(purchase_order_item_id)';
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SELECT 'Created index: idx_stock_inbound_item_purchase_order_item_id' as result;
    ELSE
        SELECT 'Index already exists: idx_stock_inbound_item_purchase_order_item_id' as result;
    END IF;

    -- 检查idx_stock_inbound_item_is_deleted索引是否存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = DATABASE() 
        AND table_name = 'stock_inbound_item' 
        AND index_name = 'idx_stock_inbound_item_is_deleted'
    ) THEN
        SET @sql = 'CREATE INDEX idx_stock_inbound_item_is_deleted ON stock_inbound_item(is_deleted)';
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SELECT 'Created index: idx_stock_inbound_item_is_deleted' as result;
    ELSE
        SELECT 'Index already exists: idx_stock_inbound_item_is_deleted' as result;
    END IF;

    -- 检查idx_purchase_order_item_quantity索引是否存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = DATABASE() 
        AND table_name = 'purchase_order_item' 
        AND index_name = 'idx_purchase_order_item_quantity'
    ) THEN
        SET @sql = 'CREATE INDEX idx_purchase_order_item_quantity ON purchase_order_item(quantity)';
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SELECT 'Created index: idx_purchase_order_item_quantity' as result;
    ELSE
        SELECT 'Index already exists: idx_purchase_order_item_quantity' as result;
    END IF;

END$$
DELIMITER ;

-- 执行存储过程创建索引
CALL CreateIndexIfNotExists();

-- 删除临时存储过程
DROP PROCEDURE CreateIndexIfNotExists;

-- 2. 直接创建索引（使用IF NOT EXISTS语法，MySQL 5.7+支持）
-- 如果上面的存储过程方式不适用，可以使用下面的简单方式

-- 核心复合索引
CREATE INDEX IF NOT EXISTS idx_stock_inbound_item_calculation 
ON stock_inbound_item(purchase_order_item_id, is_deleted, quantity);

-- 采购订单明细ID索引
CREATE INDEX IF NOT EXISTS idx_stock_inbound_item_purchase_order_item_id 
ON stock_inbound_item(purchase_order_item_id);

-- 软删除标记索引
CREATE INDEX IF NOT EXISTS idx_stock_inbound_item_is_deleted 
ON stock_inbound_item(is_deleted);

-- 采购数量索引
CREATE INDEX IF NOT EXISTS idx_purchase_order_item_quantity 
ON purchase_order_item(quantity);

-- 3. 分析表统计信息
ANALYZE TABLE stock_inbound_item;
ANALYZE TABLE purchase_order_item;

-- 4. 验证索引创建结果
SELECT 
    table_name,
    index_name,
    column_name,
    cardinality,
    index_type,
    CASE 
        WHEN non_unique = 0 THEN 'UNIQUE'
        ELSE 'NON-UNIQUE'
    END as uniqueness
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name IN ('stock_inbound_item', 'purchase_order_item')
AND index_name LIKE 'idx_%'
ORDER BY table_name, index_name, seq_in_index;

-- 5. 性能测试查询
-- 测试单个采购订单明细的已入库数量计算
EXPLAIN FORMAT=JSON
SELECT COALESCE(SUM(sii.quantity), 0) 
FROM stock_inbound_item sii 
WHERE sii.purchase_order_item_id = 1 
AND sii.is_deleted = false;

-- 测试批量采购订单明细的已入库数量计算
EXPLAIN FORMAT=JSON
SELECT sii.purchase_order_item_id, COALESCE(SUM(sii.quantity), 0) 
FROM stock_inbound_item sii 
WHERE sii.purchase_order_item_id IN (1, 2, 3, 4, 5) 
AND sii.is_deleted = false 
GROUP BY sii.purchase_order_item_id;

-- 6. 索引使用情况监控查询
-- 查看索引大小
SELECT 
    table_name,
    index_name,
    ROUND(stat_value * @@innodb_page_size / 1024 / 1024, 2) AS size_mb
FROM mysql.innodb_index_stats 
WHERE database_name = DATABASE()
AND table_name IN ('stock_inbound_item', 'purchase_order_item')
AND stat_name = 'size'
ORDER BY size_mb DESC;

-- 查看索引统计信息
SELECT 
    table_name,
    index_name,
    stat_name,
    stat_value
FROM mysql.innodb_index_stats 
WHERE database_name = DATABASE()
AND table_name IN ('stock_inbound_item', 'purchase_order_item')
AND index_name LIKE 'idx_%'
ORDER BY table_name, index_name, stat_name;
