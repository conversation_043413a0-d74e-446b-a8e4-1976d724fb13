package com.czerp.erpbackend.system.config;

import com.czerp.erpbackend.system.dto.LengthUnit;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

/**
 * 字符串到长度单位枚举的转换器
 * 支持大小写不敏感的单位转换，如"cm"、"CM"、"Cm"都会转换为LengthUnit.CM
 */
@Component
public class StringToLengthUnitConverter implements Converter<String, LengthUnit> {
    
    @Override
    public LengthUnit convert(String source) {
        if (source == null || source.isEmpty()) {
            return LengthUnit.CM; // 默认使用厘米
        }
        
        try {
            // 将输入字符串转换为大写，然后尝试匹配枚举值
            return LengthUnit.valueOf(source.toUpperCase());
        } catch (IllegalArgumentException e) {
            // 如果无法匹配，返回默认值
            return LengthUnit.CM;
        }
    }
}
