package com.czerp.erpbackend.purchase.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 创建采购订单请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreatePurchaseOrderRequest {

    /**
     * 采购日期
     */
    @NotNull(message = "采购日期不能为空")
    private LocalDate purchaseDate;

    /**
     * 采购员
     */
    private String purchaser;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 采购类型
     */
    private String purchaseType;

    /**
     * 交易单位
     */
    private String tradingUnit;

    /**
     * 供应商编码
     */
    @NotBlank(message = "供应商编码不能为空")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空")
    private String supplierName;

    /**
     * 地址
     */
    private String address;

    /**
     * 电话
     */
    private String phone;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 订单明细
     */
    @NotEmpty(message = "订单明细不能为空")
    @Valid
    private List<CreatePurchaseOrderItemRequest> items = new ArrayList<>();
}
