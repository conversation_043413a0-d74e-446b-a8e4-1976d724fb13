package com.czerp.erpbackend.system.init;

import com.czerp.erpbackend.common.constant.CommonStatus;
import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.entity.User;
import com.czerp.erpbackend.system.entity.UserRole;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import com.czerp.erpbackend.system.repository.UserRepository;
import com.czerp.erpbackend.system.repository.UserRoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 权限初始化器
 * 用于初始化系统权限、角色和管理员用户
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final UserRepository userRepository;
    private final RolePermissionRepository rolePermissionRepository;
    private final UserRoleRepository userRoleRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing permissions, roles and admin user...");

        // 初始化权限
        initPermissions();

        // 初始化角色
        initRoles();

        // 初始化管理员用户
        initAdminUser();

        log.info("Initialization completed.");
    }

    /**
     * 初始化权限
     */
    private void initPermissions() {
        log.info("Initializing permissions...");

        // 检查是否已经初始化
        if (permissionRepository.count() > 0) {
            log.info("Permissions already initialized, skipping...");
            return;
        }

        // 用户管理权限
        createPermission("用户管理", "user", "menu", null, "/system/users", "system/user/index", "user", 10);
        createPermission("用户列表", "user:list", "button", findPermissionIdByCode("user"), null, null, null, 11);
        createPermission("用户详情", "user:read", "button", findPermissionIdByCode("user"), null, null, null, 12);
        createPermission("创建用户", "user:create", "button", findPermissionIdByCode("user"), null, null, null, 13);
        createPermission("更新用户", "user:update", "button", findPermissionIdByCode("user"), null, null, null, 14);
        createPermission("删除用户", "user:delete", "button", findPermissionIdByCode("user"), null, null, null, 15);

        // 角色管理权限
        createPermission("角色管理", "role", "menu", null, "/system/roles", "system/role/index", "team", 20);
        createPermission("角色列表", "role:list", "button", findPermissionIdByCode("role"), null, null, null, 21);
        createPermission("角色详情", "role:read", "button", findPermissionIdByCode("role"), null, null, null, 22);
        createPermission("创建角色", "role:create", "button", findPermissionIdByCode("role"), null, null, null, 23);
        createPermission("更新角色", "role:update", "button", findPermissionIdByCode("role"), null, null, null, 24);
        createPermission("删除角色", "role:delete", "button", findPermissionIdByCode("role"), null, null, null, 25);

        // 权限管理权限
        createPermission("权限管理", "permission", "menu", null, "/system/permissions", "system/permission/index", "safety", 30);
        createPermission("权限列表", "permission:list", "button", findPermissionIdByCode("permission"), null, null, null, 31);
        createPermission("权限详情", "permission:read", "button", findPermissionIdByCode("permission"), null, null, null, 32);

        log.info("Permissions initialized successfully.");
    }

    /**
     * 初始化角色
     */
    private void initRoles() {
        log.info("Initializing roles...");

        // 检查是否已经初始化
        if (roleRepository.count() > 0) {
            log.info("Roles already initialized, skipping...");
            return;
        }

        // 创建管理员角色
        Role adminRole = new Role();
        adminRole.setId(UUID.randomUUID().toString());
        adminRole.setName("管理员");
        adminRole.setCode("admin");
        adminRole.setDescription("系统管理员，拥有所有权限");
        adminRole.setStatus(CommonStatus.ENABLED.getValue());
        roleRepository.save(adminRole);

        // 创建普通用户角色
        Role userRole = new Role();
        userRole.setId(UUID.randomUUID().toString());
        userRole.setName("普通用户");
        userRole.setCode("user");
        userRole.setDescription("普通用户，拥有基本权限");
        userRole.setStatus(CommonStatus.ENABLED.getValue());
        roleRepository.save(userRole);

        // 为管理员角色分配所有权限
        List<Permission> allPermissions = permissionRepository.findAll();
        List<RolePermission> adminRolePermissions = new ArrayList<>();

        for (Permission permission : allPermissions) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setId(UUID.randomUUID().toString());
            rolePermission.setRoleId(adminRole.getId());
            rolePermission.setPermissionId(permission.getId());
            adminRolePermissions.add(rolePermission);
        }

        rolePermissionRepository.saveAll(adminRolePermissions);

        // 为普通用户角色分配基本权限
        List<String> userPermissionCodes = Arrays.asList("user:list", "user:read", "role:list", "role:read");
        List<Permission> userPermissions = permissionRepository.findByCodeIn(userPermissionCodes);
        List<RolePermission> userRolePermissions = new ArrayList<>();

        for (Permission permission : userPermissions) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setId(UUID.randomUUID().toString());
            rolePermission.setRoleId(userRole.getId());
            rolePermission.setPermissionId(permission.getId());
            userRolePermissions.add(rolePermission);
        }

        rolePermissionRepository.saveAll(userRolePermissions);

        log.info("Roles initialized successfully.");
    }

    /**
     * 初始化管理员用户
     */
    private void initAdminUser() {
        log.info("Initializing admin user...");

        // 检查是否已经初始化
        Optional<User> existingAdmin = userRepository.findByUsername("admin");
        User adminUser;

        if (existingAdmin.isPresent()) {
            log.info("Admin user already exists, checking role assignment...");
            adminUser = existingAdmin.get();
        } else {
            log.info("Creating admin user...");
            // 创建管理员用户
            adminUser = new User();
            adminUser.setId(UUID.randomUUID().toString());
            adminUser.setUsername("admin");
            adminUser.setPassword(passwordEncoder.encode("admin123"));
            adminUser.setNickname("系统管理员");
            adminUser.setEmail("<EMAIL>");
            adminUser.setPhone("13800138000");
            adminUser.setStatus("active");
            userRepository.save(adminUser);
            log.info("Admin user created successfully.");
        }

        // 为管理员用户分配管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isPresent()) {
            // 检查用户是否已分配角色
            if (!userRoleRepository.existsByUserIdAndRoleId(adminUser.getId(), adminRole.get().getId())) {
                log.info("Assigning admin role to admin user...");
                UserRole userRole = new UserRole();
                userRole.setId(UUID.randomUUID().toString());
                userRole.setUserId(adminUser.getId());
                userRole.setRoleId(adminRole.get().getId());
                userRoleRepository.save(userRole);
                log.info("Admin role assigned to admin user successfully.");
            } else {
                log.info("Admin user already has admin role, skipping...");
            }
        }

        log.info("Admin user initialized successfully.");
    }

    /**
     * 创建权限
     */
    private Permission createPermission(String name, String code, String type, String parentId, String path, String component, String icon, Integer sort) {
        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setName(name);
        permission.setCode(code);
        permission.setType(type);
        permission.setParentId(parentId);
        permission.setPath(path);
        permission.setComponent(component);
        permission.setIcon(icon);
        permission.setSort(sort);
        permission.setStatus(CommonStatus.ENABLED.getValue());
        return permissionRepository.save(permission);
    }

    /**
     * 根据编码查找权限ID
     */
    private String findPermissionIdByCode(String code) {
        Optional<Permission> permission = permissionRepository.findByCode(code);
        return permission.map(Permission::getId).orElse(null);
    }
}
