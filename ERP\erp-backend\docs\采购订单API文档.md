# 采购订单API文档

## 目录

- [1. 概述](#1-概述)
- [2. 接口列表](#2-接口列表)
- [3. 接口详情](#3-接口详情)
  - [3.1 生成采购单号](#31-生成采购单号)
  - [3.2 创建采购订单](#32-创建采购订单)
  - [3.3 分页查询采购订单](#33-分页查询采购订单)
  - [3.4 分页查询采购订单明细](#34-分页查询采购订单明细)
  - [3.5 更新采购订单](#35-更新采购订单)
  - [3.6 根据ID查询采购订单](#36-根据id查询采购订单)
- [4. 数据结构](#4-数据结构)
  - [4.1 采购订单创建请求](#41-采购订单创建请求)
  - [4.2 采购订单更新请求](#42-采购订单更新请求)
  - [4.3 采购订单明细创建请求](#43-采购订单明细创建请求)
  - [4.4 采购订单响应](#44-采购订单响应)
  - [4.5 采购订单明细响应](#45-采购订单明细响应)
  - [4.6 采购订单明细查询响应](#46-采购订单明细查询响应)
  - [4.7 分页响应](#47-分页响应)
  - [4.8 API统一响应](#48-api统一响应)
  - [4.9 采购订单明细字段来源说明](#49-采购订单明细字段来源说明)

## 1. 概述

采购订单API提供了创建、更新、查询采购订单的功能，包括生成采购单号、创建采购订单、更新采购订单、分页查询采购订单列表、分页查询采购订单明细列表以及根据ID查询采购订单详情。

### 1.1 查询接口说明

系统提供两种查询模式以适应不同的业务场景：

1. **订单级别查询** (`/purchase-orders`)：按采购订单数量分页，每个订单包含完整的明细列表，适用于订单管理和详情查看
2. **明细级别查询** (`/purchase-orders/items`)：按明细行数量分页，返回扁平化的明细数据，适用于表格显示和行合并展示

## 2. 接口列表

| 接口名称 | 请求方式 | 接口路径 | 描述 |
| --- | --- | --- | --- |
| 生成采购单号 | GET | /purchase-orders/generate-order-no | 生成新的采购单号 |
| 创建采购订单 | POST | /purchase-orders | 创建新的采购订单 |
| 分页查询采购订单 | GET | /purchase-orders | 分页查询采购订单列表（按订单级别分页） |
| 分页查询采购订单明细 | GET | /purchase-orders/items | 分页查询采购订单明细列表（按明细行级别分页） |
| 更新采购订单 | PUT | /purchase-orders/{id} | 更新采购订单 |
| 根据ID查询采购订单 | GET | /purchase-orders/{id} | 根据ID查询采购订单详情 |

## 3. 接口详情

### 3.1 生成采购单号

#### 请求

```
GET /purchase-orders/generate-order-no
```

#### 权限要求

需要`purchase:order:create`权限

#### 响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": "CG2023060100001"
}
```

### 3.2 创建采购订单

#### 请求

```
POST /purchase-orders
```

#### 权限要求

需要`purchase:order:create`权限

#### 请求体

```json
{
  "purchaseDate": "2023-06-01",
  "purchaser": "张三",
  "paymentMethod": "月结",
  "purchaseType": "常规采购",
  "tradingUnit": "元",
  "supplierCode": "S001",
  "supplierName": "供应商A",
  "address": "北京市朝阳区",
  "phone": "010-12345678",
  "contactPerson": "李四",
  "mobile": "13800138000",
  "email": "<EMAIL>",
  "remarks": "紧急订单",
  "items": [
    {
      "paperQuality": "A级纸",
      "paperBoardCategory": "瓦楞纸板",
      "corrugationType": "三层",
      "paperWidth": 100.5,
      "paperLength": 200.5,
      "bindingMethod": true,
      "bindingSpecification": "标准规格",
      "materialChange": false,
      "specialQuotation": "无",
      "paperQuotation": 10.5,
      "discount": 0.95,
      "quantity": 1000,
      "price": 5.5,
      "amount": 5500,
      "creasingSize": "100x200",
      "creasingMethod": "标准",
      "remarks": "无",
      "foldingSpecification": "标准",
      "lengthMeters": 200.5,
      "areaSquareMeters": 20150.25,
      "volumeCubicMeters": 2015.025,
      "unitWeight": 0.5,
      "totalWeightKg": 500,
      "processingFee": 1000,
      "currency": "CNY",
      "deliveryDate": "2023-06-15"
    }
  ]
}
```

#### 响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "purchaseOrderNo": "CG2023060100001",
    "purchaseDate": "2023-06-01",
    "purchaser": "张三",
    "paymentMethod": "月结",
    "purchaseType": "常规采购",
    "tradingUnit": "元",
    "supplierCode": "S001",
    "supplierName": "供应商A",
    "address": "北京市朝阳区",
    "phone": "010-12345678",
    "contactPerson": "李四",
    "mobile": "13800138000",
    "email": "<EMAIL>",
    "remarks": "紧急订单",
    "createdBy": "admin",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-06-01T10:00:00",
    "items": [
      {
        "id": 1,
        "purchaseOrderId": 1,
        "paperQuality": "A级纸",
        "paperBoardCategory": "瓦楞纸板",
        "corrugationType": "三层",
        "paperWidth": 100.5,
        "paperLength": 200.5,
        "bindingMethod": true,
        "bindingSpecification": "标准规格",
        "materialChange": false,
        "specialQuotation": "无",
        "paperQuotation": 10.5,
        "discount": 0.95,
        "quantity": 1000,
        "price": 5.5,
        "amount": 5500,
        "creasingSize": "100x200",
        "creasingMethod": "标准",
        "remarks": "无",
        "foldingSpecification": "标准",
        "lengthMeters": 200.5,
        "areaSquareMeters": 20150.25,
        "volumeCubicMeters": 2015.025,
        "unitWeight": 0.5,
        "totalWeightKg": 500,
        "processingFee": 1000,
        "currency": "CNY",
        "deliveryDate": "2023-06-15",
        "createdBy": "admin",
        "createdTime": "2023-06-01T10:00:00",
        "updatedBy": "admin",
        "updatedTime": "2023-06-01T10:00:00"
      }
    ]
  }
}
```

### 3.3 分页查询采购订单

#### 请求

```
GET /purchase-orders?page=1&size=10&keyword=供应商A&purchaseDateStart=2023-06-01&purchaseDateEnd=2023-06-30&supplierCode=S001&purchaseType=常规采购&paymentMethod=月结
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| page | Integer | 否 | 页码，从1开始，默认为1 |
| size | Integer | 否 | 每页大小，默认为10 |
| keyword | String | 否 | 关键字（采购单号、供应商名称、采购员） |
| purchaseDateStart | LocalDate | 否 | 采购日期开始 |
| purchaseDateEnd | LocalDate | 否 | 采购日期结束 |
| supplierCode | String | 否 | 供应商编码 |
| purchaseType | String | 否 | 采购类型 |
| paymentMethod | String | 否 | 付款方式 |

#### 权限要求

需要`purchase:order:read`权限

#### 响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": 1,
        "purchaseOrderNo": "CG2023060100001",
        "purchaseDate": "2023-06-01",
        "purchaser": "张三",
        "paymentMethod": "月结",
        "purchaseType": "常规采购",
        "tradingUnit": "元",
        "supplierCode": "S001",
        "supplierName": "供应商A",
        "address": "北京市朝阳区",
        "phone": "010-12345678",
        "contactPerson": "李四",
        "mobile": "13800138000",
        "email": "<EMAIL>",
        "remarks": "紧急订单",
        "createdBy": "admin",
        "createdTime": "2023-06-01T10:00:00",
        "updatedBy": "admin",
        "updatedTime": "2023-06-01T10:00:00",
        "items": [
          {
            "id": 1,
            "purchaseOrderId": 1,
            "paperQuality": "A级纸",
            "paperBoardCategory": "瓦楞纸板",
            "corrugationType": "三层",
            "paperWidth": 100.5,
            "paperLength": 200.5,
            "bindingMethod": true,
            "bindingSpecification": "标准规格",
            "materialChange": false,
            "specialQuotation": "无",
            "paperQuotation": 10.5,
            "discount": 0.95,
            "quantity": 1000,
            "price": 5.5,
            "amount": 5500,
            "creasingSize": "100x200",
            "creasingMethod": "标准",
            "remarks": "无",
            "foldingSpecification": "标准",
            "lengthMeters": 200.5,
            "areaSquareMeters": 20150.25,
            "volumeCubicMeters": 2015.025,
            "unitWeight": 0.5,
            "totalWeightKg": 500,
            "processingFee": 1000,
            "currency": "CNY",
            "deliveryDate": "2023-06-15",
            "createdBy": "admin",
            "createdTime": "2023-06-01T10:00:00",
            "updatedBy": "admin",
            "updatedTime": "2023-06-01T10:00:00"
          }
        ]
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "page": 1,
    "size": 10,
    "first": true,
    "last": true,
    "empty": false
  }
}
```

### 3.4 分页查询采购订单明细

#### 请求

```
GET /purchase-orders/items?page=1&size=10&keyword=供应商A&purchaseDateStart=2023-06-01&purchaseDateEnd=2023-06-30&supplierCode=S001&purchaseType=常规采购&paymentMethod=月结&productionOrderNo=000001
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| page | Integer | 否 | 页码，从1开始，默认为1 |
| size | Integer | 否 | 每页大小，默认为10 |
| keyword | String | 否 | 关键字（采购单号、供应商名称、采购员） |
| purchaseDateStart | LocalDate | 否 | 采购日期开始 |
| purchaseDateEnd | LocalDate | 否 | 采购日期结束 |
| supplierCode | String | 否 | 供应商编码 |
| purchaseType | String | 否 | 采购类型 |
| paymentMethod | String | 否 | 付款方式 |
| productionOrderNo | String | 否 | 生产单号（模糊查询） |

#### 权限要求

需要`purchase:order:read`权限

#### 响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": 1,
        "purchaseOrderId": 1,
        "paperQuality": "A级纸",
        "paperBoardCategory": "瓦楞纸板",
        "corrugationType": "三层",
        "paperWidth": 100.5,
        "paperLength": 200.5,
        "bindingMethod": true,
        "bindingSpecification": "标准规格",
        "materialChange": false,
        "specialQuotation": "无",
        "paperQuotation": 10.5,
        "discount": 0.95,
        "quantity": 1000,
        "price": 5.5,
        "amount": 5500,
        "creasingSize": "100x200",
        "creasingMethod": "标准",
        "remarks": "无",
        "foldingSpecification": "标准",
        "lengthMeters": 200.5,
        "areaSquareMeters": 20150.25,
        "volumeCubicMeters": 2015.025,
        "unitWeight": 0.5,
        "totalWeightKg": 500,
        "processingFee": 1000,
        "currency": "CNY",
        "deliveryDate": "2023-06-15",
        "productionOrderNo": "000001",
        "purchaseOrderNo": "CG2023060100001",
        "purchaseDate": "2023-06-01",
        "purchaser": "张三",
        "supplierCode": "S001",
        "supplierName": "供应商A",
        "paymentMethod": "月结",
        "purchaseType": "常规采购",
        "contactPerson": "李四",
        "phone": "010-12345678",
        "address": "北京市朝阳区",
        "customerName": "客户A",
        "salesPerson": "王五",
        "customerOrderNo": "C2023060100001",
        "customerProductCode": "P001",
        "productName": "纸箱",
        "processRequirements": "开槽→打角→打钉",
        "boxType": "0201",
        "productSpecification": "100 x 200 x 50 mm",
        "productionSpecification": "105 x 205 x 55 mm",
        "dieOpenCount": 1,
        "boardCount": 1000,
        "salesOrderDeliveryDate": "2023-06-10",
        "salesOrderCreatedTime": "2023-05-25T09:00:00",
        "salesOrderProductionRemark": "注意质量",
        "salesOrderRemark": "客户要求",
        "createdBy": "admin",
        "createdTime": "2023-06-01T10:00:00",
        "updatedBy": "admin",
        "updatedTime": "2023-06-01T10:00:00"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "page": 1,
    "size": 10,
    "first": true,
    "last": true,
    "empty": false
  }
}
```

#### 接口说明

- **分页单位**：按明细行数量分页，而不是按采购订单数量分页
- **数据结构**：返回扁平化的明细数据，每个明细行包含完整的采购订单基本信息和关联的销售订单信息
- **适用场景**：适用于前端表格显示，支持行合并展示一个采购单号对应多个明细行的情况
- **生产单号查询**：支持通过生产单号模糊查询，会查找关联销售订单明细的生产单号
- **数据丰富**：自动关联销售订单数据，包括客户信息、产品信息、工艺要求等

### 3.5 更新采购订单

#### 请求

```
PUT /purchase-orders/{id}
```

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| id | Long | 是 | 采购订单ID |

#### 权限要求

需要`purchase:order:update`权限

#### 请求体

```json
{
  "purchaseDate": "2023-06-01",
  "purchaser": "张三",
  "paymentMethod": "月结",
  "purchaseType": "常规采购",
  "tradingUnit": "元",
  "supplierCode": "S001",
  "supplierName": "供应商A",
  "address": "北京市朝阳区",
  "phone": "010-12345678",
  "contactPerson": "李四",
  "mobile": "13800138000",
  "email": "<EMAIL>",
  "remarks": "紧急订单已更新",
  "items": [
    {
      "paperQuality": "A级纸",
      "paperBoardCategory": "瓦楞纸板",
      "corrugationType": "三层",
      "paperWidth": 100.5,
      "paperLength": 200.5,
      "bindingMethod": true,
      "bindingSpecification": "标准规格",
      "materialChange": false,
      "specialQuotation": "无",
      "paperQuotation": 10.5,
      "discount": 0.95,
      "quantity": 1000,
      "price": 5.5,
      "amount": 5500,
      "creasingSize": "100x200",
      "creasingMethod": "标准",
      "remarks": "无",
      "foldingSpecification": "标准",
      "lengthMeters": 200.5,
      "areaSquareMeters": 20150.25,
      "volumeCubicMeters": 2015.025,
      "unitWeight": 0.5,
      "totalWeightKg": 500,
      "processingFee": 1000,
      "currency": "CNY",
      "deliveryDate": "2023-06-15"
    }
  ]
}
```

#### 响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "purchaseOrderNo": "CG2023060100001",
    "purchaseDate": "2023-06-01",
    "purchaser": "张三",
    "paymentMethod": "月结",
    "purchaseType": "常规采购",
    "tradingUnit": "元",
    "supplierCode": "S001",
    "supplierName": "供应商A",
    "address": "北京市朝阳区",
    "phone": "010-12345678",
    "contactPerson": "李四",
    "mobile": "13800138000",
    "email": "<EMAIL>",
    "remarks": "紧急订单已更新",
    "createdBy": "admin",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-06-01T11:30:00",
    "items": [
      {
        "id": 2,
        "purchaseOrderId": 1,
        "paperQuality": "A级纸",
        "paperBoardCategory": "瓦楞纸板",
        "corrugationType": "三层",
        "paperWidth": 100.5,
        "paperLength": 200.5,
        "bindingMethod": true,
        "bindingSpecification": "标准规格",
        "materialChange": false,
        "specialQuotation": "无",
        "paperQuotation": 10.5,
        "discount": 0.95,
        "quantity": 1000,
        "price": 5.5,
        "amount": 5500,
        "creasingSize": "100x200",
        "creasingMethod": "标准",
        "remarks": "无",
        "foldingSpecification": "标准",
        "lengthMeters": 200.5,
        "areaSquareMeters": 20150.25,
        "volumeCubicMeters": 2015.025,
        "unitWeight": 0.5,
        "totalWeightKg": 500,
        "processingFee": 1000,
        "currency": "CNY",
        "deliveryDate": "2023-06-15",
        "createdBy": "admin",
        "createdTime": "2023-06-01T11:30:00",
        "updatedBy": "admin",
        "updatedTime": "2023-06-01T11:30:00"
      }
    ]
  }
}
```

### 3.6 根据ID查询采购订单

#### 请求

```
GET /purchase-orders/{id}
```

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| id | Long | 是 | 采购订单ID |

#### 权限要求

需要`purchase:order:read`权限

#### 响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "purchaseOrderNo": "CG2023060100001",
    "purchaseDate": "2023-06-01",
    "purchaser": "张三",
    "paymentMethod": "月结",
    "purchaseType": "常规采购",
    "tradingUnit": "元",
    "supplierCode": "S001",
    "supplierName": "供应商A",
    "address": "北京市朝阳区",
    "phone": "010-12345678",
    "contactPerson": "李四",
    "mobile": "13800138000",
    "email": "<EMAIL>",
    "remarks": "紧急订单",
    "createdBy": "admin",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-06-01T10:00:00",
    "items": [
      {
        "id": 1,
        "purchaseOrderId": 1,
        "paperQuality": "A级纸",
        "paperBoardCategory": "瓦楞纸板",
        "corrugationType": "三层",
        "paperWidth": 100.5,
        "paperLength": 200.5,
        "bindingMethod": true,
        "bindingSpecification": "标准规格",
        "materialChange": false,
        "specialQuotation": "无",
        "paperQuotation": 10.5,
        "discount": 0.95,
        "quantity": 1000,
        "price": 5.5,
        "amount": 5500,
        "creasingSize": "100x200",
        "creasingMethod": "标准",
        "remarks": "无",
        "foldingSpecification": "标准",
        "lengthMeters": 200.5,
        "areaSquareMeters": 20150.25,
        "volumeCubicMeters": 2015.025,
        "unitWeight": 0.5,
        "totalWeightKg": 500,
        "processingFee": 1000,
        "currency": "CNY",
        "deliveryDate": "2023-06-15",
        "createdBy": "admin",
        "createdTime": "2023-06-01T10:00:00",
        "updatedBy": "admin",
        "updatedTime": "2023-06-01T10:00:00"
      }
    ]
  }
}
```

## 4. 数据结构

### 4.1 采购订单创建请求

| 字段名 | 类型 | 必填 | 描述 | 中文名 |
| --- | --- | --- | --- | --- |
| purchaseDate | LocalDate | 是 | 采购日期 | 采购日期 |
| purchaser | String | 否 | 采购员 | 采购员 |
| paymentMethod | String | 否 | 付款方式 | 付款方式 |
| purchaseType | String | 否 | 采购类型 | 采购类型 |
| tradingUnit | String | 否 | 交易单位 | 交易单位 |
| supplierCode | String | 是 | 供应商编码 | 供应商编码 |
| supplierName | String | 是 | 供应商名称 | 供应商名称 |
| address | String | 否 | 地址 | 地址 |
| phone | String | 否 | 电话 | 电话 |
| contactPerson | String | 否 | 联系人 | 联系人 |
| mobile | String | 否 | 手机 | 手机 |
| email | String | 否 | 邮箱 | 邮箱 |
| remarks | String | 否 | 备注 | 备注 |
| items | List<CreatePurchaseOrderItemRequest> | 是 | 订单明细 | 订单明细 |

### 4.2 采购订单更新请求

| 字段名 | 类型 | 必填 | 描述 | 中文名 |
| --- | --- | --- | --- | --- |
| purchaseDate | LocalDate | 是 | 采购日期 | 采购日期 |
| purchaser | String | 否 | 采购员 | 采购员 |
| paymentMethod | String | 否 | 付款方式 | 付款方式 |
| purchaseType | String | 否 | 采购类型 | 采购类型 |
| tradingUnit | String | 否 | 交易单位 | 交易单位 |
| supplierCode | String | 是 | 供应商编码 | 供应商编码 |
| supplierName | String | 是 | 供应商名称 | 供应商名称 |
| address | String | 否 | 地址 | 地址 |
| phone | String | 否 | 电话 | 电话 |
| contactPerson | String | 否 | 联系人 | 联系人 |
| mobile | String | 否 | 手机 | 手机 |
| email | String | 否 | 邮箱 | 邮箱 |
| remarks | String | 否 | 备注 | 备注 |
| items | List<CreatePurchaseOrderItemRequest> | 是 | 订单明细 | 订单明细 |

### 4.3 采购订单明细创建请求

| 字段名 | 类型 | 必填 | 描述 | 中文名 |
| --- | --- | --- | --- | --- |
| paperQuality | String | 否 | 纸质 | 纸质 |
| paperBoardCategory | String | 否 | 纸板类别 | 纸板类别 |
| corrugationType | String | 否 | 楞别 | 楞别 |
| paperWidth | String | 否 | 纸度 | 纸度 |
| paperLength | String | 否 | 纸长 | 纸长 |
| bindingMethod | Boolean | 否 | 合订 | 合订 |
| bindingSpecification | String | 否 | 合订规格 | 合订规格 |
| materialChange | Boolean | 否 | 用料异动 | 用料异动 |
| specialQuotation | String | 否 | 报价特价 | 报价特价 |
| paperQuotation | BigDecimal | 否 | 纸质报价 | 纸质报价 |
| discount | BigDecimal | 否 | 折扣 | 折扣 |
| quantity | Integer | 是 | 数量 | 数量 |
| price | BigDecimal | 是 | 价格 | 价格 |
| amount | BigDecimal | 是 | 金额 | 金额 |
| creasingSize | String | 否 | 压线尺寸(纸度) | 压线尺寸(纸度) |
| creasingMethod | String | 否 | 压线方式 | 压线方式 |
| remarks | String | 否 | 备注 | 备注 |
| foldingSpecification | String | 否 | 折度规格 | 折度规格 |
| lengthMeters | BigDecimal | 否 | 长度(米) | 长度(米) |
| areaSquareMeters | BigDecimal | 否 | 面积(平米) | 面积(平米) |
| volumeCubicMeters | BigDecimal | 否 | 体积(立方米) | 体积(立方米) |
| unitWeight | BigDecimal | 否 | 单重 | 单重 |
| totalWeightKg | BigDecimal | 否 | 总重(KG) | 总重(KG) |
| processingFee | BigDecimal | 否 | 加工费 | 加工费 |
| currency | String | 否 | 币种 | 币种 |
| deliveryDate | LocalDate | 否 | 交期 | 交期 |

### 4.4 采购订单响应

| 字段名 | 类型 | 描述 | 中文名 |
| --- | --- | --- | --- |
| id | Long | 采购订单ID | 采购订单ID |
| purchaseOrderNo | String | 采购单号 | 采购单号 |
| purchaseDate | LocalDate | 采购日期 | 采购日期 |
| purchaser | String | 采购员 | 采购员 |
| paymentMethod | String | 付款方式 | 付款方式 |
| purchaseType | String | 采购类型 | 采购类型 |
| tradingUnit | String | 交易单位 | 交易单位 |
| supplierCode | String | 供应商编码 | 供应商编码 |
| supplierName | String | 供应商名称 | 供应商名称 |
| address | String | 地址 | 地址 |
| phone | String | 电话 | 电话 |
| contactPerson | String | 联系人 | 联系人 |
| mobile | String | 手机 | 手机 |
| email | String | 邮箱 | 邮箱 |
| remarks | String | 备注 | 备注 |
| createdBy | String | 创建人 | 创建人 |
| createdTime | LocalDateTime | 创建时间 | 创建时间 |
| updatedBy | String | 更新人 | 更新人 |
| updatedTime | LocalDateTime | 更新时间 | 更新时间 |
| items | List<PurchaseOrderItemDTO> | 订单明细 | 订单明细 |

### 4.5 采购订单明细响应

| 字段名 | 类型 | 描述 | 中文名 |
| --- | --- | --- | --- |
| id | Long | 采购订单明细ID | 采购订单明细ID |
| purchaseOrderId | Long | 采购订单ID | 采购订单ID |
| paperQuality | String | 纸质 | 纸质 |
| paperBoardCategory | String | 纸板类别（根据纸质自动获取） | 纸板类别 |
| corrugationType | String | 楞别 | 楞别 |
| paperWidth | BigDecimal | 纸度 | 纸度 |
| paperLength | BigDecimal | 纸长 | 纸长 |
| bindingMethod | Boolean | 合订 | 合订 |
| bindingSpecification | String | 合订规格 | 合订规格 |
| materialChange | Boolean | 用料异动 | 用料异动 |
| specialQuotation | String | 报价特价 | 报价特价 |
| paperQuotation | BigDecimal | 纸质报价 | 纸质报价 |
| discount | BigDecimal | 折扣 | 折扣 |
| quantity | Integer | 数量 | 数量 |
| price | BigDecimal | 价格 | 价格 |
| amount | BigDecimal | 金额 | 金额 |
| creasingSize | String | 压线尺寸(纸度) | 压线尺寸(纸度) |
| creasingMethod | String | 压线方式 | 压线方式 |
| remarks | String | 备注 | 备注 |
| foldingSpecification | String | 折度规格 | 折度规格 |
| lengthMeters | BigDecimal | 长度(米) | 长度(米) |
| areaSquareMeters | BigDecimal | 面积(平米) | 面积(平米) |
| volumeCubicMeters | BigDecimal | 体积(立方米) | 体积(立方米) |
| unitWeight | BigDecimal | 单重 | 单重 |
| totalWeightKg | BigDecimal | 总重(KG) | 总重(KG) |
| processingFee | BigDecimal | 加工费 | 加工费 |
| currency | String | 币种 | 币种 |
| deliveryDate | LocalDate | 交期 | 交期 |
| salesOrderDeliveryDate | LocalDate | 销售订单交期 | 销售订单交期 |
| salesOrderCreatedTime | LocalDateTime | 销售订单创建时间 | 销售订单创建时间 |
| salesOrderProductionRemark | String | 销售订单明细生产备注 | 销售订单明细生产备注 |
| salesOrderRemark | String | 销售订单明细备注 | 销售订单明细备注 |
| createdBy | String | 创建人 | 创建人 |
| createdTime | LocalDateTime | 创建时间 | 创建时间 |
| updatedBy | String | 更新人 | 更新人 |
| updatedTime | LocalDateTime | 更新时间 | 更新时间 |
| customerName | String | 客户名称 | 客户名称 |
| salesPerson | String | 销售员 | 销售员 |
| customerOrderNo | String | 客户订单号 | 客户订单号 |
| customerProductCode | String | 客方货号 | 客方货号 |
| productName | String | 品名 | 品名 |
| processRequirements | String | 工艺要求 | 工艺要求 |
| boxType | String | 盒式 | 盒式 |
| productSpecification | String | 产品规格 | 产品规格 |
| productionSpecification | String | 生产规格 | 生产规格 |
| dieOpenCount | Integer | 模开数 | 模开数 |
| boardCount | Integer | 纸板数 | 纸板数 |

### 4.6 采购订单明细查询响应

采购订单明细查询接口 (`/purchase-orders/items`) 返回的数据结构，包含采购订单明细的所有字段以及关联的采购订单基本信息：

| 字段名 | 类型 | 描述 | 中文名 |
| --- | --- | --- | --- |
| **明细基本信息** | | | |
| id | Long | 采购订单明细ID | 采购订单明细ID |
| purchaseOrderId | Long | 采购订单ID | 采购订单ID |
| **采购订单基本信息（冗余字段）** | | | |
| purchaseOrderNo | String | 采购单号 | 采购单号 |
| purchaseDate | LocalDate | 采购日期 | 采购日期 |
| purchaser | String | 采购员 | 采购员 |
| supplierCode | String | 供应商编码 | 供应商编码 |
| supplierName | String | 供应商名称 | 供应商名称 |
| paymentMethod | String | 付款方式 | 付款方式 |
| purchaseType | String | 采购类型 | 采购类型 |
| contactPerson | String | 联系人 | 联系人 |
| phone | String | 电话 | 电话 |
| address | String | 地址 | 地址 |
| **明细产品信息** | | | |
| paperQuality | String | 纸质 | 纸质 |
| paperBoardCategory | String | 纸板类别 | 纸板类别 |
| corrugationType | String | 楞别 | 楞别 |
| paperWidth | BigDecimal | 纸度 | 纸度 |
| paperLength | BigDecimal | 纸长 | 纸长 |
| bindingMethod | Boolean | 合订 | 合订 |
| bindingSpecification | String | 合订规格 | 合订规格 |
| materialChange | Boolean | 用料异动 | 用料异动 |
| specialQuotation | String | 报价特价 | 报价特价 |
| paperQuotation | BigDecimal | 纸质报价 | 纸质报价 |
| discount | BigDecimal | 折扣 | 折扣 |
| quantity | Integer | 数量 | 数量 |
| price | BigDecimal | 价格 | 价格 |
| amount | BigDecimal | 金额 | 金额 |
| creasingSize | String | 压线尺寸(纸度) | 压线尺寸(纸度) |
| creasingMethod | String | 压线方式 | 压线方式 |
| remarks | String | 备注 | 备注 |
| foldingSpecification | String | 折度规格 | 折度规格 |
| lengthMeters | BigDecimal | 长度(米) | 长度(米) |
| areaSquareMeters | BigDecimal | 面积(平米) | 面积(平米) |
| volumeCubicMeters | BigDecimal | 体积(立方米) | 体积(立方米) |
| unitWeight | BigDecimal | 单重 | 单重 |
| totalWeightKg | BigDecimal | 总重(KG) | 总重(KG) |
| processingFee | BigDecimal | 加工费 | 加工费 |
| currency | String | 币种 | 币种 |
| deliveryDate | LocalDate | 交期 | 交期 |
| **关联销售订单信息** | | | |
| productionOrderNo | String | 生产单号 | 生产单号 |
| customerName | String | 客户名称 | 客户名称 |
| salesPerson | String | 销售员 | 销售员 |
| customerOrderNo | String | 客户订单号 | 客户订单号 |
| customerProductCode | String | 客方货号 | 客方货号 |
| productName | String | 品名 | 品名 |
| processRequirements | String | 工艺要求 | 工艺要求 |
| boxType | String | 盒式 | 盒式 |
| productSpecification | String | 产品规格 | 产品规格 |
| productionSpecification | String | 生产规格 | 生产规格 |
| dieOpenCount | Integer | 模开数 | 模开数 |
| boardCount | Integer | 纸板数 | 纸板数 |
| salesOrderDeliveryDate | LocalDate | 销售订单交期 | 销售订单交期 |
| salesOrderCreatedTime | LocalDateTime | 销售订单创建时间 | 销售订单创建时间 |
| salesOrderProductionRemark | String | 销售订单明细生产备注 | 销售订单明细生产备注 |
| salesOrderRemark | String | 销售订单明细备注 | 销售订单明细备注 |
| **审计信息** | | | |
| createdBy | String | 创建人 | 创建人 |
| createdTime | LocalDateTime | 创建时间 | 创建时间 |
| updatedBy | String | 更新人 | 更新人 |
| updatedTime | LocalDateTime | 更新时间 | 更新时间 |

#### 接口特点

1. **扁平化数据结构**：每个明细行包含完整的采购订单基本信息，便于前端表格显示
2. **按明细行分页**：分页单位是明细行数量，而不是采购订单数量
3. **数据完整性**：自动关联销售订单数据，提供完整的业务信息
4. **支持行合并**：前端可以根据 `purchaseOrderNo` 字段进行行合并显示

### 4.7 分页响应

| 字段名 | 类型 | 描述 | 中文名 |
| --- | --- | --- | --- |
| content | List<T> | 数据列表 | 数据列表 |
| totalElements | long | 总记录数 | 总记录数 |
| totalPages | int | 总页数 | 总页数 |
| page | int | 当前页码 | 当前页码 |
| size | int | 每页大小 | 每页大小 |
| first | boolean | 是否为第一页 | 是否为第一页 |
| last | boolean | 是否为最后一页 | 是否为最后一页 |
| empty | boolean | 是否为空 | 是否为空 |

### 4.8 API统一响应

| 字段名 | 类型 | 描述 | 中文名 |
| --- | --- | --- | --- |
| success | boolean | 是否成功 | 是否成功 |
| code | String | 错误码 | 错误码 |
| message | String | 错误消息 | 错误消息 |
| data | T | 数据 | 数据 |

### 4.9 采购订单明细字段来源说明

采购订单明细响应中的部分字段来自关联的销售订单数据，具体来源如下：

| 字段名 | 来源表 | 来源字段 | 说明 |
| --- | --- | --- | --- |
| customerName | sales_order | customer_name | 客户名称 |
| salesPerson | sales_order | sales_person | 销售员 |
| customerOrderNo | sales_order_item | customer_order_no | 客户订单号 |
| customerProductCode | sales_order_item | customer_product_code | 客方货号 |
| productName | sales_order_item | product_name | 品名 |
| processRequirements | sales_order_item | process_requirements | 工艺要求 |
| boxType | sales_order_item | box_type | 盒式 |
| productSpecification | sales_order_item | length + width + height + size_unit | 产品规格，格式为"长 x 宽 x 高 单位" |
| productionSpecification | sales_order_item | production_length + production_width + production_height + size_unit | 生产规格，格式为"长 x 宽 x 高 单位" |
| dieOpenCount | sales_order_material | die_open_count | 模开数，取关联销售订单明细的第一个材料记录 |
| boardCount | sales_order_material | board_count | 纸板数，取关联销售订单明细的第一个材料记录，如果没有材料记录，则取销售订单明细的数量 |
| paperBoardCategory | paper_material, paper_type | paper_type_name | 纸板类别，根据采购订单明细的纸质(paperQuality)字段，通过paper_material表关联到paper_type表获取 |
| salesOrderDeliveryDate | sales_order_item | delivery_date | 销售订单交期，从关联的销售订单明细获取 |
| salesOrderCreatedTime | sales_order_item | created_time | 销售订单创建时间，从关联的销售订单明细获取 |
| salesOrderProductionRemark | sales_order_item | production_remark | 销售订单明细生产备注，从关联的销售订单明细获取 |
| salesOrderRemark | sales_order_item | remark | 销售订单明细备注，从关联的销售订单明细获取 |

这些字段通过采购订单明细的`source_sales_order_item_id`字段关联到销售订单明细，再通过销售订单明细关联到销售订单和销售订单材料。
