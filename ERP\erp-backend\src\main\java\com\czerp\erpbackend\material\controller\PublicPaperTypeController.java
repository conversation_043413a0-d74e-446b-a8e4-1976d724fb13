package com.czerp.erpbackend.material.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.material.dto.PaperTypeDTO;
import com.czerp.erpbackend.material.service.PaperTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 纸质类别公共控制器
 * 提供给其他模块使用的公共接口，不需要权限验证
 */
@RestController
@RequestMapping("/public/material/paper-types")
@Tag(name = "纸质类别公共接口", description = "纸质类别公共接口，不需要权限验证")
@RequiredArgsConstructor
@Slf4j
public class PublicPaperTypeController {

    private final PaperTypeService paperTypeService;

    /**
     * 查询所有纸质类别
     * @return 纸质类别列表
     */
    @GetMapping
    @Operation(summary = "查询所有纸质类别", description = "查询所有纸质类别，供其他模块使用")
    public ResponseEntity<ApiResponse<List<PaperTypeDTO>>> findAllPaperTypes() {
        log.debug("Finding all paper types (public)");
        List<PaperTypeDTO> response = paperTypeService.findAllPaperTypes();
        return ResponseEntity.ok(ApiResponse.success(response));
    }
}
