package com.czerp.erpbackend.system.repository;

import com.czerp.erpbackend.system.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户存储库
 */
@Repository
public interface UserRepository extends JpaRepository<User, String> {
    
    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     * @param email 邮箱
     * @return 用户
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 判断用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 判断邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 搜索用户列表
     * @param keyword 关键字
     * @param status 状态
     * @param departmentId 部门ID
     * @param pageable 分页参数
     * @return 用户分页列表
     */
    @Query("SELECT u FROM User u WHERE u.isDeleted = false " +
            "AND (:keyword IS NULL OR u.username LIKE %:keyword% OR u.nickname LIKE %:keyword% OR u.email LIKE %:keyword%) " +
            "AND (:status IS NULL OR u.status = :status) " +
            "AND (:departmentId IS NULL OR u.departmentId = :departmentId)")
    Page<User> search(
            @Param("keyword") String keyword,
            @Param("status") String status,
            @Param("departmentId") String departmentId,
            Pageable pageable);
    
    /**
     * 根据部门ID查询用户列表
     * @param departmentId 部门ID
     * @param pageable 分页参数
     * @return 用户分页列表
     */
    Page<User> findByDepartmentIdAndIsDeletedFalse(String departmentId, Pageable pageable);
    
    /**
     * 根据ID列表查询用户列表
     * @param ids ID列表
     * @param pageable 分页参数
     * @return 用户分页列表
     */
    Page<User> findByIdInAndIsDeletedFalse(List<String> ids, Pageable pageable);
} 