# 销售订单工序管理服务API文档

## 1. 概述

销售订单工序管理服务提供了对销售订单工序的管理功能，包括查询、添加、更新和删除工序等操作。工序与销售订单直接关联，一个销售订单可以有多个工序。

## 2. 数据模型

### 2.1 工序实体 (SalesOrderProcess)

| 字段名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| id | String | 工序ID | 是 |
| orderId | String | 订单ID | 是 |
| sequence | Integer | 工序顺序 | 是 |
| processName | String | 工序名称 | 是 |
| processRequirements | String | 工艺要求 | 否 |
| plateNumber | String | 印版编号 | 否 |
| inkNumber | String | 水墨编号 | 否 |
| inkName | String | 水墨名称 | 否 |
| colorCount | Integer | 颜色数 | 否 |
| isDeleted | Boolean | 是否删除 | 是 |
| version | Integer | 版本号 | 是 |
| createdBy | String | 创建人 | 否 |
| createdByName | String | 创建人姓名 | 否 |
| createdTime | DateTime | 创建时间 | 否 |
| updatedBy | String | 更新人 | 否 |
| updatedByName | String | 更新人姓名 | 否 |
| updatedTime | DateTime | 更新时间 | 否 |

### 2.2 工序DTO (SalesOrderProcessDTO)

| 字段名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| id | String | 工序ID | 否(新增时) |
| orderId | String | 订单ID | 是 |
| sequence | Integer | 工序顺序 | 是 |
| processName | String | 工序名称 | 是 |
| processRequirements | String | 工艺要求 | 否 |
| plateNumber | String | 印版编号 | 否 |
| inkNumber | String | 水墨编号 | 否 |
| inkName | String | 水墨名称 | 否 |
| colorCount | Integer | 颜色数 | 否 |
| createdBy | String | 创建人 | 否 |
| createdByName | String | 创建人姓名 | 否 |
| createdTime | DateTime | 创建时间 | 否 |
| updatedBy | String | 更新人 | 否 |
| updatedByName | String | 更新人姓名 | 否 |
| updatedTime | DateTime | 更新时间 | 否 |

## 3. API接口

### 3.1 查询订单工序列表

#### 请求

```
GET /sales/orders/{orderId}/processes
```

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "process1",
      "orderId": "order1",
      "sequence": 1,
      "processName": "分纸",
      "processRequirements": "按规格分切",
      "plateNumber": null,
      "inkNumber": null,
      "inkName": null,
      "colorCount": null,
      "createdBy": "admin",
      "createdByName": "管理员",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": null,
      "updatedByName": null,
      "updatedTime": null
    },
    {
      "id": "process2",
      "orderId": "order1",
      "sequence": 2,
      "processName": "印刷",
      "processRequirements": "四色印刷，对色精准",
      "plateNumber": "P001",
      "inkNumber": "I001",
      "inkName": "蓝色",
      "colorCount": 4,
      "createdBy": "admin",
      "createdByName": "管理员",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": null,
      "updatedByName": null,
      "updatedTime": null
    }
  ]
}
```

### 3.2 添加订单工序

#### 请求

```
POST /sales/orders/{orderId}/processes
```

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |

#### 请求体

```json
{
  "sequence": 1,
  "processName": "分纸",
  "processRequirements": "按规格分切",
  "plateNumber": null,
  "inkNumber": null,
  "inkName": null,
  "colorCount": null
}
```

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "process1",
    "orderId": "order1",
    "sequence": 1,
    "processName": "分纸",
    "processRequirements": "按规格分切",
    "plateNumber": null,
    "inkNumber": null,
    "inkName": null,
    "colorCount": null,
    "createdBy": "admin",
    "createdByName": "管理员",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": null,
    "updatedByName": null,
    "updatedTime": null
  }
}
```

### 3.3 批量添加订单工序

#### 请求

```
POST /sales/orders/{orderId}/processes/batch
```

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |

#### 请求体

```json
[
  {
    "sequence": 1,
    "processName": "分纸",
    "processRequirements": "按规格分切",
    "plateNumber": null,
    "inkNumber": null,
    "inkName": null,
    "colorCount": null
  },
  {
    "sequence": 2,
    "processName": "印刷",
    "processRequirements": "四色印刷，对色精准",
    "plateNumber": "P001",
    "inkNumber": "I001",
    "inkName": "蓝色",
    "colorCount": 4
  }
]
```

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "process1",
      "orderId": "order1",
      "sequence": 1,
      "processName": "分纸",
      "processRequirements": "按规格分切",
      "plateNumber": null,
      "inkNumber": null,
      "inkName": null,
      "colorCount": null,
      "createdBy": "admin",
      "createdByName": "管理员",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": null,
      "updatedByName": null,
      "updatedTime": null
    },
    {
      "id": "process2",
      "orderId": "order1",
      "sequence": 2,
      "processName": "印刷",
      "processRequirements": "四色印刷，对色精准",
      "plateNumber": "P001",
      "inkNumber": "I001",
      "inkName": "蓝色",
      "colorCount": 4,
      "createdBy": "admin",
      "createdByName": "管理员",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": null,
      "updatedByName": null,
      "updatedTime": null
    }
  ]
}
```

### 3.4 更新订单工序

#### 请求

```
PUT /sales/orders/{orderId}/processes/{id}
```

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |
| id | String | 工序ID | 是 |

#### 请求体

```json
{
  "sequence": 1,
  "processName": "分纸",
  "processRequirements": "按规格精确分切",
  "plateNumber": null,
  "inkNumber": null,
  "inkName": null,
  "colorCount": null
}
```

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "process1",
    "orderId": "order1",
    "sequence": 1,
    "processName": "分纸",
    "processRequirements": "按规格精确分切",
    "plateNumber": null,
    "inkNumber": null,
    "inkName": null,
    "colorCount": null,
    "createdBy": "admin",
    "createdByName": "管理员",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": "admin",
    "updatedByName": "管理员",
    "updatedTime": "2023-06-01T11:00:00"
  }
}
```

### 3.5 删除订单工序

#### 请求

```
DELETE /sales/orders/{orderId}/processes/{id}
```

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |
| id | String | 工序ID | 是 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.6 删除订单所有工序

#### 请求

```
DELETE /sales/orders/{orderId}/processes
```

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

## 4. 与销售订单集成

销售订单工序管理功能已与销售订单管理功能集成，在创建和更新销售订单时，可以同时提交工序信息。

### 4.1 创建销售订单时添加工序

在创建销售订单的请求体中，可以包含工序列表：

```json
{
  "orderNo": "SO2023060100001",
  "orderDate": "2023-06-01",
  "customerCode": "C001",
  "salesPerson": "张三",
  "items": [
    {
      "customerProductCode": "CP001",
      "productName": "产品1",
      "quantity": 100,
      "price": 10.5
    }
  ],
  "processes": [
    {
      "sequence": 1,
      "processName": "分纸",
      "processRequirements": "按规格分切"
    },
    {
      "sequence": 2,
      "processName": "印刷",
      "processRequirements": "四色印刷，对色精准",
      "plateNumber": "P001",
      "inkNumber": "I001",
      "inkName": "蓝色",
      "colorCount": 4
    }
  ]
}
```

### 4.2 更新销售订单时更新工序

在更新销售订单的请求体中，可以包含工序列表：

```json
{
  "orderNo": "SO2023060100001",
  "orderDate": "2023-06-01",
  "customerCode": "C001",
  "salesPerson": "张三",
  "items": [
    {
      "id": "item1",
      "customerProductCode": "CP001",
      "productName": "产品1",
      "quantity": 100,
      "price": 10.5
    }
  ],
  "processes": [
    {
      "sequence": 1,
      "processName": "分纸",
      "processRequirements": "按规格精确分切"
    },
    {
      "sequence": 2,
      "processName": "印刷",
      "processRequirements": "四色印刷，对色精准",
      "plateNumber": "P001",
      "inkNumber": "I001",
      "inkName": "蓝色",
      "colorCount": 4
    },
    {
      "sequence": 3,
      "processName": "打订",
      "processRequirements": "四角打钉"
    }
  ]
}
```

### 4.3 查询销售订单时返回工序

在查询销售订单详情的响应中，会包含工序列表：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "order1",
    "orderNo": "SO2023060100001",
    "orderDate": "2023-06-01",
    "customerCode": "C001",
    "customerName": "客户1",
    "salesPerson": "张三",
    "items": [
      {
        "id": "item1",
        "customerProductCode": "CP001",
        "productName": "产品1",
        "quantity": 100,
        "price": 10.5
      }
    ],
    "processes": [
      {
        "id": "process1",
        "orderId": "order1",
        "sequence": 1,
        "processName": "分纸",
        "processRequirements": "按规格精确分切"
      },
      {
        "id": "process2",
        "orderId": "order1",
        "sequence": 2,
        "processName": "印刷",
        "processRequirements": "四色印刷，对色精准",
        "plateNumber": "P001",
        "inkNumber": "I001",
        "inkName": "蓝色",
        "colorCount": 4
      },
      {
        "id": "process3",
        "orderId": "order1",
        "sequence": 3,
        "processName": "打订",
        "processRequirements": "四角打钉"
      }
    ]
  }
}
```

## 5. 权限要求

访问销售订单工序管理API需要以下权限：

- 查询工序列表：`sales:order:read`
- 添加工序：`sales:order:update`
- 批量添加工序：`sales:order:update`
- 更新工序：`sales:order:update`
- 删除工序：`sales:order:update`
- 删除所有工序：`sales:order:update`

## 6. 错误码

| 错误码 | 描述 |
| --- | --- |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 7. 注意事项

1. 工序顺序(sequence)字段用于指定工序的执行顺序，应确保其唯一性和连续性。
2. 在更新销售订单时，会先删除所有现有工序，然后重新添加请求中的工序，因此请确保请求中包含所有需要保留的工序。
3. 删除销售订单时，会自动删除该订单的所有工序。
4. 工序名称(processName)字段为必填项，其他字段根据实际需要填写。
5. 印刷工序通常需要填写印版编号、水墨编号、水墨名称和颜色数等信息。
