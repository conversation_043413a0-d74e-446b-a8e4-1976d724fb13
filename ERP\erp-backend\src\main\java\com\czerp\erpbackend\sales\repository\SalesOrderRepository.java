package com.czerp.erpbackend.sales.repository;

import com.czerp.erpbackend.sales.entity.SalesOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;

/**
 * 销售订单Repository
 */
@Repository
public interface SalesOrderRepository extends JpaRepository<SalesOrder, String>, JpaSpecificationExecutor<SalesOrder> {

    /**
     * 根据订单号查询订单
     * @param orderNo 订单号
     * @return 订单
     */
    Optional<SalesOrder> findByOrderNo(String orderNo);

    // 生产单号在 SalesOrderItem 中，不在 SalesOrder 中，所以移除这个方法

    /**
     * 根据客户编码查询订单
     * @param customerCode 客户编码
     * @param pageable 分页参数
     * @return 订单分页列表
     */
    Page<SalesOrder> findByCustomerCode(String customerCode, Pageable pageable);

    /**
     * 根据客户编码和订单日期范围查询订单
     * @param customerCode 客户编码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageable 分页参数
     * @return 订单分页列表
     */
    Page<SalesOrder> findByCustomerCodeAndOrderDateBetween(String customerCode, LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * 根据销售员查询订单
     * @param salesPerson 销售员
     * @param pageable 分页参数
     * @return 订单分页列表
     */
    Page<SalesOrder> findBySalesPerson(String salesPerson, Pageable pageable);

    // 客户订单号在 SalesOrderItem 中，不在 SalesOrder 中，所以移除这个方法

    /**
     * 根据关键字模糊查询订单
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 订单分页列表
     */
    @Query("SELECT o FROM SalesOrder o WHERE o.orderNo LIKE %:keyword% OR o.customerName LIKE %:keyword%")
    Page<SalesOrder> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 统计客户的订单数量
     * @param customerCode 客户编码
     * @return 订单数量
     */
    long countByCustomerCode(String customerCode);

    /**
     * 查询客户最近的订单
     * @param customerCode 客户编码
     * @return 最近的订单
     */
    Optional<SalesOrder> findFirstByCustomerCodeOrderByOrderDateDesc(String customerCode);

    /**
     * 根据前缀查询最大订单号
     * @param prefix 订单号前缀
     * @return 最大订单号
     */
    @Query("SELECT MAX(o.orderNo) FROM SalesOrder o WHERE o.orderNo LIKE :prefix%")
    String findMaxOrderNoByPrefix(@Param("prefix") String prefix);
}
