package com.czerp.erpbackend.warehouse.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 仓库数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 备料仓（0-否，1-是）
     */
    private Boolean isMaterialWarehouse;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 版本号（乐观锁）
     */
    private Integer version;
}
