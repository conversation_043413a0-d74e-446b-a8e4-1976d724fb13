-- 仓库表创建脚本
-- 表名：warehouse
-- 字符集：utf8mb4，排序规则：utf8mb4_0900_ai_ci

CREATE TABLE `warehouse` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `warehouse_name` VARCHAR(100) NOT NULL COMMENT '仓库名称',
    `is_material_warehouse` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '备料仓（0-否，1-是）',
    `created_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除，1-已删除）',
    PRIMARY KEY (`id`),
    INDEX `idx_warehouse_name` (`warehouse_name`),
    INDEX `idx_is_material_warehouse` (`is_material_warehouse`),
    INDEX `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='仓库表';
