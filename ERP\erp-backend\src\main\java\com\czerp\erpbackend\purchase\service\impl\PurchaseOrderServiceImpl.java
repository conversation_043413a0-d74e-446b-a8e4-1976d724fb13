package com.czerp.erpbackend.purchase.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.purchase.dto.CreatePurchaseOrderItemRequest;
import com.czerp.erpbackend.purchase.dto.CreatePurchaseOrderRequest;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderQueryRequest;
import com.czerp.erpbackend.purchase.dto.UpdatePurchaseOrderRequest;
import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import com.czerp.erpbackend.purchase.mapper.PurchaseOrderItemMapper;
import com.czerp.erpbackend.purchase.mapper.PurchaseOrderMapper;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderRepository;
import com.czerp.erpbackend.purchase.service.PurchaseOrderEnrichmentService;
import com.czerp.erpbackend.purchase.service.PurchaseOrderService;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderMaterialRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.Objects;

/**
 * 采购订单服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PurchaseOrderServiceImpl implements PurchaseOrderService {

    @PersistenceContext
    private EntityManager entityManager;

    private final PurchaseOrderRepository purchaseOrderRepository;
    private final PurchaseOrderItemRepository purchaseOrderItemRepository;
    private final PurchaseOrderMapper purchaseOrderMapper;
    private final PurchaseOrderItemMapper purchaseOrderItemMapper;
    private final SalesOrderItemRepository salesOrderItemRepository;
    private final SalesOrderMaterialRepository salesOrderMaterialRepository;
    private final PurchaseOrderEnrichmentService purchaseOrderEnrichmentService;

    /**
     * 分页查询采购订单
     * @param request 查询请求
     * @return 采购订单分页列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<PurchaseOrderDTO> findPurchaseOrders(PurchaseOrderQueryRequest request) {
        log.debug("Finding purchase orders with request: {}", request);

        // 构建分页参数
        int page = request.getPage() != null ? request.getPage() : 0;
        int size = request.getSize() != null ? request.getSize() : 10;
        Sort sort = Sort.by(Sort.Direction.DESC, "purchaseDate", "purchaseOrderNo");
        Pageable pageable = PageRequest.of(page, size, sort);

        // 构建查询条件
        Specification<PurchaseOrder> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 关键字查询（采购单号、供应商名称、采购员）
            if (StringUtils.hasText(request.getKeyword())) {
                String keyword = "%" + request.getKeyword() + "%";
                predicates.add(cb.or(
                        cb.like(root.get("purchaseOrderNo"), keyword),
                        cb.like(root.get("supplierName"), keyword),
                        cb.like(root.get("purchaser"), keyword)
                ));
            }

            // 采购日期范围
            if (request.getPurchaseDateStart() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("purchaseDate"), request.getPurchaseDateStart()));
            }
            if (request.getPurchaseDateEnd() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("purchaseDate"), request.getPurchaseDateEnd()));
            }

            // 供应商编码
            if (StringUtils.hasText(request.getSupplierCode())) {
                predicates.add(cb.equal(root.get("supplierCode"), request.getSupplierCode()));
            }

            // 采购类型
            if (StringUtils.hasText(request.getPurchaseType())) {
                predicates.add(cb.equal(root.get("purchaseType"), request.getPurchaseType()));
            }

            // 付款方式
            if (StringUtils.hasText(request.getPaymentMethod())) {
                predicates.add(cb.equal(root.get("paymentMethod"), request.getPaymentMethod()));
            }

            // 生产单号查询
            if (StringUtils.hasText(request.getProductionOrderNo())) {
                // 使用EXISTS子查询
                Subquery<Long> subquery = query.subquery(Long.class);
                Root<PurchaseOrderItem> itemRoot = subquery.from(PurchaseOrderItem.class);

                // 构建子查询条件：采购订单明细关联到当前采购订单，且sourceSalesOrderItemId不为空
                subquery.select(itemRoot.get("id"))
                        .where(cb.and(
                                cb.equal(itemRoot.get("purchaseOrder"), root),
                                cb.isNotNull(itemRoot.get("sourceSalesOrderItemId"))
                        ));

                // 添加EXISTS子查询条件
                predicates.add(cb.exists(subquery));

                // 添加生产单号匹配条件
                // 注意：这里我们使用原生SQL查询，因为JPA Criteria API不支持直接关联不同实体的字段
                // 这是一个变通方法，实际项目中可能需要更复杂的处理
                String productionOrderNoLike = "%" + request.getProductionOrderNo() + "%";
                predicates.add(cb.in(root.get("id")).value(
                        entityManager.createQuery(
                                "SELECT DISTINCT poi.purchaseOrder.id FROM PurchaseOrderItem poi " +
                                "JOIN SalesOrderItem soi ON poi.sourceSalesOrderItemId = soi.id " +
                                "WHERE soi.productionOrderNo LIKE :productionOrderNo", Long.class)
                                .setParameter("productionOrderNo", productionOrderNoLike)
                                .getResultList()
                ));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 执行查询
        Page<PurchaseOrder> purchaseOrderPage = purchaseOrderRepository.findAll(spec, pageable);

        // 转换为DTO
        List<PurchaseOrderDTO> purchaseOrderDTOs = purchaseOrderMapper.toDto(purchaseOrderPage.getContent());

        // 设置生产单号
        for (PurchaseOrderDTO dto : purchaseOrderDTOs) {
            setProductionOrderNo(dto);
        }

        // 使用丰富服务添加销售订单相关数据
        purchaseOrderEnrichmentService.enrichWithSalesOrderData(purchaseOrderDTOs);

        // 构建分页响应
        return new PageResponse<>(
                purchaseOrderDTOs,
                purchaseOrderPage.getTotalElements(),
                purchaseOrderPage.getTotalPages(),
                purchaseOrderPage.getNumber() + 1,
                purchaseOrderPage.getSize()
        );
    }

    /**
     * 分页查询采购订单明细（按明细行级别分页）
     * @param request 查询请求
     * @return 采购订单明细分页列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<PurchaseOrderItemDTO> findPurchaseOrderItems(PurchaseOrderQueryRequest request) {
        log.debug("Finding purchase order items with request: {}", request);

        // 构建分页参数
        int page = request.getPage() != null ? request.getPage() : 0;
        int size = request.getSize() != null ? request.getSize() : 10;
        Sort sort = Sort.by(Sort.Direction.DESC, "purchaseOrder.purchaseDate", "purchaseOrder.purchaseOrderNo", "id");
        Pageable pageable = PageRequest.of(page, size, sort);

        // 构建查询条件
        Specification<PurchaseOrderItem> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 关联采购订单表
            Join<PurchaseOrderItem, PurchaseOrder> purchaseOrderJoin = root.join("purchaseOrder", JoinType.INNER);

            // 关键字查询（采购单号、供应商名称、采购员）
            if (StringUtils.hasText(request.getKeyword())) {
                String keyword = "%" + request.getKeyword() + "%";
                predicates.add(cb.or(
                        cb.like(purchaseOrderJoin.get("purchaseOrderNo"), keyword),
                        cb.like(purchaseOrderJoin.get("supplierName"), keyword),
                        cb.like(purchaseOrderJoin.get("purchaser"), keyword)
                ));
            }

            // 采购日期范围
            if (request.getPurchaseDateStart() != null) {
                predicates.add(cb.greaterThanOrEqualTo(purchaseOrderJoin.get("purchaseDate"), request.getPurchaseDateStart()));
            }
            if (request.getPurchaseDateEnd() != null) {
                predicates.add(cb.lessThanOrEqualTo(purchaseOrderJoin.get("purchaseDate"), request.getPurchaseDateEnd()));
            }

            // 供应商编码
            if (StringUtils.hasText(request.getSupplierCode())) {
                predicates.add(cb.equal(purchaseOrderJoin.get("supplierCode"), request.getSupplierCode()));
            }

            // 采购类型
            if (StringUtils.hasText(request.getPurchaseType())) {
                predicates.add(cb.equal(purchaseOrderJoin.get("purchaseType"), request.getPurchaseType()));
            }

            // 付款方式
            if (StringUtils.hasText(request.getPaymentMethod())) {
                predicates.add(cb.equal(purchaseOrderJoin.get("paymentMethod"), request.getPaymentMethod()));
            }

            // 生产单号查询
            if (StringUtils.hasText(request.getProductionOrderNo())) {
                // 使用EXISTS子查询
                Subquery<Long> subquery = query.subquery(Long.class);
                Root<SalesOrderItem> salesOrderItemRoot = subquery.from(SalesOrderItem.class);

                // 构建子查询条件：销售订单明细的生产单号匹配，且与当前采购订单明细关联
                String productionOrderNoLike = "%" + request.getProductionOrderNo() + "%";
                subquery.select(salesOrderItemRoot.get("id"))
                        .where(cb.and(
                                cb.equal(salesOrderItemRoot.get("id"), root.get("sourceSalesOrderItemId")),
                                cb.like(salesOrderItemRoot.get("productionOrderNo"), productionOrderNoLike)
                        ));

                // 添加EXISTS子查询条件
                predicates.add(cb.exists(subquery));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 执行查询
        Page<PurchaseOrderItem> purchaseOrderItemPage = purchaseOrderItemRepository.findAll(spec, pageable);

        // 转换为DTO
        List<PurchaseOrderItemDTO> purchaseOrderItemDTOs = purchaseOrderItemMapper.toDto(purchaseOrderItemPage.getContent());

        // 设置生产单号和丰富数据
        for (PurchaseOrderItemDTO dto : purchaseOrderItemDTOs) {
            // 设置采购订单基本信息
            PurchaseOrder purchaseOrder = purchaseOrderItemPage.getContent().stream()
                    .filter(item -> item.getId().equals(dto.getId()))
                    .findFirst()
                    .map(PurchaseOrderItem::getPurchaseOrder)
                    .orElse(null);

            if (purchaseOrder != null) {
                // 设置采购订单基本信息到明细DTO中
                dto.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
                dto.setPurchaseDate(purchaseOrder.getPurchaseDate());
                dto.setPurchaser(purchaseOrder.getPurchaser());
                dto.setSupplierCode(purchaseOrder.getSupplierCode());
                dto.setSupplierName(purchaseOrder.getSupplierName());
                dto.setPaymentMethod(purchaseOrder.getPaymentMethod());
                dto.setPurchaseType(purchaseOrder.getPurchaseType());
                dto.setContactPerson(purchaseOrder.getContactPerson());
                dto.setPhone(purchaseOrder.getPhone());
                dto.setAddress(purchaseOrder.getAddress());
                dto.setRemarks(purchaseOrder.getRemarks());
            }

            // 设置生产单号
            if (StringUtils.hasText(dto.getSourceSalesOrderItemId())) {
                Optional<SalesOrderItem> salesOrderItemOpt = salesOrderItemRepository.findById(dto.getSourceSalesOrderItemId());
                if (salesOrderItemOpt.isPresent()) {
                    SalesOrderItem salesOrderItem = salesOrderItemOpt.get();
                    if (StringUtils.hasText(salesOrderItem.getProductionOrderNo())) {
                        dto.setProductionOrderNo(salesOrderItem.getProductionOrderNo());
                    }
                }
            }
        }

        // 使用丰富服务添加销售订单相关数据
        // 将明细DTO转换为订单DTO格式以便使用现有的丰富服务
        Map<Long, List<PurchaseOrderItemDTO>> groupedItems = purchaseOrderItemDTOs.stream()
                .collect(Collectors.groupingBy(PurchaseOrderItemDTO::getPurchaseOrderId));

        List<PurchaseOrderDTO> tempOrderDTOs = groupedItems.entrySet().stream()
                .map(entry -> {
                    PurchaseOrderDTO orderDTO = new PurchaseOrderDTO();
                    List<PurchaseOrderItemDTO> items = entry.getValue();
                    if (!items.isEmpty()) {
                        PurchaseOrderItemDTO firstItem = items.get(0);
                        orderDTO.setId(entry.getKey());
                        orderDTO.setPurchaseOrderNo(firstItem.getPurchaseOrderNo());
                        orderDTO.setItems(items);
                    }
                    return orderDTO;
                })
                .collect(Collectors.toList());

        purchaseOrderEnrichmentService.enrichWithSalesOrderData(tempOrderDTOs);

        // 构建分页响应
        return new PageResponse<>(
                purchaseOrderItemDTOs,
                purchaseOrderItemPage.getTotalElements(),
                purchaseOrderItemPage.getTotalPages(),
                purchaseOrderItemPage.getNumber() + 1,
                purchaseOrderItemPage.getSize()
        );
    }

    /**
     * 根据ID查询采购订单
     * @param id 采购订单ID
     * @return 采购订单
     */
    @Override
    @Transactional(readOnly = true)
    public PurchaseOrderDTO findPurchaseOrderById(Long id) {
        log.debug("Finding purchase order by id: {}", id);

        PurchaseOrder purchaseOrder = purchaseOrderRepository.findById(id)
                .orElseThrow(() -> new BusinessException("采购订单不存在"));

        PurchaseOrderDTO dto = purchaseOrderMapper.toDto(purchaseOrder);
        setProductionOrderNo(dto);

        // 使用丰富服务添加销售订单相关数据
        List<PurchaseOrderDTO> dtos = Collections.singletonList(dto);
        purchaseOrderEnrichmentService.enrichWithSalesOrderData(dtos);

        return dto;
    }

    /**
     * 创建采购订单
     * @param request 创建请求
     * @return 采购订单
     */
    @Override
    @Transactional
    public PurchaseOrderDTO createPurchaseOrder(CreatePurchaseOrderRequest request) {
        log.debug("Creating purchase order with request: {}", request);

        // 创建采购订单
        PurchaseOrder purchaseOrder = purchaseOrderMapper.toEntity(request);

        // 生成采购单号
        String purchaseOrderNo = generatePurchaseOrderNo();
        purchaseOrder.setPurchaseOrderNo(purchaseOrderNo);

        // 保存采购订单
        purchaseOrder = purchaseOrderRepository.save(purchaseOrder);

        // 创建采购订单明细
        for (CreatePurchaseOrderItemRequest itemRequest : request.getItems()) {
            PurchaseOrderItem item = purchaseOrderItemMapper.toEntity(itemRequest);

            // 设置来源销售订单明细ID
            if (StringUtils.hasText(itemRequest.getSourceSalesOrderItemId())) {
                item.setSourceSalesOrderItemId(itemRequest.getSourceSalesOrderItemId());

                // 更新销售订单明细的采购状态
                updateSalesOrderItemPurchaseStatus(itemRequest.getSourceSalesOrderItemId(), item.getQuantity(), true);
            }

            purchaseOrder.addItem(item);
        }

        // 再次保存采购订单（包含明细）
        purchaseOrder = purchaseOrderRepository.save(purchaseOrder);

        PurchaseOrderDTO dto = purchaseOrderMapper.toDto(purchaseOrder);
        setProductionOrderNo(dto);

        // 使用丰富服务添加销售订单相关数据
        List<PurchaseOrderDTO> dtos = Collections.singletonList(dto);
        purchaseOrderEnrichmentService.enrichWithSalesOrderData(dtos);

        return dto;
    }

    /**
     * 生成采购单号
     * @return 采购单号
     */
    @Override
    public String generatePurchaseOrderNo() {
        // 生成格式：CG + 年月日 + 5位序号，例如：CG2023060100001
        LocalDate today = LocalDate.now();
        String datePrefix = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String purchaseOrderNoPrefix = "CG" + datePrefix;

        // 查询当天最大采购单号
        String maxPurchaseOrderNo = purchaseOrderRepository.findMaxPurchaseOrderNoByPrefix(purchaseOrderNoPrefix);

        int sequence = 1;
        if (maxPurchaseOrderNo != null && maxPurchaseOrderNo.length() >= purchaseOrderNoPrefix.length() + 5) {
            String sequenceStr = maxPurchaseOrderNo.substring(purchaseOrderNoPrefix.length());
            try {
                sequence = Integer.parseInt(sequenceStr) + 1;
            } catch (NumberFormatException e) {
                log.warn("Failed to parse sequence from purchase order no: {}", maxPurchaseOrderNo);
            }
        }

        // 格式化序号为5位数字
        return purchaseOrderNoPrefix + String.format("%05d", sequence);
    }

    /**
     * 更新采购订单
     * @param id 采购订单ID
     * @param request 更新请求
     * @return 采购订单
     */
    @Override
    @Transactional
    public PurchaseOrderDTO updatePurchaseOrder(Long id, UpdatePurchaseOrderRequest request) {
        log.debug("Updating purchase order with id: {} and request: {}", id, request);

        // 查询采购订单
        PurchaseOrder purchaseOrder = purchaseOrderRepository.findById(id)
                .orElseThrow(() -> new BusinessException("采购订单不存在"));

        // 更新采购订单基本信息
        purchaseOrderMapper.updateEntity(request, purchaseOrder);

        // 清空原有明细
        purchaseOrder.getItems().clear();

        // 添加新的明细
        for (CreatePurchaseOrderItemRequest itemRequest : request.getItems()) {
            PurchaseOrderItem item = purchaseOrderItemMapper.toEntity(itemRequest);
            purchaseOrder.addItem(item);

            // 设置关联的销售订单明细ID
            if (itemRequest.getSourceSalesOrderItemId() != null) {
                item.setSourceSalesOrderItemId(itemRequest.getSourceSalesOrderItemId());
            }
        }

        // 保存采购订单
        purchaseOrder = purchaseOrderRepository.save(purchaseOrder);

        PurchaseOrderDTO dto = purchaseOrderMapper.toDto(purchaseOrder);
        setProductionOrderNo(dto);

        // 使用丰富服务添加销售订单相关数据
        List<PurchaseOrderDTO> dtos = Collections.singletonList(dto);
        purchaseOrderEnrichmentService.enrichWithSalesOrderData(dtos);

        return dto;
    }

    /**
     * 设置采购订单DTO的生产单号
     * 为每个明细项设置对应的生产单号，同时保持向后兼容，设置顶层的productionOrderNo字段
     * @param purchaseOrderDTO 采购订单DTO
     */
    private void setProductionOrderNo(PurchaseOrderDTO purchaseOrderDTO) {
        if (purchaseOrderDTO == null || purchaseOrderDTO.getItems() == null || purchaseOrderDTO.getItems().isEmpty()) {
            log.debug("采购订单DTO为空或没有明细项，无法设置生产单号");
            return;
        }

        log.debug("开始为采购订单 {} 设置生产单号，共有 {} 个明细项", purchaseOrderDTO.getId(), purchaseOrderDTO.getItems().size());

        String firstProductionOrderNo = null; // 用于设置顶层productionOrderNo字段（向后兼容）

        // 为每个明细项设置生产单号
        for (PurchaseOrderItemDTO item : purchaseOrderDTO.getItems()) {
            log.debug("检查明细项 {}，sourceSalesOrderItemId = {}", item.getId(), item.getSourceSalesOrderItemId());

            if (StringUtils.hasText(item.getSourceSalesOrderItemId())) {
                log.debug("明细项 {} 有关联的销售订单明细ID: {}", item.getId(), item.getSourceSalesOrderItemId());

                // 查询关联的销售订单明细
                Optional<SalesOrderItem> salesOrderItemOpt = salesOrderItemRepository.findById(item.getSourceSalesOrderItemId());

                if (salesOrderItemOpt.isPresent()) {
                    SalesOrderItem salesOrderItem = salesOrderItemOpt.get();
                    log.debug("找到关联的销售订单明细: id={}, productionOrderNo={}", salesOrderItem.getId(), salesOrderItem.getProductionOrderNo());

                    if (StringUtils.hasText(salesOrderItem.getProductionOrderNo())) {
                        // 设置明细项的生产单号
                        item.setProductionOrderNo(salesOrderItem.getProductionOrderNo());
                        log.debug("成功设置明细项 {} 的生产单号为: {}", item.getId(), salesOrderItem.getProductionOrderNo());

                        // 记录第一个有效的生产单号，用于设置顶层字段
                        if (firstProductionOrderNo == null) {
                            firstProductionOrderNo = salesOrderItem.getProductionOrderNo();
                        }
                    } else {
                        log.debug("关联的销售订单明细没有生产单号");
                    }
                } else {
                    log.debug("未找到ID为 {} 的销售订单明细", item.getSourceSalesOrderItemId());
                }
            }
        }

        // 为了保持向后兼容，设置顶层的productionOrderNo字段
        if (firstProductionOrderNo != null) {
            purchaseOrderDTO.setProductionOrderNo(firstProductionOrderNo);
            log.debug("成功设置采购订单 {} 的顶层生产单号为: {}", purchaseOrderDTO.getId(), firstProductionOrderNo);
        } else {
            log.debug("采购订单 {} 没有找到有效的生产单号", purchaseOrderDTO.getId());
        }
    }

    /**
     * 更新销售订单明细的采购状态
     * @param salesOrderItemId 销售订单明细ID
     * @param quantity 采购数量
     * @param isAdd 是否是添加采购（true：添加，false：删除）
     */
    private void updateSalesOrderItemPurchaseStatus(String salesOrderItemId, Integer quantity, boolean isAdd) {
        if (!StringUtils.hasText(salesOrderItemId) || quantity == null || quantity <= 0) {
            return;
        }

        // 查询销售订单明细
        SalesOrderItem salesOrderItem = salesOrderItemRepository.findById(salesOrderItemId)
                .orElse(null);

        if (salesOrderItem == null) {
            log.warn("Sales order item not found with id: {}", salesOrderItemId);
            return;
        }

        // 计算已采购数量
        Integer purchasedQuantity = salesOrderItem.getPurchasedQuantity();
        if (purchasedQuantity == null) {
            purchasedQuantity = 0;
        }

        // 更新已采购数量
        if (isAdd) {
            purchasedQuantity += quantity;
        } else {
            purchasedQuantity = Math.max(0, purchasedQuantity - quantity);
        }

        salesOrderItem.setPurchasedQuantity(purchasedQuantity);

        // 查询销售订单材料信息，获取纸板数作为采购需求基准
        List<SalesOrderMaterial> materials = salesOrderMaterialRepository.findByOrderItemIdOrderBySerialNoAsc(salesOrderItemId);

        // 计算总纸板数（采购需求量）
        Integer totalBoardCount = 0;
        if (!materials.isEmpty()) {
            for (SalesOrderMaterial material : materials) {
                if (material.getBoardCount() != null) {
                    totalBoardCount += material.getBoardCount();
                }
            }
        }

        // 如果没有找到有效的纸板数，则使用订单数量作为备选
        if (totalBoardCount <= 0) {
            totalBoardCount = salesOrderItem.getQuantity();
            if (totalBoardCount == null || totalBoardCount <= 0) {
                totalBoardCount = 1; // 防止除以0
            }
            log.warn("No valid board count found for sales order item {}, using order quantity {} as fallback",
                    salesOrderItemId, totalBoardCount);
        }

        // 更新采购状态
        if (purchasedQuantity <= 0) {
            salesOrderItem.setPurchaseStatus("NOT_PURCHASED");
        } else if (purchasedQuantity >= totalBoardCount) {
            salesOrderItem.setPurchaseStatus("FULLY_PURCHASED");
        } else {
            salesOrderItem.setPurchaseStatus("PARTIALLY_PURCHASED");
        }

        // 保存销售订单明细
        salesOrderItemRepository.save(salesOrderItem);

        log.info("Updated sales order item purchase status: id={}, purchasedQuantity={}, totalBoardCount={}, purchaseStatus={}",
                salesOrderItemId, purchasedQuantity, totalBoardCount, salesOrderItem.getPurchaseStatus());
    }
}
