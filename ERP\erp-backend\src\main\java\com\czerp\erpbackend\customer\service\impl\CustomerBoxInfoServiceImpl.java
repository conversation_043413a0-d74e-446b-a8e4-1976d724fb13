package com.czerp.erpbackend.customer.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.customer.dto.CustomerBoxInfoDTO;
import com.czerp.erpbackend.customer.dto.CustomerBoxInfoQueryRequest;
import com.czerp.erpbackend.customer.entity.CustomerBoxInfo;
import com.czerp.erpbackend.customer.repository.CustomerBoxInfoRepository;
import com.czerp.erpbackend.customer.service.CustomerBoxInfoService;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户盒式信息服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerBoxInfoServiceImpl implements CustomerBoxInfoService {

    private final CustomerBoxInfoRepository customerBoxInfoRepository;

    /**
     * 分页查询客户盒式信息列表
     * @param request 查询请求
     * @return 客户盒式信息分页列表
     */
    @Override
    public PageResponse<CustomerBoxInfoDTO> findCustomerBoxInfos(CustomerBoxInfoQueryRequest request) {
        log.debug("Finding customer box infos with request: {}", request);

        // 构建查询条件
        Specification<CustomerBoxInfo> spec = buildSpecification(request);

        // 构建分页和排序
        Pageable pageable = buildPageable(request);

        // 执行查询
        Page<CustomerBoxInfo> page = customerBoxInfoRepository.findAll(spec, pageable);

        // 转换为DTO
        List<CustomerBoxInfoDTO> content = page.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 构建分页响应
        return PageResponse.<CustomerBoxInfoDTO>builder()
                .content(content)
                .page(request.getPage())
                .size(request.getSize())
                .totalElements(page.getTotalElements())
                .totalPages(page.getTotalPages())
                .first(page.isFirst())
                .last(page.isLast())
                .empty(page.isEmpty())
                .build();
    }

    /**
     * 根据ID查询客户盒式信息
     * @param id 客户盒式信息ID
     * @return 客户盒式信息
     */
    @Override
    public CustomerBoxInfoDTO findCustomerBoxInfoById(String id) {
        log.debug("Finding customer box info by id: {}", id);
        CustomerBoxInfo customerBoxInfo = customerBoxInfoRepository.findById(id)
                .orElseThrow(() -> new BusinessException("客户盒式信息不存在"));
        return convertToDTO(customerBoxInfo);
    }

    /**
     * 根据盒式编码查询客户盒式信息
     * @param boxCode 盒式编码
     * @return 客户盒式信息
     */
    @Override
    public CustomerBoxInfoDTO findCustomerBoxInfoByCode(String boxCode) {
        log.debug("Finding customer box info by code: {}", boxCode);
        CustomerBoxInfo customerBoxInfo = customerBoxInfoRepository.findByBoxCode(boxCode)
                .orElseThrow(() -> new BusinessException("客户盒式信息不存在"));
        return convertToDTO(customerBoxInfo);
    }

    /**
     * 查询所有启用的客户盒式信息
     * @return 客户盒式信息列表
     */
    @Override
    public List<CustomerBoxInfoDTO> findActiveCustomerBoxInfos() {
        log.debug("Finding all active customer box infos");
        List<CustomerBoxInfo> customerBoxInfos = customerBoxInfoRepository.findByStatus("active");
        return customerBoxInfos.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 查询默认盒式
     * @return 默认盒式
     */
    @Override
    public CustomerBoxInfoDTO findDefaultCustomerBoxInfo() {
        log.debug("Finding default customer box info");
        List<CustomerBoxInfo> defaultBoxInfos = customerBoxInfoRepository.findByIsDefaultTrue();
        if (defaultBoxInfos.isEmpty()) {
            throw new BusinessException("未找到默认盒式");
        }
        return convertToDTO(defaultBoxInfos.get(0));
    }

    /**
     * 构建查询条件
     * @param request 查询请求
     * @return 查询条件
     */
    private Specification<CustomerBoxInfo> buildSpecification(CustomerBoxInfoQueryRequest request) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 添加未删除条件
            predicates.add(cb.equal(root.get("isDeleted"), false));

            // 关键字搜索（盒式编码或盒式名称）
            if (StringUtils.hasText(request.getKeyword())) {
                String keyword = "%" + request.getKeyword() + "%";
                predicates.add(cb.or(
                        cb.like(root.get("boxCode"), keyword),
                        cb.like(root.get("boxName"), keyword)
                ));
            }

            // 状态过滤
            if (StringUtils.hasText(request.getStatus())) {
                predicates.add(cb.equal(root.get("status"), request.getStatus()));
            }

            // 是否默认盒式
            if (request.getIsDefault() != null) {
                predicates.add(cb.equal(root.get("isDefault"), request.getIsDefault()));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 构建分页和排序
     * @param request 查询请求
     * @return 分页和排序
     */
    private Pageable buildPageable(CustomerBoxInfoQueryRequest request) {
        // 页码从0开始
        int page = Math.max(0, request.getPage() - 1);
        int size = request.getSize();

        // 排序
        if (StringUtils.hasText(request.getSortField())) {
            Sort.Direction direction = "desc".equalsIgnoreCase(request.getSortDirection())
                    ? Sort.Direction.DESC : Sort.Direction.ASC;
            return PageRequest.of(page, size, Sort.by(direction, request.getSortField()));
        } else {
            // 默认按盒式编码排序
            return PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "boxCode"));
        }
    }

    /**
     * 将实体转换为DTO
     * @param customerBoxInfo 客户盒式信息实体
     * @return 客户盒式信息DTO
     */
    private CustomerBoxInfoDTO convertToDTO(CustomerBoxInfo customerBoxInfo) {
        return CustomerBoxInfoDTO.builder()
                .id(customerBoxInfo.getId())
                .boxCode(customerBoxInfo.getBoxCode())
                .boxName(customerBoxInfo.getBoxName())
                .quoteFormula(customerBoxInfo.getQuoteFormula())
                .calculationUnit(customerBoxInfo.getCalculationUnit())
                .quoteUnit(customerBoxInfo.getQuoteUnit())
                .connectionMethod(customerBoxInfo.getConnectionMethod())
                .forbidDoubleFluting(customerBoxInfo.getForbidDoubleFluting())
                .doubleFlutingLengthThreshold(customerBoxInfo.getDoubleFlutingLengthThreshold())
                .pitchTolerance(customerBoxInfo.getPitchTolerance())
                .hasBoxGraphic(customerBoxInfo.getHasBoxGraphic())
                .isDefault(customerBoxInfo.getIsDefault())
                .status(customerBoxInfo.getStatus())
                .createdBy(customerBoxInfo.getCreatedBy())
                .createdTime(customerBoxInfo.getCreatedTime())
                .updatedBy(customerBoxInfo.getUpdatedBy())
                .updatedTime(customerBoxInfo.getUpdatedTime())
                .build();
    }
}
