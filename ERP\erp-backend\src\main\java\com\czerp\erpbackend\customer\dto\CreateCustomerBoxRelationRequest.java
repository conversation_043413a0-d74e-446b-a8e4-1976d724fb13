package com.czerp.erpbackend.customer.dto;

import jakarta.validation.constraints.AssertTrue;
import lombok.Data;

/**
 * 创建客户盒式关联请求
 */
@Data
public class CreateCustomerBoxRelationRequest {

    /**
     * 盒式信息ID
     * 如果提供了productId，则可以为空
     */
    private String boxInfoId;

    /**
     * 货品ID（盒式档案ID）
     * 如果提供了boxInfoId，则可以为空
     */
    private String productId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 验证至少提供了boxInfoId或productId中的一个
     */
    @AssertTrue(message = "盒式信息ID和货品ID不能同时为空")
    public boolean isEitherBoxInfoIdOrProductIdProvided() {
        return boxInfoId != null && !boxInfoId.isEmpty() ||
               productId != null && !productId.isEmpty();
    }
}
