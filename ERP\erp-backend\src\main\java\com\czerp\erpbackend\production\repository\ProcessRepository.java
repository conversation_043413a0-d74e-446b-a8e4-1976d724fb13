package com.czerp.erpbackend.production.repository;

import com.czerp.erpbackend.production.entity.Process;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 工序存储库
 */
@Repository
public interface ProcessRepository extends JpaRepository<Process, Long> {
    
    /**
     * 根据工序名称查找工序
     * @param process 工序名称
     * @return 工序
     */
    Optional<Process> findByProcess(String process);
    
    /**
     * 判断工序名称是否存在
     * @param process 工序名称
     * @return 是否存在
     */
    boolean existsByProcess(String process);
    
    /**
     * 判断工序名称是否存在（排除指定ID）
     * @param process 工序名称
     * @param id ID
     * @return 是否存在
     */
    boolean existsByProcessAndIdNot(String process, Long id);
    
    /**
     * 多条件查询工序
     * @param keyword 关键词
     * @param startDateTime 开始时间
     * @param endDateTime 结束时间
     * @param pageable 分页参数
     * @return 工序分页列表
     */
    @Query("SELECT p FROM Process p WHERE p.isDeleted = false AND " +
           "(:keyword IS NULL OR :keyword = '' OR " +
           "p.process LIKE %:keyword% OR " +
           "p.processRequirements LIKE %:keyword% OR " +
           "p.printingPlateNo LIKE %:keyword% OR " +
           "p.inkNo LIKE %:keyword% OR " +
           "p.inkName LIKE %:keyword%) AND " +
           "(:startDateTime IS NULL OR p.createdTime >= :startDateTime) AND " +
           "(:endDateTime IS NULL OR p.createdTime <= :endDateTime)")
    Page<Process> search(
            @Param("keyword") String keyword,
            @Param("startDateTime") LocalDateTime startDateTime,
            @Param("endDateTime") LocalDateTime endDateTime,
            Pageable pageable);
}
