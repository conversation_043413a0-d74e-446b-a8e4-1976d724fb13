package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.system.dto.PermissionDTO;
import com.czerp.erpbackend.system.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 权限控制器
 */
@RestController
@RequestMapping("/system/permissions")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Permission Management", description = "权限管理相关接口")
public class PermissionController {
    
    private final PermissionService permissionService;
    
    @GetMapping
    @Operation(summary = "获取权限列表", description = "获取所有权限列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('permission:list')")
    public ResponseEntity<ApiResponse<List<PermissionDTO>>> getAllPermissions() {
        log.info("Getting all permissions");
        List<PermissionDTO> permissions = permissionService.findAllPermissions();
        return ResponseEntity.ok(ApiResponse.success(permissions));
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取权限详情", description = "根据ID获取权限详情")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('permission:read')")
    public ResponseEntity<ApiResponse<PermissionDTO>> getPermission(@PathVariable String id) {
        log.info("Getting permission with id: {}", id);
        PermissionDTO permission = permissionService.findPermissionById(id);
        return ResponseEntity.ok(ApiResponse.success(permission));
    }
    
    @GetMapping("/tree")
    @Operation(summary = "获取权限树", description = "获取权限树结构")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('permission:list')")
    public ResponseEntity<ApiResponse<List<PermissionDTO>>> getPermissionTree() {
        log.info("Getting permission tree");
        List<PermissionDTO> permissionTree = permissionService.findPermissionTree();
        return ResponseEntity.ok(ApiResponse.success(permissionTree));
    }
    
    @GetMapping("/by-role/{roleId}")
    @Operation(summary = "获取角色权限", description = "获取指定角色的权限列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('permission:list')")
    public ResponseEntity<ApiResponse<List<PermissionDTO>>> getPermissionsByRole(@PathVariable String roleId) {
        log.info("Getting permissions by role id: {}", roleId);
        List<PermissionDTO> permissions = permissionService.findPermissionsByRoleId(roleId);
        return ResponseEntity.ok(ApiResponse.success(permissions));
    }
}
