# 生产排程管理API接口文档

## 1. 概述

生产排程管理模块提供了对生产排程单的完整CRUD操作，包括创建、查询、更新、删除等功能。本文档详细描述了所有API接口的定义、参数、响应格式和使用示例。

## 2. 基础信息

- **模块名称**: 生产排程管理
- **基础路径**: `/api/production-schedules`
- **权限前缀**: `production:schedule`

## 3. 数据结构

### 3.1 生产排程单DTO (ProductionScheduleDTO)

```typescript
interface ProductionScheduleDTO {
  id: string;                           // 主键ID
  scheduleNo: string;                   // 排程单号
  scheduleDate: string;                 // 排程日期 (YYYY-MM-DD)
  remark?: string;                      // 备注
  createdBy: string;                    // 创建人
  createdTime: string;                  // 创建时间
  updatedBy?: string;                   // 更新人
  updatedTime?: string;                 // 更新时间
  version: number;                      // 版本号（乐观锁）
  items: ProductionScheduleItemDTO[];   // 排程明细列表
}
```

### 3.2 生产排程单明细DTO (ProductionScheduleItemDTO)

```typescript
interface ProductionScheduleItemDTO {
  id: string;                           // 主键ID
  scheduleId: string;                   // 排程单ID
  isUrgent: boolean;                    // 是否急单
  urgentSequence?: number;              // 急单序号
  isPrinted: boolean;                   // 是否已打印
  plannedCompletionDate?: string;       // 计划完成日期 (YYYY-MM-DD)
  scheduleQuantity: number;             // 排程数量
  packageCount?: number;                // 包装数
  remark?: string;                      // 备注
  salesOrderItemId?: string;            // 销售订单明细ID
  createdBy: string;                    // 创建人
  createdTime: string;                  // 创建时间
  updatedBy?: string;                   // 更新人
  updatedTime?: string;                 // 更新时间
  version: number;                      // 版本号（乐观锁）

  // 关联销售订单信息（查询时返回）
  salesOrderNo?: string;                // 销售订单号
  productionOrderNo?: string;           // 生产单号
  customerName?: string;                // 客户名称
  productName?: string;                 // 产品名称
  processRequirements?: string;         // 工艺要求
  specification?: string;               // 规格
  productionSpecification?: string;     // 生产规格
  salesQuantity?: number;               // 销售数量
  deliveryDate?: string;                // 送货日期
}
```

### 3.3 查询请求参数 (ProductionScheduleQueryRequest)

```typescript
interface ProductionScheduleQueryRequest {
  page?: number;                        // 页码（从1开始，默认1）
  size?: number;                        // 每页大小（默认10）
  keyword?: string;                     // 关键字（排程单号、客户名称、产品名称）
  scheduleDateStart?: string;           // 排程日期开始 (YYYY-MM-DD)
  scheduleDateEnd?: string;             // 排程日期结束 (YYYY-MM-DD)
  plannedCompletionDateStart?: string;  // 计划完成日期开始 (YYYY-MM-DD)
  plannedCompletionDateEnd?: string;    // 计划完成日期结束 (YYYY-MM-DD)
  isUrgent?: boolean;                   // 是否急单
  isPrinted?: boolean;                  // 是否已打印
  customerName?: string;                // 客户名称
  productionOrderNo?: string;           // 生产单号
  salesOrderNo?: string;                // 销售订单号
  productName?: string;                 // 产品名称
}
```

### 3.4 创建请求参数 (CreateProductionScheduleRequest)

```typescript
interface CreateProductionScheduleRequest {
  scheduleDate?: string;                // 排程日期 (YYYY-MM-DD)
  remark?: string;                      // 备注
  items: CreateProductionScheduleItemRequest[]; // 排程明细列表（必填）
}

interface CreateProductionScheduleItemRequest {
  isUrgent?: boolean;                   // 是否急单（默认false）
  urgentSequence?: number;              // 急单序号
  plannedCompletionDate?: string;       // 计划完成日期 (YYYY-MM-DD)
  scheduleQuantity: number;             // 排程数量（必填）
  packageCount?: number;                // 包装数
  remark?: string;                      // 备注
  salesOrderItemId?: string;            // 销售订单明细ID
}
```

### 3.5 更新请求参数 (UpdateProductionScheduleRequest)

```typescript
interface UpdateProductionScheduleRequest {
  version: number;                      // 版本号（乐观锁，必填）
  scheduleDate?: string;                // 排程日期 (YYYY-MM-DD)
  remark?: string;                      // 备注
  items: UpdateProductionScheduleItemRequest[]; // 排程明细列表（必填）
}

interface UpdateProductionScheduleItemRequest {
  id?: string;                          // 明细ID（新增时为空，更新时必填）
  version?: number;                     // 版本号（乐观锁）
  isUrgent?: boolean;                   // 是否急单（默认false）
  urgentSequence?: number;              // 急单序号
  isPrinted?: boolean;                  // 是否已打印（默认false）
  plannedCompletionDate?: string;       // 计划完成日期 (YYYY-MM-DD)
  scheduleQuantity: number;             // 排程数量（必填）
  packageCount?: number;                // 包装数
  remark?: string;                      // 备注
  salesOrderItemId?: string;            // 销售订单明细ID
}
```

## 4. API接口

### 4.1 分页查询生产排程单

**接口地址**: `GET /api/production-schedules`

**权限要求**: `production:schedule:read`

**请求参数**: 查询参数形式传递 `ProductionScheduleQueryRequest`

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": "uuid-string",
        "scheduleNo": "SC2023060100001",
        "scheduleDate": "2023-06-01",
        "remark": "紧急排程",
        "createdBy": "admin",
        "createdTime": "2023-06-01T10:00:00",
        "updatedBy": "admin",
        "updatedTime": "2023-06-01T11:00:00",
        "version": 1,
        "items": [
          {
            "id": "item-uuid",
            "scheduleId": "uuid-string",
            "isUrgent": true,
            "urgentSequence": 1,
            "isPrinted": false,
            "plannedCompletionDate": "2023-06-05",
            "scheduleQuantity": 1000,
            "packageCount": 10,
            "remark": "急单处理",
            "salesOrderItemId": "sales-item-uuid",
            "salesOrderNo": "SO2023060100001",
            "productionOrderNo": "PO000001",
            "customerName": "测试客户",
            "productName": "测试产品",
            "processRequirements": "开槽→打角→打钉",
            "specification": "300×200×150",
            "productionSpecification": "310×210×160",
            "salesQuantity": 1000,
            "deliveryDate": "2023-06-10"
          }
        ]
      }
    ],
    "totalElements": 100,
    "totalPages": 10,
    "page": 1,
    "size": 10,
    "first": true,
    "last": false,
    "empty": false
  }
}
```

### 4.2 分页查询生产排程单明细（明细级别分页）

**接口地址**: `GET /api/production-schedules/items`

**权限要求**: `production:schedule:read`

**请求参数**: 查询参数形式传递 `ProductionScheduleQueryRequest`

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": "item-uuid",
        "scheduleId": "uuid-string",
        "isUrgent": true,
        "urgentSequence": 1,
        "isPrinted": false,
        "plannedCompletionDate": "2023-06-05",
        "scheduleQuantity": 1000,
        "packageCount": 10,
        "remark": "急单处理",
        "salesOrderItemId": "sales-item-uuid",
        "salesOrderNo": "SO2023060100001",
        "productionOrderNo": "PO000001",
        "customerName": "测试客户",
        "productName": "测试产品",
        "specification": "300×200×150",
        "productionSpecification": "310×210×160",
        "salesQuantity": 1000,
        "deliveryDate": "2023-06-10"
      }
    ],
    "totalElements": 500,
    "totalPages": 50,
    "page": 1,
    "size": 10,
    "first": true,
    "last": false,
    "empty": false
  }
}
```

### 4.3 根据ID查询生产排程单

**接口地址**: `GET /api/production-schedules/{id}`

**权限要求**: `production:schedule:read`

**路径参数**:
- `id`: 排程单ID (string, 必填)

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": "uuid-string",
    "scheduleNo": "SC2023060100001",
    "scheduleDate": "2023-06-01",
    "remark": "紧急排程",
    "createdBy": "admin",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-06-01T11:00:00",
    "version": 1,
    "items": [
      {
        "id": "item-uuid",
        "scheduleId": "uuid-string",
        "isUrgent": true,
        "urgentSequence": 1,
        "isPrinted": false,
        "plannedCompletionDate": "2023-06-05",
        "scheduleQuantity": 1000,
        "packageCount": 10,
        "remark": "急单处理",
        "salesOrderItemId": "sales-item-uuid",
        "salesOrderNo": "SO2023060100001",
        "productionOrderNo": "PO000001",
        "customerName": "测试客户",
        "productName": "测试产品",
        "specification": "300×200×150",
        "productionSpecification": "310×210×160",
        "salesQuantity": 1000,
        "deliveryDate": "2023-06-10"
      }
    ]
  }
}
```

### 4.4 创建生产排程单

**接口地址**: `POST /api/production-schedules`

**权限要求**: `production:schedule:create`

**请求体**: `CreateProductionScheduleRequest`

**请求示例**:
```json
{
  "scheduleDate": "2023-06-01",
  "remark": "紧急排程",
  "items": [
    {
      "isUrgent": true,
      "urgentSequence": 1,
      "plannedCompletionDate": "2023-06-05",
      "scheduleQuantity": 1000,
      "packageCount": 10,
      "remark": "急单处理",
      "salesOrderItemId": "sales-item-uuid"
    }
  ]
}
```

**响应示例**: 同查询单个排程单的响应格式

### 4.5 更新生产排程单

**接口地址**: `PUT /api/production-schedules/{id}`

**权限要求**: `production:schedule:update`

**路径参数**:
- `id`: 排程单ID (string, 必填)

**请求体**: `UpdateProductionScheduleRequest`

**请求示例**:
```json
{
  "version": 1,
  "scheduleDate": "2023-06-01",
  "remark": "更新后的备注",
  "items": [
    {
      "id": "item-uuid",
      "version": 1,
      "isUrgent": true,
      "urgentSequence": 1,
      "isPrinted": false,
      "plannedCompletionDate": "2023-06-05",
      "scheduleQuantity": 1200,
      "packageCount": 12,
      "remark": "更新数量",
      "salesOrderItemId": "sales-item-uuid"
    }
  ]
}
```

**响应示例**: 同查询单个排程单的响应格式

### 4.6 删除生产排程单

**接口地址**: `DELETE /api/production-schedules/{id}`

**权限要求**: `production:schedule:delete`

**路径参数**:
- `id`: 排程单ID (string, 必填)

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

### 4.7 生成排程单号

**接口地址**: `GET /api/production-schedules/generate-schedule-no`

**权限要求**: `production:schedule:create`

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": "SC2023060100001"
}
```

### 4.8 批量更新打印状态

**接口地址**: `PUT /api/production-schedules/items/print-status`

**权限要求**: `production:schedule:update`

**请求参数**:
- `itemIds`: 明细ID列表 (string[], 必填)
- `isPrinted`: 是否已打印 (boolean, 必填)

**请求示例**:
```
PUT /api/production-schedules/items/print-status?itemIds=uuid1,uuid2,uuid3&isPrinted=true
```

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

## 5. 业务规则

### 5.1 排程单号生成规则
- 格式：SC + 年月日 + 5位序号
- 示例：SC2023060100001
- 序号每天从00001开始递增

### 5.2 数据验证规则
- 排程明细不能为空
- 排程数量必须大于0
- 版本号用于乐观锁控制，更新时必须提供正确的版本号

### 5.3 权限控制
- 所有接口都需要Bearer Token认证
- 需要对应的功能权限才能访问相应接口

### 5.4 关联数据
- 排程明细可以关联销售订单明细
- 关联后会自动填充销售订单相关信息（客户名称、产品信息等）

## 6. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 数据冲突（版本号不匹配） |
| 500 | 服务器内部错误 |

## 7. 使用示例

### 7.1 创建排程单完整流程

1. **生成排程单号**
```javascript
const scheduleNoResponse = await fetch('/api/production-schedules/generate-schedule-no', {
  headers: { 'Authorization': 'Bearer ' + token }
});
const scheduleNo = scheduleNoResponse.data;
```

2. **创建排程单**
```javascript
const createRequest = {
  scheduleDate: '2023-06-01',
  remark: '紧急排程',
  items: [
    {
      isUrgent: true,
      urgentSequence: 1,
      plannedCompletionDate: '2023-06-05',
      scheduleQuantity: 1000,
      packageCount: 10,
      remark: '急单处理',
      salesOrderItemId: 'sales-item-uuid'
    }
  ]
};

const response = await fetch('/api/production-schedules', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify(createRequest)
});
```

### 7.2 查询排程单列表

```javascript
const queryParams = new URLSearchParams({
  page: '1',
  size: '10',
  keyword: '测试',
  scheduleDateStart: '2023-06-01',
  scheduleDateEnd: '2023-06-30',
  isUrgent: 'true'
});

const response = await fetch(`/api/production-schedules?${queryParams}`, {
  headers: { 'Authorization': 'Bearer ' + token }
});
```

### 7.3 更新排程单

```javascript
const updateRequest = {
  version: 1, // 必须提供当前版本号
  scheduleDate: '2023-06-01',
  remark: '更新后的备注',
  items: [
    {
      id: 'item-uuid', // 更新现有明细
      version: 1,
      scheduleQuantity: 1200, // 更新数量
      // ... 其他字段
    },
    {
      // 新增明细（不提供id）
      scheduleQuantity: 500,
      // ... 其他字段
    }
  ]
};

const response = await fetch(`/api/production-schedules/${scheduleId}`, {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify(updateRequest)
});
```

## 8. 数据库表结构

### 8.1 生产排程单头表 (production_schedule)

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | VARCHAR | 36 | 是 | - | 主键ID |
| schedule_no | VARCHAR | 50 | 是 | - | 排程单号 |
| schedule_date | DATE | - | 否 | - | 排程日期 |
| remark | VARCHAR | 500 | 否 | - | 备注 |
| created_by | VARCHAR | 50 | 是 | - | 创建人 |
| created_time | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_by | VARCHAR | 50 | 否 | - | 更新人 |
| updated_time | DATETIME | - | 否 | NULL ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| version | INT | - | 否 | 0 | 版本号(乐观锁) |
| is_deleted | TINYINT(1) | - | 是 | 0 | 是否删除 |

### 8.2 生产排程单明细表 (production_schedule_item)

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | VARCHAR | 36 | 是 | - | 主键ID |
| schedule_id | VARCHAR | 36 | 是 | - | 排程单ID |
| is_urgent | TINYINT(1) | - | 否 | 0 | 急单 |
| urgent_sequence | INT | - | 否 | - | 急单序号 |
| is_printed | TINYINT(1) | - | 否 | 0 | 已打印 |
| planned_completion_date | DATE | - | 否 | - | 计划完成日期 |
| schedule_quantity | INT | - | 否 | - | 排程数量 |
| package_count | INT | - | 否 | - | 包装数 |
| remark | VARCHAR | 500 | 否 | - | 备注 |
| sales_order_item_id | VARCHAR | 36 | 否 | - | 销售订单明细ID |
| created_by | VARCHAR | 50 | 是 | - | 创建人 |
| created_time | DATETIME | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_by | VARCHAR | 50 | 否 | - | 更新人 |
| updated_time | DATETIME | - | 否 | NULL ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| version | INT | - | 否 | 0 | 版本号(乐观锁) |
| is_deleted | TINYINT(1) | - | 是 | 0 | 是否删除 |

## 9. 权限配置

### 9.1 权限列表

| 权限编码 | 权限名称 | 权限类型 | 说明 |
|----------|----------|----------|------|
| production | 生产管理 | MENU | 生产管理模块菜单 |
| production:schedule | 生产排程 | MENU | 生产排程子模块菜单 |
| production:schedule:list | 生产排程列表 | BUTTON | 查看生产排程列表权限 |
| production:schedule:read | 生产排程详情 | BUTTON | 查看生产排程详情权限 |
| production:schedule:create | 创建生产排程 | BUTTON | 创建生产排程权限 |
| production:schedule:update | 更新生产排程 | BUTTON | 更新生产排程权限 |
| production:schedule:delete | 删除生产排程 | BUTTON | 删除生产排程权限 |

### 9.2 权限初始化

系统启动时会自动为admin角色分配所有生产排程管理相关权限。

## 10. 注意事项

### 10.1 乐观锁机制
- 所有更新操作都使用乐观锁机制
- 更新时必须提供正确的版本号
- 版本号不匹配时会返回409错误

### 10.2 逻辑删除
- 系统使用逻辑删除，不会物理删除数据
- 删除的数据通过is_deleted字段标识

### 10.3 关联数据处理
- 排程明细关联销售订单明细时，会自动填充相关信息
- 销售订单信息变更不会自动同步到排程单，需要手动更新

### 10.4 数据完整性
- 创建排程单时会自动生成排程单号
- 排程单号具有唯一性约束
- 明细数据的排程数量必须大于0