本次排程数	
已排程数	

从销售模块sales_order_item表获取
生产单号	
客户编码	
客户名称	
客户订单号	
客方货号	
品名	
[工艺要求]	注意这个字段直接从sales_order_item表中的process_requirements字段获取【不需要xxx-xxx-xxx这样特殊处理】
盒式	
纸质	
生产纸质	
规格	动态字段由length x width x height size_unit 长x宽x高 单位组成 示例：100x200x300 cm
订单日期	
交期	

从采购模块purchase_order_item表获取
已采购数	quantity字段获取
到料日期	stock_inbound表中的inbound_date字段获取
已到料数	stock_inbound_item表中的quantity字段获取
订单类型	默认为“销售订单”
销售单号	从销售模块sales_order表获取
[是否含税]	复选框 从销售模块sales_order_item表获取
纸板类型	从销售模块sales_order_item表中的paper_type(纸质)字段对应的“纸质类别”

从销售模块sales_order_item表获取
长	
宽	
高	
生产长	
生产宽	
生产高	


已送货数 暂时忽略这个字段	
成品入库 暂时忽略这个字段
工序	这个字段参考现有“销售订单”查询接口中的“工序”实现逻辑，需要特殊处理格式为例如：开槽→打角→打钉


订单数	从销售模块sales_order_item表获取
备品数	从销售模块sales_order_item表获取

从销售模块sales_order_item表获取	
纸度	
纸长	
实际用料宽	
实际用料长	
实际用料单位	

	
原料规格	从销售模块sales_order_material表获取paper_width和paper_length，示例 51 x 26 inch	
未送货数	暂时默认为0
成品库存数	暂时默认为0
生产备注	从销售模块sales_order_item表production_remark字段获取