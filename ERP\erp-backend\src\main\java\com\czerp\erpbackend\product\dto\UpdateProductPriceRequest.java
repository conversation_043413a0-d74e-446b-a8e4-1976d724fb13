package com.czerp.erpbackend.product.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 更新产品价格请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateProductPriceRequest {
    
    /**
     * 价格项列表
     */
    @NotEmpty(message = "价格项列表不能为空")
    @Valid
    private List<PriceItem> items;
    
    /**
     * 价格项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceItem {
        
        /**
         * 产品ID
         */
        @NotNull(message = "产品ID不能为空")
        private String id;
        
        /**
         * 价格
         */
        @NotNull(message = "价格不能为空")
        private BigDecimal price;
    }
}
