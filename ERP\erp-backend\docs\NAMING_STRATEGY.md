# 命名策略和数据库字段命名规范

## 问题背景

我们在项目中遇到了一个问题：数据库表中出现了重复的列，例如同时存在 `created_by` 和 `CreatedBy`、`is_deleted` 和 `IsDeleted` 等。这些重复列不仅浪费存储空间，还可能导致数据不一致和应用程序错误。

## 问题原因

这个问题的根本原因是：

1. **命名策略配置**：我们之前使用了 `PhysicalNamingStrategyStandardImpl`，它不会对实体类的字段名进行任何转换，而是直接使用 `@Column` 注解指定的名称作为数据库列名。

2. **重写审计字段**：在某些实体类中，我们重写了 `BaseEntity` 类中的审计字段方法（如 `getCreatedBy()`、`getIsDeleted()` 等），并使用了不同的列名。

3. **Hibernate 自动创建表**：当 Hibernate 自动创建或更新表结构时（`ddl-auto: update`），它会检测到这些不同的列名，并创建多个独立的列。

## 解决方案

为了解决这个问题，我们采取了以下措施：

### 1. 自定义命名策略

我们创建了一个自定义的命名策略类 `CustomPhysicalNamingStrategy`，它会将驼峰命名转换为小写下划线命名。这样，无论实体类中的字段名是什么，最终在数据库中都会使用小写下划线命名。

```java
public class CustomPhysicalNamingStrategy implements PhysicalNamingStrategy {
    // ...
    private Identifier apply(Identifier name) {
        if (name == null) {
            return null;
        }

        // 获取原始名称
        String text = name.getText();
        
        // 如果已经是小写下划线命名，直接返回
        if (text.equals(text.toLowerCase(Locale.ROOT)) && text.contains("_")) {
            return name;
        }
        
        // 将驼峰命名转换为下划线命名
        String newName = PATTERN_UNDERSCORE.matcher(text).replaceAll(REPLACE_UNDERSCORE).toLowerCase(Locale.ROOT);
        
        // 如果名称没有变化，直接返回原始标识符
        if (text.equals(newName)) {
            return name;
        }
        
        // 创建新的标识符
        return Identifier.toIdentifier(newName, name.isQuoted());
    }
}
```

### 2. 配置命名策略

在 `application.yml` 文件中，我们配置了自定义命名策略：

```yaml
spring:
  jpa:
    hibernate:
      naming:
        physical-strategy: com.czerp.erpbackend.config.CustomPhysicalNamingStrategy
```

### 3. 移除重写的审计字段

我们移除了实体类中重写的审计字段方法和对应的 `@Column` 注解，确保只有一个地方定义列名。

### 4. 统一命名规范

我们统一了命名规范，确保所有实体类都使用相同的命名规范：

- 实体类字段名使用驼峰命名法（camelCase）
- 数据库列名使用小写下划线命名法（snake_case）

## 最佳实践

为了避免类似问题再次发生，我们应该遵循以下最佳实践：

### 1. 使用自定义命名策略

始终使用自定义命名策略，确保数据库列名的一致性。

### 2. 不要重写审计字段

不要在实体类中重写 `BaseEntity` 类中的审计字段方法，如 `getCreatedBy()`、`getIsDeleted()` 等。

### 3. 使用数据库迁移工具

使用 Flyway 或 Liquibase 等数据库迁移工具来管理数据库结构变更，而不是依赖 Hibernate 的自动创建表功能。

### 4. 检查现有代码

定期检查实体类，确保没有重写审计字段。定期检查数据库表结构，确保没有重复的列。

## 结论

通过使用自定义命名策略和统一命名规范，我们可以避免数据库中出现重复列的问题，确保数据库结构的一致性和应用程序的稳定性。
