# ERP 系统后端接口使用指南

## 基本信息

- **基础 URL**: `http://localhost:8080/api`
- **开发环境**: Spring Boot 3.3.10, Java 21
- **数据格式**: JSON

## 安全认证

### 开发环境临时认证

系统启动时会在日志中显示自动生成的安全密码，如：
```
Using generated security password: 8bb8f4e9-b338-46ec-b03d-82e305837dba
```

这是 Spring Security 自动生成的临时密码，使用方式如下：

- **认证方式**: HTTP Basic Authentication
- **用户名**: `user`
- **密码**: 日志中显示的生成密码

> **注意**: 每次应用重启后，密码会重新生成，需要从日志中获取新密码。这只适用于开发环境，不应在生产环境中使用。

### 生产环境建议

在生产环境中，建议配置以下安全措施：

1. **配置固定的用户名和密码**
2. **实现 JWT 认证**
3. **配置 CORS 策略**
4. **启用 HTTPS**

## API 调用注意事项

1. **认证头**: 所有请求需要包含 Basic Authentication 头
2. **内容类型**: 请求和响应均使用 `application/json`
3. **分页参数**: 使用 `page`（从0开始）和 `size` 参数
4. **排序参数**: 使用 `sortField` 和 `sortDirection` 参数

## 主要 API 接口

### 1. 用户管理接口

#### 1.1 获取用户列表

- **URL**: `/api/users`
- **Method**: `GET`
- **权限**: `user:list`
- **参数**:
  - `keyword`: 关键词(用户名/姓名/邮箱)
  - `status`: 状态(active/inactive/locked)
  - `department`: 部门ID
  - `page`: 页码(从0开始)
  - `size`: 每页条数

#### 1.2 获取用户详情

- **URL**: `/api/users/{id}`
- **Method**: `GET`
- **权限**: `user:view`

#### 1.3 创建用户

- **URL**: `/api/users`
- **Method**: `POST`
- **权限**: `user:create`
- **请求参数**:
  - `username`: 用户名
  - `nickname`: 姓名
  - `password`: 密码
  - `email`: 邮箱
  - `phone`: 手机号
  - `department`: 部门ID
  - `position`: 职位
  - `roles`: 角色ID列表
  - `status`: 状态

#### 1.4 更新用户

- **URL**: `/api/users/{id}`
- **Method**: `PUT`
- **权限**: `user:update`
- **请求参数**:
  - `nickname`: 姓名
  - `email`: 邮箱
  - `phone`: 手机号
  - `department`: 部门ID
  - `position`: 职位
  - `roles`: 角色ID列表
  - `status`: 状态

### 2. 角色管理接口

#### 2.1 获取角色列表

- **URL**: `/api/roles`
- **Method**: `GET`
- **权限**: `role:list`
- **参数**:
  - `keyword`: 关键词(名称/编码)
  - `status`: 状态(active/inactive)
  - `page`: 页码
  - `size`: 每页条数

#### 2.2 获取角色详情

- **URL**: `/api/roles/{id}`
- **Method**: `GET`
- **权限**: `role:view`

#### 2.3 创建角色

- **URL**: `/api/roles`
- **Method**: `POST`
- **权限**: `role:create`
- **请求参数**:
  - `name`: 角色名称
  - `code`: 角色编码
  - `description`: 角色描述
  - `permissions`: 权限ID列表
  - `status`: 状态

#### 2.4 更新角色

- **URL**: `/api/roles/{id}`
- **Method**: `PUT`
- **权限**: `role:update`
- **请求参数**:
  - `name`: 角色名称
  - `description`: 角色描述
  - `permissions`: 权限ID列表
  - `status`: 状态

### 3. 部门管理接口

#### 3.1 获取部门树

- **URL**: `/api/departments/tree`
- **Method**: `GET`
- **权限**: `department:list`

#### 3.2 获取部门详情

- **URL**: `/api/departments/{id}`
- **Method**: `GET`
- **权限**: `department:view`

#### 3.3 创建部门

- **URL**: `/api/departments`
- **Method**: `POST`
- **权限**: `department:create`
- **请求参数**:
  - `name`: 部门名称
  - `code`: 部门编码
  - `parentId`: 父部门ID
  - `managerId`: 部门负责人ID
  - `sort`: 排序
  - `status`: 状态

#### 3.4 更新部门

- **URL**: `/api/departments/{id}`
- **Method**: `PUT`
- **权限**: `department:update`
- **请求参数**:
  - `name`: 部门名称
  - `parentId`: 父部门ID
  - `managerId`: 部门负责人ID
  - `status`: 状态

## 响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": { ... }
}
```

### 分页响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 100,
    "items": [ ... ]
  }
}
```

### 错误响应

```json
{
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

## 常见状态码

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 最佳实践

1. **保存认证信息**: 在前端保存认证信息，避免每次请求都需要用户重新输入
2. **错误处理**: 妥善处理 API 返回的错误信息，并向用户展示友好的提示
3. **加载状态**: 在请求过程中显示加载状态，提升用户体验
4. **数据缓存**: 适当缓存不常变化的数据，减少请求次数
5. **请求防抖**: 对于搜索等频繁操作，实现请求防抖，避免过多请求

## 安全建议

1. **不要在前端存储敏感信息**
2. **实现 CSRF 保护**
3. **定期更换认证令牌**
4. **限制 API 请求频率**
5. **验证所有用户输入**

## 常见问题

### Q: 如何处理认证过期?
A: 监听 401 响应，引导用户重新登录。

### Q: 如何处理大量数据?
A: 使用分页加载，或实现虚拟滚动。

### Q: 如何优化请求性能?
A: 合理使用缓存，减少不必要的请求，使用批量操作。
