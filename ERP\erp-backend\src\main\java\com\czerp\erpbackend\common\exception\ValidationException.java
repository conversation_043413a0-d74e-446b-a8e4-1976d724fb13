package com.czerp.erpbackend.common.exception;

/**
 * 数据验证异常
 */
public class ValidationException extends BusinessException {
    
    public ValidationException(String message) {
        super(SystemErrorCode.DATA_VALIDATION_ERROR.getCode(), message);
    }
    
    public ValidationException(String field, String message) {
        super(SystemErrorCode.DATA_VALIDATION_ERROR.getCode(), String.format("Field '%s' validation error: %s", field, message));
    }
}
