package com.czerp.erpbackend.warehouse.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.warehouse.dto.CreateWarehouseRequest;
import com.czerp.erpbackend.warehouse.dto.UpdateWarehouseRequest;
import com.czerp.erpbackend.warehouse.dto.WarehouseDTO;
import com.czerp.erpbackend.warehouse.dto.WarehouseQueryRequest;
import com.czerp.erpbackend.warehouse.entity.Warehouse;
import com.czerp.erpbackend.warehouse.repository.WarehouseRepository;
import com.czerp.erpbackend.warehouse.service.WarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WarehouseServiceImpl implements WarehouseService {

    private final WarehouseRepository warehouseRepository;

    /**
     * 分页查询仓库列表
     * @param request 查询请求
     * @return 仓库分页列表
     */
    @Override
    public PageResponse<WarehouseDTO> findWarehouses(WarehouseQueryRequest request) {
        log.debug("Finding warehouses with request: {}", request);

        // 构建分页参数
        Sort sort = buildSort(request.getSortBy(), request.getSortDirection());
        Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize(), sort);

        // 执行查询
        Page<Warehouse> warehousePage = warehouseRepository.search(
                request.getKeyword(),
                request.getIsMaterialWarehouse(),
                pageable
        );

        // 转换为DTO
        List<WarehouseDTO> warehouseDTOs = warehousePage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        return PageResponse.<WarehouseDTO>builder()
                .content(warehouseDTOs)
                .totalElements(warehousePage.getTotalElements())
                .totalPages(warehousePage.getTotalPages())
                .page(request.getPage())
                .size(request.getSize())
                .first(warehousePage.isFirst())
                .last(warehousePage.isLast())
                .empty(warehousePage.isEmpty())
                .build();
    }

    /**
     * 获取所有仓库列表（不分页）
     * @return 仓库列表
     */
    @Override
    public List<WarehouseDTO> findAllWarehouses() {
        log.debug("Finding all warehouses");

        List<Warehouse> warehouses = warehouseRepository.findByIsDeletedFalseOrderByCreatedTimeDesc();
        return warehouses.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID查询仓库
     * @param id 仓库ID
     * @return 仓库信息
     */
    @Override
    public WarehouseDTO findWarehouseById(Long id) {
        log.debug("Finding warehouse by id: {}", id);

        Warehouse warehouse = getWarehouseById(id);
        return convertToDTO(warehouse);
    }

    /**
     * 创建仓库
     * @param request 创建仓库请求
     * @return 仓库信息
     */
    @Override
    @Transactional
    public WarehouseDTO createWarehouse(CreateWarehouseRequest request) {
        log.debug("Creating warehouse with request: {}", request);

        // 检查仓库名称是否已存在
        if (warehouseRepository.existsByWarehouseNameAndIsDeletedFalse(request.getWarehouseName())) {
            throw new BusinessException("仓库名称已存在");
        }

        // 创建仓库实体
        Warehouse warehouse = new Warehouse();
        warehouse.setWarehouseName(request.getWarehouseName());
        warehouse.setIsMaterialWarehouse(request.getIsMaterialWarehouse());

        // 保存仓库
        warehouse = warehouseRepository.save(warehouse);

        return convertToDTO(warehouse);
    }

    /**
     * 更新仓库
     * @param id 仓库ID
     * @param request 更新仓库请求
     * @return 仓库信息
     */
    @Override
    @Transactional
    public WarehouseDTO updateWarehouse(Long id, UpdateWarehouseRequest request) {
        log.debug("Updating warehouse with id: {} and request: {}", id, request);

        // 查询仓库
        Warehouse warehouse = getWarehouseById(id);

        // 检查仓库名称是否已存在（排除当前仓库）
        if (StringUtils.hasText(request.getWarehouseName()) &&
                warehouseRepository.existsByWarehouseNameAndIsDeletedFalseAndIdNot(request.getWarehouseName(), id)) {
            throw new BusinessException("仓库名称已存在");
        }

        // 更新仓库信息
        if (StringUtils.hasText(request.getWarehouseName())) {
            warehouse.setWarehouseName(request.getWarehouseName());
        }
        if (request.getIsMaterialWarehouse() != null) {
            warehouse.setIsMaterialWarehouse(request.getIsMaterialWarehouse());
        }

        // 保存仓库
        warehouse = warehouseRepository.save(warehouse);

        return convertToDTO(warehouse);
    }

    /**
     * 删除仓库
     * @param id 仓库ID
     */
    @Override
    @Transactional
    public void deleteWarehouse(Long id) {
        log.debug("Deleting warehouse with id: {}", id);

        // 查询仓库
        Warehouse warehouse = getWarehouseById(id);

        // 逻辑删除
        warehouse.setIsDeleted(true);
        warehouseRepository.save(warehouse);
    }

    /**
     * 批量删除仓库
     * @param ids 仓库ID列表
     */
    @Override
    @Transactional
    public void deleteWarehouses(List<Long> ids) {
        log.debug("Deleting warehouses with ids: {}", ids);

        for (Long id : ids) {
            deleteWarehouse(id);
        }
    }

    /**
     * 检查仓库名称是否存在
     * @param warehouseName 仓库名称
     * @return 是否存在
     */
    @Override
    public boolean existsByWarehouseName(String warehouseName) {
        return warehouseRepository.existsByWarehouseNameAndIsDeletedFalse(warehouseName);
    }

    /**
     * 检查仓库名称是否存在（排除指定ID）
     * @param warehouseName 仓库名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    @Override
    public boolean existsByWarehouseName(String warehouseName, Long excludeId) {
        return warehouseRepository.existsByWarehouseNameAndIsDeletedFalseAndIdNot(warehouseName, excludeId);
    }

    /**
     * 根据ID获取仓库实体
     * @param id 仓库ID
     * @return 仓库实体
     */
    private Warehouse getWarehouseById(Long id) {
        return warehouseRepository.findByIdAndIsDeletedFalse(id)
                .orElseThrow(() -> new BusinessException("仓库不存在"));
    }

    /**
     * 构建排序对象
     * @param sortBy 排序字段
     * @param sortDirection 排序方向
     * @return 排序对象
     */
    private Sort buildSort(String sortBy, String sortDirection) {
        Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) ? Sort.Direction.ASC : Sort.Direction.DESC;
        return Sort.by(direction, sortBy);
    }

    /**
     * 转换为DTO
     * @param warehouse 仓库实体
     * @return 仓库DTO
     */
    private WarehouseDTO convertToDTO(Warehouse warehouse) {
        return WarehouseDTO.builder()
                .id(warehouse.getId())
                .warehouseName(warehouse.getWarehouseName())
                .isMaterialWarehouse(warehouse.getIsMaterialWarehouse())
                .createdBy(warehouse.getCreatedBy())
                .createdTime(warehouse.getCreatedTime())
                .updatedBy(warehouse.getUpdatedBy())
                .updatedTime(warehouse.getUpdatedTime())
                .version(warehouse.getVersion())
                .build();
    }
}
