-- 添加计量单位模块权限

-- 1. 添加计量单位模块菜单
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, create_by, create_time)
SELECT
    UUID(),
    (SELECT id FROM sys_permission WHERE code = 'system' AND type = 'MENU' LIMIT 1),
    'system:measurement-unit',
    '计量单位管理',
    'MENU',
    '/system/measurement-unit',
    'system/MeasurementUnit',
    'ruler',
    50,
    'active',
    'system',
    NOW()
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'system:measurement-unit');

-- 2. 添加计量单位模块按钮权限
-- 查询权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, create_by, create_time)
SELECT
    UUID(),
    (SELECT id FROM sys_permission WHERE code = 'system:measurement-unit' LIMIT 1),
    'system:measurement-unit:list',
    '计量单位查询',
    'BUTTON',
    NULL,
    NULL,
    NULL,
    10,
    'active',
    'system',
    NOW()
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'system:measurement-unit:list');

-- 详情权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, create_by, create_time)
SELECT
    UUID(),
    (SELECT id FROM sys_permission WHERE code = 'system:measurement-unit' LIMIT 1),
    'system:measurement-unit:read',
    '计量单位详情',
    'BUTTON',
    NULL,
    NULL,
    NULL,
    20,
    'active',
    'system',
    NOW()
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'system:measurement-unit:read');

-- 创建权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, create_by, create_time)
SELECT
    UUID(),
    (SELECT id FROM sys_permission WHERE code = 'system:measurement-unit' LIMIT 1),
    'system:measurement-unit:create',
    '计量单位创建',
    'BUTTON',
    NULL,
    NULL,
    NULL,
    30,
    'active',
    'system',
    NOW()
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'system:measurement-unit:create');

-- 更新权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, create_by, create_time)
SELECT
    UUID(),
    (SELECT id FROM sys_permission WHERE code = 'system:measurement-unit' LIMIT 1),
    'system:measurement-unit:update',
    '计量单位更新',
    'BUTTON',
    NULL,
    NULL,
    NULL,
    40,
    'active',
    'system',
    NOW()
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'system:measurement-unit:update');

-- 删除权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, create_by, create_time)
SELECT
    UUID(),
    (SELECT id FROM sys_permission WHERE code = 'system:measurement-unit' LIMIT 1),
    'system:measurement-unit:delete',
    '计量单位删除',
    'BUTTON',
    NULL,
    NULL,
    NULL,
    50,
    'active',
    'system',
    NOW()
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'system:measurement-unit:delete');

-- 3. 为admin角色分配计量单位模块所有权限
INSERT INTO sys_role_permission (id, role_id, permission_id, create_by, create_time)
SELECT
    UUID(),
    (SELECT id FROM sys_role WHERE code = 'admin' LIMIT 1),
    p.id,
    'system',
    NOW()
FROM
    sys_permission p
WHERE
    p.code LIKE 'system:measurement-unit%'
    AND NOT EXISTS (
        SELECT 1 FROM sys_role_permission rp
        JOIN sys_role r ON rp.role_id = r.id
        WHERE r.code = 'admin' AND rp.permission_id = p.id
    );
