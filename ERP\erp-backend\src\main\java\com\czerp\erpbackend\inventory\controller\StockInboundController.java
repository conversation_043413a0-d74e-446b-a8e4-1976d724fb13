package com.czerp.erpbackend.inventory.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.inventory.dto.CreateStockInboundRequest;
import com.czerp.erpbackend.inventory.dto.StockInboundDTO;
import com.czerp.erpbackend.inventory.dto.StockInboundItemDTO;
import com.czerp.erpbackend.inventory.dto.StockInboundQueryRequest;
import com.czerp.erpbackend.inventory.dto.UpdateStockInboundRequest;
import com.czerp.erpbackend.inventory.service.StockInboundService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 入库单控制器
 */
@RestController
@RequestMapping("/stock-inbounds")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Stock Inbound Management", description = "入库单管理相关接口")
public class StockInboundController {

    private final StockInboundService stockInboundService;

    /**
     * 分页查询入库单（按订单级别分页）
     * @param request 查询请求
     * @return 入库单分页列表
     */
    @GetMapping
    @Operation(summary = "分页查询入库单", description = "按订单级别分页查询入库单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:read')")
    public ResponseEntity<ApiResponse<PageResponse<StockInboundDTO>>> findStockInbounds(StockInboundQueryRequest request) {
        log.info("Finding stock inbounds with request: {}", request);
        PageResponse<StockInboundDTO> stockInbounds = stockInboundService.findStockInbounds(request);
        return ResponseEntity.ok(ApiResponse.success(stockInbounds));
    }

    /**
     * 分页查询入库单明细（按明细行级别分页）- 使用优化查询
     * @param request 查询请求
     * @return 入库单明细分页列表
     */
    @GetMapping("/items")
    @Operation(summary = "分页查询入库单明细", description = "按明细行级别分页查询入库单明细，用于前端表格显示（使用优化查询）")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:read')")
    public ResponseEntity<ApiResponse<PageResponse<StockInboundItemDTO>>> findStockInboundItems(StockInboundQueryRequest request) {
        log.info("Finding stock inbound items with request: {}", request);
        PageResponse<StockInboundItemDTO> stockInboundItems = stockInboundService.findStockInboundItems(request);
        return ResponseEntity.ok(ApiResponse.success(stockInboundItems));
    }

    /**
     * 分页查询入库单明细（优化版本）- 性能测试用
     * @param request 查询请求
     * @return 入库单明细分页列表
     */
    @GetMapping("/items/optimized")
    @Operation(summary = "分页查询入库单明细（优化版本）", description = "使用JOIN查询优化性能，避免N+1查询问题")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:read')")
    public ResponseEntity<ApiResponse<PageResponse<StockInboundItemDTO>>> findStockInboundItemsOptimized(StockInboundQueryRequest request) {
        log.info("Finding stock inbound items with optimized query, request: {}", request);
        long startTime = System.currentTimeMillis();
        PageResponse<StockInboundItemDTO> stockInboundItems = stockInboundService.findStockInboundItemsOptimized(request);
        long endTime = System.currentTimeMillis();
        log.info("Optimized query completed in {} ms, found {} items", endTime - startTime, stockInboundItems.getTotalElements());
        return ResponseEntity.ok(ApiResponse.success(stockInboundItems));
    }

    /**
     * 分页查询入库单明细（原始版本）- 性能对比用
     * @param request 查询请求
     * @return 入库单明细分页列表
     */
    @GetMapping("/items/original")
    @Operation(summary = "分页查询入库单明细（原始版本）", description = "原始查询方法，用于性能对比测试")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:read')")
    public ResponseEntity<ApiResponse<PageResponse<StockInboundItemDTO>>> findStockInboundItemsOriginal(StockInboundQueryRequest request) {
        log.info("Finding stock inbound items with original query, request: {}", request);
        long startTime = System.currentTimeMillis();
        PageResponse<StockInboundItemDTO> stockInboundItems = stockInboundService.findStockInboundItemsOriginal(request);
        long endTime = System.currentTimeMillis();
        log.info("Original query completed in {} ms, found {} items", endTime - startTime, stockInboundItems.getTotalElements());
        return ResponseEntity.ok(ApiResponse.success(stockInboundItems));
    }

    /**
     * 生成入库单号
     * @return 入库单号
     */
    @GetMapping("/generate-inbound-no")
    @Operation(summary = "生成入库单号", description = "生成新的入库单号")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:create')")
    public ResponseEntity<ApiResponse<String>> generateInboundNo() {
        log.info("Generating inbound no");
        String inboundNo = stockInboundService.generateInboundNo();
        return ResponseEntity.ok(ApiResponse.success(inboundNo));
    }

    /**
     * 创建入库单
     * @param request 创建请求
     * @return 入库单
     */
    @PostMapping
    @Operation(summary = "创建入库单", description = "创建入库单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:create')")
    public ResponseEntity<ApiResponse<StockInboundDTO>> createStockInbound(@Valid @RequestBody CreateStockInboundRequest request) {
        log.info("Creating stock inbound with request: {}", request);
        StockInboundDTO stockInbound = stockInboundService.createStockInbound(request);
        return ResponseEntity.ok(ApiResponse.success(stockInbound));
    }

    /**
     * 更新入库单
     * @param id 入库单ID
     * @param request 更新请求
     * @return 入库单
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新入库单", description = "更新入库单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:update')")
    public ResponseEntity<ApiResponse<StockInboundDTO>> updateStockInbound(
            @PathVariable Long id,
            @Valid @RequestBody UpdateStockInboundRequest request) {
        log.info("Updating stock inbound with ID: {} and request: {}", id, request);
        StockInboundDTO stockInbound = stockInboundService.updateStockInbound(id, request);
        return ResponseEntity.ok(ApiResponse.success(stockInbound));
    }

    /**
     * 根据ID查询入库单
     * @param id 入库单ID
     * @return 入库单
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询入库单", description = "根据ID查询入库单详情")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:read')")
    public ResponseEntity<ApiResponse<StockInboundDTO>> getStockInboundById(@PathVariable Long id) {
        log.info("Getting stock inbound by ID: {}", id);
        StockInboundDTO stockInbound = stockInboundService.getStockInboundById(id);
        return ResponseEntity.ok(ApiResponse.success(stockInbound));
    }

    /**
     * 根据入库单号查询入库单
     * @param inboundNo 入库单号
     * @return 入库单
     */
    @GetMapping("/by-inbound-no/{inboundNo}")
    @Operation(summary = "根据入库单号查询入库单", description = "根据入库单号查询入库单详情")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:read')")
    public ResponseEntity<ApiResponse<StockInboundDTO>> getStockInboundByNo(@PathVariable String inboundNo) {
        log.info("Getting stock inbound by inbound no: {}", inboundNo);
        StockInboundDTO stockInbound = stockInboundService.getStockInboundByNo(inboundNo);
        return ResponseEntity.ok(ApiResponse.success(stockInbound));
    }

    /**
     * 删除入库单
     * @param id 入库单ID
     * @return 成功响应
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除入库单", description = "删除入库单（软删除）")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('inventory:inbound:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteStockInbound(@PathVariable Long id) {
        log.info("Deleting stock inbound with ID: {}", id);
        stockInboundService.deleteStockInbound(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
}
