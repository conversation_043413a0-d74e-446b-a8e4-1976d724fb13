package com.czerp.erpbackend.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

/**
 * Excel工具类
 */
@Slf4j
public class ExcelUtil {

    /**
     * 从Excel文件中读取数据
     *
     * @param file Excel文件
     * @param rowMapper 行映射函数
     * @param <T> 目标类型
     * @return 数据列表
     * @throws IOException 如果读取文件失败
     */
    public static <T> List<T> readExcel(MultipartFile file, Function<Row, T> rowMapper) throws IOException {
        return readExcel(file, rowMapper, 1); // 默认从第2行开始（跳过标题行）
    }

    /**
     * 从Excel文件中读取数据
     *
     * @param file Excel文件
     * @param rowMapper 行映射函数
     * @param startRowIndex 开始行索引（0-based）
     * @param <T> 目标类型
     * @return 数据列表
     * @throws IOException 如果读取文件失败
     */
    public static <T> List<T> readExcel(MultipartFile file, Function<Row, T> rowMapper, int startRowIndex) throws IOException {
        validateExcelFile(file);
        
        List<T> resultList = new ArrayList<>();
        
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
            
            for (int i = startRowIndex; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                
                T item = rowMapper.apply(row);
                if (item != null) {
                    resultList.add(item);
                }
            }
        }
        
        return resultList;
    }
    
    /**
     * 验证Excel文件
     *
     * @param file 文件
     * @throws IOException 如果文件无效
     */
    private static void validateExcelFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IOException("文件为空");
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null || !(filename.endsWith(".xlsx") || filename.endsWith(".xls"))) {
            throw new IOException("文件格式不正确，只支持.xlsx或.xls格式");
        }
    }
    
    /**
     * 从单元格获取字符串值
     *
     * @param row 行
     * @param columnIndex 列索引
     * @return 字符串值，如果单元格为空则返回null
     */
    public static String getStringCellValue(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            return null;
        }
        
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue();
    }
    
    /**
     * 从单元格获取整数值
     *
     * @param row 行
     * @param columnIndex 列索引
     * @return 整数值，如果单元格为空或不是数字则返回null
     */
    public static Integer getIntegerCellValue(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            return null;
        }
        
        try {
            cell.setCellType(CellType.NUMERIC);
            double value = cell.getNumericCellValue();
            return (int) value;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 从单元格获取BigDecimal值
     *
     * @param row 行
     * @param columnIndex 列索引
     * @return BigDecimal值，如果单元格为空或不是数字则返回null
     */
    public static BigDecimal getBigDecimalCellValue(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            return null;
        }
        
        try {
            cell.setCellType(CellType.NUMERIC);
            double value = cell.getNumericCellValue();
            return BigDecimal.valueOf(value);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 从单元格获取布尔值
     *
     * @param row 行
     * @param columnIndex 列索引
     * @return 布尔值，如果单元格为空则返回null
     */
    public static Boolean getBooleanCellValue(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            return null;
        }
        
        try {
            cell.setCellType(CellType.BOOLEAN);
            return cell.getBooleanCellValue();
        } catch (Exception e) {
            // 尝试从字符串解析
            String strValue = getStringCellValue(row, columnIndex);
            if (strValue == null) {
                return null;
            }
            
            strValue = strValue.trim().toLowerCase();
            if ("true".equals(strValue) || "yes".equals(strValue) || "1".equals(strValue) || "是".equals(strValue)) {
                return true;
            } else if ("false".equals(strValue) || "no".equals(strValue) || "0".equals(strValue) || "否".equals(strValue)) {
                return false;
            }
            
            return null;
        }
    }
    
    /**
     * 从单元格获取日期值
     *
     * @param row 行
     * @param columnIndex 列索引
     * @return 日期值，如果单元格为空则返回null
     */
    public static LocalDateTime getDateTimeCellValue(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            return null;
        }
        
        try {
            if (cell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell)) {
                Date date = cell.getDateCellValue();
                return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            }
        } catch (Exception e) {
            return null;
        }
        
        return null;
    }
    
    /**
     * 创建Excel工作簿
     *
     * @return 工作簿
     */
    public static Workbook createWorkbook() {
        return new XSSFWorkbook();
    }
}
