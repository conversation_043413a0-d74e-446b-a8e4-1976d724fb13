package com.czerp.erpbackend.sales.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.customer.entity.Customer;
import com.czerp.erpbackend.customer.repository.CustomerRepository;
import com.czerp.erpbackend.sales.dto.SalesOrderDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderDetailDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderItemDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryDTO;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderRepository;
import com.czerp.erpbackend.sales.service.impl.SalesOrderServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SalesOrderServiceTest {

    @Mock
    private SalesOrderRepository salesOrderRepository;

    @Mock
    private SalesOrderItemRepository salesOrderItemRepository;

    @Mock
    private CustomerRepository customerRepository;

    @InjectMocks
    private SalesOrderServiceImpl salesOrderService;

    private Customer testCustomer;
    private SalesOrder testOrder;
    private SalesOrderItem testOrderItem;
    private SalesOrderDetailDTO testOrderDetailDTO;
    private SalesOrderItemDTO testOrderItemDTO;
    private List<SalesOrder> testOrders;
    private List<SalesOrderItem> testOrderItems;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testCustomer = new Customer();
        testCustomer.setId("customer1");
        testCustomer.setCustomerCode("C001");
        testCustomer.setCustomerName("测试客户");

        testOrder = new SalesOrder();
        testOrder.setId("order1");
        testOrder.setOrderNo("SO2023060100001");
        testOrder.setCustomerCode(testCustomer.getCustomerCode());
        testOrder.setCustomerName(testCustomer.getCustomerName());
        testOrder.setOrderDate(LocalDate.now());
        testOrder.setPaymentMethod("现金");
        testOrder.setSalesPerson("销售员A");
        testOrder.setCustomerPurchaser("采购员B");
        testOrder.setReceivingUnit("收货单位C");
        testOrder.setReceiver("收货人D");
        testOrder.setReceiverPhone("13800138000");
        testOrder.setReceivingAddress("收货地址E");
        testOrder.setRemark("备注");
        testOrder.setOrderType("普通订单");
        testOrder.setCreatedTime(LocalDateTime.now());
        testOrder.setUpdatedTime(LocalDateTime.now());

        testOrderItem = new SalesOrderItem();
        testOrderItem.setId("item1");
        testOrderItem.setOrder(testOrder);
        testOrderItem.setProductName("测试产品");
        testOrderItem.setQuantity(10);
        testOrderItem.setUnit("个");
        testOrderItem.setPrice(BigDecimal.valueOf(100));
        testOrderItem.setAmount(BigDecimal.valueOf(1000));
        testOrderItem.setCreatedTime(LocalDateTime.now());
        testOrderItem.setUpdatedTime(LocalDateTime.now());

        testOrderItems = new ArrayList<>();
        testOrderItems.add(testOrderItem);
        testOrder.setItems(testOrderItems);

        testOrders = new ArrayList<>();
        testOrders.add(testOrder);

        testOrderItemDTO = new SalesOrderItemDTO();
        testOrderItemDTO.setProductName("测试产品");
        testOrderItemDTO.setQuantity(10);
        testOrderItemDTO.setUnit("个");
        testOrderItemDTO.setPrice(BigDecimal.valueOf(100));
        testOrderItemDTO.setAmount(BigDecimal.valueOf(1000));

        testOrderDetailDTO = new SalesOrderDetailDTO();
        testOrderDetailDTO.setCustomerCode(testCustomer.getCustomerCode());
        testOrderDetailDTO.setCustomerName(testCustomer.getCustomerName());
        testOrderDetailDTO.setOrderDate(LocalDate.now());
        testOrderDetailDTO.setPaymentMethod("现金");
        testOrderDetailDTO.setSalesPerson("销售员A");
        testOrderDetailDTO.setCustomerPurchaser("采购员B");
        testOrderDetailDTO.setReceivingUnit("收货单位C");
        testOrderDetailDTO.setReceiver("收货人D");
        testOrderDetailDTO.setReceiverPhone("13800138000");
        testOrderDetailDTO.setReceivingAddress("收货地址E");
        testOrderDetailDTO.setRemark("备注");
        testOrderDetailDTO.setOrderType("普通订单");
        List<SalesOrderItemDTO> itemDTOs = new ArrayList<>();
        itemDTOs.add(testOrderItemDTO);
        testOrderDetailDTO.setItems(itemDTOs);
    }

    @Test
    void testCreateOrder() {
        // 配置Mock行为
        when(customerRepository.findByCustomerCode(anyString())).thenReturn(Optional.of(testCustomer));
        when(salesOrderRepository.save(any(SalesOrder.class))).thenReturn(testOrder);
        when(salesOrderItemRepository.saveAll(anyList())).thenReturn(testOrderItems);
        when(salesOrderRepository.findMaxOrderNoByPrefix(anyString())).thenReturn(null);

        // 执行测试
        SalesOrderDetailDTO result = salesOrderService.createOrder(testOrderDetailDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(testCustomer.getCustomerCode(), result.getCustomerCode());
        assertEquals(testCustomer.getCustomerName(), result.getCustomerName());
        assertEquals(1, result.getItems().size());

        // 验证方法调用
        verify(customerRepository).findByCustomerCode(testCustomer.getCustomerCode());
        verify(salesOrderRepository, atLeastOnce()).save(any(SalesOrder.class));
        verify(salesOrderItemRepository).saveAll(anyList());
    }

    @Test
    void testCreateOrderWithInvalidCustomer() {
        // 配置Mock行为
        when(customerRepository.findByCustomerCode(anyString())).thenReturn(Optional.empty());

        // 执行测试并验证异常
        assertThrows(BusinessException.class, () -> salesOrderService.createOrder(testOrderDetailDTO));

        // 验证方法调用
        verify(customerRepository).findByCustomerCode(testCustomer.getCustomerCode());
        verify(salesOrderRepository, never()).save(any(SalesOrder.class));
    }

    @Test
    void testGetOrderById() {
        // 配置Mock行为
        when(salesOrderRepository.findById(anyString())).thenReturn(Optional.of(testOrder));
        when(salesOrderItemRepository.findByOrderId(anyString())).thenReturn(testOrderItems);

        // 执行测试
        SalesOrderDetailDTO result = salesOrderService.getOrderById("order1");

        // 验证结果
        assertNotNull(result);
        assertEquals(testOrder.getId(), result.getId());
        assertEquals(testOrder.getOrderNo(), result.getOrderNo());
        assertEquals(testOrder.getCustomerCode(), result.getCustomerCode());
        assertEquals(testOrder.getCustomerName(), result.getCustomerName());
        assertEquals(1, result.getItems().size());

        // 验证方法调用
        verify(salesOrderRepository).findById("order1");
        verify(salesOrderItemRepository).findByOrderId("order1");
    }

    @Test
    void testGetOrderByIdNotFound() {
        // 配置Mock行为
        when(salesOrderRepository.findById(anyString())).thenReturn(Optional.empty());

        // 执行测试并验证异常
        assertThrows(BusinessException.class, () -> salesOrderService.getOrderById("nonexistent"));

        // 验证方法调用
        verify(salesOrderRepository).findById("nonexistent");
        verify(salesOrderItemRepository, never()).findByOrderId(anyString());
    }

    @Test
    void testGetOrderByOrderNo() {
        // 配置Mock行为
        when(salesOrderRepository.findByOrderNo(anyString())).thenReturn(Optional.of(testOrder));
        when(salesOrderItemRepository.findByOrderId(anyString())).thenReturn(testOrderItems);

        // 执行测试
        SalesOrderDetailDTO result = salesOrderService.getOrderByOrderNo("SO2023060100001");

        // 验证结果
        assertNotNull(result);
        assertEquals(testOrder.getId(), result.getId());
        assertEquals(testOrder.getOrderNo(), result.getOrderNo());
        assertEquals(testOrder.getCustomerCode(), result.getCustomerCode());
        assertEquals(testOrder.getCustomerName(), result.getCustomerName());
        assertEquals(1, result.getItems().size());

        // 验证方法调用
        verify(salesOrderRepository).findByOrderNo("SO2023060100001");
        verify(salesOrderItemRepository).findByOrderId(testOrder.getId());
    }

    @Test
    void testGetOrders() {
        // 准备测试数据
        SalesOrderQueryDTO queryDTO = new SalesOrderQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);

        Page<SalesOrder> page = new PageImpl<>(testOrders);

        // 配置Mock行为
        when(salesOrderRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // 执行测试
        PageResponse<SalesOrderDTO> result = salesOrderService.getOrders(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(testOrder.getId(), result.getContent().get(0).getId());
        assertEquals(testOrder.getOrderNo(), result.getContent().get(0).getOrderNo());
        assertEquals(testOrder.getCustomerCode(), result.getContent().get(0).getCustomerCode());
        assertEquals(testOrder.getCustomerName(), result.getContent().get(0).getCustomerName());

        // 验证方法调用
        verify(salesOrderRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void testDeleteOrder() {
        // 配置Mock行为
        when(salesOrderRepository.findById(anyString())).thenReturn(Optional.of(testOrder));
        when(salesOrderItemRepository.findByOrderId(anyString())).thenReturn(testOrderItems);

        // 执行测试
        salesOrderService.deleteOrder("order1");

        // 验证方法调用
        verify(salesOrderRepository).findById("order1");
        verify(salesOrderItemRepository).findByOrderId("order1");
        verify(salesOrderItemRepository).deleteAll(anyList());
        verify(salesOrderRepository).delete(any(SalesOrder.class));
    }

    // 移除与订单终止和恢复相关的测试方法

    @Test
    void testUpdateOrder() {
        // 准备测试数据
        SalesOrderDetailDTO updateDTO = new SalesOrderDetailDTO();
        updateDTO.setCustomerCode(testCustomer.getCustomerCode());
        updateDTO.setRemark("更新的备注");
        updateDTO.setPaymentMethod("银行转账");

        SalesOrderItemDTO updatedItemDTO = new SalesOrderItemDTO();
        updatedItemDTO.setId(testOrderItem.getId());
        updatedItemDTO.setProductName("更新的产品名称");
        updatedItemDTO.setQuantity(20);
        updatedItemDTO.setPrice(BigDecimal.valueOf(120));
        updatedItemDTO.setAmount(BigDecimal.valueOf(2400));

        List<SalesOrderItemDTO> updatedItems = new ArrayList<>();
        updatedItems.add(updatedItemDTO);
        updateDTO.setItems(updatedItems);

        // 配置Mock行为
        when(salesOrderRepository.findById(anyString())).thenReturn(Optional.of(testOrder));
        when(customerRepository.findByCustomerCode(anyString())).thenReturn(Optional.of(testCustomer));
        when(salesOrderRepository.save(any(SalesOrder.class))).thenReturn(testOrder);
        when(salesOrderItemRepository.findByOrderId(anyString())).thenReturn(testOrderItems);
        when(salesOrderItemRepository.saveAll(anyList())).thenReturn(testOrderItems);

        // 执行测试
        SalesOrderDetailDTO result = salesOrderService.updateOrder("order1", updateDTO);

        // 验证结果
        assertNotNull(result);

        // 捕获保存的订单对象
        ArgumentCaptor<SalesOrder> orderCaptor = ArgumentCaptor.forClass(SalesOrder.class);
        verify(salesOrderRepository, atLeastOnce()).save(orderCaptor.capture());

        // 验证订单信息被更新
        // 获取第一次捕获的值，这应该是updateOrder方法中保存的订单
        SalesOrder savedOrder = orderCaptor.getAllValues().get(0);
        assertEquals("更新的备注", savedOrder.getRemark());
        assertEquals("银行转账", savedOrder.getPaymentMethod());

        // 验证方法调用
        verify(salesOrderRepository).findById("order1");
        verify(customerRepository).findByCustomerCode(testCustomer.getCustomerCode());
        verify(salesOrderItemRepository, atLeastOnce()).findByOrderId("order1");
        verify(salesOrderItemRepository).saveAll(anyList());
    }

    @Test
    void testGetOrdersByCustomerCode() {
        // 配置Mock行为
        Page<SalesOrder> page = new PageImpl<>(testOrders);
        when(salesOrderRepository.findByCustomerCode(anyString(), any(Pageable.class))).thenReturn(page);

        // 执行测试
        List<SalesOrderDTO> result = salesOrderService.getOrdersByCustomerCode("C001");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testOrder.getId(), result.get(0).getId());
        assertEquals(testOrder.getOrderNo(), result.get(0).getOrderNo());
        assertEquals(testOrder.getCustomerName(), result.get(0).getCustomerName());

        // 验证方法调用
        verify(salesOrderRepository).findByCustomerCode(eq("C001"), any(Pageable.class));
    }

    // 移除与订单状态相关的测试方法

    @Test
    void testGenerateOrderNo() {
        // 配置Mock行为
        when(salesOrderRepository.findMaxOrderNoByPrefix(anyString())).thenReturn("SO2023060100005");

        // 执行测试
        String result = salesOrderService.generateOrderNo();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.startsWith("SO"));

        // 验证序号递增
        String dateStr = LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
        String expectedPrefix = "SO" + dateStr;
        assertTrue(result.startsWith(expectedPrefix));

        // 序号应该是00006（00005 + 1）
        String sequenceStr = result.substring(expectedPrefix.length());
        assertEquals("00006", sequenceStr);
    }
}
