package com.czerp.erpbackend.sales.service;

import com.czerp.erpbackend.sales.dto.SalesOrderProcessDTO;

import java.util.List;

/**
 * 销售订单工序Service接口
 */
public interface SalesOrderProcessService {

    /**
     * 根据订单ID查询工序列表
     * @param orderId 订单ID
     * @return 工序列表
     */
    List<SalesOrderProcessDTO> getProcessesByOrderId(String orderId);

    /**
     * 根据订单明细项ID查询工序列表
     * @param orderItemId 订单明细项ID
     * @return 工序列表
     */
    List<SalesOrderProcessDTO> getProcessesByOrderItemId(String orderItemId);

    /**
     * 添加工序
     * @param processDTO 工序DTO
     * @return 添加的工序
     */
    SalesOrderProcessDTO addProcess(SalesOrderProcessDTO processDTO);

    /**
     * 批量添加工序
     * @param processDTOs 工序DTO列表
     * @return 添加的工序列表
     */
    List<SalesOrderProcessDTO> addProcesses(List<SalesOrderProcessDTO> processDTOs);

    /**
     * 更新工序
     * @param id 工序ID
     * @param processDTO 工序DTO
     * @return 更新后的工序
     */
    SalesOrderProcessDTO updateProcess(String id, SalesOrderProcessDTO processDTO);

    /**
     * 删除工序
     * @param id 工序ID
     */
    void deleteProcess(String id);

    /**
     * 删除订单的所有工序
     * @param orderId 订单ID
     */
    void deleteProcessesByOrderId(String orderId);

    /**
     * 删除订单明细项的所有工序
     * @param orderItemId 订单明细项ID
     */
    void deleteProcessesByOrderItemId(String orderItemId);
}
