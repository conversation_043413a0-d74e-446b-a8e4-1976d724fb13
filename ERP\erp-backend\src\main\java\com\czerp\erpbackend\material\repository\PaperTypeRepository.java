package com.czerp.erpbackend.material.repository;

import com.czerp.erpbackend.material.entity.PaperType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 纸质类别存储库
 */
@Repository
public interface PaperTypeRepository extends JpaRepository<PaperType, Integer>, JpaSpecificationExecutor<PaperType> {

    /**
     * 根据纸板类别名称查找纸质类别
     * @param paperTypeName 纸板类别名称
     * @return 纸质类别
     */
    Optional<PaperType> findByPaperTypeName(String paperTypeName);

    /**
     * 判断纸板类别名称是否存在
     * @param paperTypeName 纸板类别名称
     * @return 是否存在
     */
    boolean existsByPaperTypeName(String paperTypeName);
}
