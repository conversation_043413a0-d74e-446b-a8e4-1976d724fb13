package com.czerp.erpbackend.product.mapper;

import com.czerp.erpbackend.product.dto.CreateSpecRequest;
import com.czerp.erpbackend.product.dto.ProductSpecDTO;
import com.czerp.erpbackend.product.dto.UpdateSpecRequest;
import com.czerp.erpbackend.product.entity.ProductSpec;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 产品规格Mapper
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ProductSpecMapper {
    
    /**
     * 实体转DTO
     * @param spec 实体
     * @return DTO
     */
    @Mapping(source = "createdTime", target = "createTime")
    @Mapping(source = "updatedTime", target = "updateTime")
    ProductSpecDTO toDto(ProductSpec spec);
    
    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "isDeleted", constant = "false")
    ProductSpec toEntity(CreateSpecRequest request);
    
    /**
     * 更新实体
     * @param request 更新请求
     * @param spec 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateSpecRequest request, @MappingTarget ProductSpec spec);
}
