package com.czerp.erpbackend.system.mapper;

import com.czerp.erpbackend.system.dto.PermissionDTO;
import com.czerp.erpbackend.system.entity.Permission;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 权限映射器
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PermissionMapper {
    
    /**
     * 权限实体转DTO
     * @param permission 权限实体
     * @return 权限DTO
     */
    @Mapping(target = "createTime", source = "createdTime")
    @Mapping(target = "updateTime", source = "updatedTime")
    @Mapping(target = "children", ignore = true)
    PermissionDTO toDto(Permission permission);
} 