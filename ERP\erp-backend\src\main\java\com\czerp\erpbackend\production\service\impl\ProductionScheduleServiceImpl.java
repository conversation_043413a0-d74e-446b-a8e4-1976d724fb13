package com.czerp.erpbackend.production.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.production.dto.*;
import com.czerp.erpbackend.production.entity.ProductionSchedule;
import com.czerp.erpbackend.production.entity.ProductionScheduleItem;
import com.czerp.erpbackend.production.repository.ProductionScheduleRepository;
import com.czerp.erpbackend.production.repository.ProductionScheduleItemRepository;
import com.czerp.erpbackend.production.service.ProductionScheduleService;
import com.czerp.erpbackend.production.mapper.ProductionScheduleMapper;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import com.czerp.erpbackend.sales.entity.SalesOrderProcess;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderMaterialRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderProcessRepository;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import com.czerp.erpbackend.inventory.entity.StockInboundItem;
import com.czerp.erpbackend.inventory.repository.StockInboundItemRepository;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Comparator;

/**
 * 生产排程单服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductionScheduleServiceImpl implements ProductionScheduleService {

    private final ProductionScheduleRepository productionScheduleRepository;
    private final ProductionScheduleItemRepository productionScheduleItemRepository;
    private final SalesOrderItemRepository salesOrderItemRepository;
    private final SalesOrderMaterialRepository salesOrderMaterialRepository;
    private final SalesOrderProcessRepository salesOrderProcessRepository;
    private final PurchaseOrderItemRepository purchaseOrderItemRepository;
    private final StockInboundItemRepository stockInboundItemRepository;
    private final ProductionScheduleMapper productionScheduleMapper;

    /**
     * 分页查询生产排程单
     * @param request 查询请求
     * @return 生产排程单分页列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<ProductionScheduleDTO> findProductionSchedules(ProductionScheduleQueryRequest request) {
        log.debug("Finding production schedules with request: {}", request);

        // 构建查询条件
        Specification<ProductionSchedule> spec = buildProductionScheduleSpecification(request);

        // 构建分页和排序
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize(), sort);

        // 执行查询
        Page<ProductionSchedule> schedulePage = productionScheduleRepository.findAll(spec, pageable);

        // 转换为DTO
        List<ProductionScheduleDTO> scheduleDTOs = productionScheduleMapper.toDto(schedulePage.getContent());

        // 丰富销售订单相关数据
        enrichWithSalesOrderData(scheduleDTOs);

        return PageResponse.<ProductionScheduleDTO>builder()
                .content(scheduleDTOs)
                .page(request.getPage())
                .size(request.getSize())
                .totalElements(schedulePage.getTotalElements())
                .totalPages(schedulePage.getTotalPages())
                .first(request.getPage() == 1)
                .last(request.getPage() >= schedulePage.getTotalPages())
                .empty(scheduleDTOs.isEmpty())
                .build();
    }

    /**
     * 分页查询生产排程单明细（按明细行级别分页）
     * @param request 查询请求
     * @return 生产排程单明细分页列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<ProductionScheduleItemDTO> findProductionScheduleItems(ProductionScheduleQueryRequest request) {
        log.debug("Finding production schedule items with request: {}", request);

        // 构建查询条件
        Specification<ProductionScheduleItem> spec = buildProductionScheduleItemSpecification(request);

        // 构建分页和排序
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize(), sort);

        // 执行查询
        Page<ProductionScheduleItem> itemPage = productionScheduleItemRepository.findAll(spec, pageable);

        // 转换为DTO
        List<ProductionScheduleItemDTO> itemDTOs = productionScheduleMapper.toItemDto(itemPage.getContent());

        // 丰富销售订单相关数据
        enrichItemsWithSalesOrderData(itemDTOs);

        return PageResponse.<ProductionScheduleItemDTO>builder()
                .content(itemDTOs)
                .page(request.getPage())
                .size(request.getSize())
                .totalElements(itemPage.getTotalElements())
                .totalPages(itemPage.getTotalPages())
                .first(request.getPage() == 1)
                .last(request.getPage() >= itemPage.getTotalPages())
                .empty(itemDTOs.isEmpty())
                .build();
    }

    /**
     * 根据ID查询生产排程单
     * @param id 排程单ID
     * @return 生产排程单
     */
    @Override
    @Transactional(readOnly = true)
    public ProductionScheduleDTO findProductionScheduleById(String id) {
        log.debug("Finding production schedule by id: {}", id);

        ProductionSchedule schedule = productionScheduleRepository.findByIdWithItems(id);
        if (schedule == null) {
            throw new BusinessException("生产排程单不存在");
        }

        ProductionScheduleDTO dto = productionScheduleMapper.toDto(schedule);

        // 丰富销售订单相关数据
        List<ProductionScheduleDTO> dtos = Collections.singletonList(dto);
        enrichWithSalesOrderData(dtos);

        return dto;
    }

    /**
     * 创建生产排程单
     * @param request 创建请求
     * @return 生产排程单
     */
    @Override
    @Transactional
    public ProductionScheduleDTO createProductionSchedule(CreateProductionScheduleRequest request) {
        log.debug("Creating production schedule with request: {}", request);

        // 创建生产排程单
        ProductionSchedule schedule = productionScheduleMapper.toEntity(request);
        schedule.setId(UUID.randomUUID().toString());
        schedule.setScheduleNo(generateScheduleNo());

        // 保存排程单
        schedule = productionScheduleRepository.save(schedule);

        // 创建明细
        List<ProductionScheduleItem> items = new ArrayList<>();
        for (CreateProductionScheduleItemRequest itemRequest : request.getItems()) {
            ProductionScheduleItem item = new ProductionScheduleItem();
            item.setId(UUID.randomUUID().toString());
            item.setSchedule(schedule);
            item.setIsUrgent(itemRequest.getIsUrgent());
            item.setUrgentSequence(itemRequest.getUrgentSequence());
            item.setIsPrinted(itemRequest.getIsPrinted() != null ? itemRequest.getIsPrinted() : false);
            item.setPlannedCompletionDate(itemRequest.getPlannedCompletionDate());
            item.setScheduleQuantity(itemRequest.getScheduleQuantity());
            item.setPackageCount(itemRequest.getPackageCount());
            item.setRemark(itemRequest.getRemark());
            item.setSalesOrderItemId(itemRequest.getSalesOrderItemId());
            item.setIsDeleted(false);
            items.add(item);
        }

        // 保存明细
        productionScheduleItemRepository.saveAll(items);
        schedule.setItems(items);

        ProductionScheduleDTO dto = productionScheduleMapper.toDto(schedule);

        // 丰富销售订单相关数据
        List<ProductionScheduleDTO> dtos = Collections.singletonList(dto);
        enrichWithSalesOrderData(dtos);

        return dto;
    }

    /**
     * 更新生产排程单
     * @param id 排程单ID
     * @param request 更新请求
     * @return 生产排程单
     */
    @Override
    @Transactional
    public ProductionScheduleDTO updateProductionSchedule(String id, UpdateProductionScheduleRequest request) {
        log.debug("Updating production schedule with id: {} and request: {}", id, request);

        ProductionSchedule schedule = productionScheduleRepository.findByIdWithItems(id);
        if (schedule == null) {
            throw new BusinessException("生产排程单不存在");
        }

        // 检查版本号（乐观锁）
        if (!Objects.equals(schedule.getVersion(), request.getVersion())) {
            throw new BusinessException("数据已被其他用户修改，请刷新后重试");
        }

        // 更新主表信息
        schedule.setScheduleDate(request.getScheduleDate());
        schedule.setRemark(request.getRemark());

        // 处理明细更新
        updateScheduleItems(schedule, request.getItems());

        // 保存更新
        schedule = productionScheduleRepository.save(schedule);

        ProductionScheduleDTO dto = productionScheduleMapper.toDto(schedule);

        // 丰富销售订单相关数据
        List<ProductionScheduleDTO> dtos = Collections.singletonList(dto);
        enrichWithSalesOrderData(dtos);

        return dto;
    }

    /**
     * 删除生产排程单
     * @param id 排程单ID
     */
    @Override
    @Transactional
    public void deleteProductionSchedule(String id) {
        log.debug("Deleting production schedule with id: {}", id);

        ProductionSchedule schedule = productionScheduleRepository.findById(id)
                .orElseThrow(() -> new BusinessException("生产排程单不存在"));

        // 逻辑删除
        schedule.setIsDeleted(true);
        productionScheduleRepository.save(schedule);

        // 删除明细
        productionScheduleItemRepository.deleteByScheduleId(id);
    }

    /**
     * 生成排程单号
     * @return 排程单号
     */
    @Override
    public String generateScheduleNo() {
        // 生成格式：SC + 年月日 + 5位序号，例如：SC2023060100001
        LocalDate today = LocalDate.now();
        String datePrefix = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String scheduleNoPrefix = "SC" + datePrefix;

        // 查询当天最大排程单号
        String maxScheduleNo = productionScheduleRepository.findMaxScheduleNoByPrefix(scheduleNoPrefix);

        int sequence = 1;
        if (StringUtils.hasText(maxScheduleNo)) {
            // 提取序号部分
            String sequenceStr = maxScheduleNo.substring(scheduleNoPrefix.length());
            try {
                sequence = Integer.parseInt(sequenceStr) + 1;
            } catch (NumberFormatException e) {
                log.warn("Failed to parse sequence from schedule no: {}", maxScheduleNo);
                sequence = 1;
            }
        }

        return scheduleNoPrefix + String.format("%05d", sequence);
    }

    /**
     * 批量更新打印状态
     * @param itemIds 明细ID列表
     * @param isPrinted 是否已打印
     */
    @Override
    @Transactional
    public void updatePrintStatus(List<String> itemIds, Boolean isPrinted) {
        log.debug("Updating print status for items: {}, isPrinted: {}", itemIds, isPrinted);

        List<ProductionScheduleItem> items = productionScheduleItemRepository.findAllById(itemIds);

        // 更新明细的打印状态
        items.forEach(item -> item.setIsPrinted(isPrinted));
        productionScheduleItemRepository.saveAll(items);

        // 如果设置为已打印，更新排程单的最近打印时间
        if (Boolean.TRUE.equals(isPrinted)) {
            Set<String> scheduleIds = items.stream()
                .map(item -> item.getSchedule().getId())
                .collect(Collectors.toSet());

            LocalDateTime now = LocalDateTime.now();
            for (String scheduleId : scheduleIds) {
                ProductionSchedule schedule = productionScheduleRepository.findById(scheduleId).orElse(null);
                if (schedule != null) {
                    schedule.setLastPrintTime(now);
                    productionScheduleRepository.save(schedule);
                }
            }
        }
    }

    /**
     * 构建生产排程单查询条件
     */
    private Specification<ProductionSchedule> buildProductionScheduleSpecification(ProductionScheduleQueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 逻辑删除过滤
            predicates.add(criteriaBuilder.equal(root.get("isDeleted"), false));

            // 关键字搜索（排程单号）
            if (StringUtils.hasText(request.getKeyword())) {
                Predicate keywordPredicate = criteriaBuilder.or(
                        criteriaBuilder.like(root.get("scheduleNo"), "%" + request.getKeyword() + "%")
                );
                predicates.add(keywordPredicate);
            }

            // 排程日期范围
            if (request.getScheduleDateStart() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("scheduleDate"), request.getScheduleDateStart()));
            }
            if (request.getScheduleDateEnd() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("scheduleDate"), request.getScheduleDateEnd()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 构建生产排程单明细查询条件
     */
    private Specification<ProductionScheduleItem> buildProductionScheduleItemSpecification(ProductionScheduleQueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 逻辑删除过滤
            predicates.add(criteriaBuilder.equal(root.get("isDeleted"), false));

            // 关联排程单表
            Join<ProductionScheduleItem, ProductionSchedule> scheduleJoin = root.join("schedule", JoinType.INNER);
            predicates.add(criteriaBuilder.equal(scheduleJoin.get("isDeleted"), false));

            // 关键字搜索
            if (StringUtils.hasText(request.getKeyword())) {
                // 关联销售订单表进行搜索
                Join<ProductionScheduleItem, SalesOrderItem> salesOrderItemJoin = root.join("salesOrderItem", JoinType.LEFT);
                Join<SalesOrderItem, SalesOrder> salesOrderJoin = salesOrderItemJoin.join("order", JoinType.LEFT);

                Predicate keywordPredicate = criteriaBuilder.or(
                        criteriaBuilder.like(scheduleJoin.get("scheduleNo"), "%" + request.getKeyword() + "%"),
                        criteriaBuilder.like(salesOrderJoin.get("customerName"), "%" + request.getKeyword() + "%"),
                        criteriaBuilder.like(salesOrderItemJoin.get("productName"), "%" + request.getKeyword() + "%")
                );
                predicates.add(keywordPredicate);
            }

            // 排程日期范围
            if (request.getScheduleDateStart() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(scheduleJoin.get("scheduleDate"), request.getScheduleDateStart()));
            }
            if (request.getScheduleDateEnd() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(scheduleJoin.get("scheduleDate"), request.getScheduleDateEnd()));
            }

            // 计划完成日期范围
            if (request.getPlannedCompletionDateStart() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("plannedCompletionDate"), request.getPlannedCompletionDateStart()));
            }
            if (request.getPlannedCompletionDateEnd() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("plannedCompletionDate"), request.getPlannedCompletionDateEnd()));
            }

            // 是否急单
            if (request.getIsUrgent() != null) {
                predicates.add(criteriaBuilder.equal(root.get("isUrgent"), request.getIsUrgent()));
            }

            // 是否已打印
            if (request.getIsPrinted() != null) {
                predicates.add(criteriaBuilder.equal(root.get("isPrinted"), request.getIsPrinted()));
            }

            // 生产单号
            if (StringUtils.hasText(request.getProductionOrderNo())) {
                Join<ProductionScheduleItem, SalesOrderItem> salesOrderItemJoin = root.join("salesOrderItem", JoinType.LEFT);
                predicates.add(criteriaBuilder.like(salesOrderItemJoin.get("productionOrderNo"), "%" + request.getProductionOrderNo() + "%"));
            }

            // 销售订单号
            if (StringUtils.hasText(request.getSalesOrderNo())) {
                Join<ProductionScheduleItem, SalesOrderItem> salesOrderItemJoin = root.join("salesOrderItem", JoinType.LEFT);
                Join<SalesOrderItem, SalesOrder> salesOrderJoin = salesOrderItemJoin.join("order", JoinType.LEFT);
                predicates.add(criteriaBuilder.like(salesOrderJoin.get("orderNo"), "%" + request.getSalesOrderNo() + "%"));
            }

            // 客户名称
            if (StringUtils.hasText(request.getCustomerName())) {
                Join<ProductionScheduleItem, SalesOrderItem> salesOrderItemJoin = root.join("salesOrderItem", JoinType.LEFT);
                Join<SalesOrderItem, SalesOrder> salesOrderJoin = salesOrderItemJoin.join("order", JoinType.LEFT);
                predicates.add(criteriaBuilder.like(salesOrderJoin.get("customerName"), "%" + request.getCustomerName() + "%"));
            }

            // 产品名称
            if (StringUtils.hasText(request.getProductName())) {
                Join<ProductionScheduleItem, SalesOrderItem> salesOrderItemJoin = root.join("salesOrderItem", JoinType.LEFT);
                predicates.add(criteriaBuilder.like(salesOrderItemJoin.get("productName"), "%" + request.getProductName() + "%"));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }



    /**
     * 丰富销售订单相关数据
     */
    private void enrichWithSalesOrderData(List<ProductionScheduleDTO> scheduleDTOs) {
        for (ProductionScheduleDTO scheduleDTO : scheduleDTOs) {
            if (scheduleDTO.getItems() != null) {
                enrichItemsWithSalesOrderData(scheduleDTO.getItems());
            }
        }
    }

    /**
     * 丰富明细的销售订单相关数据
     */
    private void enrichItemsWithSalesOrderData(List<ProductionScheduleItemDTO> itemDTOs) {
        // 收集所有销售订单明细ID
        Set<String> salesOrderItemIds = itemDTOs.stream()
                .map(ProductionScheduleItemDTO::getSalesOrderItemId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (salesOrderItemIds.isEmpty()) {
            return;
        }

        // 批量查询销售订单明细
        List<SalesOrderItem> salesOrderItems = salesOrderItemRepository.findAllById(salesOrderItemIds);
        Map<String, SalesOrderItem> salesOrderItemMap = salesOrderItems.stream()
                .collect(Collectors.toMap(SalesOrderItem::getId, item -> item));

        // 批量查询材料信息
        enrichWithMaterialData(itemDTOs, new ArrayList<>(salesOrderItemIds));

        // 批量查询采购信息
        enrichWithPurchaseData(itemDTOs, new ArrayList<>(salesOrderItemIds));

        // 批量查询入库信息
        enrichWithInboundData(itemDTOs, new ArrayList<>(salesOrderItemIds));

        // 填充销售订单数据
        for (ProductionScheduleItemDTO itemDTO : itemDTOs) {
            if (itemDTO.getSalesOrderItemId() != null) {
                SalesOrderItem salesOrderItem = salesOrderItemMap.get(itemDTO.getSalesOrderItemId());
                if (salesOrderItem != null) {
                    // 现有字段
                    itemDTO.setSalesOrderNo(salesOrderItem.getOrder().getOrderNo());
                    itemDTO.setProductionOrderNo(salesOrderItem.getProductionOrderNo());
                    itemDTO.setCustomerName(salesOrderItem.getOrder().getCustomerName());
                    itemDTO.setProductName(salesOrderItem.getProductName());
                    itemDTO.setProcessRequirements(salesOrderItem.getProcessRequirements());
                    itemDTO.setSalesQuantity(salesOrderItem.getQuantity());
                    itemDTO.setDeliveryDate(salesOrderItem.getDeliveryDate());

                    // 新增字段
                    itemDTO.setOrderDate(salesOrderItem.getOrder().getOrderDate());
                    itemDTO.setUnitPrice(salesOrderItem.getPrice());
                    itemDTO.setTotalAmount(salesOrderItem.getAmount());
                    itemDTO.setCustomerCategory("订单客户"); // 固定值
                    itemDTO.setOrderType("销售订单"); // 固定值
                    itemDTO.setIsSample(salesOrderItem.getIsSample() != null ? salesOrderItem.getIsSample() : false);

                    // 设置规格信息
                    if (salesOrderItem.getLength() != null && salesOrderItem.getWidth() != null && salesOrderItem.getHeight() != null) {
                        String specification = String.format("%.0f×%.0f×%.0f",
                                salesOrderItem.getLength(), salesOrderItem.getWidth(), salesOrderItem.getHeight());
                        itemDTO.setSpecification(specification);
                    }

                    if (salesOrderItem.getProductionLength() != null && salesOrderItem.getProductionWidth() != null && salesOrderItem.getProductionHeight() != null) {
                        String productionSpecification = String.format("%.0f×%.0f×%.0f",
                                salesOrderItem.getProductionLength(), salesOrderItem.getProductionWidth(), salesOrderItem.getProductionHeight());
                        itemDTO.setProductionSpecification(productionSpecification);
                    }
                }
            }
        }
    }

    /**
     * 丰富材料信息
     */
    private void enrichWithMaterialData(List<ProductionScheduleItemDTO> itemDTOs, List<String> salesOrderItemIds) {
        try {
            // 批量查询材料信息
            List<SalesOrderMaterial> materials = salesOrderMaterialRepository.findByOrderItemIdIn(salesOrderItemIds);

            // 构建映射关系（每个销售订单明细ID对应第一个材料记录）
            Map<String, SalesOrderMaterial> materialMap = materials.stream()
                .collect(Collectors.toMap(
                    m -> m.getOrderItem().getId(),
                    m -> m,
                    (existing, replacement) -> existing // 如果有多个材料记录，保留第一个
                ));

            // 填充材料数据到DTO
            for (ProductionScheduleItemDTO dto : itemDTOs) {
                if (dto.getSalesOrderItemId() != null) {
                    SalesOrderMaterial material = materialMap.get(dto.getSalesOrderItemId());
                    if (material != null) {
                        dto.setMaterialPaperWidth(material.getPaperWidth());
                        dto.setMaterialPaperLength(material.getPaperLength());
                        dto.setWidthOpen(material.getWidthOpen());
                        dto.setLengthOpen(material.getLengthOpen());
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to enrich material data: {}", e.getMessage());
            // 材料信息获取失败不影响主流程，只记录警告日志
        }
    }

    /**
     * 丰富采购信息
     */
    private void enrichWithPurchaseData(List<ProductionScheduleItemDTO> itemDTOs, List<String> salesOrderItemIds) {
        try {
            // 批量查询采购订单明细
            List<PurchaseOrderItem> purchaseItems = purchaseOrderItemRepository.findBySourceSalesOrderItemIdIn(salesOrderItemIds);

            // 构建映射关系并计算采购数量
            Map<String, Integer> purchaseQuantityMap = purchaseItems.stream()
                .collect(Collectors.groupingBy(
                    PurchaseOrderItem::getSourceSalesOrderItemId,
                    Collectors.summingInt(PurchaseOrderItem::getQuantity)
                ));

            // 填充采购数据到DTO
            for (ProductionScheduleItemDTO dto : itemDTOs) {
                if (dto.getSalesOrderItemId() != null) {
                    Integer purchaseQuantity = purchaseQuantityMap.get(dto.getSalesOrderItemId());
                    dto.setPurchaseQuantity(purchaseQuantity != null ? purchaseQuantity : 0);
                }
            }
        } catch (Exception e) {
            log.warn("Failed to enrich purchase data: {}", e.getMessage());
            // 采购信息获取失败不影响主流程，只记录警告日志
        }
    }

    /**
     * 丰富入库信息
     */
    private void enrichWithInboundData(List<ProductionScheduleItemDTO> itemDTOs, List<String> salesOrderItemIds) {
        try {
            // 通过采购订单明细查询入库信息
            List<PurchaseOrderItem> purchaseItems = purchaseOrderItemRepository.findBySourceSalesOrderItemIdIn(salesOrderItemIds);

            if (purchaseItems.isEmpty()) {
                return;
            }

            // 收集采购订单明细ID
            List<Long> purchaseOrderItemIds = purchaseItems.stream()
                .map(PurchaseOrderItem::getId)
                .collect(Collectors.toList());

            // 批量查询入库明细
            List<StockInboundItem> allInboundItems = stockInboundItemRepository.findByPurchaseOrderItemIdIn(purchaseOrderItemIds);

            // 构建采购订单明细ID到入库明细的映射
            Map<Long, List<StockInboundItem>> purchaseToInboundMap = allInboundItems.stream()
                .collect(Collectors.groupingBy(item -> item.getPurchaseOrderItem().getId()));

            // 构建销售订单明细ID到入库明细的映射
            Map<String, List<StockInboundItem>> salesOrderToInboundMap = new HashMap<>();
            for (PurchaseOrderItem purchaseItem : purchaseItems) {
                List<StockInboundItem> inbounds = purchaseToInboundMap.get(purchaseItem.getId());
                if (inbounds != null && !inbounds.isEmpty()) {
                    salesOrderToInboundMap.computeIfAbsent(purchaseItem.getSourceSalesOrderItemId(), k -> new ArrayList<>())
                        .addAll(inbounds);
                }
            }

            // 填充入库数据到DTO
            for (ProductionScheduleItemDTO dto : itemDTOs) {
                if (dto.getSalesOrderItemId() != null) {
                    List<StockInboundItem> inbounds = salesOrderToInboundMap.get(dto.getSalesOrderItemId());
                    if (inbounds != null && !inbounds.isEmpty()) {
                        // 计算总到料数量
                        int totalArrivedQuantity = inbounds.stream()
                            .mapToInt(item -> item.getQuantity() != null ? item.getQuantity() : 0)
                            .sum();
                        dto.setArrivedMaterialQuantity(totalArrivedQuantity);

                        // 获取最新的到料日期
                        inbounds.stream()
                            .map(item -> item.getStockInbound().getInboundDate())
                            .filter(Objects::nonNull)
                            .max(LocalDate::compareTo)
                            .ifPresent(dto::setMaterialArrivalDate);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to enrich inbound data: {}", e.getMessage());
            // 入库信息获取失败不影响主流程，只记录警告日志
        }
    }

    /**
     * 更新排程明细
     */
    private void updateScheduleItems(ProductionSchedule schedule, List<UpdateProductionScheduleItemRequest> itemRequests) {
        // 获取现有明细
        Map<String, ProductionScheduleItem> existingItems = schedule.getItems().stream()
                .collect(Collectors.toMap(ProductionScheduleItem::getId, item -> item));

        List<ProductionScheduleItem> updatedItems = new ArrayList<>();

        for (UpdateProductionScheduleItemRequest itemRequest : itemRequests) {
            ProductionScheduleItem item;

            if (StringUtils.hasText(itemRequest.getId())) {
                // 更新现有明细
                item = existingItems.get(itemRequest.getId());
                if (item == null) {
                    throw new BusinessException("明细不存在: " + itemRequest.getId());
                }

                // 检查版本号
                if (itemRequest.getVersion() != null && !Objects.equals(item.getVersion(), itemRequest.getVersion())) {
                    throw new BusinessException("明细数据已被其他用户修改，请刷新后重试");
                }
            } else {
                // 新增明细
                item = new ProductionScheduleItem();
                item.setId(UUID.randomUUID().toString());
                item.setSchedule(schedule);
                item.setIsDeleted(false);
            }

            // 更新字段
            item.setIsUrgent(itemRequest.getIsUrgent());
            item.setUrgentSequence(itemRequest.getUrgentSequence());
            item.setIsPrinted(itemRequest.getIsPrinted());
            item.setPlannedCompletionDate(itemRequest.getPlannedCompletionDate());
            item.setScheduleQuantity(itemRequest.getScheduleQuantity());
            item.setPackageCount(itemRequest.getPackageCount());
            item.setRemark(itemRequest.getRemark());
            item.setSalesOrderItemId(itemRequest.getSalesOrderItemId());

            updatedItems.add(item);
        }

        // 删除不在更新列表中的明细
        Set<String> updatedItemIds = itemRequests.stream()
                .map(UpdateProductionScheduleItemRequest::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        List<ProductionScheduleItem> itemsToDelete = existingItems.values().stream()
                .filter(item -> !updatedItemIds.contains(item.getId()))
                .collect(Collectors.toList());

        if (!itemsToDelete.isEmpty()) {
            productionScheduleItemRepository.deleteAll(itemsToDelete);
        }

        // 保存更新的明细
        productionScheduleItemRepository.saveAll(updatedItems);
        schedule.setItems(updatedItems);
    }

    /**
     * 构建销售订单明细查询条件
     */
    private Specification<SalesOrderItem> buildSalesOrderItemSpecification(ProductionScheduleQueryRequest request) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 基础条件：未删除
            predicates.add(cb.equal(root.get("isDeleted"), false));

            // 关联销售订单表
            Join<SalesOrderItem, SalesOrder> orderJoin = root.join("order", JoinType.INNER);
            predicates.add(cb.equal(orderJoin.get("isDeleted"), false));

            // 关键字搜索（支持生产单号、客户名称、产品名称等）
            if (StringUtils.hasText(request.getKeyword())) {
                String keyword = "%" + request.getKeyword().trim() + "%";
                Predicate keywordPredicate = cb.or(
                        cb.like(root.get("productionOrderNo"), keyword),
                        cb.like(orderJoin.get("customerName"), keyword),
                        cb.like(root.get("productName"), keyword),
                        cb.like(orderJoin.get("orderNo"), keyword),
                        cb.like(root.get("customerOrderNo"), keyword)
                );
                predicates.add(keywordPredicate);
            }

            // 客户名称筛选
            if (StringUtils.hasText(request.getCustomerName())) {
                predicates.add(cb.like(orderJoin.get("customerName"), "%" + request.getCustomerName() + "%"));
            }

            // 生产单号筛选
            if (StringUtils.hasText(request.getProductionOrderNo())) {
                predicates.add(cb.like(root.get("productionOrderNo"), "%" + request.getProductionOrderNo() + "%"));
            }

            // 销售订单号筛选
            if (StringUtils.hasText(request.getSalesOrderNo())) {
                predicates.add(cb.like(orderJoin.get("orderNo"), "%" + request.getSalesOrderNo() + "%"));
            }

            // 产品名称筛选
            if (StringUtils.hasText(request.getProductName())) {
                predicates.add(cb.like(root.get("productName"), "%" + request.getProductName() + "%"));
            }

            // 日期范围筛选（使用订单日期）
            if (request.getScheduleDateStart() != null) {
                predicates.add(cb.greaterThanOrEqualTo(orderJoin.get("orderDate"), request.getScheduleDateStart()));
            }
            if (request.getScheduleDateEnd() != null) {
                predicates.add(cb.lessThanOrEqualTo(orderJoin.get("orderDate"), request.getScheduleDateEnd()));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 将销售订单明细转换为生产排程单明细DTO（包含完整的引用数据）
     */
    private List<ProductionScheduleItemDTO> convertSalesOrderItemsToProductionScheduleItemDTOs(List<SalesOrderItem> salesOrderItems) {
        if (salesOrderItems.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("Converting {} sales order items to production schedule item DTOs", salesOrderItems.size());

        List<ProductionScheduleItemDTO> result = new ArrayList<>();

        // 收集所有销售订单明细ID
        List<String> salesOrderItemIds = salesOrderItems.stream()
                .map(SalesOrderItem::getId)
                .collect(Collectors.toList());

        // 批量查询相关数据
        Map<String, List<SalesOrderMaterial>> materialsByItemId = fetchSalesOrderMaterials(salesOrderItemIds);
        Map<String, List<SalesOrderProcess>> processesByItemId = fetchSalesOrderProcesses(salesOrderItemIds);
        Map<String, PurchaseOrderItem> purchaseOrderItemsByItemId = fetchPurchaseOrderItems(salesOrderItemIds);
        Map<String, Integer> arrivedQuantitiesByItemId = fetchArrivedQuantities(salesOrderItemIds);
        Map<String, Integer> scheduledQuantitiesByItemId = fetchScheduledQuantities(salesOrderItemIds);

        // 转换每个销售订单明细
        for (SalesOrderItem salesOrderItem : salesOrderItems) {
            ProductionScheduleItemDTO dto = convertSingleSalesOrderItemToDTO(
                    salesOrderItem,
                    materialsByItemId.get(salesOrderItem.getId()),
                    processesByItemId.get(salesOrderItem.getId()),
                    purchaseOrderItemsByItemId.get(salesOrderItem.getId()),
                    arrivedQuantitiesByItemId.get(salesOrderItem.getId()),
                    scheduledQuantitiesByItemId.get(salesOrderItem.getId())
            );
            result.add(dto);
        }

        log.debug("Successfully converted {} sales order items to DTOs", result.size());
        return result;
    }

    /**
     * 查询可引用的销售订单明细列表（用于生产排程单引用销售订单）
     * @param request 查询请求
     * @return 销售订单明细分页列表（包含完整的引用数据）
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<ProductionScheduleItemDTO> findSalesOrderItemsForReference(ProductionScheduleQueryRequest request) {
        log.debug("Finding sales order items for production schedule reference with request: {}", request);

        // 构建查询条件
        Specification<SalesOrderItem> spec = buildSalesOrderItemSpecification(request);

        // 构建分页和排序
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize(), sort);

        // 执行查询
        Page<SalesOrderItem> salesOrderItemPage = salesOrderItemRepository.findAll(spec, pageable);

        // 转换为DTO并丰富数据
        List<ProductionScheduleItemDTO> itemDTOs = convertSalesOrderItemsToProductionScheduleItemDTOs(salesOrderItemPage.getContent());

        return PageResponse.<ProductionScheduleItemDTO>builder()
                .content(itemDTOs)
                .page(request.getPage())
                .size(request.getSize())
                .totalElements(salesOrderItemPage.getTotalElements())
                .totalPages(salesOrderItemPage.getTotalPages())
                .first(request.getPage() == 1)
                .last(request.getPage() >= salesOrderItemPage.getTotalPages())
                .empty(itemDTOs.isEmpty())
                .build();
    }

    /**
     * 批量查询销售订单材料信息
     */
    private Map<String, List<SalesOrderMaterial>> fetchSalesOrderMaterials(List<String> salesOrderItemIds) {
        List<SalesOrderMaterial> materials = salesOrderMaterialRepository.findByOrderItemIdIn(salesOrderItemIds);
        return materials.stream().collect(Collectors.groupingBy(m -> m.getOrderItem().getId()));
    }

    /**
     * 批量查询销售订单工序信息
     */
    private Map<String, List<SalesOrderProcess>> fetchSalesOrderProcesses(List<String> salesOrderItemIds) {
        List<SalesOrderProcess> processes = salesOrderProcessRepository
                .findByOrderItemIdInAndIsDeletedFalseOrderByOrderItemIdAscSequenceAsc(salesOrderItemIds);
        return processes.stream().collect(Collectors.groupingBy(SalesOrderProcess::getOrderItemId));
    }

    /**
     * 批量查询采购订单明细信息
     */
    private Map<String, PurchaseOrderItem> fetchPurchaseOrderItems(List<String> salesOrderItemIds) {
        List<PurchaseOrderItem> purchaseOrderItems = purchaseOrderItemRepository
                .findBySourceSalesOrderItemIdIn(salesOrderItemIds);
        return purchaseOrderItems.stream()
                .collect(Collectors.toMap(
                        PurchaseOrderItem::getSourceSalesOrderItemId,
                        item -> item,
                        (existing, replacement) -> existing // 如果有多个采购订单明细，取第一个
                ));
    }

    /**
     * 批量查询已到料数量
     */
    private Map<String, Integer> fetchArrivedQuantities(List<String> salesOrderItemIds) {
        // 通过采购订单明细ID查询入库明细，然后聚合数量
        List<PurchaseOrderItem> purchaseOrderItems = purchaseOrderItemRepository
                .findBySourceSalesOrderItemIdIn(salesOrderItemIds);

        Map<String, Integer> result = new HashMap<>();

        for (PurchaseOrderItem purchaseOrderItem : purchaseOrderItems) {
            List<StockInboundItem> inboundItems = stockInboundItemRepository
                    .findByPurchaseOrderItemId(purchaseOrderItem.getId());

            int totalArrived = inboundItems.stream()
                    .mapToInt(item -> item.getQuantity() != null ? item.getQuantity() : 0)
                    .sum();

            String salesOrderItemId = purchaseOrderItem.getSourceSalesOrderItemId();
            result.merge(salesOrderItemId, totalArrived, Integer::sum);
        }

        return result;
    }

    /**
     * 转换单个销售订单明细为生产排程单明细DTO
     */
    private ProductionScheduleItemDTO convertSingleSalesOrderItemToDTO(
            SalesOrderItem salesOrderItem,
            List<SalesOrderMaterial> materials,
            List<SalesOrderProcess> processes,
            PurchaseOrderItem purchaseOrderItem,
            Integer arrivedQuantity,
            Integer scheduledQuantity) {

        ProductionScheduleItemDTO dto = new ProductionScheduleItemDTO();
        SalesOrder salesOrder = salesOrderItem.getOrder();

        // ========== 基础字段 ==========
        dto.setSalesOrderItemId(salesOrderItem.getId());
        dto.setSalesOrderNo(salesOrder.getOrderNo());
        dto.setProductionOrderNo(salesOrderItem.getProductionOrderNo());
        dto.setCustomerName(salesOrder.getCustomerName());
        dto.setCustomerCode(salesOrder.getCustomerCode());
        dto.setProductName(salesOrderItem.getProductName());
        dto.setProcessRequirements(salesOrderItem.getProcessRequirements());

        // ========== 引用销售订单接口专用字段 ==========

        // 已排程数
        int actualScheduledQuantity = scheduledQuantity != null ? scheduledQuantity : 0;
        dto.setScheduledQuantity(actualScheduledQuantity);

        // 本次排程数 = 订单数 - 已排程数
        int orderQuantity = salesOrderItem.getQuantity() != null ? salesOrderItem.getQuantity() : 0;
        int currentScheduleQuantity = Math.max(0, orderQuantity - actualScheduledQuantity);
        dto.setCurrentScheduleQuantity(currentScheduleQuantity);

        // 客户订单号
        dto.setCustomerOrderNo(salesOrderItem.getCustomerOrderNo());

        // 客方货号
        dto.setCustomerProductNo(salesOrderItem.getCustomerProductCode());

        // 品名（使用产品名称）
        dto.setProductName2(salesOrderItem.getProductName());

        // 盒式
        dto.setBoxType(salesOrderItem.getBoxType());

        // 纸质
        dto.setPaperType(salesOrderItem.getPaperType());

        // 生产纸质
        dto.setProductionPaperType(salesOrderItem.getProductionPaperType());

        // 规格（动态字段：长x宽x高 单位）
        dto.setSpecification(buildSpecification(salesOrderItem.getLength(),
                salesOrderItem.getWidth(), salesOrderItem.getHeight(), salesOrderItem.getSizeUnit()));

        // 订单日期
        dto.setOrderDate(salesOrder.getOrderDate());

        // 交期
        dto.setDeliveryDate(salesOrderItem.getDeliveryDate());

        // 订单类型（默认为"销售订单"）
        dto.setOrderType("销售订单");

        // 是否含税
        dto.setIsTaxed(salesOrderItem.getIsTaxed());

        // 纸板类型（从纸质字段对应的"纸质类别"）
        dto.setPaperBoardType(salesOrderItem.getPaperType()); // 简化处理，直接使用纸质

        // 长宽高
        dto.setLength(salesOrderItem.getLength());
        dto.setWidth(salesOrderItem.getWidth());
        dto.setHeight(salesOrderItem.getHeight());

        // 生产长宽高
        dto.setProductionLength(salesOrderItem.getProductionLength());
        dto.setProductionWidth(salesOrderItem.getProductionWidth());
        dto.setProductionHeight(salesOrderItem.getProductionHeight());

        // 订单数
        dto.setOrderQuantity(salesOrderItem.getQuantity());
        dto.setSalesQuantity(salesOrderItem.getQuantity());

        // 备品数
        dto.setSpareQuantity(salesOrderItem.getSpareQuantity());

        // ========== 采购相关字段 ==========
        if (purchaseOrderItem != null) {
            dto.setPurchasedQuantity(purchaseOrderItem.getQuantity());
            // 到料日期（从入库单获取，这里简化处理）
            dto.setMaterialArrivalDate(purchaseOrderItem.getDeliveryDate());
        } else {
            dto.setPurchasedQuantity(0);
        }

        // 已到料数
        dto.setArrivedQuantity(arrivedQuantity != null ? arrivedQuantity : 0);

        // ========== 材料相关字段 ==========
        if (materials != null && !materials.isEmpty()) {
            SalesOrderMaterial firstMaterial = materials.get(0);

            // 纸度
            dto.setPaperWidth(firstMaterial.getPressSizeWidth());

            // 纸长
            dto.setPaperLength(firstMaterial.getPressSizeLength());

            // 实际用料宽
            dto.setActualMaterialWidth(firstMaterial.getActualMaterialWidth());

            // 实际用料长
            dto.setActualMaterialLength(firstMaterial.getActualMaterialLength());

            // 实际用料单位
            dto.setActualMaterialUnit(firstMaterial.getUnit());

            // 原料规格（纸宽 x 纸长 单位）
            dto.setMaterialSpecification(buildMaterialSpecification(
                    firstMaterial.getPaperWidth(), firstMaterial.getPaperLength(), firstMaterial.getUnit()));
        }

        // ========== 工序字段 ==========
        if (processes != null && !processes.isEmpty()) {
            dto.setProcessSequence(buildProcessSequence(processes));
        }

        // ========== 其他字段 ==========
        // 未送货数（暂时默认为0）
        dto.setUndeliveredQuantity(0);

        // 成品库存数（暂时默认为0）
        dto.setFinishedStockQuantity(0);

        // 生产备注
        dto.setProductionRemark(salesOrderItem.getProductionRemark());

        return dto;
    }

    /**
     * 批量查询已排程数量
     */
    private Map<String, Integer> fetchScheduledQuantities(List<String> salesOrderItemIds) {
        List<ProductionScheduleItem> scheduleItems = productionScheduleItemRepository
                .findBySalesOrderItemIdIn(salesOrderItemIds);

        return scheduleItems.stream()
                .collect(Collectors.groupingBy(
                        ProductionScheduleItem::getSalesOrderItemId,
                        Collectors.summingInt(item -> item.getScheduleQuantity() != null ? item.getScheduleQuantity() : 0)
                ));
    }

    /**
     * 构建规格字符串（长x宽x高 单位）
     */
    private String buildSpecification(java.math.BigDecimal length, java.math.BigDecimal width,
                                    java.math.BigDecimal height, String sizeUnit) {
        if (length == null && width == null && height == null) {
            return null;
        }

        StringBuilder spec = new StringBuilder();
        if (length != null) {
            spec.append(length);
        }
        if (width != null) {
            if (spec.length() > 0) spec.append("x");
            spec.append(width);
        }
        if (height != null) {
            if (spec.length() > 0) spec.append("x");
            spec.append(height);
        }
        if (StringUtils.hasText(sizeUnit)) {
            spec.append(" ").append(sizeUnit);
        }

        return spec.toString();
    }

    /**
     * 构建原料规格字符串（纸宽 x 纸长 单位）
     */
    private String buildMaterialSpecification(java.math.BigDecimal paperWidth, java.math.BigDecimal paperLength, String paperUnit) {
        if (paperWidth == null && paperLength == null) {
            return null;
        }

        StringBuilder spec = new StringBuilder();
        if (paperWidth != null) {
            spec.append(paperWidth);
        }
        if (paperLength != null) {
            if (spec.length() > 0) spec.append(" x ");
            spec.append(paperLength);
        }
        if (StringUtils.hasText(paperUnit)) {
            spec.append(" ").append(paperUnit);
        }

        return spec.toString();
    }

    /**
     * 构建工序序列字符串（格式：开槽→打角→打钉）
     */
    private String buildProcessSequence(List<SalesOrderProcess> processes) {
        if (processes == null || processes.isEmpty()) {
            return null;
        }

        return processes.stream()
                .filter(process -> StringUtils.hasText(process.getProcessName()))
                .sorted(Comparator.comparing(SalesOrderProcess::getSequence))
                .map(SalesOrderProcess::getProcessName)
                .collect(Collectors.joining("→"));
    }
}
