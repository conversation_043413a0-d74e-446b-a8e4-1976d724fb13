 sales_order_item_process | CREATE TABLE `sales_order_item_process` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键ID',
  `order_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '销售订单ID',
  `order_item_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '销售订单明细项ID',
  `sequence` int NOT NULL COMMENT '工序顺序',
  `process_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工序名称',
  `process_requirements` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工艺要求',
  `plate_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '印版编号',
  `ink_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '水墨编号',
  `ink_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '水墨名称',
  `color_count` int DEFAULT NULL COMMENT '颜色数',
  `is_deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `version` int DEFAULT '0' COMMENT '版本号',
  `created_by` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_time` datetime(6) NOT NULL,
  `updated_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_sales_order_item_process_process_name` (`process_name`),
  KEY `idx_sales_order_item_process_order_id` (`order_id`),
  KEY `idx_sales_order_item_process_order_item_id` (`order_item_id`),
  CONSTRAINT `fk_sales_order_item_process_sales_order` FOREIGN KEY (`order_id`) REFERENCES `sales_order` (`id`),
  CONSTRAINT `fk_sales_order_item_process_sales_order_item` FOREIGN KEY (`order_item_id`) REFERENCES `sales_order_item` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售订单工序表' |