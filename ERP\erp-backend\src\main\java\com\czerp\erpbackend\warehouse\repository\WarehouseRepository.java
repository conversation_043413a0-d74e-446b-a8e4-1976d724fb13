package com.czerp.erpbackend.warehouse.repository;

import com.czerp.erpbackend.warehouse.entity.Warehouse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 仓库存储库
 */
@Repository
public interface WarehouseRepository extends JpaRepository<Warehouse, Long>, JpaSpecificationExecutor<Warehouse> {

    /**
     * 根据仓库名称查找仓库
     * @param warehouseName 仓库名称
     * @return 仓库
     */
    Optional<Warehouse> findByWarehouseNameAndIsDeletedFalse(String warehouseName);

    /**
     * 判断仓库名称是否存在
     * @param warehouseName 仓库名称
     * @return 是否存在
     */
    boolean existsByWarehouseNameAndIsDeletedFalse(String warehouseName);

    /**
     * 判断仓库名称是否存在（排除指定ID）
     * @param warehouseName 仓库名称
     * @param id 排除的ID
     * @return 是否存在
     */
    boolean existsByWarehouseNameAndIsDeletedFalseAndIdNot(String warehouseName, Long id);

    /**
     * 查询所有未删除的仓库
     * @return 仓库列表
     */
    List<Warehouse> findByIsDeletedFalseOrderByCreatedTimeDesc();

    /**
     * 根据备料仓标识查询仓库列表
     * @param isMaterialWarehouse 备料仓标识
     * @param pageable 分页参数
     * @return 仓库分页列表
     */
    Page<Warehouse> findByIsMaterialWarehouseAndIsDeletedFalse(Boolean isMaterialWarehouse, Pageable pageable);

    /**
     * 多条件查询仓库
     * @param keyword 关键词
     * @param isMaterialWarehouse 备料仓标识
     * @param pageable 分页参数
     * @return 仓库分页列表
     */
    @Query("SELECT w FROM Warehouse w WHERE w.isDeleted = false " +
            "AND (:keyword IS NULL OR :keyword = '' OR w.warehouseName LIKE %:keyword%) " +
            "AND (:isMaterialWarehouse IS NULL OR w.isMaterialWarehouse = :isMaterialWarehouse)")
    Page<Warehouse> search(
            @Param("keyword") String keyword,
            @Param("isMaterialWarehouse") Boolean isMaterialWarehouse,
            Pageable pageable);

    /**
     * 根据ID查询未删除的仓库
     * @param id 仓库ID
     * @return 仓库
     */
    Optional<Warehouse> findByIdAndIsDeletedFalse(Long id);
}
