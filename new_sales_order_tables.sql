-- 销售订单数据库表结构
-- 使用小写下划线命名规则

-- 销售订单头表
CREATE TABLE sales_order (
    id VARCHAR(36) PRIMARY KEY,
    order_no VARCHAR(50) NOT NULL COMMENT '订单编号',
    order_date DATE COMMENT '订单日期',
    payment_method VARCHAR(50) COMMENT '付款方式',
    customer_code VARCHAR(50) COMMENT '客户编码',
    customer_name VARCHAR(200) COMMENT '客户名称',
    sales_person VARCHAR(50) COMMENT '销售员',
    customer_purchaser VARCHAR(50) COMMENT '客户采购员',
    receiving_unit VARCHAR(200) COMMENT '收货单位',
    receiver VARCHAR(50) COMMENT '收货人',
    receiver_phone VARCHAR(50) COMMENT '收货人电话',
    receiving_address VARCHAR(500) COMMENT '收货地址',
    remark VARCHAR(500) COMMENT '备注',
    order_type VARCHAR(50) COMMENT '订单类型',
    created_by VARCHAR(36) COMMENT '创建人',
    created_time DATETIME COMMENT '创建时间',
    updated_by VARCHAR(36) COMMENT '更新人',
    updated_time DATETIME COMMENT '更新时间',
    UNIQUE KEY uk_order_no (order_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单头表';

-- 销售订单行表
CREATE TABLE sales_order_item (
    id VARCHAR(36) PRIMARY KEY,
    order_id VARCHAR(36) NOT NULL COMMENT '订单ID',
    production_order_no VARCHAR(50) COMMENT '生产单号',
    customer_order_no VARCHAR(50) COMMENT '客户订单号',
    customer_product_code VARCHAR(100) COMMENT '客方货号',
    product_name VARCHAR(200) COMMENT '品名',
    process_requirements VARCHAR(500) COMMENT '工艺要求',
    is_taxed TINYINT(1) COMMENT '是否含税',
    box_type VARCHAR(50) COMMENT '盒式',
    paper_type VARCHAR(100) COMMENT '纸质',
    corrugation_type VARCHAR(50) COMMENT '楞别',
    production_paper_type VARCHAR(100) COMMENT '生产纸质',
    length DECIMAL(10,2) COMMENT '长',
    width DECIMAL(10,2) COMMENT '宽',
    height DECIMAL(10,2) COMMENT '高',
    size_unit VARCHAR(10) COMMENT '尺寸单位',
    quantity INT COMMENT '数量',
    spare_quantity INT COMMENT '备品数',
    price DECIMAL(18,4) COMMENT '单价',
    amount DECIMAL(18,2) COMMENT '金额',
    delivery_date DATE COMMENT '送货日期',
    is_special_price TINYINT(1) COMMENT '特价',
    paper_quotation DECIMAL(18,4) COMMENT '纸质报价',
    connection_method VARCHAR(50) COMMENT '连接方式',
    staple_position VARCHAR(50) COMMENT '钉口',
    packaging_count INT COMMENT '包装数',
    production_remark VARCHAR(500) COMMENT '生产备注',
    remark VARCHAR(500) COMMENT '备注',
    production_length DECIMAL(10,2) COMMENT '生产长',
    production_width DECIMAL(10,2) COMMENT '生产宽',
    production_height DECIMAL(10,2) COMMENT '生产高',
    spare_ratio DECIMAL(10,4) COMMENT '成套比例',
    spare_quantity_total INT COMMENT '成套数量',
    current_inventory INT COMMENT '当前库存',
    available_inventory INT COMMENT '可用库存',
    use_inventory INT COMMENT '使用库存',
    is_special_specification TINYINT(1) COMMENT '特殊规格',
    unit_weight DECIMAL(10,4) COMMENT '单重',
    total_weight DECIMAL(18,2) COMMENT '总重(KG)',
    product_area DECIMAL(10,4) COMMENT '产品面积',
    total_area DECIMAL(18,2) COMMENT '总面积(平米)',
    product_volume DECIMAL(10,4) COMMENT '产品体积',
    total_volume DECIMAL(18,2) COMMENT '总体积(立方米)',
    component VARCHAR(100) COMMENT '部件',
    is_sample TINYINT(1) COMMENT '样品',
    delivered_quantity INT COMMENT '已送货数',
    delivered_spare_quantity INT COMMENT '已送备品数',
    returned_quantity INT COMMENT '退货数',
    safety_stock INT COMMENT '安全库存数',
    reconciliation_quantity INT COMMENT '已对账数',
    unit VARCHAR(20) COMMENT '单位',
    currency VARCHAR(20) COMMENT '币种',
    tax_rate DECIMAL(5,2) COMMENT '税率',
    created_by VARCHAR(36) COMMENT '创建人',
    created_time DATETIME COMMENT '创建时间',
    updated_by VARCHAR(36) COMMENT '更新人',
    updated_time DATETIME COMMENT '更新时间',
    FOREIGN KEY (order_id) REFERENCES sales_order(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单行表';
