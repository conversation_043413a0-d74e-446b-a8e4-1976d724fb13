# 销售订单管理字段名称文档

## 1. 销售订单基本信息 (SalesOrderDTO)

| 字段名称 | 类型 | 描述 |
|---------|------|------|
| id | String | 订单ID |
| orderNo | String | 销售单号 |
| orderDate | LocalDate | 订单日期 |
| paymentMethod | String | 付款方式 |
| customerCode | String | 客户编码 |
| customerName | String | 客户名称 |
| salesPerson | String | 销售员 |
| customerPurchaser | String | 客户采购员 |
| receivingUnit | String | 收货单位 |
| receiver | String | 收货人 |
| receiverPhone | String | 收货人电话 |
| receivingAddress | String | 收货地址 |
| remark | String | 备注 |
| orderType | String | 订单类型 |
| createdBy | String | 创建人 |
| createdByName | String | 创建人姓名 |
| createdTime | LocalDateTime | 创建时间 |
| updatedBy | String | 更新人 |
| updatedByName | String | 更新人姓名 |
| updatedTime | LocalDateTime | 更新时间 |

## 2. 销售订单详情 (SalesOrderDetailDTO)

| 字段名称 | 类型 | 描述 |
|---------|------|------|
| id | String | 订单ID |
| orderNo | String | 销售单号 |
| orderDate | LocalDate | 订单日期 |
| paymentMethod | String | 付款方式 |
| customerCode | String | 客户编码 |
| customerName | String | 客户名称 |
| salesPerson | String | 销售员 |
| customerPurchaser | String | 客户采购员 |
| receivingUnit | String | 收货单位 |
| receiver | String | 收货人 |
| receiverPhone | String | 收货人电话 |
| receivingAddress | String | 收货地址 |
| remark | String | 备注 |
| orderType | String | 订单类型 |
| createdBy | String | 创建人 |
| createdByName | String | 创建人姓名 |
| createdTime | LocalDateTime | 创建时间 |
| updatedBy | String | 更新人 |
| updatedByName | String | 更新人姓名 |
| updatedTime | LocalDateTime | 更新时间 |
| items | List<SalesOrderItemDTO> | 订单明细列表 |

## 3. 销售订单明细 (SalesOrderItemDTO)

| 字段名称 | 类型 | 描述 |
|---------|------|------|
| id | String | 明细ID |
| orderId | String | 订单ID |
| productionOrderNo | String | 生产单号 |
| customerOrderNo | String | 客户订单号 |
| customerProductCode | String | 客方货号 |
| productName | String | 品名 |
| processRequirements | String | 工艺要求 |
| isTaxed | Boolean | 是否含税 |
| boxType | String | 盒式 |
| paperType | String | 纸质 |
| corrugationType | String | 楞别 |
| productionPaperType | String | 生产纸质 |
| length | BigDecimal | 长 |
| width | BigDecimal | 宽 |
| height | BigDecimal | 高 |
| sizeUnit | String | 尺寸单位 |
| quantity | Integer | 数量 |
| spareQuantity | Integer | 备品数 |
| price | BigDecimal | 单价 |
| amount | BigDecimal | 金额 |
| deliveryDate | LocalDate | 送货日期 |
| isSpecialPrice | Boolean | 特价 |
| paperQuotation | BigDecimal | 纸质报价 |
| connectionMethod | String | 连接方式 |
| staplePosition | String | 钉口 |
| packagingCount | Integer | 包装数 |
| productionRemark | String | 生产备注 |
| remark | String | 备注 |
| productionLength | BigDecimal | 生产长 |
| productionWidth | BigDecimal | 生产宽 |
| productionHeight | BigDecimal | 生产高 |
| spareRatio | BigDecimal | 成套比例 |
| spareQuantityTotal | Integer | 成套数量 |
| currentInventory | Integer | 当前库存 |
| availableInventory | Integer | 可用库存 |
| useInventory | Integer | 使用库存 |
| isSpecialSpecification | Boolean | 特殊规格 |
| unitWeight | BigDecimal | 单重 |
| totalWeight | BigDecimal | 总重(KG) |
| productArea | BigDecimal | 产品面积 |
| totalArea | BigDecimal | 总面积(平米) |
| productVolume | BigDecimal | 产品体积 |
| totalVolume | BigDecimal | 总体积(立方米) |
| component | String | 部件 |
| isSample | Boolean | 样品 |
| deliveredQuantity | Integer | 已送货数 |
| deliveredSpareQuantity | Integer | 已送备品数 |
| returnedQuantity | Integer | 退货数 |
| safetyStock | Integer | 安全库存数 |
| reconciliationQuantity | Integer | 已对账数 |
| unit | String | 单位 |
| currency | String | 币种 |
| taxRate | BigDecimal | 税率 |
| createdBy | String | 创建人 |
| createdTime | LocalDateTime | 创建时间 |
| updatedBy | String | 更新人 |
| updatedTime | LocalDateTime | 更新时间 |

## 4. 销售订单查询参数 (SalesOrderQueryParamDTO)

| 字段名称 | 类型 | 描述 |
|---------|------|------|
| keyword | String | 关键字搜索 |
| orderNo | String | 销售单号 |
| productionOrderNo | String | 生产单号 |
| customerName | String | 客户名称 |
| customerOrderNo | String | 客户订单号 |
| customerProductCode | String | 客方货号 |
| productName | String | 品名 |
| startDate | LocalDate | 开始日期 |
| endDate | LocalDate | 结束日期 |
| salesPerson | String | 销售员 |
| page | Integer | 页码 |
| pageSize | Integer | 每页大小 |
| sortField | String | 排序字段 |
| sortOrder | String | 排序方向 |

## 5. 销售订单查询DTO (SalesOrderQueryDTO)

| 字段名称 | 类型 | 描述 |
|---------|------|------|
| keyword | String | 关键字(单号、客户名称、客户订单号等) |
| orderNo | String | 订单编号 |
| productionOrderNo | String | 生产单号 |
| customerCode | String | 客户编码 |
| customerName | String | 客户名称 |
| customerOrderNo | String | 客户订单号 |
| salesPerson | String | 销售员 |
| customerPurchaser | String | 客户采购员 |
| orderDateStart | LocalDate | 订单日期开始 |
| orderDateEnd | LocalDate | 订单日期结束 |
| createdBy | String | 创建人 |
| page | Integer | 页码 |
| pageSize | Integer | 每页大小 |
| sortField | String | 排序字段 |
| sortOrder | String | 排序方向(asc,desc) |

## 6. 销售订单查询结果 (SalesOrderQueryResultDTO)

| 字段名称 | 类型 | 描述 |
|---------|------|------|
| id | String | 订单ID |
| orderNo | String | 销售单号 |
| orderDate | LocalDate | 日期 |
| customerName | String | 客户名称 |
| salesPerson | String | 销售员 |
| receivingUnit | String | 收货单位 |
| receiver | String | 收货人 |
| receiverPhone | String | 收货人电话 |
| receivingAddress | String | 收货地址 |
| createdBy | String | 创建人 |
| createdByName | String | 创建人姓名 |
| createdTime | LocalDateTime | 创建时间 |
| itemId | String | 明细ID |
| productionOrderNo | String | 生产单号 |
| customerOrderNo | String | 客户订单号 |
| customerProductCode | String | 客方货号 |
| productName | String | 品名 |
| processRequirements | String | 工艺要求 |
| boxType | String | 盒式 |
| paperType | String | 纸质 |
| productionPaperType | String | 生产纸质 |
| specification | String | 规格 |
| productionSpecification | String | 生产规格 |
| quantity | Integer | 数量 |
| spareQuantity | Integer | 备品数 |
| price | BigDecimal | 单价 |
| amount | BigDecimal | 金额 |
| isSpecialPrice | Boolean | 特价 |
| productionRemark | String | 生产备注 |
| remark | String | 备注 |
| connectionMethod | String | 连接方式 |
| unit | String | 单位 |
| paperQuotation | BigDecimal | 纸质报价 |
| currency | String | 币种 |
| unitWeight | BigDecimal | 单重 |
| totalWeight | BigDecimal | 总重(KG) |
| productArea | BigDecimal | 产品面积 |
| totalArea | BigDecimal | 总面积(平米) |
| productVolume | BigDecimal | 产品体积 |
| totalVolume | BigDecimal | 总体积(立方米) |
| taxRate | BigDecimal | 税率 |
| isTaxed | Boolean | 是否含税 |
| corrugationType | String | 楞别 |
| deliveryDate | LocalDate | 交期（送货日期） |

