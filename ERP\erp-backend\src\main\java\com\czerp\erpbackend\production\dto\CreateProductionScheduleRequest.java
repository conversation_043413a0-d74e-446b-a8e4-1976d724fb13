package com.czerp.erpbackend.production.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 创建生产排程单请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateProductionScheduleRequest {

    /**
     * 排程日期
     */
    private LocalDate scheduleDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 生产排程明细列表
     */
    @Valid
    @NotEmpty(message = "生产排程明细不能为空")
    private List<CreateProductionScheduleItemRequest> items;
}
