package com.czerp.erpbackend.purchase.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.purchase.dto.CreatePurchaseOrderRequest;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderQueryRequest;
import com.czerp.erpbackend.purchase.dto.UpdatePurchaseOrderRequest;
import com.czerp.erpbackend.purchase.service.PurchaseOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 采购订单Controller
 */
@RestController
@RequestMapping("/purchase-orders")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "采购订单管理", description = "采购订单管理相关接口")
public class PurchaseOrderController {

    private final PurchaseOrderService purchaseOrderService;

    /**
     * 分页查询采购订单
     * @param request 查询请求
     * @return 采购订单分页列表
     */
    @GetMapping
    @Operation(summary = "分页查询采购订单", description = "分页查询采购订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('purchase:order:read')")
    public ResponseEntity<ApiResponse<PageResponse<PurchaseOrderDTO>>> findPurchaseOrders(PurchaseOrderQueryRequest request) {
        log.info("Finding purchase orders with request: {}", request);
        PageResponse<PurchaseOrderDTO> purchaseOrders = purchaseOrderService.findPurchaseOrders(request);
        return ResponseEntity.ok(ApiResponse.success(purchaseOrders));
    }

    /**
     * 根据ID查询采购订单
     * @param id 采购订单ID
     * @return 采购订单
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询采购订单", description = "根据ID查询采购订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('purchase:order:read')")
    public ResponseEntity<ApiResponse<PurchaseOrderDTO>> findPurchaseOrderById(@PathVariable Long id) {
        log.info("Finding purchase order by id: {}", id);
        PurchaseOrderDTO purchaseOrder = purchaseOrderService.findPurchaseOrderById(id);
        return ResponseEntity.ok(ApiResponse.success(purchaseOrder));
    }

    /**
     * 创建采购订单
     * @param request 创建请求
     * @return 采购订单
     */
    @PostMapping
    @Operation(summary = "创建采购订单", description = "创建采购订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('purchase:order:create')")
    public ResponseEntity<ApiResponse<PurchaseOrderDTO>> createPurchaseOrder(@Valid @RequestBody CreatePurchaseOrderRequest request) {
        log.info("Creating purchase order with request: {}", request);
        PurchaseOrderDTO purchaseOrder = purchaseOrderService.createPurchaseOrder(request);
        return ResponseEntity.ok(ApiResponse.success(purchaseOrder));
    }

    /**
     * 分页查询采购订单明细（按明细行级别分页）
     * @param request 查询请求
     * @return 采购订单明细分页列表
     */
    @GetMapping("/items")
    @Operation(summary = "分页查询采购订单明细", description = "按明细行级别分页查询采购订单明细，用于前端表格显示")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('purchase:order:read')")
    public ResponseEntity<ApiResponse<PageResponse<PurchaseOrderItemDTO>>> findPurchaseOrderItems(PurchaseOrderQueryRequest request) {
        log.info("Finding purchase order items with request: {}", request);
        PageResponse<PurchaseOrderItemDTO> purchaseOrderItems = purchaseOrderService.findPurchaseOrderItems(request);
        return ResponseEntity.ok(ApiResponse.success(purchaseOrderItems));
    }

    /**
     * 生成采购单号
     * @return 采购单号
     */
    @GetMapping("/generate-order-no")
    @Operation(summary = "生成采购单号", description = "生成采购单号")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('purchase:order:create')")
    public ResponseEntity<ApiResponse<String>> generatePurchaseOrderNo() {
        log.info("Generating purchase order no");
        String purchaseOrderNo = purchaseOrderService.generatePurchaseOrderNo();
        return ResponseEntity.ok(ApiResponse.success(purchaseOrderNo));
    }

    /**
     * 更新采购订单
     * @param id 采购订单ID
     * @param request 更新请求
     * @return 采购订单
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新采购订单", description = "更新采购订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('purchase:order:update')")
    public ResponseEntity<ApiResponse<PurchaseOrderDTO>> updatePurchaseOrder(
            @PathVariable Long id,
            @Valid @RequestBody UpdatePurchaseOrderRequest request) {
        log.info("Updating purchase order with id: {} and request: {}", id, request);
        PurchaseOrderDTO purchaseOrder = purchaseOrderService.updatePurchaseOrder(id, request);
        return ResponseEntity.ok(ApiResponse.success(purchaseOrder));
    }
}
