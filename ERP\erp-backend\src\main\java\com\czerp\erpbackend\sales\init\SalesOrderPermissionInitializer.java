package com.czerp.erpbackend.sales.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 销售订单权限初始化器
 * 负责初始化销售订单模块的权限并分配给管理员角色
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(10) // 在系统权限初始化之后执行
public class SalesOrderPermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing sales order permissions...");

        // 初始化销售订单模块权限
        initSalesOrderPermissions();

        // 为管理员角色分配销售订单模块权限
        assignPermissionsToAdminRole();

        log.info("Sales order permissions initialized successfully");
    }

    /**
     * 初始化销售订单模块权限
     */
    private void initSalesOrderPermissions() {
        log.info("Initializing sales order module permissions...");

        // 检查销售模块是否已存在
        if (permissionRepository.existsByCode("sales")) {
            log.info("Sales module already exists, checking sales order permissions...");
        } else {
            // 创建销售模块
            createPermission("销售管理", "sales", "module", null, null, null, "shopping-cart", 20);
            log.info("Sales module created successfully");
        }

        String salesModuleId = findPermissionIdByCode("sales");

        // 检查销售订单模块是否已存在
        if (permissionRepository.existsByCode("sales:order")) {
            log.info("Sales order module already exists, skipping...");
        } else {
            // 创建销售订单模块
            String salesOrderModuleId = createPermission("销售订单管理", "sales:order", "menu", salesModuleId, "/sales/orders", "sales/order/index", "file-text", 10);

            // 创建销售订单基本操作权限
            createPermission("销售订单列表", "sales:order:list", "button", salesOrderModuleId, null, null, null, 11);
            createPermission("销售订单详情", "sales:order:read", "button", salesOrderModuleId, null, null, null, 12);
            createPermission("创建销售订单", "sales:order:create", "button", salesOrderModuleId, null, null, null, 13);
            createPermission("更新销售订单", "sales:order:update", "button", salesOrderModuleId, null, null, null, 14);
            createPermission("删除销售订单", "sales:order:delete", "button", salesOrderModuleId, null, null, null, 15);
            createPermission("审核销售订单", "sales:order:approve", "button", salesOrderModuleId, null, null, null, 16);
            createPermission("打印销售订单", "sales:order:print", "button", salesOrderModuleId, null, null, null, 17);

            // 创建销售订单生产相关权限
            createPermission("销售订单生产管理", "sales:production", "menu", salesModuleId, "/sales/production", "sales/production/index", "tool", 20);
            createPermission("生产排程列表", "sales:production:list", "button", findPermissionIdByCode("sales:production"), null, null, null, 21);
            createPermission("生产排程详情", "sales:production:read", "button", findPermissionIdByCode("sales:production"), null, null, null, 22);
            createPermission("创建生产排程", "sales:production:create", "button", findPermissionIdByCode("sales:production"), null, null, null, 23);
            createPermission("更新生产排程", "sales:production:update", "button", findPermissionIdByCode("sales:production"), null, null, null, 24);
            createPermission("删除生产排程", "sales:production:delete", "button", findPermissionIdByCode("sales:production"), null, null, null, 25);

            // 创建销售订单入库相关权限
            createPermission("销售订单入库管理", "sales:inventory", "menu", salesModuleId, "/sales/inventory", "sales/inventory/index", "inbox", 30);
            createPermission("入库记录列表", "sales:inventory:list", "button", findPermissionIdByCode("sales:inventory"), null, null, null, 31);
            createPermission("入库记录详情", "sales:inventory:read", "button", findPermissionIdByCode("sales:inventory"), null, null, null, 32);
            createPermission("创建入库记录", "sales:inventory:create", "button", findPermissionIdByCode("sales:inventory"), null, null, null, 33);
            createPermission("更新入库记录", "sales:inventory:update", "button", findPermissionIdByCode("sales:inventory"), null, null, null, 34);
            createPermission("删除入库记录", "sales:inventory:delete", "button", findPermissionIdByCode("sales:inventory"), null, null, null, 35);

            // 创建销售订单送货相关权限
            createPermission("销售订单送货管理", "sales:delivery", "menu", salesModuleId, "/sales/delivery", "sales/delivery/index", "car", 40);
            createPermission("送货记录列表", "sales:delivery:list", "button", findPermissionIdByCode("sales:delivery"), null, null, null, 41);
            createPermission("送货记录详情", "sales:delivery:read", "button", findPermissionIdByCode("sales:delivery"), null, null, null, 42);
            createPermission("创建送货记录", "sales:delivery:create", "button", findPermissionIdByCode("sales:delivery"), null, null, null, 43);
            createPermission("更新送货记录", "sales:delivery:update", "button", findPermissionIdByCode("sales:delivery"), null, null, null, 44);
            createPermission("删除送货记录", "sales:delivery:delete", "button", findPermissionIdByCode("sales:delivery"), null, null, null, 45);

            // 创建销售订单统计相关权限
            createPermission("销售订单统计", "sales:statistics", "menu", salesModuleId, "/sales/statistics", "sales/statistics/index", "bar-chart", 50);
            createPermission("查看统计数据", "sales:statistics:read", "button", findPermissionIdByCode("sales:statistics"), null, null, null, 51);

            log.info("Sales order module permissions created successfully");
        }
    }

    /**
     * 为管理员角色分配销售订单模块权限
     */
    private void assignPermissionsToAdminRole() {
        log.info("Assigning sales order permissions to admin role...");

        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping permission assignment");
            return;
        }

        // 获取所有销售订单相关权限
        List<String> permissionCodes = Arrays.asList(
                // 销售模块
                "sales",
                // 销售订单基本操作
                "sales:order", "sales:order:list", "sales:order:read", "sales:order:create", "sales:order:update", "sales:order:delete", "sales:order:approve", "sales:order:print",
                // 生产相关
                "sales:production", "sales:production:list", "sales:production:read", "sales:production:create", "sales:production:update", "sales:production:delete",
                // 入库相关
                "sales:inventory", "sales:inventory:list", "sales:inventory:read", "sales:inventory:create", "sales:inventory:update", "sales:inventory:delete",
                // 送货相关
                "sales:delivery", "sales:delivery:list", "sales:delivery:read", "sales:delivery:create", "sales:delivery:update", "sales:delivery:delete",
                // 统计相关
                "sales:statistics", "sales:statistics:read"
        );

        List<Permission> permissions = permissionRepository.findByCodeIn(permissionCodes);

        // 为管理员角色分配权限
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : permissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString());
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermission.setCreateBy("system");
                rolePermission.setCreateTime(LocalDateTime.now());
                rolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }

        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} sales order permissions to admin role", rolePermissions.size());
        } else {
            log.info("All sales order permissions already assigned to admin role");
        }
    }

    /**
     * 创建权限
     * @param name 权限名称
     * @param code 权限编码
     * @param type 权限类型
     * @param parentId 父权限ID
     * @param path 路径
     * @param component 组件
     * @param icon 图标
     * @param sort 排序
     * @return 权限ID
     */
    private String createPermission(String name, String code, String type, String parentId, String path, String component, String icon, int sort) {
        // 检查权限是否已存在
        Optional<Permission> existingPermission = permissionRepository.findByCode(code);
        if (existingPermission.isPresent()) {
            return existingPermission.get().getId();
        }

        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setName(name);
        permission.setCode(code);
        permission.setType(type);
        permission.setParentId(parentId);
        permission.setPath(path);
        permission.setComponent(component);
        permission.setIcon(icon);
        permission.setSort(sort);
        permission.setStatus("active");
        permission.setCreatedBy("system");
        permission.setCreatedTime(LocalDateTime.now());
        permission.setUpdatedBy("system");
        permission.setUpdatedTime(LocalDateTime.now());
        permission.setIsDeleted(false);

        Permission savedPermission = permissionRepository.save(permission);
        return savedPermission.getId();
    }

    /**
     * 根据编码查找权限ID
     * @param code 权限编码
     * @return 权限ID
     */
    private String findPermissionIdByCode(String code) {
        Optional<Permission> permission = permissionRepository.findByCode(code);
        return permission.map(Permission::getId).orElse(null);
    }
}
