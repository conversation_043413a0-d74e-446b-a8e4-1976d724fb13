# 供应商分类API接口文档

本文档详细描述了供应商分类模块的API接口定义，包括请求路径、请求方法、请求参数、响应结果等信息，供前端开发人员参考使用。

## 基础信息

- 基础路径: `/api/supplier-categories`
- 认证方式: Bearer Token
- 响应格式: JSON

## 通用响应结构

所有API响应都遵循以下格式：

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {} // 具体数据，根据接口不同而不同
}
```

错误响应示例：

```json
{
  "success": false,
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

## API接口列表

### 1. 查询所有供应商分类

获取所有供应商分类列表。

- **URL**: `/api/supplier-categories`
- **方法**: `GET`
- **权限要求**: `supplier:read`
- **请求参数**: 无
- **响应结果**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "string",
      "categoryCode": "string",
      "categoryName": "string",
      "sortOrder": 0,
      "status": "active",
      "remark": "string",
      "createdBy": "string",
      "createTime": "2023-01-01T00:00:00",
      "updatedBy": "string",
      "updateTime": "2023-01-01T00:00:00"
    }
  ]
}
```

### 2. 查询所有启用的供应商分类

获取所有状态为启用的供应商分类列表。

- **URL**: `/api/supplier-categories/active`
- **方法**: `GET`
- **权限要求**: `supplier:read`
- **请求参数**: 无
- **响应结果**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "string",
      "categoryCode": "string",
      "categoryName": "string",
      "sortOrder": 0,
      "status": "active",
      "remark": "string",
      "createdBy": "string",
      "createTime": "2023-01-01T00:00:00",
      "updatedBy": "string",
      "updateTime": "2023-01-01T00:00:00"
    }
  ]
}
```

### 3. 根据ID查询供应商分类

根据ID获取单个供应商分类的详细信息。

- **URL**: `/api/supplier-categories/{id}`
- **方法**: `GET`
- **权限要求**: `supplier:read`
- **路径参数**:
  - `id`: 供应商分类ID，字符串类型
- **响应结果**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "string",
    "categoryCode": "string",
    "categoryName": "string",
    "sortOrder": 0,
    "status": "active",
    "remark": "string",
    "createdBy": "string",
    "createTime": "2023-01-01T00:00:00",
    "updatedBy": "string",
    "updateTime": "2023-01-01T00:00:00"
  }
}
```

### 4. 创建供应商分类

创建新的供应商分类。

- **URL**: `/api/supplier-categories`
- **方法**: `POST`
- **权限要求**: `supplier:create`
- **请求体**:

```json
{
  "categoryCode": "string", // 必填，分类编码，最大长度50
  "categoryName": "string", // 必填，分类名称，最大长度100
  "sortOrder": 0, // 必填，排序
  "status": "active", // 可选，状态，可选值：active, inactive
  "remark": "string" // 可选，备注，最大长度255
}
```

- **响应结果**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "string",
    "categoryCode": "string",
    "categoryName": "string",
    "sortOrder": 0,
    "status": "active",
    "remark": "string",
    "createdBy": "string",
    "createTime": "2023-01-01T00:00:00",
    "updatedBy": "string",
    "updateTime": "2023-01-01T00:00:00"
  }
}
```

### 5. 更新供应商分类

更新现有的供应商分类信息。

- **URL**: `/api/supplier-categories/{id}`
- **方法**: `PUT`
- **权限要求**: `supplier:update`
- **路径参数**:
  - `id`: 供应商分类ID，字符串类型
- **请求体**:

```json
{
  "categoryName": "string", // 可选，分类名称，最大长度100
  "sortOrder": 0, // 可选，排序
  "status": "active", // 可选，状态，可选值：active, inactive
  "remark": "string" // 可选，备注，最大长度255
}
```

- **响应结果**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "string",
    "categoryCode": "string",
    "categoryName": "string",
    "sortOrder": 0,
    "status": "active",
    "remark": "string",
    "createdBy": "string",
    "createTime": "2023-01-01T00:00:00",
    "updatedBy": "string",
    "updateTime": "2023-01-01T00:00:00"
  }
}
```

### 6. 删除供应商分类

删除指定ID的供应商分类。

- **URL**: `/api/supplier-categories/{id}`
- **方法**: `DELETE`
- **权限要求**: `supplier:delete`
- **路径参数**:
  - `id`: 供应商分类ID，字符串类型
- **响应结果**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
| ------ | ---- |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 创建供应商分类时，分类编码必须唯一，否则会返回错误
2. 更新供应商分类时，不能修改分类编码
3. 删除供应商分类前，应确保该分类未被任何供应商引用，否则可能导致数据一致性问题
4. 所有接口都需要在请求头中携带有效的JWT令牌，格式为`Authorization: Bearer {token}`
