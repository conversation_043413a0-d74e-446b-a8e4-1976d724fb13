package com.czerp.erpbackend.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * 自定义CORS过滤器
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class CustomCorsFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // log.debug("CustomCorsFilter processing request: {} {}", request.getMethod(), request.getRequestURI());

        String origin = request.getHeader("Origin");
        if (origin != null) {
            // log.debug("Request has Origin header: {}", origin);
            response.setHeader("Access-Control-Allow-Origin", origin);
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "Authorization, Content-Type, X-Requested-With, Accept");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Max-Age", "3600");

            // 对于OPTIONS请求，直接返回200
            if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
                // log.debug("Handling OPTIONS request, returning 200 OK");
                response.setStatus(HttpServletResponse.SC_OK);
                return;
            }
        } else {
            // log.debug("Request does not have Origin header");
        }

        filterChain.doFilter(request, response);
    }
}
