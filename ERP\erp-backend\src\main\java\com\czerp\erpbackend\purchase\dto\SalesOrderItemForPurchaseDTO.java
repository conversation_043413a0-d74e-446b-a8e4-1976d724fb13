package com.czerp.erpbackend.purchase.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 用于采购订单引用销售订单明细的DTO
 * 包含采购订单需要的销售订单明细字段
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderItemForPurchaseDTO {
    /**
     * 生产单号
     */
    private String productionOrderNo;

    /**
     * 纸质
     */
    private String paperType;

    /**
     * 纸板类别
     */
    private String paperBoardCategory;

    /**
     * 楞别
     */
    private String corrugationType;

    /**
     * 纸度
     */
    private BigDecimal width;

    /**
     * 纸长
     */
    private BigDecimal length;

    /**
     * 压线尺寸(纸度)
     */
    private String creasingSize;

    /**
     * 压线方式
     */
    private String creasingMethod;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 客方货号
     */
    private String customerProductCode;

    /**
     * 品名
     */
    private String productName;

    /**
     * 工艺要求
     */
    private String processRequirements;

    /**
     * 盒式
     */
    private String boxType;

    /**
     * 产品规格
     */
    private String specification;

    /**
     * 订单纸质
     */
    private String orderPaperType;

    /**
     * 模开数
     */
    private Integer mouldOpenCount;

    /**
     * 订单数
     */
    private Integer quantity;

    /**
     * 订单交期
     */
    private LocalDate deliveryDate;

    /**
     * 销售单交期
     */
    private LocalDate salesOrderDeliveryDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 销售订单ID
     */
    private String orderId;

    /**
     * 销售订单明细ID
     */
    private String orderItemId;

    /**
     * 已采购数量
     */
    private Integer purchasedQuantity;

    /**
     * 已采购数量（别名，与purchasedQuantity保持一致，用于前端兼容）
     */
    private Integer purchasedBoardCount;

    /**
     * 未采购数量（动态计算：boardCount - purchasedQuantity）
     */
    private Integer unpurchasedQuantity;

    /**
     * 采购状态
     */
    private String purchaseStatus;

    /**
     * 销售单号 - 从sales_order表获取
     */
    private String orderNo;

    /**
     * 客户编码 - 从sales_order表获取
     */
    private String customerCode;

    /**
     * 订单日期 - 从sales_order表获取
     */
    private LocalDate orderDate;

    /**
     * 生产备注 - 从sales_order_item表获取
     */
    private String productionRemark;

    /**
     * 备品数 - 从sales_order_item表获取
     */
    private Integer spareQuantity;

    /**
     * 纸质 - 从sales_order_material表获取
     */
    private String paperQuality;

    /**
     * 纸度 - 从sales_order_material表获取
     */
    private BigDecimal paperWidth;

    /**
     * 纸长 - 从sales_order_material表获取
     */
    private BigDecimal paperLength;

    /**
     * 纸板数 - 从sales_order_material表获取
     */
    private Integer boardCount;

    /**
     * 尺寸单位
     */
    private String sizeUnit;

    /**
     * 生产长
     */
    private BigDecimal productionLength;

    /**
     * 生产宽
     */
    private BigDecimal productionWidth;

    /**
     * 生产高
     */
    private BigDecimal productionHeight;

    /**
     * 生产规格
     */
    private String productionSpecification;
}
