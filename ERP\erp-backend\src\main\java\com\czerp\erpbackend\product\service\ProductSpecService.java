package com.czerp.erpbackend.product.service;

import com.czerp.erpbackend.product.dto.CreateSpecRequest;
import com.czerp.erpbackend.product.dto.ProductSpecDTO;
import com.czerp.erpbackend.product.dto.UpdateSpecRequest;

import java.util.List;

/**
 * 产品规格服务接口
 */
public interface ProductSpecService {
    
    /**
     * 查询所有规格
     * @return 规格列表
     */
    List<ProductSpecDTO> findAllSpecs();
    
    /**
     * 根据ID查询规格
     * @param id 规格ID
     * @return 规格信息
     */
    ProductSpecDTO findSpecById(String id);
    
    /**
     * 创建规格
     * @param request 创建请求
     * @return 规格信息
     */
    ProductSpecDTO createSpec(CreateSpecRequest request);
    
    /**
     * 更新规格
     * @param id 规格ID
     * @param request 更新请求
     * @return 规格信息
     */
    ProductSpecDTO updateSpec(String id, UpdateSpecRequest request);
    
    /**
     * 删除规格
     * @param id 规格ID
     */
    void deleteSpec(String id);
}
