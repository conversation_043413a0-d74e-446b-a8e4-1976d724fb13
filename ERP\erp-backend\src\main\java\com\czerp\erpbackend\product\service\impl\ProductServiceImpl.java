package com.czerp.erpbackend.product.service.impl;

import com.czerp.erpbackend.common.dto.ImportResult;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.common.util.ExcelUtil;
import com.czerp.erpbackend.product.dto.*;
import com.czerp.erpbackend.product.entity.Product;
import com.czerp.erpbackend.product.entity.ProductCategory;
import com.czerp.erpbackend.product.entity.ProductSpec;
import com.czerp.erpbackend.product.mapper.ProductMapper;
import com.czerp.erpbackend.product.repository.ProductCategoryRepository;
import com.czerp.erpbackend.product.repository.ProductRepository;
import com.czerp.erpbackend.product.repository.ProductSpecRepository;
import com.czerp.erpbackend.product.service.ProductService;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 货品服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductServiceImpl implements ProductService {

    private final ProductRepository productRepository;
    private final ProductCategoryRepository categoryRepository;
    private final ProductSpecRepository specRepository;
    private final ProductMapper productMapper;

    /**
     * 分页查询货品列表
     * @param request 查询请求
     * @return 货品分页列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<ProductDTO> findProducts(ProductQueryRequest request) {
        log.debug("Finding products with request: {}", request);

        // 构建分页参数
        int page = request.getPage() != null ? request.getPage() - 1 : 0;
        int size = request.getSize() != null ? request.getSize() : 10;

        // 构建排序参数
        Sort sort = Sort.unsorted();
        if (request.getSortField() != null && !request.getSortField().isEmpty()) {
            // 处理字段名称映射，前端使用驼峰命名，后端使用下划线命名
            String sortField = request.getSortField();
            // 适配前端传入的createTime到后端的createdTime
            if ("createTime".equals(sortField)) {
                sortField = "createdTime";
            } else if ("updateTime".equals(sortField)) {
                sortField = "updatedTime";
            }

            sort = Sort.by(
                    "desc".equalsIgnoreCase(request.getSortDirection()) ?
                            Sort.Direction.DESC : Sort.Direction.ASC,
                    sortField
            );
        } else {
            // 默认按创建时间降序排序
            sort = Sort.by(Sort.Direction.DESC, "createdTime");
        }

        Pageable pageable = PageRequest.of(page, size, sort);

        // 处理日期参数
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;

        if (request.getStartDate() != null) {
            startDateTime = LocalDateTime.of(request.getStartDate(), LocalTime.MIN);
        }

        if (request.getEndDate() != null) {
            endDateTime = LocalDateTime.of(request.getEndDate(), LocalTime.MAX);
        }

        // 记录查询参数
        log.debug("Query params - keyword: '{}', categoryId: '{}', disabled: {}, startDateTime: {}, endDateTime: {}",
                request.getKeyword(), request.getCategoryId(), request.getDisabled(), startDateTime, endDateTime);

        // 执行查询
        Page<Product> productPage = productRepository.search(
                request.getKeyword(),
                request.getCategoryId(),
                request.getDisabled(),
                startDateTime,
                endDateTime,
                pageable
        );

        // 记录查询结果
        log.debug("Query result - content size: {}, totalElements: {}, totalPages: {}",
                productPage.getContent().size(), productPage.getTotalElements(), productPage.getTotalPages());

        // 转换为DTO
        List<ProductDTO> productDTOs = productPage.getContent().stream()
                .map(productMapper::toDto)
                .collect(Collectors.toList());

        // 构建分页响应
        // 计算正确的页码（从0开始转为从1开始）
        int pageNumber = request.getPage() != null ? request.getPage() : 1;

        // 计算正确的总页数（如果没有数据，总页数应该为0）
        long totalElements = productPage.getTotalElements();
        int totalPages = totalElements > 0 ? productPage.getTotalPages() : 0;

        // 确保size正确
        int pageSize = productPage.getSize() > 0 ? productPage.getSize() : size;

        log.debug("Page info - page: {}, size: {}, totalElements: {}, totalPages: {}",
                pageNumber, pageSize, totalElements, totalPages);

        return new PageResponse<>(
                productDTOs,
                totalElements,
                totalPages,
                pageNumber,
                pageSize
        );
    }

    /**
     * 根据ID查询货品
     * @param id 货品ID
     * @return 货品信息
     */
    @Override
    @Transactional(readOnly = true)
    public ProductDTO findProductById(String id) {
        log.debug("Finding product by id: {}", id);

        Product product = productRepository.findById(id)
                .orElseThrow(() -> new BusinessException("货品不存在"));

        return productMapper.toDto(product);
    }

    /**
     * 查询所有启用的货品
     * @return 货品列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<ProductDTO> findActiveProducts() {
        log.debug("Finding all active products");

        List<Product> products = productRepository.findAllActive();

        return products.stream()
                .map(productMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 创建货品
     * @param request 创建请求
     * @return 货品信息
     */
    @Override
    @Transactional
    public ProductDTO createProduct(CreateProductRequest request) {
        log.debug("Creating product with request: {}", request);

        // 检查编码是否已存在
        if (productRepository.existsByCode(request.getCode())) {
            throw new BusinessException("货品编码已存在");
        }

        // 检查分类是否存在
        ProductCategory category = categoryRepository.findById(request.getCategoryId())
                .orElseThrow(() -> new BusinessException("分类不存在"));

        // 检查规格是否存在
        ProductSpec spec = specRepository.findById(request.getSpecId())
                .orElseThrow(() -> new BusinessException("规格不存在"));

        // 创建货品
        Product product = productMapper.toEntity(request);
        product.setId(UUID.randomUUID().toString());

        // 保存货品
        product = productRepository.save(product);

        return productMapper.toDto(product);
    }

    /**
     * 更新货品
     * @param id 货品ID
     * @param request 更新请求
     * @return 货品信息
     */
    @Override
    @Transactional
    public ProductDTO updateProduct(String id, UpdateProductRequest request) {
        log.debug("Updating product with id: {} and request: {}", id, request);

        // 查询货品
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new BusinessException("货品不存在"));

        // 检查分类是否存在
        if (request.getCategoryId() != null && !request.getCategoryId().equals(product.getCategoryId())) {
            categoryRepository.findById(request.getCategoryId())
                    .orElseThrow(() -> new BusinessException("分类不存在"));
        }

        // 检查规格是否存在
        if (request.getSpecId() != null && !request.getSpecId().equals(product.getSpecId())) {
            specRepository.findById(request.getSpecId())
                    .orElseThrow(() -> new BusinessException("规格不存在"));
        }

        // 更新货品
        productMapper.updateEntity(request, product);

        // 保存货品
        product = productRepository.save(product);

        return productMapper.toDto(product);
    }

    /**
     * 删除货品
     * @param id 货品ID
     */
    @Override
    @Transactional
    public void deleteProduct(String id) {
        log.debug("Deleting product with id: {}", id);

        // 查询货品
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new BusinessException("货品不存在"));

        // 删除货品（逻辑删除）
        product.setIsDeleted(true);
        productRepository.save(product);
    }

    /**
     * 批量删除货品
     * @param ids 货品ID列表
     */
    @Override
    @Transactional
    public void batchDeleteProducts(List<String> ids) {
        log.debug("Batch deleting products with ids: {}", ids);

        // 查询货品列表
        List<Product> products = productRepository.findAllById(ids);

        if (products.isEmpty()) {
            return;
        }

        // 删除货品（逻辑删除）
        products.forEach(product -> product.setIsDeleted(true));
        productRepository.saveAll(products);
    }

    /**
     * 切换货品状态
     * @param id 货品ID
     * @param disabled 是否禁用
     * @return 货品信息
     */
    @Override
    @Transactional
    public ProductDTO toggleProductStatus(String id, boolean disabled) {
        log.debug("Toggling product status with id: {} and disabled: {}", id, disabled);

        // 查询货品
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new BusinessException("货品不存在"));

        // 更新状态
        product.setDisabled(disabled);
        product = productRepository.save(product);

        return productMapper.toDto(product);
    }

    /**
     * 批量更新货品价格
     * @param request 更新请求
     */
    @Override
    @Transactional
    public void updateProductPrices(UpdateProductPriceRequest request) {
        log.debug("Updating product prices with request: {}", request);

        // 获取所有货品ID
        List<String> productIds = request.getItems().stream()
                .map(UpdateProductPriceRequest.PriceItem::getId)
                .collect(Collectors.toList());

        // 查询货品列表
        List<Product> products = productRepository.findAllById(productIds);

        if (products.isEmpty()) {
            return;
        }

        // 更新价格
        for (Product product : products) {
            request.getItems().stream()
                    .filter(item -> item.getId().equals(product.getId()))
                    .findFirst()
                    .ifPresent(item -> product.setPrice(item.getPrice()));
        }

        // 保存货品
        productRepository.saveAll(products);
    }

    /**
     * 导入货品
     * @param file Excel文件
     * @return 导入结果
     */
    @Override
    @Transactional
    public ImportResult importProducts(MultipartFile file) {
        log.debug("Importing products from Excel file: {}", file.getOriginalFilename());

        ImportResult result = ImportResult.builder()
                .success(0)
                .fail(0)
                .errors(new ArrayList<>())
                .build();

        try {
            List<CreateProductRequest> requests = ExcelUtil.readExcel(file, this::mapRowToProduct);

            if (requests == null || requests.isEmpty()) {
                result.addError("Excel文件中没有有效数据");
                return result;
            }

            log.debug("Found {} products in Excel file", requests.size());

            // 获取当前用户名
            String currentUsername = com.czerp.erpbackend.common.util.SecurityUtils.getCurrentUsername();
            log.debug("Current username for import operation: {}", currentUsername);

            for (CreateProductRequest request : requests) {
                try {
                    // 验证必填字段
                    if (!StringUtils.hasText(request.getCode())) {
                        throw new BusinessException("盒式编码不能为空");
                    }
                    if (!StringUtils.hasText(request.getName())) {
                        throw new BusinessException("盒式名称不能为空");
                    }

                    // 验证前端特有的必填字段（通过描述字段解析）
                    String description = request.getDescription();
                    if (description == null) {
                        description = "";
                    }

                    if (!description.contains("报价公式:") || description.contains("报价公式: ,")) {
                        throw new BusinessException("报价公式不能为空");
                    }

                    if (!StringUtils.hasText(request.getUnit())) {
                        throw new BusinessException("算价单位不能为空");
                    }

                    // 连接方式不再是必填字段
                    // 移除了对连接方式的必填验证

                    // 检查编码是否已存在
                    if (productRepository.existsByCode(request.getCode())) {
                        throw new BusinessException("货品编码已存在: " + request.getCode());
                    }

                    // 检查分类是否存在
                    if (StringUtils.hasText(request.getCategoryId())) {
                        categoryRepository.findById(request.getCategoryId())
                                .orElseThrow(() -> new BusinessException("分类不存在: " + request.getCategoryId()));
                    }

                    // 检查规格是否存在
                    if (StringUtils.hasText(request.getSpecId())) {
                        specRepository.findById(request.getSpecId())
                                .orElseThrow(() -> new BusinessException("规格不存在: " + request.getSpecId()));
                    }

                    // 创建货品实体并手动设置审计字段
                    Product product = productMapper.toEntity(request);
                    product.setId(UUID.randomUUID().toString());

                    // 手动设置审计字段，确保使用小写下划线命名的字段
                    product.setCreatedBy(currentUsername);
                    product.setUpdatedBy(currentUsername);
                    product.setCreatedTime(LocalDateTime.now());
                    product.setUpdatedTime(LocalDateTime.now());
                    product.setVersion(0);
                    product.setIsDeleted(false);

                    // 保存产品
                    productRepository.save(product);

                    result.incrementSuccess();
                } catch (Exception e) {
                    log.error("Error importing product: {}", e.getMessage(), e);
                    result.incrementFail();
                    result.addError("导入失败: " + request.getCode() + " - " + request.getName() + ", 原因: " + e.getMessage());
                }
            }

            log.info("Imported products: {} success, {} fail", result.getSuccess(), result.getFail());
            return result;
        } catch (IOException e) {
            log.error("Error reading Excel file: {}", e.getMessage(), e);
            result.incrementFail();
            result.addError("读取Excel文件失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 将Excel行映射为货品创建请求
     * @param row Excel行
     * @return 货品创建请求
     */
    private CreateProductRequest mapRowToProduct(Row row) {
        // 如果前两列都为空，则认为是空行
        String code = ExcelUtil.getStringCellValue(row, 0);
        String name = ExcelUtil.getStringCellValue(row, 1);
        if (!StringUtils.hasText(code) && !StringUtils.hasText(name)) {
            return null;
        }

        // 获取前端特有字段
        String categoryName = ExcelUtil.getStringCellValue(row, 2);
        String specName = ExcelUtil.getStringCellValue(row, 3);
        String formula = ExcelUtil.getStringCellValue(row, 4);
        String calcUnit = ExcelUtil.getStringCellValue(row, 5);
        String quoteUnit = ExcelUtil.getStringCellValue(row, 6);
        String connectionMethod = ExcelUtil.getStringCellValue(row, 7);
        Boolean prohibitDoubleBerth = ExcelUtil.getBooleanCellValue(row, 8);
        Integer doubleBerthLengthThreshold = ExcelUtil.getIntegerCellValue(row, 9);
        String jumpTolerance = ExcelUtil.getStringCellValue(row, 10);
        String customerCode = ExcelUtil.getStringCellValue(row, 11);
        String customerName = ExcelUtil.getStringCellValue(row, 12);
        BigDecimal price = ExcelUtil.getBigDecimalCellValue(row, 13);
        String imageUrl = ExcelUtil.getStringCellValue(row, 14);
        Boolean disabled = ExcelUtil.getBooleanCellValue(row, 15);

        // 根据分类名称和规格名称查找对应的ID
        String categoryId = null;
        String specId = null;

        // 查找分类ID
        if (StringUtils.hasText(categoryName)) {
            try {
                // 获取所有分类
                List<ProductCategory> categories = categoryRepository.findAll();
                // 查找匹配的分类
                Optional<ProductCategory> categoryOpt = categories.stream()
                        .filter(c -> categoryName.equals(c.getName()))
                        .findFirst();
                if (categoryOpt.isPresent()) {
                    categoryId = categoryOpt.get().getId();
                } else {
                    // 如果找不到分类，抛出异常，中断处理
                    throw new BusinessException("分类名称 '" + categoryName + "' 不存在");
                }
            } catch (BusinessException e) {
                // 直接抛出业务异常
                throw e;
            } catch (Exception e) {
                log.error("查找分类ID失败: {}", e.getMessage(), e);
                throw new BusinessException("查找分类ID失败: " + e.getMessage());
            }
        } else {
            // 分类名称为空，抛出异常
            throw new BusinessException("分类名称不能为空");
        }

        // 查找规格ID
        if (StringUtils.hasText(specName)) {
            try {
                // 获取所有规格
                List<ProductSpec> specs = specRepository.findAll();
                // 查找匹配的规格
                Optional<ProductSpec> specOpt = specs.stream()
                        .filter(s -> specName.equals(s.getName()))
                        .findFirst();
                if (specOpt.isPresent()) {
                    specId = specOpt.get().getId();
                } else {
                    // 如果找不到规格，抛出异常，中断处理
                    throw new BusinessException("规格名称 '" + specName + "' 不存在");
                }
            } catch (BusinessException e) {
                // 直接抛出业务异常
                throw e;
            } catch (Exception e) {
                log.error("查找规格ID失败: {}", e.getMessage(), e);
                throw new BusinessException("查找规格ID失败: " + e.getMessage());
            }
        } else {
            // 规格名称为空，抛出异常
            throw new BusinessException("规格名称不能为空");
        }

        // 构建描述字段，包含前端特有字段
        StringBuilder description = new StringBuilder();
        if (StringUtils.hasText(formula)) {
            description.append("报价公式: ").append(formula).append(", ");
        }
        // 连接方式现在是可选的，只有在有值时才添加到描述中
        if (StringUtils.hasText(connectionMethod)) {
            description.append("连接方式: ").append(connectionMethod).append(", ");
        }
        if (StringUtils.hasText(jumpTolerance)) {
            description.append("跳度公差: ").append(jumpTolerance).append(", ");
        }
        if (prohibitDoubleBerth != null) {
            description.append("禁止双驳: ").append(prohibitDoubleBerth).append(", ");
        }
        if (doubleBerthLengthThreshold != null) {
            description.append("长度大于此值双驳: ").append(doubleBerthLengthThreshold).append(", ");
        }
        if (StringUtils.hasText(customerCode) || StringUtils.hasText(customerName)) {
            description.append("客户: ");
            if (StringUtils.hasText(customerCode)) {
                description.append(customerCode);
                if (StringUtils.hasText(customerName)) {
                    description.append("-");
                }
            }
            if (StringUtils.hasText(customerName)) {
                description.append(customerName);
            }
        }

        // 移除最后的逗号和空格（如果有）
        String descriptionStr = description.toString();
        if (descriptionStr.endsWith(", ")) {
            descriptionStr = descriptionStr.substring(0, descriptionStr.length() - 2);
        }

        // 创建请求对象
        CreateProductRequest request = new CreateProductRequest();
        request.setCode(code);
        request.setName(name);
        request.setUnit(calcUnit); // 使用算价单位作为后端的单位
        request.setPrice(price);
        request.setDescription(descriptionStr);
        request.setImageUrl(imageUrl);
        request.setDisabled(disabled);

        // 只有在找到对应ID时才设置分类ID和规格ID
        if (categoryId != null) {
            request.setCategoryId(categoryId);
        }

        if (specId != null) {
            request.setSpecId(specId);
        }

        return request;
    }
}
