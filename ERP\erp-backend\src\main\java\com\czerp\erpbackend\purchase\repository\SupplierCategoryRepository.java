package com.czerp.erpbackend.purchase.repository;

import com.czerp.erpbackend.purchase.entity.SupplierCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 供应商分类存储库
 */
@Repository
public interface SupplierCategoryRepository extends JpaRepository<SupplierCategory, String> {
    
    /**
     * 根据分类编码查找分类
     * @param categoryCode 分类编码
     * @return 分类
     */
    Optional<SupplierCategory> findByCategoryCode(String categoryCode);
    
    /**
     * 判断分类编码是否存在
     * @param categoryCode 分类编码
     * @return 是否存在
     */
    boolean existsByCategoryCode(String categoryCode);
    
    /**
     * 查询所有启用的分类
     * @param status 状态
     * @return 分类列表
     */
    List<SupplierCategory> findByStatusOrderBySortOrderAsc(String status);
}
