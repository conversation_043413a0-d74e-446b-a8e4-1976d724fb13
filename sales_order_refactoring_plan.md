# 销售管理模块重构实施方案

## 一、背景分析

当前销售管理模块已实现了销售订单的增删改查等核心功能，同时集成了订单状态、生产、采购、送货等相关功能。由于数据库结构发生变化，特别是字段名称的变更，需要对后端代码进行同步调整。同时，需要移除与订单状态、生产、采购、送货等相关的功能代码。

## 二、现状分析

### 1. 数据库结构变更

原数据库表结构：
- `sal_order`：销售订单主表
- `sal_order_item`：销售订单明细表
- `sal_order_production_schedule`：销售订单生产排程表
- `sal_order_delivery`：销售订单送货记录表
- `sal_order_inventory`：销售订单入库记录表

新数据库表结构：
- `sales_order`：销售订单头表
- `sales_order_item`：销售订单行表

主要变更点：
1. 表名从 `sal_order` 变更为 `sales_order`
2. 字段命名采用小写下划线规则
3. 移除了订单状态相关字段（如 `order_status`, `production_status`, `inventory_status`, `delivery_status`）
4. 移除了与生产、送货、入库相关的统计字段
5. 新增了一些字段，如 `payment_method`, `customer_purchaser` 等

### 2. 代码结构分析

当前代码结构：
- 实体类：`SalesOrder`, `SalesOrderItem` 等
- DTO类：`SalesOrderDTO`, `SalesOrderDetailDTO`, `SalesOrderItemDTO` 等
- 枚举类：`OrderStatus`, `ProductionStatus`, `InventoryStatus`, `DeliveryStatus` 等
- 控制器：`SalesOrderController`, `SalesOrderStatisticsController`, `SalesOrderInventoryController` 等
- 服务类：`SalesOrderService`, `SalesOrderStatisticsService`, `SalesOrderInventoryService` 等

## 三、实施方案

### 1. 数据库迁移方案

由于数据库结构发生了较大变化，需要进行数据迁移。建议采用以下步骤：

1. 创建新的数据库表结构（`sales_order` 和 `sales_order_item`）
2. 编写数据迁移脚本，将旧表数据迁移到新表
3. 在迁移过程中进行字段映射和转换
4. 迁移完成后，保留旧表一段时间，确认无问题后再删除

### 2. 代码重构方案

#### 2.1 实体类重构

1. 修改 `SalesOrder` 实体类：
   - 更新 `@Table` 注解，指向新表名 `sales_order`
   - 更新字段注解，匹配新的字段名
   - 移除订单状态、生产状态、入库状态、送货状态等相关字段
   - 移除与生产、送货、入库相关的统计字段
   - 添加新增字段，如 `paymentMethod`, `customerPurchaser` 等

2. 修改 `SalesOrderItem` 实体类：
   - 更新 `@Table` 注解，指向新表名 `sales_order_item`
   - 更新字段注解，匹配新的字段名
   - 移除与生产、送货、入库相关的字段

3. 移除不再需要的实体类：
   - `SalesOrderProductionSchedule`
   - `SalesOrderDelivery`
   - `SalesOrderInventory`

#### 2.2 DTO类重构

1. 修改 `SalesOrderDTO` 和 `SalesOrderDetailDTO`：
   - 移除订单状态、生产状态、入库状态、送货状态等相关字段
   - 移除与生产、送货、入库相关的统计字段
   - 添加新增字段，如 `paymentMethod`, `customerPurchaser` 等

2. 修改 `SalesOrderItemDTO`：
   - 更新字段，匹配新的字段名
   - 移除与生产、送货、入库相关的字段

3. 修改 `SalesOrderQueryDTO`：
   - 移除订单状态、生产状态、入库状态、送货状态等相关查询条件

4. 移除不再需要的DTO类：
   - `SalesOrderProductionScheduleDTO`
   - `SalesOrderDeliveryDTO`
   - `SalesOrderInventoryDTO`
   - `SalesOrderStatisticsDTO`

#### 2.3 枚举类处理

1. 移除不再需要的枚举类：
   - `OrderStatus`
   - `ProductionStatus`
   - `InventoryStatus`
   - `DeliveryStatus`

#### 2.4 控制器重构

1. 修改 `SalesOrderController`：
   - 更新请求处理方法，移除与订单状态、生产、送货、入库相关的逻辑
   - 更新参数验证和返回结果，适配新的DTO结构

2. 移除不再需要的控制器：
   - `SalesOrderStatusController`
   - `SalesOrderProductionController`
   - `SalesOrderInventoryController`
   - `SalesOrderDeliveryController`
   - `SalesOrderStatisticsController`

#### 2.5 服务类重构

1. 修改 `SalesOrderService` 接口和实现类：
   - 移除与订单状态、生产、送货、入库相关的方法
   - 更新现有方法，移除与订单状态、生产、送货、入库相关的逻辑
   - 更新实体类和DTO之间的转换方法，适配新的字段结构

2. 移除不再需要的服务类：
   - `SalesOrderStatusService`
   - `SalesOrderProductionService`
   - `SalesOrderInventoryService`
   - `SalesOrderDeliveryService`
   - `SalesOrderStatisticsService`

#### 2.6 Repository重构

1. 修改 `SalesOrderRepository` 和 `SalesOrderItemRepository`：
   - 更新查询方法，移除与订单状态、生产、送货、入库相关的查询
   - 更新自定义查询，适配新的表名和字段名

2. 移除不再需要的Repository：
   - `SalesOrderProductionScheduleRepository`
   - `SalesOrderDeliveryRepository`
   - `SalesOrderInventoryRepository`

### 3. 前端适配方案

前端代码也需要进行相应的调整，主要包括：

1. 更新API调用：
   - 移除与订单状态、生产、送货、入库相关的API调用
   - 更新请求参数和响应处理，适配新的字段结构

2. 更新界面组件：
   - 移除与订单状态、生产、送货、入库相关的界面元素
   - 更新表单字段，适配新的字段结构
   - 更新表格列定义，适配新的字段结构

3. 更新类型定义：
   - 更新 `SalesOrderDTO`, `SalesOrderItemDTO` 等类型定义
   - 移除 `OrderStatus`, `ProductionStatus`, `InventoryStatus`, `DeliveryStatus` 等枚举定义

## 四、实施步骤

### 第一阶段：准备工作

1. 备份当前代码和数据库
2. 创建新的数据库表结构
3. 编写数据迁移脚本

### 第二阶段：后端代码重构

1. 重构实体类
2. 重构DTO类
3. 移除不需要的枚举类
4. 重构Repository
5. 重构Service
6. 重构Controller
7. 更新单元测试

### 第三阶段：前端代码适配

1. 更新API调用
2. 更新界面组件
3. 更新类型定义

### 第四阶段：测试与部署

1. 单元测试
2. 集成测试
3. 功能测试
4. 部署上线

## 五、风险与应对措施

### 1. 数据迁移风险

**风险**：数据迁移过程中可能出现数据丢失或不一致的情况。
**应对措施**：
- 在迁移前进行完整备份
- 编写详细的数据校验脚本
- 先在测试环境进行迁移测试
- 制定回滚方案

### 2. 功能缺失风险

**风险**：移除订单状态、生产、送货、入库等功能后，可能影响用户的正常业务流程。
**应对措施**：
- 与业务部门充分沟通，确认功能移除的影响
- 提供替代方案或临时解决方案
- 分阶段实施，先保留部分功能，再逐步移除

### 3. 接口兼容性风险

**风险**：API接口变更可能影响第三方系统的集成。
**应对措施**：
- 梳理所有接口调用方
- 提前通知接口变更计划
- 考虑提供兼容性接口，逐步过渡

## 六、时间规划

| 阶段 | 工作内容 | 预计时间 |
| --- | --- | --- |
| 准备工作 | 备份、创建新表、编写迁移脚本 | 2天 |
| 后端重构 | 实体类、DTO、Repository、Service、Controller重构 | 5天 |
| 前端适配 | API调用、界面组件、类型定义更新 | 3天 |
| 测试与部署 | 单元测试、集成测试、功能测试、部署 | 3天 |
| 总计 | | 13天 |

## 七、总结

本实施方案针对销售管理模块的重构，主要解决数据库字段变更以及移除订单状态、生产、采购、送货等相关功能的问题。通过分阶段实施，可以确保系统平稳过渡，同时降低风险。在实施过程中，需要与业务部门保持密切沟通，确保功能变更不影响正常业务流程。
