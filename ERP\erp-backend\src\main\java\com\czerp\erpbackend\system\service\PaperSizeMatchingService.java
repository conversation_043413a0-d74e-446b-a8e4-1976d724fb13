package com.czerp.erpbackend.system.service;

import com.czerp.erpbackend.system.dto.PaperSizeMatchingRequest;
import com.czerp.erpbackend.system.dto.PaperSizeMatchingResponse;
import com.czerp.erpbackend.system.dto.PaperSizeRecommendation;

import java.util.List;

/**
 * 纸度匹配服务接口
 */
public interface PaperSizeMatchingService {

    /**
     * 查找最佳纸度
     * @param request 纸度匹配请求
     * @return 纸度匹配响应
     */
    PaperSizeMatchingResponse findBestPaperSize(PaperSizeMatchingRequest request);

    /**
     * 获取纸度推荐列表
     * @param request 纸度匹配请求
     * @return 纸度推荐列表
     */
    List<PaperSizeRecommendation> getPaperSizeRecommendations(PaperSizeMatchingRequest request);
}
