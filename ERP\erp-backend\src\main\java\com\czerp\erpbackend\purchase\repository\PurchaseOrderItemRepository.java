package com.czerp.erpbackend.purchase.repository;

import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 采购订单明细Repository
 */
@Repository
public interface PurchaseOrderItemRepository extends JpaRepository<PurchaseOrderItem, Long>, JpaSpecificationExecutor<PurchaseOrderItem> {

    /**
     * 根据采购订单ID查询采购订单明细
     * @param purchaseOrderId 采购订单ID
     * @return 采购订单明细列表
     */
    List<PurchaseOrderItem> findByPurchaseOrderId(Long purchaseOrderId);

    /**
     * 根据采购订单ID删除采购订单明细
     * @param purchaseOrderId 采购订单ID
     */
    void deleteByPurchaseOrderId(Long purchaseOrderId);

    /**
     * 根据销售订单明细ID查询采购订单明细
     * @param sourceSalesOrderItemId 销售订单明细ID
     * @return 采购订单明细列表
     */
    List<PurchaseOrderItem> findBySourceSalesOrderItemId(String sourceSalesOrderItemId);

    /**
     * 根据销售订单明细ID列表查询采购订单明细
     * @param sourceSalesOrderItemIds 销售订单明细ID列表
     * @return 采购订单明细列表
     */
    List<PurchaseOrderItem> findBySourceSalesOrderItemIdIn(List<String> sourceSalesOrderItemIds);

    /**
     * 根据销售订单明细ID查询采购订单明细及其关联的采购订单信息
     * @param sourceSalesOrderItemIds 销售订单明细ID列表
     * @return 包含采购订单明细、采购单号和供应商名称的结果列表
     */
    @Query("SELECT poi, po.purchaseOrderNo, po.supplierName FROM PurchaseOrderItem poi " +
           "JOIN poi.purchaseOrder po " +
           "WHERE poi.sourceSalesOrderItemId IN :itemIds")
    List<Object[]> findWithPurchaseOrderInfoBySourceSalesOrderItemIdIn(@Param("itemIds") List<String> sourceSalesOrderItemIds);

    /**
     * 动态计算采购订单明细的已入库数量
     * @param purchaseOrderItemId 采购订单明细ID
     * @return 已入库数量，如果没有入库记录则返回0
     */
    @Query("SELECT COALESCE(SUM(sii.quantity), 0) FROM StockInboundItem sii " +
           "WHERE sii.purchaseOrderItem.id = :purchaseOrderItemId " +
           "AND sii.isDeleted = false")
    Integer calculateReceivedQuantity(@Param("purchaseOrderItemId") Long purchaseOrderItemId);

    /**
     * 批量动态计算多个采购订单明细的已入库数量
     * @param purchaseOrderItemIds 采购订单明细ID列表
     * @return 包含采购订单明细ID和对应已入库数量的结果列表
     */
    @Query("SELECT sii.purchaseOrderItem.id, COALESCE(SUM(sii.quantity), 0) FROM StockInboundItem sii " +
           "WHERE sii.purchaseOrderItem.id IN :purchaseOrderItemIds " +
           "AND sii.isDeleted = false " +
           "GROUP BY sii.purchaseOrderItem.id")
    List<Object[]> calculateReceivedQuantitiesBatch(@Param("purchaseOrderItemIds") List<Long> purchaseOrderItemIds);

    /**
     * 查询未完全入库的采购订单明细（使用动态计算）
     * 只返回 quantity > 已入库数量 的记录
     */
    @Query("SELECT poi FROM PurchaseOrderItem poi " +
           "WHERE poi.quantity > COALESCE(" +
           "    (SELECT SUM(sii.quantity) FROM StockInboundItem sii " +
           "     WHERE sii.purchaseOrderItem.id = poi.id AND sii.isDeleted = false), 0)")
    Page<PurchaseOrderItem> findAvailableForInbound(Pageable pageable);

    /**
     * 根据条件查询未完全入库的采购订单明细（使用动态计算）
     */
    @Query("SELECT poi FROM PurchaseOrderItem poi " +
           "JOIN poi.purchaseOrder po " +
           "WHERE poi.quantity > COALESCE(" +
           "    (SELECT SUM(sii.quantity) FROM StockInboundItem sii " +
           "     WHERE sii.purchaseOrderItem.id = poi.id AND sii.isDeleted = false), 0) " +
           "AND (:supplierName IS NULL OR po.supplierName LIKE %:supplierName%) " +
           "AND (:purchaseOrderNo IS NULL OR po.purchaseOrderNo LIKE %:purchaseOrderNo%) " +
           "AND (:paperBoardCategory IS NULL OR poi.paperBoardCategory = :paperBoardCategory) " +
           "AND (:purchaseDateStart IS NULL OR po.purchaseDate >= :purchaseDateStart) " +
           "AND (:purchaseDateEnd IS NULL OR po.purchaseDate <= :purchaseDateEnd) " +
           "AND (:deliveryDateStart IS NULL OR poi.deliveryDate >= :deliveryDateStart) " +
           "AND (:deliveryDateEnd IS NULL OR poi.deliveryDate <= :deliveryDateEnd)")
    Page<PurchaseOrderItem> findAvailableForInboundWithConditions(
            @Param("supplierName") String supplierName,
            @Param("purchaseOrderNo") String purchaseOrderNo,
            @Param("paperBoardCategory") String paperBoardCategory,
            @Param("purchaseDateStart") LocalDate purchaseDateStart,
            @Param("purchaseDateEnd") LocalDate purchaseDateEnd,
            @Param("deliveryDateStart") LocalDate deliveryDateStart,
            @Param("deliveryDateEnd") LocalDate deliveryDateEnd,
            Pageable pageable);
}
