-- 添加生产排程管理权限脚本
-- 解决admin用户缺少production:schedule相关权限的问题

-- 设置变量
SET @now = NOW();
SET @admin_role_id = (SELECT id FROM sys_role WHERE code = 'admin' LIMIT 1);
SET @production_module_id = (SELECT id FROM sys_permission WHERE code = 'production' LIMIT 1);

-- 1. 创建生产排程管理子模块权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, created_by, created_time, is_deleted, version)
SELECT
    UUID(),
    @production_module_id,
    'production:schedule',
    '生产排程',
    'MENU',
    '/production/schedule',
    'production/schedule/index',
    'schedule',
    10,
    'active',
    'system',
    @now,
    0,
    0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'production:schedule');

-- 获取生产排程模块ID
SET @schedule_module_id = (SELECT id FROM sys_permission WHERE code = 'production:schedule' LIMIT 1);

-- 2. 创建生产排程管理操作权限
-- 生产排程列表权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, created_by, created_time, is_deleted, version)
SELECT
    UUID(),
    @schedule_module_id,
    'production:schedule:list',
    '生产排程列表',
    'BUTTON',
    NULL,
    NULL,
    NULL,
    11,
    'active',
    'system',
    @now,
    0,
    0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'production:schedule:list');

-- 生产排程详情权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, created_by, created_time, is_deleted, version)
SELECT
    UUID(),
    @schedule_module_id,
    'production:schedule:read',
    '生产排程详情',
    'BUTTON',
    NULL,
    NULL,
    NULL,
    12,
    'active',
    'system',
    @now,
    0,
    0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'production:schedule:read');

-- 创建生产排程权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, created_by, created_time, is_deleted, version)
SELECT
    UUID(),
    @schedule_module_id,
    'production:schedule:create',
    '创建生产排程',
    'BUTTON',
    NULL,
    NULL,
    NULL,
    13,
    'active',
    'system',
    @now,
    0,
    0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'production:schedule:create');

-- 更新生产排程权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, created_by, created_time, is_deleted, version)
SELECT
    UUID(),
    @schedule_module_id,
    'production:schedule:update',
    '更新生产排程',
    'BUTTON',
    NULL,
    NULL,
    NULL,
    14,
    'active',
    'system',
    @now,
    0,
    0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'production:schedule:update');

-- 删除生产排程权限
INSERT INTO sys_permission (id, parent_id, code, name, type, path, component, icon, sort, status, created_by, created_time, is_deleted, version)
SELECT
    UUID(),
    @schedule_module_id,
    'production:schedule:delete',
    '删除生产排程',
    'BUTTON',
    NULL,
    NULL,
    NULL,
    15,
    'active',
    'system',
    @now,
    0,
    0
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM sys_permission WHERE code = 'production:schedule:delete');

-- 3. 为admin角色分配生产排程管理所有权限
INSERT INTO sys_role_permission (id, role_id, permission_id, create_by, create_time)
SELECT
    UUID(),
    @admin_role_id,
    p.id,
    'system',
    @now
FROM
    sys_permission p
WHERE
    p.code LIKE 'production:schedule%'
    AND NOT EXISTS (
        SELECT 1 FROM sys_role_permission rp
        WHERE rp.role_id = @admin_role_id AND rp.permission_id = p.id
    );

-- 4. 验证权限创建结果
SELECT 
    '权限创建结果' as '检查项',
    COUNT(*) as '权限数量'
FROM sys_permission 
WHERE code LIKE 'production:schedule%';

-- 5. 验证角色权限分配结果
SELECT 
    '角色权限分配结果' as '检查项',
    COUNT(*) as '分配数量'
FROM sys_role_permission rp
JOIN sys_permission p ON rp.permission_id = p.id
JOIN sys_role r ON rp.role_id = r.id
WHERE r.code = 'admin' AND p.code LIKE 'production:schedule%';

-- 6. 显示所有生产排程相关权限
SELECT 
    p.code as '权限编码',
    p.name as '权限名称',
    p.type as '权限类型',
    CASE WHEN rp.id IS NOT NULL THEN '已分配' ELSE '未分配' END as 'admin角色状态'
FROM sys_permission p
LEFT JOIN sys_role_permission rp ON p.id = rp.permission_id 
    AND rp.role_id = @admin_role_id
WHERE p.code LIKE 'production:schedule%'
ORDER BY p.sort;

-- 执行完成提示
SELECT 
    '生产排程权限添加完成！' as '执行结果',
    '请重新登录以刷新权限缓存' as '注意事项';
