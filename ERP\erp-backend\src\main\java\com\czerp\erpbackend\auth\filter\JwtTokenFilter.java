package com.czerp.erpbackend.auth.filter;

import com.czerp.erpbackend.auth.service.TokenBlacklistService;
import com.czerp.erpbackend.auth.util.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT令牌过滤器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class JwtTokenFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;
    private final UserDetailsService userDetailsService;
    private final TokenBlacklistService tokenBlacklistService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        log.info("Processing request: {} {}", request.getMethod(), request.getRequestURI());

        try {
            String jwt = getJwtFromRequest(request);

            if (StringUtils.hasText(jwt)) {
                log.info("JWT token found in request");
            } else {
                log.info("No JWT token found in request");
            }

            if (StringUtils.hasText(jwt) && SecurityContextHolder.getContext().getAuthentication() == null) {
                // 检查令牌是否在黑名单中
                if (tokenBlacklistService.isBlacklisted(jwt)) {
                    log.warn("Blacklisted token detected: {}", jwt);
                    response.setStatus(HttpStatus.UNAUTHORIZED.value());
                    response.getWriter().write("{\"success\":false,\"code\":\"401\",\"message\":\"令牌已失效，请重新登录\"}");
                    return;
                }

                String username = jwtUtil.extractUserId(jwt);
                log.info("Extracted username from token: {}", username);

                if (username != null) {
                    UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                    log.info("Loaded user details for: {}, authorities: {}", username, userDetails.getAuthorities());

                    if (jwtUtil.validateToken(jwt, username)) {
                        log.info("Token validated successfully for user: {}", username);
                        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                userDetails, null, userDetails.getAuthorities());
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        log.info("Authentication set in SecurityContext");
                    } else {
                        log.warn("Token validation failed for user: {}", username);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Could not set user authentication in security context", e);
        }

        filterChain.doFilter(request, response);
    }

    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        log.debug("Authorization header: {}", bearerToken);

        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            String token = bearerToken.substring(7);
            log.debug("Extracted JWT token: {}", token.substring(0, Math.min(10, token.length())) + "...");
            return token;
        }

        // 检查请求参数中是否有token
        String paramToken = request.getParameter("token");
        if (StringUtils.hasText(paramToken)) {
            log.debug("Found token in request parameter");
            return paramToken;
        }

        // 检查Cookie中是否有token
        if (request.getCookies() != null) {
            for (jakarta.servlet.http.Cookie cookie : request.getCookies()) {
                if ("token".equals(cookie.getName()) && StringUtils.hasText(cookie.getValue())) {
                    log.debug("Found token in cookie");
                    return cookie.getValue();
                }
            }
        }

        log.debug("No JWT token found in request");
        return null;
    }
}
