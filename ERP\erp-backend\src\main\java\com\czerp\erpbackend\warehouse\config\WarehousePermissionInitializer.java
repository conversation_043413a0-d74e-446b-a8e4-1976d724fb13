package com.czerp.erpbackend.warehouse.config;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 仓库管理权限初始化器
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(100)
public class WarehousePermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;

    @Override
    public void run(String... args) throws Exception {
        log.info("Initializing warehouse management permissions...");
        initializeWarehousePermissions();
        log.info("Warehouse management permissions initialized successfully.");
    }

    /**
     * 初始化仓库管理权限
     */
    private void initializeWarehousePermissions() {
        // 先创建父权限，获取父权限ID
        String parentPermissionId = createOrUpdatePermission(
                new PermissionData("warehouse", "仓库管理", "MENU", null, "/warehouse", 1)
        );

        // 创建子权限
        List<PermissionData> childPermissions = Arrays.asList(
                new PermissionData("warehouse:read", "查看仓库", "BUTTON", parentPermissionId, null, 2),
                new PermissionData("warehouse:create", "创建仓库", "BUTTON", parentPermissionId, null, 3),
                new PermissionData("warehouse:update", "更新仓库", "BUTTON", parentPermissionId, null, 4),
                new PermissionData("warehouse:delete", "删除仓库", "BUTTON", parentPermissionId, null, 5)
        );

        // 创建或更新子权限
        for (PermissionData permData : childPermissions) {
            createOrUpdatePermission(permData);
        }
    }

    /**
     * 创建或更新权限
     * @return 权限ID
     */
    private String createOrUpdatePermission(PermissionData permData) {
        Permission existingPermission = permissionRepository.findByCode(permData.code).orElse(null);

        if (existingPermission == null) {
            // 创建新权限
            Permission permission = new Permission();
            permission.setId(java.util.UUID.randomUUID().toString());
            permission.setCode(permData.code);
            permission.setName(permData.name);
            permission.setType(permData.type);
            permission.setParentId(permData.parentId);
            permission.setPath(permData.path);
            permission.setSort(permData.sortOrder);
            permission.setStatus("active");

            permission = permissionRepository.save(permission);
            log.info("Created permission: {} - {}", permData.code, permData.name);
            return permission.getId();
        } else {
            // 更新现有权限
            boolean updated = false;

            if (!permData.name.equals(existingPermission.getName())) {
                existingPermission.setName(permData.name);
                updated = true;
            }

            if (!permData.type.equals(existingPermission.getType())) {
                existingPermission.setType(permData.type);
                updated = true;
            }

            if (permData.parentId != null && !permData.parentId.equals(existingPermission.getParentId())) {
                existingPermission.setParentId(permData.parentId);
                updated = true;
            }

            if (permData.path != null && !permData.path.equals(existingPermission.getPath())) {
                existingPermission.setPath(permData.path);
                updated = true;
            }

            if (!permData.sortOrder.equals(existingPermission.getSort())) {
                existingPermission.setSort(permData.sortOrder);
                updated = true;
            }

            if (updated) {
                permissionRepository.save(existingPermission);
                log.info("Updated permission: {} - {}", permData.code, permData.name);
            }
            return existingPermission.getId();
        }
    }

    /**
     * 权限数据内部类
     */
    private static class PermissionData {
        final String code;
        final String name;
        final String type;
        final String parentId;
        final String path;
        final Integer sortOrder;

        public PermissionData(String code, String name, String type, String parentId, String path, Integer sortOrder) {
            this.code = code;
            this.name = name;
            this.type = type;
            this.parentId = parentId;
            this.path = path;
            this.sortOrder = sortOrder;
        }
    }
}
