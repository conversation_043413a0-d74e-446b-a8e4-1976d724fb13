package com.czerp.erpbackend.sales.filter;

import com.czerp.erpbackend.common.filter.dto.FilterOptionDTO;
import com.czerp.erpbackend.common.filter.dto.FilterRequest;
import com.czerp.erpbackend.common.filter.handler.FilterHandler;
import com.czerp.erpbackend.common.filter.metadata.FilterFieldMetadata;
import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryParamDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售订单子查询筛选处理器
 * 处理采购相关的筛选字段：supplierName, paperTypeName, purchaseOrderNo, bindingSpecification
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SalesOrderSubqueryFilterHandler implements FilterHandler {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    public List<FilterOptionDTO> getFilterOptions(FilterRequest request, FilterFieldMetadata metadata) {
        log.debug("Getting subquery filter options for field: {}", request.getFieldName());
        
        try {
            switch (request.getFieldName()) {
                case "supplierName":
                    return getSupplierNameOptions(request);
                case "paperTypeName":
                    return getPaperTypeNameOptions(request);
                case "purchaseOrderNo":
                    return getPurchaseOrderNoOptions(request);
                case "bindingSpecification":
                    return getBindingSpecificationOptions(request);
                default:
                    log.warn("Unsupported subquery field: {}", request.getFieldName());
                    return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Failed to get subquery filter options for field: {}, error: {}", 
                     request.getFieldName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public boolean supports(String fieldName) {
        return List.of("supplierName", "paperTypeName", "purchaseOrderNo", "bindingSpecification")
                .contains(fieldName);
    }
    
    /**
     * 获取采购供应商筛选选项
     */
    private List<FilterOptionDTO> getSupplierNameOptions(FilterRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<PurchaseOrderItem> purchaseItemRoot = query.from(PurchaseOrderItem.class);
        Join<PurchaseOrderItem, PurchaseOrder> purchaseOrderJoin = purchaseItemRoot.join("purchaseOrder", JoinType.INNER);

        // 选择供应商名称，去重
        query.select(purchaseOrderJoin.get("supplierName")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(request.getSearchText())) {
            predicates.add(cb.like(purchaseOrderJoin.get("supplierName"), "%" + request.getSearchText() + "%"));
        }

        // 过滤空值
        predicates.add(cb.isNotNull(purchaseOrderJoin.get("supplierName")));
        predicates.add(cb.notEqual(purchaseOrderJoin.get("supplierName"), ""));

        // 只查询有关联销售订单明细的采购订单
        predicates.add(cb.isNotNull(purchaseItemRoot.get("sourceSalesOrderItemId")));

        // 添加级联筛选条件（排除当前字段）
        if (Boolean.TRUE.equals(request.getEnableCascade())) {
            addCascadeFilterConditions(cb, purchaseItemRoot, request, predicates, "supplierName");
        }

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(purchaseOrderJoin.get("supplierName")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(request.getMaxResults() != null ? request.getMaxResults() : 50)
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取纸板类别筛选选项
     */
    private List<FilterOptionDTO> getPaperTypeNameOptions(FilterRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<PurchaseOrderItem> purchaseItemRoot = query.from(PurchaseOrderItem.class);

        // 选择纸板类别，去重
        query.select(purchaseItemRoot.get("paperBoardCategory")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(request.getSearchText())) {
            predicates.add(cb.like(purchaseItemRoot.get("paperBoardCategory"), "%" + request.getSearchText() + "%"));
        }

        // 过滤空值
        predicates.add(cb.isNotNull(purchaseItemRoot.get("paperBoardCategory")));
        predicates.add(cb.notEqual(purchaseItemRoot.get("paperBoardCategory"), ""));

        // 只查询有关联销售订单明细的采购订单
        predicates.add(cb.isNotNull(purchaseItemRoot.get("sourceSalesOrderItemId")));

        // 添加级联筛选条件（排除当前字段）
        if (Boolean.TRUE.equals(request.getEnableCascade())) {
            addCascadeFilterConditions(cb, purchaseItemRoot, request, predicates, "paperTypeName");
        }

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(purchaseItemRoot.get("paperBoardCategory")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(request.getMaxResults() != null ? request.getMaxResults() : 50)
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取采购单号筛选选项
     */
    private List<FilterOptionDTO> getPurchaseOrderNoOptions(FilterRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<PurchaseOrderItem> purchaseItemRoot = query.from(PurchaseOrderItem.class);
        Join<PurchaseOrderItem, PurchaseOrder> purchaseOrderJoin = purchaseItemRoot.join("purchaseOrder", JoinType.INNER);

        // 选择采购单号，去重
        query.select(purchaseOrderJoin.get("purchaseOrderNo")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(request.getSearchText())) {
            predicates.add(cb.like(purchaseOrderJoin.get("purchaseOrderNo"), "%" + request.getSearchText() + "%"));
        }

        // 过滤空值
        predicates.add(cb.isNotNull(purchaseOrderJoin.get("purchaseOrderNo")));
        predicates.add(cb.notEqual(purchaseOrderJoin.get("purchaseOrderNo"), ""));

        // 只查询有关联销售订单明细的采购订单
        predicates.add(cb.isNotNull(purchaseItemRoot.get("sourceSalesOrderItemId")));

        // 添加级联筛选条件（排除当前字段）
        if (Boolean.TRUE.equals(request.getEnableCascade())) {
            addCascadeFilterConditions(cb, purchaseItemRoot, request, predicates, "purchaseOrderNo");
        }

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(purchaseOrderJoin.get("purchaseOrderNo")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(request.getMaxResults() != null ? request.getMaxResults() : 50)
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取合订规格筛选选项
     */
    private List<FilterOptionDTO> getBindingSpecificationOptions(FilterRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<PurchaseOrderItem> purchaseItemRoot = query.from(PurchaseOrderItem.class);

        // 选择合订规格，去重
        query.select(purchaseItemRoot.get("bindingSpecification")).distinct(true);

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件
        if (StringUtils.hasText(request.getSearchText())) {
            predicates.add(cb.like(purchaseItemRoot.get("bindingSpecification"), "%" + request.getSearchText() + "%"));
        }

        // 过滤空值
        predicates.add(cb.isNotNull(purchaseItemRoot.get("bindingSpecification")));
        predicates.add(cb.notEqual(purchaseItemRoot.get("bindingSpecification"), ""));

        // 只查询有关联销售订单明细的采购订单
        predicates.add(cb.isNotNull(purchaseItemRoot.get("sourceSalesOrderItemId")));

        // 添加级联筛选条件（排除当前字段）
        if (Boolean.TRUE.equals(request.getEnableCascade())) {
            addCascadeFilterConditions(cb, purchaseItemRoot, request, predicates, "bindingSpecification");
        }

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 按字母顺序排序
        query.orderBy(cb.asc(purchaseItemRoot.get("bindingSpecification")));

        List<String> results = entityManager.createQuery(query)
                .setMaxResults(request.getMaxResults() != null ? request.getMaxResults() : 50)
                .getResultList();

        return results.stream()
                .filter(StringUtils::hasText)
                .map(FilterOptionDTO::of)
                .collect(Collectors.toList());
    }
    
    /**
     * 添加级联筛选条件
     * 这里需要实现与原有逻辑一致的级联筛选
     */
    private void addCascadeFilterConditions(CriteriaBuilder cb, Root<PurchaseOrderItem> purchaseItemRoot,
                                          FilterRequest request, List<Predicate> predicates, String excludeField) {
        // TODO: 实现级联筛选逻辑
        // 这里需要根据当前筛选条件构建子查询，与原有的CascadeFilterManager逻辑保持一致
        log.debug("Cascade filtering for purchase subquery field: {} not fully implemented yet", excludeField);
    }
}
