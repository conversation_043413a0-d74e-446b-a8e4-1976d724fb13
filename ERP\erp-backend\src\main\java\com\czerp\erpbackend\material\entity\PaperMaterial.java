package com.czerp.erpbackend.material.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 纸质资料实体
 */
@Entity
@Table(name = "paper_material")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PaperMaterial extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 纸质编码
     */
    @Column(name = "paper_code", length = 50)
    private String paperCode;

    /**
     * 纸质
     */
    @Column(name = "paper_name", length = 50)
    private String paperName;

    /**
     * 纸类
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "paper_type_id")
    private PaperType paperType;

    /**
     * 楞别
     */
    @Column(name = "flute_type", length = 50)
    private String fluteType;

    /**
     * 标准纸质
     */
    @Column(name = "is_standard")
    private Boolean isStandard;

    /**
     * 生产代号
     */
    @Column(name = "production_code", length = 50)
    private String productionCode;

    /**
     * 面纸
     */
    @Column(name = "face_paper", length = 100)
    private String facePaper;

    /**
     * 芯纸1
     */
    @Column(name = "core_paper1", length = 100)
    private String corePaper1;

    /**
     * 中隔1
     */
    @Column(name = "middle_partition1", length = 100)
    private String middlePartition1;

    /**
     * 芯纸2
     */
    @Column(name = "core_paper2", length = 100)
    private String corePaper2;

    /**
     * 中隔2
     */
    @Column(name = "middle_partition2", length = 100)
    private String middlePartition2;

    /**
     * 芯纸3
     */
    @Column(name = "core_paper3", length = 100)
    private String corePaper3;

    /**
     * 里纸
     */
    @Column(name = "liner_paper", length = 100)
    private String linerPaper;

    /**
     * 层数
     */
    @Column(name = "layer_count")
    private Integer layerCount;

    /**
     * 重量(千克/千平方英寸)
     */
    @Column(name = "weight_kg_per_ksi", precision = 10, scale = 6)
    private BigDecimal weightKgPerKsi;

    /**
     * 重量(千克/平方米)
     */
    @Column(name = "weight_kg_per_sqm", precision = 10, scale = 6)
    private BigDecimal weightKgPerSqm;

    /**
     * 边压强度
     */
    @Column(name = "edge_crush_strength", length = 50)
    private String edgeCrushStrength;

    /**
     * 纸板耐破度
     */
    @Column(name = "bursting_strength", length = 50)
    private String burstingStrength;

    /**
     * 对应标准纸质
     */
    @Column(name = "corresponding_standard", length = 100)
    private String correspondingStandard;

    /**
     * 默认供应商名称
     */
    @Column(name = "default_supplier", length = 100)
    private String defaultSupplier;

    /**
     * 备注
     */
    @Column(name = "remarks", length = 500)
    private String remarks;

    /**
     * 停用
     */
    @Column(name = "is_disabled")
    private Boolean isDisabled = false;
}
