package com.czerp.erpbackend.product.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 产品分类实体
 */
@Entity
@Table(name = "prd_category")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProductCategory extends BaseEntity {
    
    /**
     * 分类ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;
    
    /**
     * 分类名称
     */
    @Column(name = "name", length = 50, nullable = false)
    private String name;
    
    /**
     * 分类编码
     */
    @Column(name = "code", length = 50, nullable = false, unique = true)
    private String code;
    
    /**
     * 父分类ID
     */
    @Column(name = "parent_id", length = 36)
    private String parentId;
    
    /**
     * 层级
     */
    @Column(name = "level", nullable = false)
    private Integer level = 1;
    
    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort = 0;
    
    /**
     * 描述
     */
    @Column(name = "description", length = 255)
    private String description;
}
