package com.czerp.erpbackend.system.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.system.dto.CreateRoleRequest;
import com.czerp.erpbackend.system.dto.RoleDTO;
import com.czerp.erpbackend.system.dto.RoleQueryRequest;
import com.czerp.erpbackend.system.dto.UpdateRoleRequest;

import java.util.List;

/**
 * 角色服务接口
 */
public interface RoleService {
    
    /**
     * 分页查询角色列表
     * @param request 查询请求
     * @return 角色分页列表
     */
    PageResponse<RoleDTO> findRoles(RoleQueryRequest request);
    
    /**
     * 查询所有角色
     * @return 角色列表
     */
    List<RoleDTO> findAllRoles();
    
    /**
     * 根据ID查询角色
     * @param id 角色ID
     * @return 角色信息
     */
    RoleDTO findRoleById(String id);
    
    /**
     * 创建角色
     * @param request 创建角色请求
     * @return 角色信息
     */
    RoleDTO createRole(CreateRoleRequest request);
    
    /**
     * 更新角色
     * @param id 角色ID
     * @param request 更新角色请求
     * @return 角色信息
     */
    RoleDTO updateRole(String id, UpdateRoleRequest request);
    
    /**
     * 删除角色
     * @param id 角色ID
     */
    void deleteRole(String id);
    
    /**
     * 更改角色状态
     * @param id 角色ID
     * @param status 状态
     * @return 角色信息
     */
    RoleDTO changeStatus(String id, String status);
    
    /**
     * 根据用户ID查询角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RoleDTO> findRolesByUserId(String userId);

    PageResponse<RoleDTO> findRoles(int page, int size);
}