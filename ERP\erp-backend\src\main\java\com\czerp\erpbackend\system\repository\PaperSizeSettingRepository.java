package com.czerp.erpbackend.system.repository;

import com.czerp.erpbackend.system.entity.PaperSizeSetting;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 纸度设置存储库
 */
@Repository
public interface PaperSizeSettingRepository extends JpaRepository<PaperSizeSetting, Long> {

    /**
     * 根据纸度(inch)查找纸度设置
     * @param paperSizeInch 纸度(inch)
     * @return 纸度设置
     */
    Optional<PaperSizeSetting> findByPaperSizeInch(BigDecimal paperSizeInch);

    /**
     * 判断纸度(inch)是否存在
     * @param paperSizeInch 纸度(inch)
     * @return 是否存在
     */
    boolean existsByPaperSizeInch(BigDecimal paperSizeInch);

    /**
     * 判断纸度(inch)是否存在（排除指定ID）
     * @param paperSizeInch 纸度(inch)
     * @param id ID
     * @return 是否存在
     */
    boolean existsByPaperSizeInchAndIdNot(BigDecimal paperSizeInch, Long id);

    /**
     * 查询所有未删除的纸度设置
     * @return 纸度设置列表
     */
    List<PaperSizeSetting> findByIsDeletedFalseOrderByPaperSizeInchAsc();

    /**
     * 分页查询纸度设置
     * @param keyword 关键字（纸度inch或cm）
     * @param pageable 分页参数
     * @return 纸度设置分页列表
     */
    @Query("SELECT p FROM PaperSizeSetting p WHERE p.isDeleted = false " +
            "AND (:keyword IS NULL OR " +
            "CAST(p.paperSizeInch AS string) LIKE %:keyword% OR " +
            "CAST(p.paperSizeCm AS string) LIKE %:keyword%)")
    Page<PaperSizeSetting> search(
            @Param("keyword") String keyword,
            Pageable pageable);
}
