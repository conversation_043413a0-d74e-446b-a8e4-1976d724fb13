package com.czerp.erpbackend.material.service;

import com.czerp.erpbackend.common.dto.ImportResult;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.material.dto.CreatePaperMaterialRequest;
import com.czerp.erpbackend.material.dto.PaperMaterialDTO;
import com.czerp.erpbackend.material.dto.PaperMaterialQueryRequest;
import com.czerp.erpbackend.material.dto.UpdatePaperMaterialRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 纸质资料服务接口
 */
public interface PaperMaterialService {

    /**
     * 分页查询纸质资料列表
     * @param request 查询请求
     * @return 纸质资料分页列表
     */
    PageResponse<PaperMaterialDTO> findPaperMaterials(PaperMaterialQueryRequest request);

    /**
     * 查询所有纸质资料
     * @return 纸质资料列表
     */
    List<PaperMaterialDTO> findAllPaperMaterials();

    /**
     * 查询所有未停用的纸质资料
     * @return 纸质资料列表
     */
    List<PaperMaterialDTO> findActivePaperMaterials();

    /**
     * 查询所有标准纸质资料
     * @return 纸质资料列表
     */
    List<PaperMaterialDTO> findStandardPaperMaterials();

    /**
     * 根据ID查询纸质资料
     * @param id 纸质资料ID
     * @return 纸质资料信息
     */
    PaperMaterialDTO findPaperMaterialById(Long id);

    /**
     * 创建纸质资料
     * @param request 创建请求
     * @return 纸质资料信息
     */
    PaperMaterialDTO createPaperMaterial(CreatePaperMaterialRequest request);

    /**
     * 更新纸质资料
     * @param id 纸质资料ID
     * @param request 更新请求
     * @return 纸质资料信息
     */
    PaperMaterialDTO updatePaperMaterial(Long id, UpdatePaperMaterialRequest request);

    /**
     * 删除纸质资料
     * @param id 纸质资料ID
     */
    void deletePaperMaterial(Long id);

    /**
     * 批量删除纸质资料
     * @param ids 纸质资料ID列表
     */
    void batchDeletePaperMaterials(List<Long> ids);

    /**
     * 导入纸质资料
     * @param file Excel文件
     * @return 导入结果
     */
    ImportResult importPaperMaterials(MultipartFile file);
}
