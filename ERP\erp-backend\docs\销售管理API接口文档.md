# 销售管理API接口文档

## 1. 概述

本文档详细描述了销售管理系统的API接口，包括销售订单管理、销售订单查询和销售订单工序管理等功能。所有接口均需要进行身份验证和授权。

## 2. 基础信息

- **基础路径**: `/api`
- **认证方式**: Bearer Token
- **响应格式**: 所有接口返回统一的ApiResponse格式
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {} // 实际返回数据
  }
  ```

## 3. 销售订单管理

### 3.1 创建销售订单

#### 请求

```
POST /sales/orders
```

#### 权限要求

需要`sales:order:create`权限

#### 请求体

```json
{
  "orderNo": "SO2023060100001",  // 订单编号，可选，不提供则自动生成
  "orderDate": "2023-06-01",     // 订单日期
  "paymentMethod": "转账",        // 付款方式
  "customerCode": "C001",        // 客户编码
  "customerName": "测试客户",      // 客户名称
  "salesPerson": "销售员",        // 销售员
  "customerPurchaser": "采购员",   // 客户采购员
  "receivingUnit": "收货单位",     // 收货单位
  "receiver": "收货人",           // 收货人
  "receiverPhone": "13800138000", // 收货人电话
  "receivingAddress": "收货地址",  // 收货地址
  "remark": "订单备注",           // 备注
  "orderType": "常规订单",        // 订单类型
  "items": [                     // 订单明细列表
    {
      "customerOrderNo": "CO001",
      "customerProductCode": "CP001",
      "productName": "产品A",
      "processRequirements": "工艺要求",
      "isTaxed": true,
      "boxType": "B01",
      "paperType": "Z01",
      "corrugationType": "楞型A",
      "productionPaperType": "生产纸质",
      "length": 100,
      "width": 200,
      "height": 300,
      "sizeUnit": "MM",
      "quantity": 1000,
      "spareQuantity": 50,
      "price": 10.5,
      "amount": 10500,
      "deliveryDate": "2023-06-15",
      "isSpecialPrice": false,
      "paperQuotation": 0,
      "connectionMethod": "连接方式",
      "staplePosition": "钉位",
      "packagingCount": 10,
      "productionRemark": "生产备注",
      "remark": "明细备注",
      "unit": "个",
      "currency": "CNY",
      "taxRate": 13
    }
  ],
  "processes": [                 // 订单工序列表
    {
      "sequence": 1,
      "processName": "分纸",
      "processRequirements": "按规格分切"
    },
    {
      "sequence": 2,
      "processName": "印刷",
      "processRequirements": "四色印刷，对色精准",
      "plateNumber": "P001",
      "inkNumber": "I001",
      "inkName": "蓝色",
      "colorCount": 4
    }
  ]
}
```

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "order-id-123",
    "orderNo": "SO2023060100001",
    "orderDate": "2023-06-01",
    "paymentMethod": "转账",
    "customerCode": "C001",
    "customerName": "测试客户",
    "salesPerson": "销售员",
    "customerPurchaser": "采购员",
    "receivingUnit": "收货单位",
    "receiver": "收货人",
    "receiverPhone": "13800138000",
    "receivingAddress": "收货地址",
    "remark": "订单备注",
    "orderType": "常规订单",
    "createdBy": "admin",
    "createdByName": "管理员",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": null,
    "updatedByName": null,
    "updatedTime": null,
    "items": [
      {
        "id": "item-id-123",
        "orderId": "order-id-123",
        "productionOrderNo": "PO2023060100001",  // 系统自动生成的生产单号
        "customerOrderNo": "CO001",
        "customerProductCode": "CP001",
        "productName": "产品A",
        "processRequirements": "工艺要求",
        "isTaxed": true,
        "boxType": "B01",
        "paperType": "Z01",
        "corrugationType": "楞型A",
        "productionPaperType": "生产纸质",
        "length": 100,
        "width": 200,
        "height": 300,
        "sizeUnit": "MM",
        "specification": "100×200×300",  // 系统自动生成的规格
        "quantity": 1000,
        "spareQuantity": 50,
        "price": 10.5,
        "amount": 10500,
        "deliveryDate": "2023-06-15",
        "isSpecialPrice": false,
        "paperQuotation": 0,
        "connectionMethod": "连接方式",
        "staplePosition": "钉位",
        "packagingCount": 10,
        "productionRemark": "生产备注",
        "remark": "明细备注",
        "unit": "个",
        "currency": "CNY",
        "taxRate": 13
      }
    ],
    "processes": [
      {
        "id": "process-id-1",
        "orderId": "order-id-123",
        "sequence": 1,
        "processName": "分纸",
        "processRequirements": "按规格分切",
        "plateNumber": null,
        "inkNumber": null,
        "inkName": null,
        "colorCount": null,
        "createdBy": "admin",
        "createdTime": "2023-06-01T10:00:00",
        "updatedBy": null,
        "updatedTime": null
      },
      {
        "id": "process-id-2",
        "orderId": "order-id-123",
        "sequence": 2,
        "processName": "印刷",
        "processRequirements": "四色印刷，对色精准",
        "plateNumber": "P001",
        "inkNumber": "I001",
        "inkName": "蓝色",
        "colorCount": 4,
        "createdBy": "admin",
        "createdTime": "2023-06-01T10:00:00",
        "updatedBy": null,
        "updatedTime": null
      }
    ]
  }
}
```

### 3.2 更新销售订单

#### 请求

```
PUT /sales/orders/{id}
```

#### 权限要求

需要`sales:order:update`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| id | String | 订单ID | 是 |

#### 请求体

同创建销售订单

#### 响应

同创建销售订单

### 3.3 根据ID查询销售订单

#### 请求

```
GET /sales/orders/{id}
```

#### 权限要求

需要`sales:order:read`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| id | String | 订单ID | 是 |

#### 响应

同创建销售订单的响应

### 3.4 根据订单号查询销售订单

#### 请求

```
GET /sales/orders/by-order-no/{orderNo}
```

#### 权限要求

需要`sales:order:read`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderNo | String | 订单号 | 是 |

#### 响应

同创建销售订单的响应

### 3.5 分页查询销售订单

#### 请求

```
GET /sales/orders
```

#### 权限要求

需要`sales:order:read`权限

#### 查询参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| page | Integer | 页码，默认1 | 否 |
| pageSize | Integer | 每页大小，默认10 | 否 |
| keyword | String | 关键字搜索（订单号、客户名称等） | 否 |
| orderNo | String | 订单号 | 否 |
| productionOrderNo | String | 生产单号 | 否 |
| customerCode | String | 客户编码 | 否 |
| customerName | String | 客户名称 | 否 |
| customerOrderNo | String | 客户订单号 | 否 |
| salesPerson | String | 销售员 | 否 |
| customerPurchaser | String | 客户采购员 | 否 |
| orderDateStart | String | 订单日期开始（格式：yyyy-MM-dd） | 否 |
| orderDateEnd | String | 订单日期结束（格式：yyyy-MM-dd） | 否 |
| createdBy | String | 创建人 | 否 |
| sortField | String | 排序字段 | 否 |
| sortOrder | String | 排序方向（asc/desc） | 否 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": "order-id-123",
        "orderNo": "SO2023060100001",
        "orderDate": "2023-06-01",
        "customerCode": "C001",
        "customerName": "测试客户",
        "salesPerson": "销售员",
        "totalAmount": 10500,
        "createdTime": "2023-06-01T10:00:00"
      }
      // 更多订单...
    ],
    "totalElements": 100,
    "totalPages": 10,
    "page": 1,
    "size": 10,
    "first": true,
    "last": false,
    "empty": false
  }
}
```

### 3.6 根据客户编码查询销售订单

#### 请求

```
GET /sales/orders/by-customer-code/{customerCode}
```

#### 权限要求

需要`sales:order:read`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| customerCode | String | 客户编码 | 是 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "order-id-123",
      "orderNo": "SO2023060100001",
      "orderDate": "2023-06-01",
      "customerCode": "C001",
      "customerName": "测试客户",
      "salesPerson": "销售员",
      "totalAmount": 10500,
      "createdTime": "2023-06-01T10:00:00"
    },
    // 更多订单...
  ]
}
```

### 3.7 根据客户ID查询历史订单明细

#### 请求

```
GET /sales/orders/customers/{customerId}/historical-items
```

#### 权限要求

需要`sales:order:read`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| customerId | String | 客户ID | 是 |

#### 查询参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| keyword | String | 关键词（用于搜索客方货号或品名） | 否 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "item-id-123",
      "orderId": "order-id-123",
      "productionOrderNo": "PO2023060100001",
      "customerOrderNo": "CO001",
      "customerProductCode": "CP001",
      "productName": "产品A",
      "processRequirements": "工艺要求",
      "boxType": "B01",
      "paperType": "Z01",
      "specification": "100×200×300",
      "quantity": 1000,
      "price": 10.5,
      "deliveryDate": "2023-06-15"
    },
    // 更多订单明细...
  ]
}
```

## 4. 销售订单查询

### 4.1 分页查询销售订单

#### 请求

```
GET /sales/orders/query
```

#### 权限要求

需要`sales:order:read`权限

#### 查询参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| page | Integer | 页码，默认1 | 否 |
| pageSize | Integer | 每页大小，默认10 | 否 |
| keyword | String | 关键字搜索（支持销售单号、客户名称、生产单号、客户订单号、客方货号、品名） | 否 |
| orderNo | String | 销售单号 | 否 |
| productionOrderNo | String | 生产单号 | 否 |
| customerName | String | 客户名称 | 否 |
| customerOrderNo | String | 客户订单号 | 否 |
| customerProductCode | String | 客方货号 | 否 |
| productName | String | 品名 | 否 |
| startDate | String | 开始日期（格式：yyyy-MM-dd） | 否 |
| endDate | String | 结束日期（格式：yyyy-MM-dd） | 否 |
| salesPerson | String | 销售员 | 否 |
| sortField | String | 排序字段 | 否 |
| sortOrder | String | 排序方向（asc/desc） | 否 |
| **🔥 列筛选参数（已全面实现）** |
| **文本筛选（单值，模糊匹配）** |
| filterProductionOrderNo | String | 生产单号筛选（模糊匹配） | 否 |
| filterOrderNo | String | 销售单号筛选（模糊匹配） | 否 |
| filterCustomerOrderNo | String | 客户订单号筛选（模糊匹配） | 否 |
| filterCustomerProductCode | String | 客方货号筛选（模糊匹配） | 否 |
| **基础字段多值筛选（精确匹配）** |
| filterCustomerNames | Array[String] | 客户名称筛选（多选） | 否 |
| filterProductNames | Array[String] | 产品名称筛选（多选） | 否 |
| filterSalesPersons | Array[String] | 销售员筛选（多选） | 否 |
| **单号类字段多值筛选** |
| filterProductionOrderNos | Array[String] | 生产单号筛选（多选） | 否 |
| filterOrderNos | Array[String] | 销售单号筛选（多选） | 否 |
| filterCustomerOrderNos | Array[String] | 客户订单号筛选（多选） | 否 |
| filterCustomerProductCodes | Array[String] | 客方货号筛选（多选） | 否 |
| filterPurchaseOrderNos | Array[String] | 采购单号筛选（多选，通过子查询） | 否 |
| **规格类字段多值筛选** |
| filterBindingSpecifications | Array[String] | 合订规格筛选（多选，通过子查询） | 否 |
| filterSpecifications | Array[String] | 规格筛选（多选，动态构建长×宽×高格式） | 否 |
| filterProductionSpecifications | Array[String] | 生产规格筛选（多选，动态构建格式） | 否 |
| filterPressSizeWidths | Array[String] | 压线尺寸筛选（多选，通过子查询） | 否 |
| **工艺和人员字段多值筛选** |
| filterPaperTypes | Array[String] | 纸质筛选（多选） | 否 |
| filterProcessRequirements | Array[String] | 工艺要求筛选（多选） | 否 |
| filterProcesses | Array[String] | 工序筛选（多选，通过子查询） | 否 |
| filterReceivingUnits | Array[String] | 收货单位筛选（多选） | 否 |
| filterReceivingPersons | Array[String] | 收货人筛选（多选） | 否 |
| **🔥 新增：采购相关字段多值筛选** |
| filterSupplierNames | Array[String] | 采购供应商筛选（多选，通过子查询） | 否 |
| filterPaperTypeNames | Array[String] | 纸板类别筛选（多选，通过子查询） | 否 |
| **字典类字段多值筛选** |
| filterBoxTypes | Array[String] | 箱型筛选（多选） | 否 |
| filterCorrugationTypes | Array[String] | 瓦楞类型筛选（多选） | 否 |
| **🔥 新增：生产和工艺相关字段筛选** |
| filterProductionPaperTypes | Array[String] | 生产纸质筛选（多选） | 否 |
| filterConnectionMethods | Array[String] | 连接方式筛选（多选） | 否 |
| filterUnits | Array[String] | 单位筛选（多选） | 否 |
| filterCurrencies | Array[String] | 币种筛选（多选） | 否 |
| filterTaxRates | Array[String] | 税率筛选（多选） | 否 |
| **🔥 新增：人员和地址相关字段筛选** |
| filterReceiverPhones | Array[String] | 收货人电话筛选（多选） | 否 |
| filterCreatedBys | Array[String] | 创建人筛选（多选） | 否 |
| **🔥 新增：文本字段筛选** |
| filterProductionRemarks | Array[String] | 生产备注筛选（多选，模糊匹配） | 否 |
| filterRemarks | Array[String] | 备注筛选（多选，模糊匹配） | 否 |
| filterReceivingAddresses | Array[String] | 收货地址筛选（多选，模糊匹配） | 否 |
| **🔥 新增：特殊标识字段筛选** |
| filterIsSpecialPrices | Array[Boolean] | 特价标识筛选（多选） | 否 |
| **数字范围筛选** |
| filterQuantityMin | Number | 数量最小值 | 否 |
| filterQuantityMax | Number | 数量最大值 | 否 |
| filterTotalAmountMin | Number | 金额最小值 | 否 |
| filterTotalAmountMax | Number | 金额最大值 | 否 |
| **🔥 新增：数字范围筛选字段** |
| filterSpareQuantityMin | Integer | 备品数最小值 | 否 |
| filterSpareQuantityMax | Integer | 备品数最大值 | 否 |
| filterPriceMin | Number | 单价最小值 | 否 |
| filterPriceMax | Number | 单价最大值 | 否 |
| filterPurchasedQuantityMin | Integer | 已采购数最小值 | 否 |
| filterPurchasedQuantityMax | Integer | 已采购数最大值 | 否 |
| filterPaperQuotationMin | Number | 纸质报价最小值 | 否 |
| filterPaperQuotationMax | Number | 纸质报价最大值 | 否 |
| filterUnitWeightMin | Number | 单重最小值 | 否 |
| filterUnitWeightMax | Number | 单重最大值 | 否 |
| filterTotalWeightMin | Number | 总重最小值 | 否 |
| filterTotalWeightMax | Number | 总重最大值 | 否 |
| filterProductAreaMin | Number | 产品面积最小值 | 否 |
| filterProductAreaMax | Number | 产品面积最大值 | 否 |
| filterTotalAreaMin | Number | 总面积最小值 | 否 |
| filterTotalAreaMax | Number | 总面积最大值 | 否 |
| filterProductVolumeMin | Number | 产品体积最小值 | 否 |
| filterProductVolumeMax | Number | 产品体积最大值 | 否 |
| filterTotalVolumeMin | Number | 总体积最小值 | 否 |
| filterTotalVolumeMax | Number | 总体积最大值 | 否 |
| **日期范围筛选** |
| filterOrderDateStart | String | 订单日期开始（格式：yyyy-MM-dd） | 否 |
| filterOrderDateEnd | String | 订单日期结束（格式：yyyy-MM-dd） | 否 |
| filterDeliveryDateStart | String | 交期开始（格式：yyyy-MM-dd） | 否 |
| filterDeliveryDateEnd | String | 交期结束（格式：yyyy-MM-dd） | 否 |
| **🔥 新增：日期范围筛选字段** |
| filterCreatedTimeStart | String | 创建时间开始（格式：yyyy-MM-dd） | 否 |
| filterCreatedTimeEnd | String | 创建时间结束（格式：yyyy-MM-dd） | 否 |
| **筛选选项控制参数** |
| includeFilterOptions | Boolean | 是否返回筛选选项 | 否 |
| filterOptionFields | Array[String] | 需要返回选项的字段列表 | 否 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": "order-id-123",
        "orderNo": "SO2023060100001",
        "orderDate": "2023-06-01",
        "customerName": "测试客户",
        "salesPerson": "销售员",
        "receivingUnit": "收货单位",
        "receiver": "收货人",
        "receiverPhone": "13800138000",
        "receivingAddress": "收货地址",
        "createdBy": "admin",
        "createdByName": "管理员",
        "createdTime": "2023-06-01T10:00:00",
        "itemId": "item-id-123",
        "productionOrderNo": "PO2023060100001",
        "customerOrderNo": "CO001",
        "customerProductCode": "CP001",
        "productName": "产品A",
        "processRequirements": "工艺要求",
        "boxType": "B01",
        "paperType": "Z01",
        "paperTypeName": "三坑",
        "productionPaperType": "生产纸质",
        "specification": "100×200×300",
        "productionSpecification": "",
        "quantity": 1000,
        "spareQuantity": 50,
        "price": 10.5,
        "amount": 10500,
        "isSpecialPrice": false,
        "productionRemark": "生产备注",
        "remark": "备注",
        "connectionMethod": "连接方式",
        "unit": "个",
        "paperQuotation": 0,
        "currency": "CNY",
        "unitWeight": 0.5,
        "totalWeight": 500,
        "productArea": 0.06,
        "totalArea": 60,
        "productVolume": 0.006,
        "totalVolume": 6,
        "taxRate": 13,
        "isTaxed": true,
        "corrugationType": "楞型A",
        "deliveryDate": "2023-06-15",
        "purchaseOrderNo": "CG2023060100001",
        "supplierName": "供应商A",
        "purchasedQuantity": 800,
        "bindingSpecification": "12 x 22 lnch",
        "processes": "分纸→开槽→粘箱→包装",
        "pressSizeWidth": "165+130+165",
        // 🔥 新增字段示例
        "productionPaperType": "生产纸质A",
        "connectionMethod": "胶水连接",
        "receiverPhone": "13800138000",
        "receivingAddress": "北京市朝阳区xxx街道",
        "createdBy": "admin",
        "createdByName": "管理员"
      }
      // 更多订单...
    ],
    "totalElements": 100,
    "totalPages": 10,
    "page": 1,
    "size": 10,
    "first": true,
    "last": false,
    "empty": false
  }
}
```

### 4.2 分页查询销售订单（增强版）

#### 请求

```
GET /sales/orders/query/enhanced
```

#### 权限要求

需要`sales:order:read`权限

#### 查询参数

支持所有4.1接口的查询参数，额外支持：

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| includeFilterOptions | Boolean | 是否返回筛选选项，默认false | 否 |
| filterOptionFields | Array[String] | 需要返回选项的字段列表，如["customerName", "productName", "salesPerson"] | 否 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "pageData": {
      "content": [
        // 与4.1接口相同的数据结构
      ],
      "totalElements": 100,
      "totalPages": 10,
      "page": 1,
      "size": 10,
      "first": true,
      "last": false,
      "empty": false
    },
    "filterOptions": {
      "customerName": [
        {
          "label": "客户A",
          "value": "客户A",
          "count": 25
        },
        {
          "label": "客户B",
          "value": "客户B",
          "count": 18
        }
      ],
      "productName": [
        {
          "label": "产品X",
          "value": "产品X",
          "count": 15
        }
      ],
      "salesPerson": [
        {
          "label": "张三",
          "value": "张三",
          "count": 30
        }
      ]
    },
    "filterStats": {
      "totalRecords": 1000,
      "filteredRecords": 100,
      "activeFilterCount": 2
    }
  }
}
```

### 4.3 获取筛选选项

#### 请求

```
GET /sales/orders/query/filter-options
```

#### 权限要求

需要`sales:order:read`权限

#### 查询参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| fieldName | String | 字段名称（支持所有多值筛选字段，详见下方支持字段列表） | 是 |
| searchText | String | 搜索文本（可选，用于筛选选项） | 否 |

**支持的筛选选项字段列表：**
- **基础字段**：customerName, productName, salesPerson, orderNo
- **明细字段**：productionOrderNo, customerOrderNo, customerProductCode, paperType, processRequirements, boxType, corrugationType, connectionMethod, unit, currency
- **🔥 新增字段**：productionPaperType, receiverPhone, createdBy, productionRemark, remark, receivingAddress
- **🔥 采购相关字段**：supplierName（采购供应商）, paperTypeName（纸板类别）
- **特殊字段**：isSpecialPrice（布尔值选项）

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "label": "客户A",
      "value": "客户A",
      "count": 25
    },
    {
      "label": "客户B",
      "value": "客户B",
      "count": 18
    }
  ]
}
```

## 5. 销售订单工序管理

### 5.1 查询订单工序列表

#### 请求

```
GET /sales/orders/{orderId}/processes
```

#### 权限要求

需要`sales:order:read`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "process-id-1",
      "orderId": "order-id-123",
      "sequence": 1,
      "processName": "分纸",
      "processRequirements": "按规格分切",
      "plateNumber": null,
      "inkNumber": null,
      "inkName": null,
      "colorCount": null,
      "createdBy": "admin",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": null,
      "updatedTime": null
    },
    {
      "id": "process-id-2",
      "orderId": "order-id-123",
      "sequence": 2,
      "processName": "印刷",
      "processRequirements": "四色印刷，对色精准",
      "plateNumber": "P001",
      "inkNumber": "I001",
      "inkName": "蓝色",
      "colorCount": 4,
      "createdBy": "admin",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": null,
      "updatedTime": null
    }
  ]
}
```

### 5.2 添加订单工序

#### 请求

```
POST /sales/orders/{orderId}/processes
```

#### 权限要求

需要`sales:order:update`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |

#### 请求体

```json
{
  "sequence": 1,
  "processName": "分纸",
  "processRequirements": "按规格分切",
  "plateNumber": null,
  "inkNumber": null,
  "inkName": null,
  "colorCount": null
}
```

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "process-id-1",
    "orderId": "order-id-123",
    "sequence": 1,
    "processName": "分纸",
    "processRequirements": "按规格分切",
    "plateNumber": null,
    "inkNumber": null,
    "inkName": null,
    "colorCount": null,
    "createdBy": "admin",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": null,
    "updatedTime": null
  }
}
```

### 5.3 批量添加订单工序

#### 请求

```
POST /sales/orders/{orderId}/processes/batch
```

#### 权限要求

需要`sales:order:update`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |

#### 请求体

```json
[
  {
    "sequence": 1,
    "processName": "分纸",
    "processRequirements": "按规格分切",
    "plateNumber": null,
    "inkNumber": null,
    "inkName": null,
    "colorCount": null
  },
  {
    "sequence": 2,
    "processName": "印刷",
    "processRequirements": "四色印刷，对色精准",
    "plateNumber": "P001",
    "inkNumber": "I001",
    "inkName": "蓝色",
    "colorCount": 4
  }
]
```

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "process-id-1",
      "orderId": "order-id-123",
      "sequence": 1,
      "processName": "分纸",
      "processRequirements": "按规格分切",
      "plateNumber": null,
      "inkNumber": null,
      "inkName": null,
      "colorCount": null,
      "createdBy": "admin",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": null,
      "updatedTime": null
    },
    {
      "id": "process-id-2",
      "orderId": "order-id-123",
      "sequence": 2,
      "processName": "印刷",
      "processRequirements": "四色印刷，对色精准",
      "plateNumber": "P001",
      "inkNumber": "I001",
      "inkName": "蓝色",
      "colorCount": 4,
      "createdBy": "admin",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": null,
      "updatedTime": null
    }
  ]
}
```

### 5.4 更新订单工序

#### 请求

```
PUT /sales/orders/{orderId}/processes/{id}
```

#### 权限要求

需要`sales:order:update`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |
| id | String | 工序ID | 是 |

#### 请求体

```json
{
  "sequence": 1,
  "processName": "分纸",
  "processRequirements": "按规格精确分切",
  "plateNumber": null,
  "inkNumber": null,
  "inkName": null,
  "colorCount": null
}
```

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "process-id-1",
    "orderId": "order-id-123",
    "sequence": 1,
    "processName": "分纸",
    "processRequirements": "按规格精确分切",
    "plateNumber": null,
    "inkNumber": null,
    "inkName": null,
    "colorCount": null,
    "createdBy": "admin",
    "createdTime": "2023-06-01T10:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-06-01T11:00:00"
  }
}
```

### 5.5 删除订单工序

#### 请求

```
DELETE /sales/orders/{orderId}/processes/{id}
```

#### 权限要求

需要`sales:order:update`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |
| id | String | 工序ID | 是 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 5.6 删除订单所有工序

#### 请求

```
DELETE /sales/orders/{orderId}/processes
```

#### 权限要求

需要`sales:order:update`权限

#### 路径参数

| 参数名 | 类型 | 描述 | 是否必填 |
| --- | --- | --- | --- |
| orderId | String | 订单ID | 是 |

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

## 6. 分页响应格式说明

所有分页查询接口都使用统一的`PageResponse`格式返回数据：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [],           // 数据列表
    "totalElements": 100,    // 总记录数
    "totalPages": 10,        // 总页数
    "page": 1,              // 当前页码（从1开始）
    "size": 10,             // 每页大小
    "first": true,          // 是否为第一页
    "last": false,          // 是否为最后一页
    "empty": false          // 是否为空
  }
}
```

### 字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| content | Array | 当前页的数据列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |
| page | Integer | 当前页码，从1开始 |
| size | Integer | 每页大小 |
| first | Boolean | 是否为第一页 |
| last | Boolean | 是否为最后一页 |
| empty | Boolean | 当前页是否为空 |

## 7. 错误码

| 错误码 | 描述 |
| --- | --- |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 8. 注意事项

1. 所有接口都需要进行身份验证，请在请求头中添加`Authorization: Bearer {token}`。
2. 创建订单时，系统会自动为每个订单明细生成生产单号。
3. 创建订单时，不需要提供`id`、`createdBy`、`createdTime`等字段，这些字段会由系统自动填充。
4. 工序顺序(sequence)字段用于指定工序的执行顺序，应确保其唯一性和连续性。
5. 在更新销售订单时，会先删除所有现有工序，然后重新添加请求中的工序，因此请确保请求中包含所有需要保留的工序。
6. 工序名称(processName)字段为必填项，其他字段根据实际需要填写。
7. 印刷工序通常需要填写印版编号、水墨编号、水墨名称和颜色数等信息。
8. 分页参数中的`page`字段从1开始计数，响应中的`page`字段也是从1开始。
9. 分页响应格式使用统一的`PageResponse`结构，包含`content`、`totalElements`、`totalPages`、`page`、`size`、`first`、`last`、`empty`等字段。
10. 销售订单查询接口(`/sales/orders/query`)返回的是订单明细级别的数据，包含销售订单头信息和明细信息的组合。
11. 关键字搜索支持多字段模糊匹配，包括销售单号、客户名称、生产单号、客户订单号、客方货号、品名等。
12. **🔥 全面列筛选功能**：已实现完整的列筛选功能，支持48个筛选字段，覆盖90%的业务需求。
13. **筛选参数命名规则**：列筛选参数以`filter`前缀命名，如`filterCustomerNames`表示客户名称的多选筛选。
14. **筛选选项获取**：可通过`includeFilterOptions=true`参数在查询时同时获取筛选选项，或单独调用`/filter-options`接口获取。
15. **向后兼容性**：原有的查询接口(`/sales/orders/query`)保持完全兼容，新的筛选参数为可选参数。
16. **筛选类型说明**：
    - 文本筛选：支持模糊匹配（如`filterProductionOrderNo`）
    - 选择筛选：支持多值精确匹配（如`filterCustomerNames`）
    - 数字范围筛选：支持最小值和最大值范围（如`filterQuantityMin/Max`）
    - 日期范围筛选：支持开始和结束日期范围（如`filterOrderDateStart/End`）
    - 🔥 特殊标识筛选：支持布尔值筛选（如`filterIsSpecialPrices`）
17. **筛选选项限制**：每个字段的筛选选项最多返回50个，按记录数量降序排列。
18. **🔥 新增筛选字段分类**：
    - **生产和工艺相关**：生产纸质、连接方式、单位、币种、税率等
    - **人员和地址相关**：收货人电话、创建人等
    - **文本字段**：生产备注、备注、收货地址等（支持模糊匹配）
    - **数字范围字段**：备品数、单价、已采购数、纸质报价、重量、面积、体积等
    - **日期范围字段**：创建时间等
    - **🔥 采购相关字段**：采购供应商、纸板类别（通过子查询实现，性能优化）
18. **采购关联信息**：销售订单查询结果包含采购相关字段：
    - `purchaseOrderNo`：采购单号
    - `supplierName`：采购供应商名称
    - `purchasedQuantity`：已采购数量
    - `bindingSpecification`：合订规格（从关联的采购订单明细中获取）
19. **工序信息**：销售订单查询结果包含工序相关字段：
    - `processes`：工序信息，格式为"分纸→开槽→粘箱→包装"（从sales_order_item_process表中获取，按sequence字段排序）
20. **用料信息**：销售订单查询结果包含用料相关字段：
    - `pressSizeWidth`：压线尺寸(纸度)，格式如"165+130+165"（从sales_order_material表中获取，如有多个用料记录则取第一个）
21. **纸质类别信息**：销售订单查询结果包含纸质类别相关字段：
    - `paperTypeName`：纸质类别名称，如"三坑"、"双坑"、"单坑"、"E坑"（通过sales_order_item.paper_type关联paper_material.paper_name，再关联paper_type.paper_type_name获取）
22. **🔥 子查询字段特殊说明**：
    - **采购供应商筛选**：通过purchase_order表的supplier_name字段关联查询，只返回有关联销售订单明细的供应商
    - **纸板类别筛选**：通过purchase_order_item表的paper_board_category字段关联查询，只返回有关联销售订单明细的纸板类别
    - **性能优化**：子查询字段使用独立的查询逻辑，避免复杂的多表JOIN，提升查询性能
    - **数据一致性**：确保筛选选项与实际可筛选的数据保持一致，只显示真实存在关联关系的选项
