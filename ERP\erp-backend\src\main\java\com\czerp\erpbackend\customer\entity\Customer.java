package com.czerp.erpbackend.customer.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 客户档案实体
 */
@Entity
@Table(name = "cus_customer")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Customer extends BaseEntity {

    /**
     * 客户ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 客户编码
     */
    @Column(name = "customer_code", length = 50, nullable = false, unique = true)
    private String customerCode;

    /**
     * 客户名称
     */
    @Column(name = "customer_name", length = 100, nullable = false)
    private String customerName;

    /**
     * 客户分类ID
     */
    @Column(name = "category_id", length = 36)
    private String categoryId;

    /**
     * 客户分类
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", insertable = false, updatable = false)
    private CustomerCategory category;

    /**
     * 客户全称
     */
    @Column(name = "full_name", length = 200)
    private String fullName;

    /**
     * 电话
     */
    @Column(name = "phone", length = 20)
    private String phone;

    /**
     * 传真
     */
    @Column(name = "fax", length = 20)
    private String fax;

    /**
     * 联系人
     */
    @Column(name = "contact_person", length = 50)
    private String contactPerson;

    /**
     * 手机
     */
    @Column(name = "mobile", length = 20)
    private String mobile;

    /**
     * 地址
     */
    @Column(name = "address", length = 200)
    private String address;

    /**
     * 收货地址
     */
    @Column(name = "shipping_address", length = 200)
    private String shippingAddress;

    /**
     * 付款方式
     */
    @Column(name = "payment_method", length = 50)
    private String paymentMethod;

    /**
     * 税率%
     */
    @Column(name = "tax_rate", precision = 5, scale = 2)
    private BigDecimal taxRate;

    /**
     * 最近下单日期
     */
    @Column(name = "last_order_date")
    private LocalDate lastOrderDate;

    /**
     * 当月接单金额
     */
    @Column(name = "current_month_order_amount", precision = 15, scale = 2)
    private BigDecimal currentMonthOrderAmount;

    /**
     * 所属总公司
     */
    @Column(name = "parent_company", length = 100)
    private String parentCompany;

    /**
     * 地区
     */
    @Column(name = "region", length = 50)
    private String region;

    /**
     * 销售员
     */
    @Column(name = "sales_person", length = 50)
    private String salesPerson;

    /**
     * 跟单员
     */
    @Column(name = "order_tracker", length = 50)
    private String orderTracker;

    /**
     * 邮编
     */
    @Column(name = "postal_code", length = 20)
    private String postalCode;

    /**
     * 收货人
     */
    @Column(name = "receiver", length = 50)
    private String receiver;

    /**
     * 币别
     */
    @Column(name = "currency", length = 20)
    private String currency;

    /**
     * 默认报价单位
     */
    @Column(name = "default_quote_unit", length = 20)
    private String defaultQuoteUnit;

    /**
     * 订单备品比例%
     */
    @Column(name = "order_spare_ratio", precision = 5, scale = 2)
    private BigDecimal orderSpareRatio;

    /**
     * 单价小数位
     */
    @Column(name = "price_decimal_places")
    private Integer priceDecimalPlaces;

    /**
     * 金额小数位
     */
    @Column(name = "amount_decimal_places")
    private Integer amountDecimalPlaces;

    /**
     * 单重小数位
     */
    @Column(name = "unit_weight_decimal_places")
    private Integer unitWeightDecimalPlaces;

    /**
     * 总重小数位
     */
    @Column(name = "total_weight_decimal_places")
    private Integer totalWeightDecimalPlaces;

    /**
     * 单位面积小数位
     */
    @Column(name = "unit_area_decimal_places")
    private Integer unitAreaDecimalPlaces;

    /**
     * 总面积小数位
     */
    @Column(name = "total_area_decimal_places")
    private Integer totalAreaDecimalPlaces;

    /**
     * 特殊月结日期
     */
    @Column(name = "special_monthly_settlement_date")
    private Integer specialMonthlySettlementDate;

    /**
     * 可用额度金额
     */
    @Column(name = "available_credit_amount", precision = 15, scale = 2)
    private BigDecimal availableCreditAmount;

    /**
     * 最近送货日期
     */
    @Column(name = "last_delivery_date")
    private LocalDate lastDeliveryDate;

    /**
     * 未下单天数
     */
    @Column(name = "days_without_order")
    private Integer daysWithoutOrder;

    /**
     * 未送货天数
     */
    @Column(name = "days_without_delivery")
    private Integer daysWithoutDelivery;

    /**
     * 首次订单日期
     */
    @Column(name = "first_order_date")
    private LocalDate firstOrderDate;

    /**
     * 首次送货日期
     */
    @Column(name = "first_delivery_date")
    private LocalDate firstDeliveryDate;

    /**
     * 打印抬头
     */
    @Column(name = "print_header", length = 200)
    private String printHeader;

    /**
     * 停用
     */
    @Column(name = "disabled", nullable = false)
    private Boolean disabled = false;

    /**
     * 行业
     */
    @Column(name = "industry", length = 100)
    private String industry;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;
}
