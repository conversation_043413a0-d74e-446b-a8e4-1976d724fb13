package com.czerp.erpbackend.sales.repository;

import com.czerp.erpbackend.sales.entity.SalesOrderProcess;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 销售订单工序Repository
 */
@Repository
public interface SalesOrderProcessRepository extends JpaRepository<SalesOrderProcess, String>, JpaSpecificationExecutor<SalesOrderProcess> {

    /**
     * 根据订单ID查询工序列表
     * @param orderId 订单ID
     * @return 工序列表
     */
    List<SalesOrderProcess> findByOrderIdOrderBySequence(String orderId);

    /**
     * 根据订单ID和工序名称查询工序列表
     * @param orderId 订单ID
     * @param processName 工序名称
     * @return 工序列表
     */
    List<SalesOrderProcess> findByOrderIdAndProcessName(String orderId, String processName);

    /**
     * 根据订单ID删除工序
     * @param orderId 订单ID
     */
    void deleteByOrderId(String orderId);

    /**
     * 根据订单ID查询未删除的工序列表
     * @param orderId 订单ID
     * @return 工序列表
     */
    List<SalesOrderProcess> findByOrderIdAndIsDeletedFalseOrderBySequence(String orderId);

    /**
     * 根据订单明细项ID查询工序列表
     * @param orderItemId 订单明细项ID
     * @return 工序列表
     */
    List<SalesOrderProcess> findByOrderItemIdOrderBySequence(String orderItemId);

    /**
     * 根据订单明细项ID查询未删除的工序列表
     * @param orderItemId 订单明细项ID
     * @return 工序列表
     */
    List<SalesOrderProcess> findByOrderItemIdAndIsDeletedFalseOrderBySequence(String orderItemId);

    /**
     * 根据订单明细项ID删除工序
     * @param orderItemId 订单明细项ID
     */
    void deleteByOrderItemId(String orderItemId);

    /**
     * 根据订单明细项ID列表批量查询工序信息
     * @param orderItemIds 订单明细项ID列表
     * @return 工序列表，按订单明细项ID和工序顺序排序
     */
    @Query("SELECT p FROM SalesOrderProcess p WHERE p.orderItemId IN :orderItemIds AND p.isDeleted = false ORDER BY p.orderItemId ASC, p.sequence ASC")
    List<SalesOrderProcess> findByOrderItemIdInAndIsDeletedFalseOrderByOrderItemIdAscSequenceAsc(@Param("orderItemIds") List<String> orderItemIds);
}
