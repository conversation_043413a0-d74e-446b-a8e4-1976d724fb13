package com.czerp.erpbackend.product.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 切换状态请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToggleStatusRequest {
    
    /**
     * 是否禁用
     */
    @NotNull(message = "状态不能为空")
    private Boolean disabled;
}
