package com.czerp.erpbackend.purchase.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemForInboundDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemForInboundQueryRequest;
import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import com.czerp.erpbackend.purchase.service.PurchaseOrderForInboundService;
import com.czerp.erpbackend.purchase.service.PurchaseOrderItemCalculationService;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.entity.SalesOrderMaterial;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderMaterialRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用于入库管理引用采购订单的服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PurchaseOrderForInboundServiceImpl implements PurchaseOrderForInboundService {

    private final PurchaseOrderItemRepository purchaseOrderItemRepository;
    private final PurchaseOrderItemCalculationService calculationService;
    private final SalesOrderRepository salesOrderRepository;
    private final SalesOrderItemRepository salesOrderItemRepository;
    private final SalesOrderMaterialRepository salesOrderMaterialRepository;

    @Override
    public PageResponse<PurchaseOrderItemForInboundDTO> findPurchaseOrderItemsForInbound(PurchaseOrderItemForInboundQueryRequest request) {
        log.info("Finding purchase order items for inbound with request: {}", request);

        // 创建分页对象
        Pageable pageable = PageRequest.of(
                request.getPage(),
                request.getSize(),
                Sort.by(Sort.Direction.DESC, "id")
        );

        // 构建查询条件
        Specification<PurchaseOrderItem> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 关联采购订单表
            Join<PurchaseOrderItem, PurchaseOrder> orderJoin = root.join("purchaseOrder", JoinType.INNER);

            // 关键字查询（采购单号、供应商名称、生产单号等）
            if (StringUtils.hasText(request.getKeyword())) {
                String keyword = "%" + request.getKeyword() + "%";
                predicates.add(cb.or(
                        cb.like(orderJoin.get("purchaseOrderNo"), keyword),
                        cb.like(orderJoin.get("supplierName"), keyword),
                        cb.like(root.get("paperQuality"), keyword)
                ));
            }

            // 供应商编码
            if (StringUtils.hasText(request.getSupplierCode())) {
                predicates.add(cb.equal(orderJoin.get("supplierCode"), request.getSupplierCode()));
            }

            // 供应商名称
            if (StringUtils.hasText(request.getSupplierName())) {
                predicates.add(cb.like(orderJoin.get("supplierName"), "%" + request.getSupplierName() + "%"));
            }

            // 采购单号
            if (StringUtils.hasText(request.getPurchaseOrderNo())) {
                predicates.add(cb.like(orderJoin.get("purchaseOrderNo"), "%" + request.getPurchaseOrderNo() + "%"));
            }

            // 纸质
            if (StringUtils.hasText(request.getPaperType())) {
                predicates.add(cb.equal(root.get("paperQuality"), request.getPaperType()));
            }

            // 楞别
            if (StringUtils.hasText(request.getCorrugationType())) {
                predicates.add(cb.equal(root.get("corrugationType"), request.getCorrugationType()));
            }

            // 采购日期范围
            if (request.getPurchaseDateStart() != null) {
                predicates.add(cb.greaterThanOrEqualTo(orderJoin.get("purchaseDate"), request.getPurchaseDateStart()));
            }
            if (request.getPurchaseDateEnd() != null) {
                predicates.add(cb.lessThanOrEqualTo(orderJoin.get("purchaseDate"), request.getPurchaseDateEnd()));
            }

            // 交期范围
            if (request.getDeliveryDateStart() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("deliveryDate"), request.getDeliveryDateStart()));
            }
            if (request.getDeliveryDateEnd() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("deliveryDate"), request.getDeliveryDateEnd()));
            }

            // 纸板类别
            if (StringUtils.hasText(request.getPaperBoardCategory())) {
                predicates.add(cb.equal(root.get("paperBoardCategory"), request.getPaperBoardCategory()));
            }

            // 注意：这里不再使用存储字段过滤，改为在查询后使用动态计算过滤
            // 原因：存储字段可能不准确，需要使用动态计算确保数据一致性

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 执行查询
        Page<PurchaseOrderItem> purchaseOrderItemPage = purchaseOrderItemRepository.findAll(spec, pageable);

        // 构建数量映射用于批量检查可用性
        List<PurchaseOrderItem> allItems = purchaseOrderItemPage.getContent();
        List<Long> itemIds = allItems.stream()
                .map(PurchaseOrderItem::getId)
                .collect(Collectors.toList());

        Map<Long, Integer> quantityMap = allItems.stream()
                .collect(Collectors.toMap(
                        PurchaseOrderItem::getId,
                        item -> item.getQuantity() != null ? item.getQuantity() : 0
                ));

        // 使用动态计算批量检查可用性
        Map<Long, Boolean> availabilityMap = calculationService.isAvailableForInboundBatch(itemIds, quantityMap);

        // 转换为DTO（使用动态计算）并过滤已完全入库的明细
        List<PurchaseOrderItemForInboundDTO> dtoList = allItems.stream()
                .filter(item -> {
                    boolean isAvailable = availabilityMap.getOrDefault(item.getId(), false);

                    if (!isAvailable) {
                        log.debug("Purchase order item {} is fully received, filtered out from page query", item.getId());
                    }

                    return isAvailable;
                })
                .map(this::convertToDTOWithDynamicCalculation)
                .collect(Collectors.toList());

        // 注意：由于在查询后进行了动态过滤，分页信息可能不完全准确
        // 这是一个权衡：为了确保数据一致性，我们选择在应用层过滤
        // 更好的解决方案是在数据库层面使用子查询，但会增加查询复杂度

        return PageResponse.<PurchaseOrderItemForInboundDTO>builder()
                .content(dtoList)
                .page(request.getPage())
                .size(request.getSize())
                .totalElements((long) dtoList.size()) // 使用过滤后的实际数量
                .totalPages(dtoList.size() > 0 ? 1 : 0) // 简化分页逻辑
                .first(true)
                .last(true)
                .build();
    }

    @Override
    public PurchaseOrderItemForInboundDTO findPurchaseOrderItemById(Long purchaseOrderItemId) {
        log.info("Finding purchase order item by id: {}", purchaseOrderItemId);

        Optional<PurchaseOrderItem> purchaseOrderItemOpt = purchaseOrderItemRepository.findById(purchaseOrderItemId);
        if (purchaseOrderItemOpt.isPresent()) {
            PurchaseOrderItem item = purchaseOrderItemOpt.get();

            // 使用动态计算检查是否为未完全入库的采购订单明细
            boolean isAvailable = calculationService.isAvailableForInbound(item.getId(), item.getQuantity());

            if (!isAvailable) {
                log.info("Purchase order item {} is fully received, not available for inbound", purchaseOrderItemId);
                return null;
            }

            return convertToDTOWithDynamicCalculation(item);
        }

        log.warn("Purchase order item not found with id: {}", purchaseOrderItemId);
        return null;
    }

    @Override
    public List<PurchaseOrderItemForInboundDTO> findPurchaseOrderItemsByIds(List<Long> purchaseOrderItemIds) {
        log.info("Finding purchase order items by ids: {}", purchaseOrderItemIds);

        List<PurchaseOrderItem> purchaseOrderItems = purchaseOrderItemRepository.findAllById(purchaseOrderItemIds);

        // 构建数量映射用于批量检查
        Map<Long, Integer> quantityMap = purchaseOrderItems.stream()
                .collect(Collectors.toMap(
                        PurchaseOrderItem::getId,
                        item -> item.getQuantity() != null ? item.getQuantity() : 0
                ));

        // 使用动态计算批量检查可用性
        Map<Long, Boolean> availabilityMap = calculationService.isAvailableForInboundBatch(purchaseOrderItemIds, quantityMap);

        // 只返回未完全入库的采购订单明细
        return purchaseOrderItems.stream()
                .filter(item -> {
                    boolean isAvailable = availabilityMap.getOrDefault(item.getId(), false);

                    if (!isAvailable) {
                        log.debug("Purchase order item {} is fully received, filtered out from batch query", item.getId());
                    }

                    return isAvailable;
                })
                .map(this::convertToDTOWithDynamicCalculation)
                .collect(Collectors.toList());
    }

    /**
     * 转换为DTO（使用动态计算）
     *
     * @param purchaseOrderItem 采购订单明细实体
     * @return DTO
     */
    private PurchaseOrderItemForInboundDTO convertToDTOWithDynamicCalculation(PurchaseOrderItem purchaseOrderItem) {
        PurchaseOrder purchaseOrder = purchaseOrderItem.getPurchaseOrder();

        // 动态计算已入库数量
        Integer receivedQuantityDynamic = calculationService.calculateReceivedQuantity(purchaseOrderItem.getId());

        PurchaseOrderItemForInboundDTO dto = PurchaseOrderItemForInboundDTO.builder()
                .id(purchaseOrderItem.getId())
                .purchaseOrderId(purchaseOrder.getId())
                .inboundQuantity(purchaseOrderItem.getQuantity()) // 默认为采购数量，可编辑
                .inboundAmount(purchaseOrderItem.getAmount()) // 默认为采购金额
                .supplierName(purchaseOrder.getSupplierName())
                .purchaseOrderNo(purchaseOrder.getPurchaseOrderNo())
                .purchaseDate(purchaseOrder.getPurchaseDate())
                .quantity(purchaseOrderItem.getQuantity())
                .paperQuality(purchaseOrderItem.getPaperQuality())
                .bindingSpecification(purchaseOrderItem.getBindingSpecification())
                .receivedQuantity(receivedQuantityDynamic) // 使用动态计算的值
                .returnedQuantity(0) // 暂时设为0，后续可从退货记录中获取
                .paperBoardCategory(purchaseOrderItem.getPaperBoardCategory())
                .price(purchaseOrderItem.getPrice())
                .amount(purchaseOrderItem.getAmount())
                .deliveryDate(purchaseOrderItem.getDeliveryDate())
                .paperWidth(purchaseOrderItem.getPaperWidth())
                .paperLength(purchaseOrderItem.getPaperLength())
                .corrugationType(purchaseOrderItem.getCorrugationType())
                .creasingSize(purchaseOrderItem.getCreasingSize())
                .creasingMethod(purchaseOrderItem.getCreasingMethod())
                .foldingSpecification(purchaseOrderItem.getFoldingSpecification())
                .lengthMeters(purchaseOrderItem.getLengthMeters())
                .areaSquareMeters(purchaseOrderItem.getAreaSquareMeters())
                .volumeCubicMeters(purchaseOrderItem.getVolumeCubicMeters())
                .unitWeight(purchaseOrderItem.getUnitWeight())
                .totalWeightKg(purchaseOrderItem.getTotalWeightKg())
                .processingFee(purchaseOrderItem.getProcessingFee())
                .currency(purchaseOrderItem.getCurrency())
                .remarks(purchaseOrderItem.getRemarks())
                .sourceSalesOrderItemId(purchaseOrderItem.getSourceSalesOrderItemId())
                .createdBy(purchaseOrder.getCreatedBy())
                .paperQuotation(purchaseOrderItem.getPaperQuotation())
                .discount(purchaseOrderItem.getDiscount())
                .build();

        // 计算未入库数（使用动态计算的已入库数）
        int returnedQty = dto.getReturnedQuantity() != null ? dto.getReturnedQuantity() : 0;
        int unreceivedQty = dto.getQuantity() - receivedQuantityDynamic + returnedQty;
        dto.setUnreceivedQuantity(Math.max(0, unreceivedQty));

        // 如果有关联的销售订单明细，获取相关信息
        enrichWithSalesOrderData(dto);

        return dto;
    }

    /**
     * 转换为DTO（保留原有方法用于向后兼容）
     *
     * @param purchaseOrderItem 采购订单明细实体
     * @return DTO
     * @deprecated 使用 {@link #convertToDTOWithDynamicCalculation(PurchaseOrderItem)} 替代
     */
    @Deprecated
    private PurchaseOrderItemForInboundDTO convertToDTO(PurchaseOrderItem purchaseOrderItem) {
        PurchaseOrder purchaseOrder = purchaseOrderItem.getPurchaseOrder();

        PurchaseOrderItemForInboundDTO dto = PurchaseOrderItemForInboundDTO.builder()
                .id(purchaseOrderItem.getId())
                .purchaseOrderId(purchaseOrder.getId())
                .inboundQuantity(purchaseOrderItem.getQuantity()) // 默认为采购数量，可编辑
                .inboundAmount(purchaseOrderItem.getAmount()) // 默认为采购金额
                .supplierName(purchaseOrder.getSupplierName())
                .purchaseOrderNo(purchaseOrder.getPurchaseOrderNo())
                .purchaseDate(purchaseOrder.getPurchaseDate())
                .quantity(purchaseOrderItem.getQuantity())
                .paperQuality(purchaseOrderItem.getPaperQuality())
                .bindingSpecification(purchaseOrderItem.getBindingSpecification())
                .receivedQuantity(purchaseOrderItem.getReceivedQuantity() != null ? purchaseOrderItem.getReceivedQuantity() : 0)
                .returnedQuantity(0) // 暂时设为0，后续可从退货记录中获取
                .paperBoardCategory(purchaseOrderItem.getPaperBoardCategory())
                .price(purchaseOrderItem.getPrice())
                .amount(purchaseOrderItem.getAmount())
                .deliveryDate(purchaseOrderItem.getDeliveryDate())
                .paperWidth(purchaseOrderItem.getPaperWidth())
                .paperLength(purchaseOrderItem.getPaperLength())
                .corrugationType(purchaseOrderItem.getCorrugationType())
                .creasingSize(purchaseOrderItem.getCreasingSize())
                .creasingMethod(purchaseOrderItem.getCreasingMethod())
                .foldingSpecification(purchaseOrderItem.getFoldingSpecification())
                .lengthMeters(purchaseOrderItem.getLengthMeters())
                .areaSquareMeters(purchaseOrderItem.getAreaSquareMeters())
                .volumeCubicMeters(purchaseOrderItem.getVolumeCubicMeters())
                .unitWeight(purchaseOrderItem.getUnitWeight())
                .totalWeightKg(purchaseOrderItem.getTotalWeightKg())
                .processingFee(purchaseOrderItem.getProcessingFee())
                .currency(purchaseOrderItem.getCurrency())
                .remarks(purchaseOrderItem.getRemarks())
                .sourceSalesOrderItemId(purchaseOrderItem.getSourceSalesOrderItemId())
                .createdBy(purchaseOrder.getCreatedBy())
                .paperQuotation(purchaseOrderItem.getPaperQuotation())
                .discount(purchaseOrderItem.getDiscount())
                .build();

        // 计算未入库数
        int receivedQty = dto.getReceivedQuantity() != null ? dto.getReceivedQuantity() : 0;
        int returnedQty = dto.getReturnedQuantity() != null ? dto.getReturnedQuantity() : 0;
        int unreceivedQty = dto.getQuantity() - receivedQty + returnedQty;
        dto.setUnreceivedQuantity(Math.max(0, unreceivedQty));

        // 如果有关联的销售订单明细，获取相关信息
        enrichWithSalesOrderData(dto);

        return dto;
    }

    /**
     * 使用销售订单数据丰富DTO
     *
     * @param dto 采购订单明细DTO
     */
    private void enrichWithSalesOrderData(PurchaseOrderItemForInboundDTO dto) {
        if (!StringUtils.hasText(dto.getSourceSalesOrderItemId())) {
            log.debug("No source sales order item id for purchase order item: {}", dto.getId());
            return;
        }

        try {
            // 查询销售订单明细
            Optional<SalesOrderItem> salesOrderItemOpt = salesOrderItemRepository.findById(dto.getSourceSalesOrderItemId());
            if (!salesOrderItemOpt.isPresent()) {
                log.warn("Sales order item not found with id: {}", dto.getSourceSalesOrderItemId());
                return;
            }

            SalesOrderItem salesOrderItem = salesOrderItemOpt.get();
            SalesOrder salesOrder = salesOrderItem.getOrder();

            // 设置生产单号
            dto.setProductionOrderNo(salesOrderItem.getProductionOrderNo());

            // 设置客户信息
            dto.setCustomerCode(salesOrder.getCustomerCode());
            dto.setCustomerName(salesOrder.getCustomerName());
            dto.setCustomerOrderNo(salesOrderItem.getCustomerOrderNo());
            dto.setCustomerProductCode(salesOrderItem.getCustomerProductCode());

            // 设置产品信息
            dto.setProductName(salesOrderItem.getProductName());
            dto.setProcessRequirements(salesOrderItem.getProcessRequirements());
            dto.setBoxType(salesOrderItem.getBoxType());
            dto.setOrderPaperType(salesOrderItem.getPaperType());
            dto.setProductSpecification(buildSpecification(salesOrderItem.getLength(), salesOrderItem.getWidth(), salesOrderItem.getHeight(), salesOrderItem.getSizeUnit()));
            dto.setProductionSpecification(buildSpecification(salesOrderItem.getProductionLength(), salesOrderItem.getProductionWidth(), salesOrderItem.getProductionHeight(), salesOrderItem.getSizeUnit()));
            dto.setOrderQuantity(salesOrderItem.getQuantity());
            dto.setSalesOrderDeliveryDate(salesOrderItem.getDeliveryDate());

            // 设置销售订单信息
            dto.setSalesOrderNo(salesOrder.getOrderNo());
            dto.setSalesOrderDate(salesOrder.getOrderDate());

            // 构建产品字段（盒式+订单纸质使用空格分隔）
            StringBuilder productBuilder = new StringBuilder();
            if (StringUtils.hasText(salesOrderItem.getBoxType())) {
                productBuilder.append(salesOrderItem.getBoxType());
            }
            if (StringUtils.hasText(salesOrderItem.getPaperType())) {
                if (productBuilder.length() > 0) {
                    productBuilder.append(" ");
                }
                productBuilder.append(salesOrderItem.getPaperType());
            }
            dto.setProduct(productBuilder.toString());

            // 查询销售订单材料信息
            enrichWithSalesOrderMaterialData(dto, salesOrderItem.getId());

        } catch (Exception e) {
            log.error("Error enriching purchase order item {} with sales order data", dto.getId(), e);
        }
    }

    /**
     * 使用销售订单材料数据丰富DTO
     *
     * @param dto              采购订单明细DTO
     * @param salesOrderItemId 销售订单明细ID
     */
    private void enrichWithSalesOrderMaterialData(PurchaseOrderItemForInboundDTO dto, String salesOrderItemId) {
        try {
            // 查询销售订单材料信息（按序号排序）
            List<SalesOrderMaterial> materials = salesOrderMaterialRepository.findByOrderItemIdOrderBySerialNoAsc(salesOrderItemId);
            if (!materials.isEmpty()) {
                // 取第一个材料记录的信息（序号最小的）
                SalesOrderMaterial material = materials.get(0);
                dto.setBoardCount(material.getBoardCount());
                dto.setDieOpenCount(material.getDieOpenCount());
            }
        } catch (Exception e) {
            log.error("Error enriching purchase order item {} with sales order material data", dto.getId(), e);
        }
    }

    /**
     * 构建规格字符串（长×宽×高+单位）
     * @param length 长
     * @param width 宽
     * @param height 高
     * @param sizeUnit 尺寸单位
     * @return 规格字符串
     */
    private String buildSpecification(BigDecimal length, BigDecimal width, BigDecimal height, String sizeUnit) {
        if (length == null && width == null && height == null) {
            return null;
        }

        StringBuilder spec = new StringBuilder();
        if (length != null) {
            spec.append(length);
        }
        if (width != null) {
            if (spec.length() > 0) {
                spec.append("×");
            }
            spec.append(width);
        }
        if (height != null) {
            if (spec.length() > 0) {
                spec.append("×");
            }
            spec.append(height);
        }

        if (StringUtils.hasText(sizeUnit) && spec.length() > 0) {
            spec.append(sizeUnit);
        }

        return spec.length() > 0 ? spec.toString() : null;
    }
}
