package com.czerp.erpbackend.production.repository;

import com.czerp.erpbackend.production.entity.ProductionScheduleItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 生产排程单明细Repository
 */
@Repository
public interface ProductionScheduleItemRepository extends JpaRepository<ProductionScheduleItem, String>, JpaSpecificationExecutor<ProductionScheduleItem> {

    /**
     * 根据排程单ID查询明细列表
     * @param scheduleId 排程单ID
     * @return 明细列表
     */
    List<ProductionScheduleItem> findByScheduleId(String scheduleId);

    /**
     * 根据销售订单明细ID查询排程明细列表
     * @param salesOrderItemId 销售订单明细ID
     * @return 排程明细列表
     */
    List<ProductionScheduleItem> findBySalesOrderItemId(String salesOrderItemId);

    /**
     * 根据销售订单明细ID列表查询排程明细列表
     * @param salesOrderItemIds 销售订单明细ID列表
     * @return 排程明细列表
     */
    List<ProductionScheduleItem> findBySalesOrderItemIdIn(List<String> salesOrderItemIds);

    /**
     * 查询急单列表（按急单序号排序）
     * @return 急单列表
     */
    @Query("SELECT p FROM ProductionScheduleItem p WHERE p.isUrgent = true ORDER BY p.urgentSequence ASC")
    List<ProductionScheduleItem> findUrgentItems();

    /**
     * 查询未打印的排程明细
     * @return 未打印的排程明细列表
     */
    @Query("SELECT p FROM ProductionScheduleItem p WHERE p.isPrinted = false")
    List<ProductionScheduleItem> findUnprintedItems();

    /**
     * 根据排程单ID删除明细
     * @param scheduleId 排程单ID
     */
    void deleteByScheduleId(String scheduleId);
}
