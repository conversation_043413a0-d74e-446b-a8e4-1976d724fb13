CREATE TABLE czerp_web.customer_box_relation (
    id VARCHAR(36) NOT NULL COMMENT '主键ID',
    customer_id VARCHAR(36) NOT NULL COMMENT '客户ID',
    box_info_id VARCHAR(36) NOT NULL COMMENT '盒式信息ID',
    remark VARCHAR(255) NULL COMMENT '备注',
    created_by VA<PERSON><PERSON>R(50) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) NULL COMMENT '更新人',
    updated_time DATETIME NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    version INT NULL COMMENT '版本号',
    PRIMARY KEY (id),
    UNIQUE KEY uk_customer_box (customer_id, box_info_id),
    KEY idx_customer_id (customer_id),
    KEY idx_box_info_id (box_info_id),
    KEY idx_created_time (created_time),
    CONSTRAINT fk_customer_box_relation_customer FOREIGN KEY (customer_id) REFERENCES czerp_web.cus_customer (id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_customer_box_relation_box_info FOREIGN KEY (box_info_id) REFERENCES czerp_web.customer_box_info (id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客户盒式关联表';
