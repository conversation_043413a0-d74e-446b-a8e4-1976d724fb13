package com.czerp.erpbackend.product.mapper;

import com.czerp.erpbackend.product.dto.CreateProductRequest;
import com.czerp.erpbackend.product.dto.ProductDTO;
import com.czerp.erpbackend.product.dto.UpdateProductRequest;
import com.czerp.erpbackend.product.entity.Product;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 货品Mapper
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ProductMapper {

    /**
     * 实体转DTO
     * @param product 实体
     * @return DTO
     */
    @Mapping(source = "category.name", target = "categoryName")
    @Mapping(source = "spec.name", target = "specName")
    @Mapping(source = "createdTime", target = "createTime")
    @Mapping(source = "updatedTime", target = "updateTime")
    ProductDTO toDto(Product product);

    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "spec", ignore = true)
    @Mapping(target = "disabled", source = "disabled", defaultValue = "false")
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "isDeleted", constant = "false")
    Product toEntity(CreateProductRequest request);

    /**
     * 更新实体
     * @param request 更新请求
     * @param product 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "spec", ignore = true)
    @Mapping(target = "disabled", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateProductRequest request, @MappingTarget Product product);
}
