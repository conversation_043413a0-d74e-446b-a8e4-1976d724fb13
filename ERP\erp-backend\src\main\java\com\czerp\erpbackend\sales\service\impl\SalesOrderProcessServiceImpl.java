package com.czerp.erpbackend.sales.service.impl;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.common.util.IdGenerator;
import com.czerp.erpbackend.sales.dto.SalesOrderProcessDTO;
import com.czerp.erpbackend.sales.entity.SalesOrderProcess;
import com.czerp.erpbackend.sales.repository.SalesOrderProcessRepository;
import com.czerp.erpbackend.sales.repository.SalesOrderRepository;
import com.czerp.erpbackend.sales.service.SalesOrderProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售订单工序Service实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SalesOrderProcessServiceImpl implements SalesOrderProcessService {

    private final SalesOrderProcessRepository salesOrderProcessRepository;
    private final SalesOrderRepository salesOrderRepository;

    /**
     * 根据订单ID查询工序列表
     * @param orderId 订单ID
     * @return 工序列表
     */
    @Override
    public List<SalesOrderProcessDTO> getProcessesByOrderId(String orderId) {
        log.info("Getting processes for order: {}", orderId);

        // 验证订单是否存在
        if (!salesOrderRepository.existsById(orderId)) {
            throw new BusinessException("订单不存在");
        }

        List<SalesOrderProcess> processes = salesOrderProcessRepository.findByOrderIdAndIsDeletedFalseOrderBySequence(orderId);
        return processes.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 根据订单明细项ID查询工序列表
     * @param orderItemId 订单明细项ID
     * @return 工序列表
     */
    @Override
    public List<SalesOrderProcessDTO> getProcessesByOrderItemId(String orderItemId) {
        log.info("Getting processes for order item: {}", orderItemId);

        List<SalesOrderProcess> processes = salesOrderProcessRepository.findByOrderItemIdAndIsDeletedFalseOrderBySequence(orderItemId);
        return processes.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 添加工序
     * @param processDTO 工序DTO
     * @return 添加的工序
     */
    @Override
    @Transactional
    public SalesOrderProcessDTO addProcess(SalesOrderProcessDTO processDTO) {
        log.info("Adding process for order: {}, order item: {}", processDTO.getOrderId(), processDTO.getOrderItemId());

        // 验证订单是否存在
        if (!StringUtils.hasText(processDTO.getOrderId()) ||
            !salesOrderRepository.existsById(processDTO.getOrderId())) {
            throw new BusinessException("订单不存在");
        }

        // 创建工序实体
        SalesOrderProcess process = new SalesOrderProcess();
        process.setId(IdGenerator.generateId());
        process.setOrderId(processDTO.getOrderId());
        process.setOrderItemId(processDTO.getOrderItemId()); // 设置订单明细项ID
        process.setSequence(processDTO.getSequence());
        process.setProcessName(processDTO.getProcessName());
        process.setProcessRequirements(processDTO.getProcessRequirements());
        process.setPlateNumber(processDTO.getPlateNumber());
        process.setInkNumber(processDTO.getInkNumber());
        process.setInkName(processDTO.getInkName());
        process.setColorCount(processDTO.getColorCount());
        process.setIsDeleted(false);
        process.setVersion(0);

        // 保存工序
        SalesOrderProcess savedProcess = salesOrderProcessRepository.save(process);

        return convertToDTO(savedProcess);
    }

    /**
     * 批量添加工序
     * @param processDTOs 工序DTO列表
     * @return 添加的工序列表
     */
    @Override
    @Transactional
    public List<SalesOrderProcessDTO> addProcesses(List<SalesOrderProcessDTO> processDTOs) {
        if (processDTOs == null || processDTOs.isEmpty()) {
            return new ArrayList<>();
        }

        String orderId = processDTOs.get(0).getOrderId();
        log.info("Adding {} processes for order: {}", processDTOs.size(), orderId);

        // 验证订单是否存在
        if (!StringUtils.hasText(orderId) ||
            !salesOrderRepository.existsById(orderId)) {
            throw new BusinessException("订单不存在");
        }

        List<SalesOrderProcess> processes = new ArrayList<>();

        for (SalesOrderProcessDTO processDTO : processDTOs) {
            // 确保所有工序都属于同一个订单
            if (!orderId.equals(processDTO.getOrderId())) {
                throw new BusinessException("批量添加的工序必须属于同一个订单");
            }

            SalesOrderProcess process = new SalesOrderProcess();
            process.setId(IdGenerator.generateId());
            process.setOrderId(processDTO.getOrderId());
            process.setOrderItemId(processDTO.getOrderItemId()); // 设置订单明细项ID
            process.setSequence(processDTO.getSequence());
            process.setProcessName(processDTO.getProcessName());
            process.setProcessRequirements(processDTO.getProcessRequirements());
            process.setPlateNumber(processDTO.getPlateNumber());
            process.setInkNumber(processDTO.getInkNumber());
            process.setInkName(processDTO.getInkName());
            process.setColorCount(processDTO.getColorCount());
            process.setIsDeleted(false);
            process.setVersion(0);

            processes.add(process);
        }

        // 批量保存工序
        List<SalesOrderProcess> savedProcesses = salesOrderProcessRepository.saveAll(processes);

        return savedProcesses.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 更新工序
     * @param id 工序ID
     * @param processDTO 工序DTO
     * @return 更新后的工序
     */
    @Override
    @Transactional
    public SalesOrderProcessDTO updateProcess(String id, SalesOrderProcessDTO processDTO) {
        log.info("Updating process: {}", id);

        // 验证工序是否存在
        SalesOrderProcess process = salesOrderProcessRepository.findById(id)
                .orElseThrow(() -> new BusinessException("工序不存在"));

        // 不允许修改订单ID
        if (!process.getOrderId().equals(processDTO.getOrderId())) {
            throw new BusinessException("不允许修改工序所属的订单");
        }

        // 更新工序信息
        process.setOrderItemId(processDTO.getOrderItemId()); // 设置订单明细项ID
        process.setSequence(processDTO.getSequence());
        process.setProcessName(processDTO.getProcessName());
        process.setProcessRequirements(processDTO.getProcessRequirements());
        process.setPlateNumber(processDTO.getPlateNumber());
        process.setInkNumber(processDTO.getInkNumber());
        process.setInkName(processDTO.getInkName());
        process.setColorCount(processDTO.getColorCount());
        process.setVersion(process.getVersion() + 1);

        // 保存更新
        SalesOrderProcess updatedProcess = salesOrderProcessRepository.save(process);

        return convertToDTO(updatedProcess);
    }

    /**
     * 删除工序
     * @param id 工序ID
     */
    @Override
    @Transactional
    public void deleteProcess(String id) {
        log.info("Deleting process: {}", id);

        // 验证工序是否存在
        SalesOrderProcess process = salesOrderProcessRepository.findById(id)
                .orElseThrow(() -> new BusinessException("工序不存在"));

        // 逻辑删除
        process.setIsDeleted(true);
        process.setVersion(process.getVersion() + 1);

        salesOrderProcessRepository.save(process);
    }

    /**
     * 删除订单的所有工序
     * @param orderId 订单ID
     */
    @Override
    @Transactional
    public void deleteProcessesByOrderId(String orderId) {
        log.info("Deleting all processes for order: {}", orderId);

        // 验证订单是否存在
        if (!salesOrderRepository.existsById(orderId)) {
            throw new BusinessException("订单不存在");
        }

        // 获取订单的所有工序
        List<SalesOrderProcess> processes = salesOrderProcessRepository.findByOrderIdAndIsDeletedFalseOrderBySequence(orderId);

        // 逻辑删除所有工序
        for (SalesOrderProcess process : processes) {
            process.setIsDeleted(true);
            process.setVersion(process.getVersion() + 1);
        }

        salesOrderProcessRepository.saveAll(processes);
    }

    /**
     * 删除订单明细项的所有工序
     * @param orderItemId 订单明细项ID
     */
    @Override
    @Transactional
    public void deleteProcessesByOrderItemId(String orderItemId) {
        log.info("Deleting all processes for order item: {}", orderItemId);

        // 获取订单明细项的所有工序
        List<SalesOrderProcess> processes = salesOrderProcessRepository.findByOrderItemIdAndIsDeletedFalseOrderBySequence(orderItemId);

        // 逻辑删除所有工序
        for (SalesOrderProcess process : processes) {
            process.setIsDeleted(true);
            process.setVersion(process.getVersion() + 1);
        }

        salesOrderProcessRepository.saveAll(processes);
    }

    /**
     * 将实体转换为DTO
     * @param process 工序实体
     * @return 工序DTO
     */
    private SalesOrderProcessDTO convertToDTO(SalesOrderProcess process) {
        return SalesOrderProcessDTO.builder()
                .id(process.getId())
                .orderId(process.getOrderId())
                .orderItemId(process.getOrderItemId())
                .sequence(process.getSequence())
                .processName(process.getProcessName())
                .processRequirements(process.getProcessRequirements())
                .plateNumber(process.getPlateNumber())
                .inkNumber(process.getInkNumber())
                .inkName(process.getInkName())
                .colorCount(process.getColorCount())
                .createdBy(process.getCreatedBy())
                .createdTime(process.getCreatedTime())
                .updatedBy(process.getUpdatedBy())
                .updatedTime(process.getUpdatedTime())
                .build();
    }
}
