package com.czerp.erpbackend.system.entity.enums;

import lombok.Getter;

/**
 * 通用状态枚举
 */
@Getter
public enum CommonStatus {
    
    /**
     * 启用
     */
    ACTIVE("active", "启用"),
    
    /**
     * 停用
     */
    INACTIVE("inactive", "停用");
    
    private final String code;
    private final String description;
    
    CommonStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据编码获取状态
     * @param code 编码
     * @return 状态
     */
    public static CommonStatus fromCode(String code) {
        for (CommonStatus status : CommonStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 