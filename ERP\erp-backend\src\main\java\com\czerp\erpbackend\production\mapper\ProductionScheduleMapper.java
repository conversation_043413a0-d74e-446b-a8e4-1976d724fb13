package com.czerp.erpbackend.production.mapper;

import com.czerp.erpbackend.production.dto.CreateProductionScheduleRequest;
import com.czerp.erpbackend.production.dto.ProductionScheduleDTO;
import com.czerp.erpbackend.production.dto.ProductionScheduleItemDTO;
import com.czerp.erpbackend.production.entity.ProductionSchedule;
import com.czerp.erpbackend.production.entity.ProductionScheduleItem;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 生产排程单映射器
 */
@Component
public class ProductionScheduleMapper {

    /**
     * Entity转DTO
     */
    public ProductionScheduleDTO toDto(ProductionSchedule entity) {
        if (entity == null) {
            return null;
        }

        ProductionScheduleDTO dto = new ProductionScheduleDTO();
        dto.setId(entity.getId());
        dto.setScheduleNo(entity.getScheduleNo());
        dto.setScheduleDate(entity.getScheduleDate());
        dto.setRemark(entity.getRemark());
        dto.setLastPrintTime(entity.getLastPrintTime());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedTime(entity.getCreatedTime());
        dto.setUpdatedBy(entity.getUpdatedBy());
        dto.setUpdatedTime(entity.getUpdatedTime());
        dto.setVersion(entity.getVersion());

        // 转换明细
        if (entity.getItems() != null) {
            List<ProductionScheduleItemDTO> itemDTOs = entity.getItems().stream()
                    .map(this::toItemDto)
                    .collect(Collectors.toList());
            dto.setItems(itemDTOs);
        }

        return dto;
    }

    /**
     * Entity列表转DTO列表
     */
    public List<ProductionScheduleDTO> toDto(List<ProductionSchedule> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 明细Entity转DTO
     */
    public ProductionScheduleItemDTO toItemDto(ProductionScheduleItem entity) {
        if (entity == null) {
            return null;
        }

        ProductionScheduleItemDTO dto = new ProductionScheduleItemDTO();
        dto.setId(entity.getId());
        dto.setScheduleId(entity.getSchedule().getId());
        dto.setIsUrgent(entity.getIsUrgent());
        dto.setUrgentSequence(entity.getUrgentSequence());
        dto.setIsPrinted(entity.getIsPrinted());
        dto.setPlannedCompletionDate(entity.getPlannedCompletionDate());
        dto.setScheduleQuantity(entity.getScheduleQuantity());
        dto.setPackageCount(entity.getPackageCount());
        dto.setRemark(entity.getRemark());
        dto.setSalesOrderItemId(entity.getSalesOrderItemId());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedTime(entity.getCreatedTime());
        dto.setUpdatedBy(entity.getUpdatedBy());
        dto.setUpdatedTime(entity.getUpdatedTime());
        dto.setVersion(entity.getVersion());

        // 从关联的排程单中获取最近打印时间
        if (entity.getSchedule() != null) {
            dto.setLastPrintTime(entity.getSchedule().getLastPrintTime());
        }

        return dto;
    }

    /**
     * 明细Entity列表转DTO列表
     */
    public List<ProductionScheduleItemDTO> toItemDto(List<ProductionScheduleItem> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::toItemDto)
                .collect(Collectors.toList());
    }

    /**
     * 创建请求转Entity
     */
    public ProductionSchedule toEntity(CreateProductionScheduleRequest request) {
        if (request == null) {
            return null;
        }

        ProductionSchedule entity = new ProductionSchedule();
        entity.setScheduleDate(request.getScheduleDate());
        entity.setRemark(request.getRemark());
        entity.setIsDeleted(false);

        return entity;
    }
}
