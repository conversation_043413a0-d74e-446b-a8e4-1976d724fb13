# 入库管理查询接口阶段二性能测试指南

## 📋 测试目标

验证阶段二性能优化的效果，确保：
1. 查询性能显著提升（>10倍）
2. 查询结果完全一致
3. 功能完整性保持
4. 系统稳定性良好

## 🧪 测试环境准备

### **1. 测试数据准备**

#### **小数据量测试（50条记录）**
```sql
-- 确保有50条入库明细记录，包含完整关联数据
SELECT COUNT(*) FROM stock_inbound_item si
JOIN stock_inbound sb ON si.inbound_id = sb.id
LEFT JOIN purchase_order_item poi ON si.purchase_order_item_id = poi.id
LEFT JOIN purchase_order po ON poi.purchase_order_id = po.id
LEFT JOIN sales_order_item soi ON poi.source_sales_order_item_id = soi.id
LEFT JOIN sales_order so ON soi.order_id = so.id
WHERE si.is_deleted = 0 AND sb.is_deleted = 0;
```

#### **中等数据量测试（500条记录）**
```sql
-- 如果数据不足，可以复制现有数据进行测试
INSERT INTO stock_inbound_item (/* 字段列表 */)
SELECT /* 字段列表，修改ID */ FROM stock_inbound_item LIMIT 500;
```

#### **大数据量测试（1000+条记录）**
```sql
-- 生成大量测试数据
-- 注意：生产环境请谨慎执行
```

### **2. 性能监控工具**

#### **数据库查询监控**
```sql
-- 开启MySQL查询日志
SET GLOBAL general_log = 'ON';
SET GLOBAL log_output = 'TABLE';

-- 查看查询日志
SELECT * FROM mysql.general_log 
WHERE command_type = 'Query' 
AND argument LIKE '%stock_inbound_item%'
ORDER BY event_time DESC;
```

#### **应用性能监控**
- 查看应用日志中的执行时间
- 监控JVM内存使用情况
- 观察数据库连接池状态

## 🔬 测试用例

### **测试用例1：功能一致性测试**

**目标：** 验证优化查询与原始查询结果完全一致

**测试步骤：**
1. 使用相同查询条件调用两个接口
2. 对比返回结果的每个字段
3. 验证分页信息一致性

**API调用：**
```bash
# 原始查询
curl -X GET "http://localhost:8080/api/stock-inbounds/items/original?page=0&size=10&keyword=测试" \
  -H "Authorization: Bearer {token}"

# 优化查询  
curl -X GET "http://localhost:8080/api/stock-inbounds/items/optimized?page=0&size=10&keyword=测试" \
  -H "Authorization: Bearer {token}"
```

**验证点：**
- ✅ 返回记录数量一致
- ✅ 每条记录的所有字段值一致
- ✅ 分页信息一致（totalElements, totalPages等）
- ✅ 排序顺序一致

### **测试用例2：小数据量性能测试**

**目标：** 验证小数据量下的性能提升

**测试参数：**
- 数据量：50条记录
- 分页大小：10条/页
- 测试次数：10次

**测试步骤：**
```bash
# 测试原始查询性能
for i in {1..10}; do
  time curl -X GET "http://localhost:8080/api/stock-inbounds/items/original?page=0&size=10" \
    -H "Authorization: Bearer {token}" > /dev/null
done

# 测试优化查询性能
for i in {1..10}; do
  time curl -X GET "http://localhost:8080/api/stock-inbounds/items/optimized?page=0&size=10" \
    -H "Authorization: Bearer {token}" > /dev/null
done
```

**预期结果：**
- 原始查询：100-200ms
- 优化查询：20-50ms
- 性能提升：3-5倍

### **测试用例3：中等数据量性能测试**

**目标：** 验证中等数据量下的显著性能提升

**测试参数：**
- 数据量：500条记录
- 分页大小：50条/页
- 测试次数：5次

**测试步骤：**
```bash
# 测试原始查询性能
for i in {1..5}; do
  time curl -X GET "http://localhost:8080/api/stock-inbounds/items/original?page=0&size=50" \
    -H "Authorization: Bearer {token}" > /dev/null
done

# 测试优化查询性能  
for i in {1..5}; do
  time curl -X GET "http://localhost:8080/api/stock-inbounds/items/optimized?page=0&size=50" \
    -H "Authorization: Bearer {token}" > /dev/null
done
```

**预期结果：**
- 原始查询：2-5秒
- 优化查询：100-200ms
- 性能提升：10-25倍

### **测试用例4：大数据量性能测试**

**目标：** 验证大数据量下的系统稳定性

**测试参数：**
- 数据量：1000+条记录
- 分页大小：100条/页
- 测试次数：3次

**测试步骤：**
```bash
# 测试优化查询性能（不建议测试原始查询，可能导致超时）
for i in {1..3}; do
  time curl -X GET "http://localhost:8080/api/stock-inbounds/items/optimized?page=0&size=100" \
    -H "Authorization: Bearer {token}" > /dev/null
done
```

**预期结果：**
- 优化查询：200-500ms
- 系统稳定，无内存溢出
- 数据库连接正常

### **测试用例5：复杂查询条件测试**

**目标：** 验证复杂查询条件下的性能

**测试参数：**
```json
{
  "keyword": "测试",
  "inboundDateStart": "2024-01-01",
  "inboundDateEnd": "2024-12-31",
  "supplierName": "供应商",
  "warehouse": "仓库"
}
```

**测试步骤：**
```bash
# 复杂条件查询
curl -X GET "http://localhost:8080/api/stock-inbounds/items/optimized" \
  -H "Authorization: Bearer {token}" \
  -G -d "keyword=测试" \
  -d "inboundDateStart=2024-01-01" \
  -d "inboundDateEnd=2024-12-31" \
  -d "supplierName=供应商" \
  -d "warehouse=仓库" \
  -d "page=0" \
  -d "size=20"
```

**验证点：**
- ✅ 查询条件正确应用
- ✅ 响应时间在可接受范围内
- ✅ 返回结果符合条件

## 📊 性能测试结果记录

### **测试结果模板**

| 测试场景 | 数据量 | 分页大小 | 原始查询时间 | 优化查询时间 | 性能提升 | 状态 |
|---------|--------|----------|-------------|-------------|----------|------|
| 小数据量 | 50条 | 10条/页 | 150ms | 30ms | 5倍 | ✅ |
| 中等数据量 | 500条 | 50条/页 | 3000ms | 150ms | 20倍 | ✅ |
| 大数据量 | 1000条 | 100条/页 | N/A | 300ms | N/A | ✅ |
| 复杂查询 | 200条 | 20条/页 | 800ms | 80ms | 10倍 | ✅ |

### **数据库查询次数对比**

| 测试场景 | 原始查询次数 | 优化查询次数 | 减少比例 |
|---------|-------------|-------------|----------|
| 50条记录 | 51次 | 1次 | 98% |
| 100条记录 | 101次 | 1次 | 99% |
| 500条记录 | 501次 | 1次 | 99.8% |

## 🔍 问题排查指南

### **常见问题及解决方案**

#### **1. 查询结果不一致**
**症状：** 优化查询与原始查询返回结果不同
**排查步骤：**
1. 检查JOIN条件是否正确
2. 验证数据类型转换
3. 确认排序逻辑一致
4. 检查分页参数

#### **2. 性能提升不明显**
**症状：** 优化查询性能提升小于预期
**排查步骤：**
1. 检查数据库索引
2. 分析查询执行计划
3. 确认测试数据量足够
4. 检查网络延迟影响

#### **3. 内存使用过高**
**症状：** 大数据量查询导致内存问题
**排查步骤：**
1. 减少分页大小
2. 检查对象创建数量
3. 分析内存泄漏
4. 调整JVM参数

#### **4. 查询超时**
**症状：** 查询执行时间过长
**排查步骤：**
1. 检查数据库连接
2. 分析SQL执行计划
3. 确认索引有效性
4. 检查数据库锁等待

## ✅ 验收标准

### **性能验收标准**
1. **小数据量（<100条）：** 性能提升 > 3倍
2. **中等数据量（100-500条）：** 性能提升 > 10倍
3. **大数据量（>500条）：** 响应时间 < 1秒

### **功能验收标准**
1. **结果一致性：** 100%一致
2. **查询条件：** 全部支持
3. **分页功能：** 正常工作
4. **排序功能：** 正常工作

### **稳定性验收标准**
1. **连续查询：** 无内存泄漏
2. **并发查询：** 系统稳定
3. **异常处理：** 完善可靠

## 🎯 测试报告模板

### **测试总结**
- 测试日期：YYYY-MM-DD
- 测试环境：开发/测试/生产
- 测试数据量：XXX条记录
- 测试结果：通过/失败

### **性能提升总结**
- 平均性能提升：XX倍
- 查询次数减少：XX%
- 响应时间改善：从X秒降低到Xms

### **问题和建议**
- 发现的问题及解决方案
- 性能优化建议
- 后续改进计划
