package com.czerp.erpbackend.auth.service.impl;

import com.czerp.erpbackend.auth.dto.LoginRequest;
import com.czerp.erpbackend.auth.dto.LoginResponse;
import com.czerp.erpbackend.auth.dto.RefreshTokenRequest;
import com.czerp.erpbackend.auth.dto.RefreshTokenResponse;
import com.czerp.erpbackend.auth.service.AuthService;
import com.czerp.erpbackend.auth.service.TokenBlacklistService;
import com.czerp.erpbackend.auth.util.JwtUtil;
import com.czerp.erpbackend.system.dto.UserDTO;
import com.czerp.erpbackend.system.entity.User;
import com.czerp.erpbackend.system.mapper.UserMapper;
import com.czerp.erpbackend.system.repository.UserRepository;
import com.czerp.erpbackend.system.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthServiceImpl implements AuthService {

    private final AuthenticationManager authenticationManager;
    private final JwtUtil jwtUtil;
    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final TokenBlacklistService tokenBlacklistService;

    @Override
    @Transactional(readOnly = true)
    public LoginResponse login(LoginRequest request) {
        log.debug("Attempting login for user: {}", request.getUsername());
        try {
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
            );
            SecurityContextHolder.getContext().setAuthentication(authentication);

            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            String username = userDetails.getUsername();

            User user = userRepository.findByUsername(username)
                    .orElseThrow(() -> new UsernameNotFoundException("User not found after authentication: " + username));

            String accessToken = jwtUtil.generateToken(username);
            String refreshToken = jwtUtil.generateRefreshToken(username);
            long expiresInSeconds = jwtUtil.getExpirationInSeconds();

            log.debug("Login successful for user: {}", username);

            UserDTO userDto = userMapper.toDto(user);

            return LoginResponse.builder()
                    .token(accessToken)
                    .refreshToken(refreshToken)
                    .user(userDto)
                    .expiresIn(expiresInSeconds)
                    .build();

        } catch (BadCredentialsException e) {
            log.warn("Login failed for user: {} - Invalid credentials", request.getUsername());
            throw new BadCredentialsException("用户名或密码错误");
        } catch (Exception e) {
            log.error("An unexpected error occurred during login for user: {}", request.getUsername(), e);
            throw new RuntimeException("登录过程中发生意外错误");
        }
    }

    @Override
    public void logout(String token) {
        if (token == null || token.isEmpty()) {
            log.warn("Logout attempt with null or empty token");
            return;
        }

        try {
            // 从令牌中提取过期时间
            Date expirationDate = jwtUtil.extractExpiration(token);
            long expirationTime = expirationDate.getTime();

            // 将令牌添加到黑名单
            tokenBlacklistService.addToBlacklist(token, expirationTime);

            // 清除安全上下文
            SecurityContextHolder.clearContext();

            log.debug("User logged out successfully, token added to blacklist");
        } catch (Exception e) {
            log.error("Error during logout", e);
        }
    }

    @Override
    public RefreshTokenResponse refreshToken(RefreshTokenRequest request) {
        // TODO: Implement refresh token logic
        log.warn("Refresh token functionality not yet implemented");
        throw new UnsupportedOperationException("Refresh token not implemented");
    }

    @Override
    public UserDTO getCurrentUser() {
        // TODO: Implement logic to get current user based on SecurityContext
        log.warn("Get current user functionality not yet implemented");
        throw new UnsupportedOperationException("Get current user not implemented");
    }

    @Override
    public void changePassword(String oldPassword, String newPassword) {
        // TODO: Implement change password logic
        log.warn("Change password functionality not yet implemented");
        throw new UnsupportedOperationException("Change password not implemented");
    }

    @Override
    public String validateToken(String token) {
        // TODO: Implement token validation logic
        log.warn("Token validation functionality not yet implemented");
        throw new UnsupportedOperationException("Token validation not implemented");
    }
}