package com.czerp.erpbackend.sales.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 销售订单实体
 */
@Entity
@Table(name = "sales_order")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrder extends BaseEntity {

    /**
     * 订单ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 订单编号
     */
    @Column(name = "order_no", length = 50, nullable = false, unique = true)
    private String orderNo;

    /**
     * 订单日期
     */
    @Column(name = "order_date")
    private LocalDate orderDate;

    /**
     * 付款方式
     */
    @Column(name = "payment_method", length = 50)
    private String paymentMethod;

    /**
     * 客户编码
     */
    @Column(name = "customer_code", length = 50)
    private String customerCode;

    /**
     * 客户名称
     */
    @Column(name = "customer_name", length = 200)
    private String customerName;

    /**
     * 销售员
     */
    @Column(name = "sales_person", length = 50)
    private String salesPerson;

    /**
     * 客户采购员
     */
    @Column(name = "customer_purchaser", length = 50)
    private String customerPurchaser;

    /**
     * 收货单位
     */
    @Column(name = "receiving_unit", length = 200)
    private String receivingUnit;

    /**
     * 收货人
     */
    @Column(name = "receiver", length = 50)
    private String receiver;

    /**
     * 收货人电话
     */
    @Column(name = "receiver_phone", length = 50)
    private String receiverPhone;

    /**
     * 收货地址
     */
    @Column(name = "receiving_address", length = 500)
    private String receivingAddress;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 订单类型
     */
    @Column(name = "order_type", length = 50)
    private String orderType;

    /**
     * 创建人姓名
     */
    @Column(name = "created_by_name", length = 50)
    private String createdByName;

    /**
     * 更新人姓名
     */
    @Column(name = "updated_by_name", length = 50)
    private String updatedByName;

    /**
     * 订单明细
     */
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<SalesOrderItem> items = new ArrayList<>();
}
