package com.czerp.erpbackend.inventory.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 创建入库单请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateStockInboundRequest {

    /**
     * 入库日期
     */
    @NotNull(message = "入库日期不能为空")
    private LocalDate inboundDate;

    /**
     * 仓库
     */
    private String warehouse;

    /**
     * 备注
     */
    private String remark;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商送货单号
     */
    private String supplierDeliveryNo;

    /**
     * 送货单日期
     */
    private LocalDate deliveryDate;

    /**
     * 入库单明细
     */
    @NotEmpty(message = "入库单明细不能为空")
    @Valid
    private List<CreateStockInboundItemRequest> items = new ArrayList<>();
}
