# 销售订单引用API文档

## 目录

- [1. 概述](#1-概述)
- [2. 接口列表](#2-接口列表)
- [3. 接口详情](#3-接口详情)
  - [3.1 分页查询可用于采购的销售订单明细](#31-分页查询可用于采购的销售订单明细)
  - [3.2 根据ID查询销售订单明细](#32-根据id查询销售订单明细)
  - [3.3 根据多个ID查询销售订单明细](#33-根据多个id查询销售订单明细)
- [4. 数据结构](#4-数据结构)
  - [4.1 销售订单明细查询请求](#41-销售订单明细查询请求)
  - [4.2 销售订单明细响应](#42-销售订单明细响应)
  - [4.3 分页响应](#43-分页响应)
  - [4.4 API统一响应](#44-api统一响应)
- [5. 字段映射说明](#5-字段映射说明)
  - [5.1 销售订单明细到采购订单明细的字段映射](#51-销售订单明细到采购订单明细的字段映射)
  - [5.2 采购状态更新逻辑](#52-采购状态更新逻辑)

## 1. 概述

销售订单引用API提供了查询销售订单明细的功能，用于在创建采购订单时引用销售订单数据。这些接口只返回未采购或部分采购的销售订单明细，以避免重复采购。当采购订单引用销售订单明细创建后，系统会自动更新销售订单明细的采购状态和已采购数量。

## 2. 接口列表

| 接口名称 | 请求方式 | 接口路径 | 描述 |
| --- | --- | --- | --- |
| 分页查询可用于采购的销售订单明细 | GET | /sales-orders-for-purchase/items | 分页查询可用于采购的销售订单明细 |
| 根据ID查询销售订单明细 | GET | /sales-orders-for-purchase/items/{id} | 根据ID查询销售订单明细 |
| 根据多个ID查询销售订单明细 | GET | /sales-orders-for-purchase/items/batch | 根据多个ID查询销售订单明细 |

## 3. 接口详情

### 3.1 分页查询可用于采购的销售订单明细

该接口只返回未采购或部分采购的销售订单明细（purchase_status为NOT_PURCHASED或PARTIALLY_PURCHASED）。

#### 请求

```
GET /sales-orders-for-purchase/items?page=0&size=10&keyword=关键字&customerCode=客户编码&orderDateStart=2023-01-01&orderDateEnd=2023-12-31&deliveryDateStart=2023-01-01&deliveryDateEnd=2023-12-31&paperType=纸质&corrugationType=楞别
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| page | Integer | 否 | 页码，从0开始，默认为0 |
| size | Integer | 否 | 每页大小，默认为10 |
| keyword | String | 否 | 关键字（生产单号、客户名称、品名等） |
| customerCode | String | 否 | 客户编码 |
| orderDateStart | LocalDate | 否 | 订单日期开始 |
| orderDateEnd | LocalDate | 否 | 订单日期结束 |
| deliveryDateStart | LocalDate | 否 | 交期开始 |
| deliveryDateEnd | LocalDate | 否 | 交期结束 |
| paperType | String | 否 | 纸质 |
| corrugationType | String | 否 | 楞别 |
| sortField | String | 否 | 排序字段 |
| sortDirection | String | 否 | 排序方向（asc/desc） |

#### 权限要求

需要`purchase:order:create`权限

#### 响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "productionOrderNo": "PO2023060100001",
        "paperType": "A级纸",
        "paperBoardCategory": "瓦楞纸板",
        "corrugationType": "三层",
        "width": 100.5,
        "length": 200.5,
        "creasingSize": "100x200",
        "creasingMethod": "标准",
        "customerName": "客户A",
        "customerOrderNo": "CO2023060100001",
        "customerProductCode": "CP001",
        "productName": "产品A",
        "processRequirements": "工艺要求A",
        "boxType": "盒式A",
        "specification": "100 × 200 × 300 CM",
      "productionSpecification": "95 × 195 × 295 CM",
      "sizeUnit": "CM",
        "orderPaperType": "A级纸",
        "mouldOpenCount": 10,
        "quantity": 1000,
        "deliveryDate": "2023-06-15",
        "salesOrderDeliveryDate": "2023-06-15",
        "remark": "备注A",
        "orderId": "order1",
        "orderItemId": "item1"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "page": 1,
    "size": 10,
    "first": true,
    "last": true,
    "empty": false
  }
}
```

### 3.2 根据ID查询销售订单明细

#### 请求

```
GET /sales-orders-for-purchase/items/{id}
```

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| id | String | 是 | 销售订单明细ID |

#### 权限要求

需要`purchase:order:create`权限

#### 响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "productionOrderNo": "PO2023060100001",
    "paperType": "A级纸",
    "paperBoardCategory": "瓦楞纸板",
    "corrugationType": "三层",
    "width": 100.5,
    "length": 200.5,
    "creasingSize": "100x200",
    "creasingMethod": "标准",
    "customerName": "客户A",
    "customerOrderNo": "CO2023060100001",
    "customerProductCode": "CP001",
    "productName": "产品A",
    "processRequirements": "工艺要求A",
    "boxType": "盒式A",
    "specification": "100 × 200 × 300 CM",
    "productionSpecification": "95 × 195 × 295 CM",
    "sizeUnit": "CM",
    "orderPaperType": "A级纸",
    "mouldOpenCount": 10,
    "quantity": 1000,
    "deliveryDate": "2023-06-15",
    "salesOrderDeliveryDate": "2023-06-15",
    "remark": "备注A",
    "orderId": "order1",
    "orderItemId": "item1"
  }
}
```

### 3.3 根据多个ID查询销售订单明细

#### 请求

```
GET /sales-orders-for-purchase/items/batch?ids=item1&ids=item2
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| ids | List<String> | 是 | 销售订单明细ID列表 |

#### 权限要求

需要`purchase:order:create`权限

#### 响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "productionOrderNo": "PO2023060100001",
      "paperType": "A级纸",
      "paperBoardCategory": "瓦楞纸板",
      "corrugationType": "三层",
      "width": 100.5,
      "length": 200.5,
      "creasingSize": "100x200",
      "creasingMethod": "标准",
      "customerName": "客户A",
      "customerOrderNo": "CO2023060100001",
      "customerProductCode": "CP001",
      "productName": "产品A",
      "processRequirements": "工艺要求A",
      "boxType": "盒式A",
      "specification": "100 × 200 × 300 CM",
      "productionSpecification": "95 × 195 × 295 CM",
      "sizeUnit": "CM",
      "orderPaperType": "A级纸",
      "mouldOpenCount": 10,
      "quantity": 1000,
      "deliveryDate": "2023-06-15",
      "salesOrderDeliveryDate": "2023-06-15",
      "remark": "备注A",
      "orderId": "order1",
      "orderItemId": "item1"
    },
    {
      "productionOrderNo": "PO2023060100002",
      "paperType": "B级纸",
      "paperBoardCategory": "瓦楞纸板",
      "corrugationType": "五层",
      "width": 150.5,
      "length": 250.5,
      "creasingSize": "150x250",
      "creasingMethod": "标准",
      "customerName": "客户B",
      "customerOrderNo": "CO2023060100002",
      "customerProductCode": "CP002",
      "productName": "产品B",
      "processRequirements": "工艺要求B",
      "boxType": "盒式B",
      "specification": "150 × 250 × 350 CM",
      "productionSpecification": "145 × 245 × 345 CM",
      "sizeUnit": "CM",
      "orderPaperType": "B级纸",
      "mouldOpenCount": 20,
      "quantity": 2000,
      "deliveryDate": "2023-06-20",
      "salesOrderDeliveryDate": "2023-06-20",
      "remark": "备注B",
      "orderId": "order2",
      "orderItemId": "item2"
    }
  ]
}
```

## 4. 数据结构

### 4.1 销售订单明细查询请求

| 字段名 | 类型 | 必填 | 描述 | 中文名 |
| --- | --- | --- | --- | --- |
| page | Integer | 否 | 页码，从0开始，默认为0 | 页码 |
| size | Integer | 否 | 每页大小，默认为10 | 每页大小 |
| keyword | String | 否 | 关键字（生产单号、客户名称、品名等） | 关键字 |
| customerCode | String | 否 | 客户编码 | 客户编码 |
| orderDateStart | LocalDate | 否 | 订单日期开始 | 订单日期开始 |
| orderDateEnd | LocalDate | 否 | 订单日期结束 | 订单日期结束 |
| deliveryDateStart | LocalDate | 否 | 交期开始 | 交期开始 |
| deliveryDateEnd | LocalDate | 否 | 交期结束 | 交期结束 |
| paperType | String | 否 | 纸质 | 纸质 |
| corrugationType | String | 否 | 楞别 | 楞别 |
| sortField | String | 否 | 排序字段 | 排序字段 |
| sortDirection | String | 否 | 排序方向（asc/desc） | 排序方向 |

### 4.2 销售订单明细响应

| 字段名 | 类型 | 描述 | 中文名 |
| --- | --- | --- | --- |
| productionOrderNo | String | 生产单号 | 生产单号 |
| paperType | String | 纸质 | 纸质 |
| paperBoardCategory | String | 纸板类别 | 纸板类别 |
| corrugationType | String | 楞别 | 楞别 |
| width | BigDecimal | 纸度 | 纸度 |
| length | BigDecimal | 纸长 | 纸长 |
| creasingSize | String | 压线尺寸(纸度) | 压线尺寸(纸度) |
| creasingMethod | String | 压线方式 | 压线方式 |
| customerName | String | 客户名称 | 客户名称 |
| customerOrderNo | String | 客户订单号 | 客户订单号 |
| customerProductCode | String | 客方货号 | 客方货号 |
| productName | String | 品名 | 品名 |
| processRequirements | String | 工艺要求 | 工艺要求 |
| boxType | String | 盒式 | 盒式 |
| specification | String | 产品规格(长×宽×高 单位) | 产品规格 |
| productionSpecification | String | 生产规格(生产长×生产宽×生产高 单位) | 生产规格 |
| sizeUnit | String | 尺寸单位 | 尺寸单位 |
| orderPaperType | String | 订单纸质 | 订单纸质 |
| mouldOpenCount | Integer | 模开数 | 模开数 |
| quantity | Integer | 订单数 | 订单数 |
| deliveryDate | LocalDate | 订单交期 | 订单交期 |
| salesOrderDeliveryDate | LocalDate | 销售单交期 | 销售单交期 |
| remark | String | 备注 | 备注 |
| orderId | String | 销售订单ID | 销售订单ID |
| orderItemId | String | 销售订单明细ID | 销售订单明细ID |
| purchasedQuantity | Integer | 已采购数量 | 已采购数量 |
| purchasedBoardCount | Integer | 已采购数量（与purchasedQuantity相同，用于前端兼容） | 已采购数量 |
| unpurchasedQuantity | Integer | 未采购数量（动态计算：boardCount - purchasedQuantity） | 未采购数量 |
| purchaseStatus | String | 采购状态（NOT_PURCHASED: 未采购, PARTIALLY_PURCHASED: 部分采购, FULLY_PURCHASED: 完全采购） | 采购状态 |
| orderNo | String | 销售单号 | 销售单号 |
| customerCode | String | 客户编码 | 客户编码 |
| orderDate | LocalDate | 订单日期 | 订单日期 |
| productionRemark | String | 生产备注 | 生产备注 |
| spareQuantity | Integer | 备品数 | 备品数 |
| paperQuality | String | 纸质 | 纸质 |
| paperWidth | BigDecimal | 纸度 | 纸度 |
| paperLength | BigDecimal | 纸长 | 纸长 |
| boardCount | Integer | 纸板数 | 纸板数 |

### 4.3 分页响应

| 字段名 | 类型 | 描述 | 中文名 |
| --- | --- | --- | --- |
| content | List<T> | 数据列表 | 数据列表 |
| totalElements | long | 总记录数 | 总记录数 |
| totalPages | int | 总页数 | 总页数 |
| page | int | 当前页码 | 当前页码 |
| size | int | 每页大小 | 每页大小 |
| first | boolean | 是否为第一页 | 是否为第一页 |
| last | boolean | 是否为最后一页 | 是否为最后一页 |
| empty | boolean | 是否为空 | 是否为空 |

### 4.4 API统一响应

| 字段名 | 类型 | 描述 | 中文名 |
| --- | --- | --- | --- |
| success | boolean | 是否成功 | 是否成功 |
| code | String | 错误码 | 错误码 |
| message | String | 错误消息 | 错误消息 |
| data | T | 数据 | 数据 |

## 5. 字段映射说明

### 5.1 销售订单明细到采购订单明细的字段映射

在创建采购订单时引用销售订单明细，系统会自动将销售订单明细的字段映射到采购订单明细的字段，具体映射关系如下：

| 销售订单明细字段 | 采购订单明细字段 | 说明 |
| --- | --- | --- |
| orderItemId | sourceSalesOrderItemId | 销售订单明细ID，用于关联销售订单明细 |
| paperType | paperQuality | 纸质 |
| corrugationType | corrugationType | 楞别 |
| width | paperWidth | 纸度 |
| length | paperLength | 纸长 |
| quantity | quantity | 数量 |
| creasingSize | creasingSize | 压线尺寸(纸度) |
| creasingMethod | creasingMethod | 压线方式 |
| remark | remarks | 备注 |
| deliveryDate | deliveryDate | 交期 |
| productionOrderNo | productionOrderNo | 生产单号 |
| orderNo | salesOrderNo | 销售单号 |
| customerCode | customerCode | 客户编码 |
| productionRemark | productionRemark | 生产备注 |
| spareQuantity | spareQuantity | 备品数 |
| paperQuality | paperQuality | 纸质（从材料表获取） |
| paperWidth | paperWidth | 纸度（从材料表获取） |
| paperLength | paperLength | 纸长（从材料表获取） |
| boardCount | boardCount | 纸板数 |

### 5.2 采购状态更新逻辑

当创建采购订单时引用销售订单明细，系统会自动更新销售订单明细的采购状态和已采购数量：

1. 创建采购订单明细时，系统会将采购数量累加到销售订单明细的已采购数量中
2. 根据已采购数量与订单数量的比较，更新销售订单明细的采购状态：
   - 已采购数量 = 0：NOT_PURCHASED（未采购）
   - 0 < 已采购数量 < 订单数量：PARTIALLY_PURCHASED（部分采购）
   - 已采购数量 >= 订单数量：FULLY_PURCHASED（完全采购）
3. 只有未采购或部分采购的销售订单明细才会在查询接口中返回，完全采购的销售订单明细不会返回
