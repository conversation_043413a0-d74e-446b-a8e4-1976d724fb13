package com.czerp.erpbackend.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Excel模板工具类
 */
@Slf4j
public class ExcelTemplateUtil {

    /**
     * 创建Excel导入模板
     *
     * @param sheetName 工作表名称
     * @param headers 表头
     * @param requiredColumns 必填列索引
     * @return Excel文件字节数组
     * @throws IOException 如果创建失败
     */
    public static byte[] createImportTemplate(String sheetName, List<String> headers, List<Integer> requiredColumns) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(sheetName);

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);

            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 创建必填列样式
            CellStyle requiredStyle = workbook.createCellStyle();
            requiredStyle.cloneStyleFrom(headerStyle);

            Font requiredFont = workbook.createFont();
            requiredFont.setBold(true);
            requiredFont.setColor(IndexedColors.RED.getIndex());
            requiredStyle.setFont(requiredFont);

            // 创建表头行
            Row headerRow = sheet.createRow(0);

            // 设置表头
            for (int i = 0; i < headers.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers.get(i));

                // 如果是必填列，使用必填样式
                if (requiredColumns != null && requiredColumns.contains(i)) {
                    cell.setCellStyle(requiredStyle);
                } else {
                    cell.setCellStyle(headerStyle);
                }

                // 设置列宽
                sheet.setColumnWidth(i, 20 * 256);
            }

            // 冻结表头
            sheet.createFreezePane(0, 1);

            // 写入到字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    /**
     * 创建带描述的Excel导入模板
     *
     * @param sheetName 工作表名称
     * @param headers 表头数组
     * @param descriptions 描述数组
     * @return Excel文件字节数组
     * @throws IOException 如果创建失败
     */
    public static byte[] createTemplate(String sheetName, String[] headers, String[] descriptions) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(sheetName);

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);

            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 创建描述样式
            CellStyle descStyle = workbook.createCellStyle();
            descStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
            descStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            descStyle.setBorderBottom(BorderStyle.THIN);
            descStyle.setBorderLeft(BorderStyle.THIN);
            descStyle.setBorderRight(BorderStyle.THIN);
            descStyle.setBorderTop(BorderStyle.THIN);
            descStyle.setAlignment(HorizontalAlignment.LEFT);
            descStyle.setWrapText(true);

            // 创建表头行
            Row headerRow = sheet.createRow(0);

            // 创建描述行
            Row descRow = sheet.createRow(1);
            descRow.setHeight((short) (descRow.getHeight() * 2)); // 设置描述行高度为默认的2倍

            // 设置表头和描述
            for (int i = 0; i < headers.length; i++) {
                Cell headerCell = headerRow.createCell(i);
                headerCell.setCellValue(headers[i]);
                headerCell.setCellStyle(headerStyle);

                Cell descCell = descRow.createCell(i);
                if (i < descriptions.length) {
                    descCell.setCellValue(descriptions[i]);
                } else {
                    descCell.setCellValue("");
                }
                descCell.setCellStyle(descStyle);

                // 设置列宽
                sheet.setColumnWidth(i, 20 * 256);
            }

            // 冻结表头和描述行
            sheet.createFreezePane(0, 2);

            // 写入到字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }
}
