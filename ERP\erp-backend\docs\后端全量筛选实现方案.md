根据我们的讨论和你的业务决策（方向3中不再需要动态计数），我来为你整理一份MD格式的落地顺序和（元数据 + 通用方法）的开发文档。

# 销售订单筛选选项优化方案落地

## 一、 最终确定的优化策略

经过深入分析和讨论，我们确定采用以下组合策略来优化销售订单的筛选选项功能：

1.  **核心方案：元数据 + 通用方法 (方向 1)**
    *   目的：解决代码冗余、提高可维护性、简化新增筛选字段的流程。
    *   实现：通过定义字段元数据（枚举）和实现一个通用的选项获取方法，来动态处理不同字段的筛选选项请求。
2.  **辅助方案：静态枚举字段直接走字典服务 (方向 3)**
    *   目的：对于值固定或来自业务字典的字段，直接从字典服务获取选项，大幅提升性能并减轻数据库压力。
    *   前提：业务确认此类字段**不再需要动态计数** (即选项旁边不再显示其在当前筛选结果中的数量)。

## 二、 落地顺序

### 第一阶段：实施核心方案（元数据 + 通用方法）并整合静态字段处理

**目标**：重构现有 `getFilterOptions` 接口，消除重复的 `getXxxOptions` 方法，并为静态字段直接从字典服务获取选项。

**步骤**：

1.  **定义字段元数据枚举 `FilterField`**:
    *   创建 `com.example.sales.filter.FilterField` 枚举。
    *   枚举常量应包含：前端请求的字段名 (`fieldName`)、JPA Metamodel 路径 (`path`)、字段来源 (`joinSource` - ROOT/ITEM)、以及一个新属性标记是否为静态字典字段 (`isStaticDictionary`)。
    *   示例：
        ```java
        // package com.example.sales.filter;
        // import javax.persistence.metamodel.SingularAttribute;
        // import com.example.sales.entity.SalesOrder_; // 假设JPA Metamodel类
        // import com.example.sales.entity.SalesOrderItem_; // 假设JPA Metamodel类

        public enum FilterField {
            // 动态查询数据库的字段
            PRODUCTION_ORDER_NO("productionOrderNo", SalesOrderItem_.productionOrderNo, JoinSource.ITEM, false, "productionOrderNo"),
            CUSTOMER_NAME("customerName", SalesOrder_.customerName, JoinSource.ROOT, false, "customerName"), // 假设 customerName 在 SalesOrder
            PRODUCT_NAME("productName", SalesOrderItem_.productName, JoinSource.ITEM, false, "productName"),
            SALES_PERSON("salesPerson", SalesOrder_.salesPersonName, JoinSource.ROOT, false, "salesPersonName"), // 假设 salesPerson 在 SalesOrder
            ORDER_NO("orderNo", SalesOrder_.orderNo, JoinSource.ROOT, false, "orderNo"), // 示例：假设orderNo在Root
            CUSTOMER_ORDER_NO("customerOrderNo", SalesOrderItem_.customerOrderNo, JoinSource.ITEM, false, "customerOrderNo"),
            CUSTOMER_PRODUCT_CODE("customerProductCode", SalesOrderItem_.customerProductCode, JoinSource.ITEM, false, "customerProductCode"),

            // 静态字典字段 (假设这些字段不再需要动态计数)
            BOX_TYPE("boxType", SalesOrderItem_.boxType, JoinSource.ITEM, true, "dict_box_type"), // 最后一个参数是字典编码
            PAPER_TYPE("paperType", SalesOrderItem_.paperType, JoinSource.ITEM, true, "dict_paper_type"),
            CORRUGATION_TYPE("corrugationType", SalesOrderItem_.corrugationType, JoinSource.ITEM, true, "dict_corrugation_type");
            // ... 其他字段

            private final String fieldName; // 前端请求及 addOtherFilterConditions 中使用的名称
            private final SingularAttribute<?, ?> attributePath; // JPA Metamodel 路径
            private final JoinSource joinSource;    // 字段位于 ROOT 还是 ITEM
            private final boolean isStaticDictionary; // 是否为静态字典字段
            private final String dictionaryCodeOrEntityFieldName; // 如果是静态字典，则为字典编码；否则为实体字段名(用于动态查询)

            FilterField(String fieldName, SingularAttribute<?, ?> attributePath, JoinSource joinSource, boolean isStaticDictionary, String dictionaryCodeOrEntityFieldName) {
                this.fieldName = fieldName;
                this.attributePath = attributePath;
                this.joinSource = joinSource;
                this.isStaticDictionary = isStaticDictionary;
                this.dictionaryCodeOrEntityFieldName = dictionaryCodeOrEntityFieldName;
            }

            public String getFieldName() { return fieldName; }
            public SingularAttribute<?, ?> getAttributePath() { return attributePath; }
            public JoinSource getJoinSource() { return joinSource; }
            public boolean isStaticDictionary() { return isStaticDictionary; }
            public String getDictionaryCodeOrEntityFieldName() { return dictionaryCodeOrEntityFieldName; }

            public static FilterField fromFieldName(String fieldName) {
                for (FilterField ff : values()) {
                    if (ff.fieldName.equalsIgnoreCase(fieldName)) {
                        return ff;
                    }
                }
                throw new IllegalArgumentException("Unsupported filter field: " + fieldName);
            }
        }

        enum JoinSource {
            ROOT, ITEM
        }
        ```

2.  **实现通用的 `getFilterOptions` 方法**:
    *   修改 `SalesOrderFilterService` (或类似服务) 中的 `getFilterOptions` 方法。
    *   方法内部首先通过 `FilterField.fromFieldName(fieldName)` 获取字段元数据。
    *   **判断 `isStaticDictionary`**：
        *   如果为 `true`：调用字典服务 (假设已存在 `DictionaryService`) 根据 `dictionaryCodeOrEntityFieldName` (作为字典编码) 获取选项列表。将字典返回的每个值包装成 `FilterOptionDTO` (此时 `count` 可以为 0、-1 或不设置，因为业务已确认不需要动态计数)。
            ```java
            // 示例字典服务调用
            // List<String> options = dictionaryService.getOptionsByCode(meta.getDictionaryCodeOrEntityFieldName());
            // return options.stream()
            //               .map(opt -> FilterOptionDTO.of(opt, 0L)) // Count设为0或特定值
            //               .collect(Collectors.toList());
            ```
        *   如果为 `false`：执行动态数据库查询逻辑（如下方开发文档所述）。

3.  **实现动态数据库查询逻辑 (针对 `isStaticDictionary = false`)**:
    *   参考下面提供的“开发文档 - （元数据 + 通用方法）”。
    *   确保 `addOtherFilterConditions` 方法的 `excludeField` 参数使用的是 `meta.getFieldName()`。
    *   查询的目标字段路径通过 `meta.getAttributePath()` 和 `meta.getJoinSource()` 动态获取。
    *   **注意**：由于静态字段不再需要动态计数，所以 `FilterOptionDTO.of(value, count)` 中的 `count` 对于动态查询的字段仍然是需要的，而对于静态字段可以设置为一个固定值（如0或null，具体看前端如何处理）。

4.  **调整 `addOtherFilterConditions` 方法**:
    *   确保此方法能正确处理 `excludeField` 逻辑，即排除当前正在获取选项的字段自身的过滤条件。
    *   确保所有在 `SalesOrderQueryParamDTO` 中定义的筛选参数都已在该方法中正确应用。

5.  **移除旧的 `getXxxOptions` 方法和主方法中的 `switch` 语句**。

6.  **单元测试与集成测试**:
    *   测试动态查询字段的选项获取和级联筛选。
    *   测试静态字典字段的选项获取。
    *   测试边界条件，如无数据、搜索文本等。


---

## 开发文档 - （元数据 + 通用方法）

**接口**: `/sales/orders/query/filter-options`

**服务类**: `SalesOrderFilterService` (示例)

**核心方法**: `public List<FilterOptionDTO> getFilterOptions(String fieldName, String searchText, SalesOrderQueryParamDTO currentFilters)`

### 1. 字段元数据定义 (`FilterField.java`)

```java
package com.example.sales.filter;

import com.example.sales.entity.SalesOrder_; // 替换为你的 SalesOrder Metamodel
import com.example.sales.entity.SalesOrderItem_; // 替换为你的 SalesOrderItem Metamodel
import javax.persistence.metamodel.SingularAttribute;

// @Getter // Lombok (optional)
public enum FilterField {
    // 动态查询数据库的字段 (isStaticDictionary = false)
    // fieldName: 前端请求的字段名, attributePath: JPA Metamodel 属性, joinSource: 字段来源, 
    // isStaticDictionary: 是否静态字典, dictionaryCodeOrEntityFieldName: 实体字段名(用于动态查询)

    PRODUCTION_ORDER_NO("productionOrderNo", SalesOrderItem_.productionOrderNo, JoinSource.ITEM, false, "productionOrderNo"),
    CUSTOMER_NAME("customerName", SalesOrder_.customerName, JoinSource.ROOT, false, "customerName"),
    PRODUCT_NAME("productName", SalesOrderItem_.productName, JoinSource.ITEM, false, "productName"),
    SALES_PERSON("salesPerson", SalesOrder_.salesPersonName, JoinSource.ROOT, false, "salesPersonName"), // 假设实体字段为 salesPersonName
    ORDER_NO("orderNo", SalesOrder_.orderNo, JoinSource.ROOT, false, "orderNo"),
    CUSTOMER_ORDER_NO("customerOrderNo", SalesOrderItem_.customerOrderNo, JoinSource.ITEM, false, "customerOrderNo"),
    CUSTOMER_PRODUCT_CODE("customerProductCode", SalesOrderItem_.customerProductCode, JoinSource.ITEM, false, "customerProductCode"),
    // ... 其他需要动态查询的文本或选择字段

    // 静态字典字段 (isStaticDictionary = true)
    // dictionaryCodeOrEntityFieldName: 字典服务中定义的字典编码
    BOX_TYPE("boxType", SalesOrderItem_.boxType, JoinSource.ITEM, true, "dict_box_type_code"), // 假设这是字典编码
    PAPER_TYPE("paperType", SalesOrderItem_.paperType, JoinSource.ITEM, true, "dict_paper_type_code"),
    CORRUGATION_TYPE("corrugationType", SalesOrderItem_.corrugationType, JoinSource.ITEM, true, "dict_corrugation_type_code");
    // ... 其他静态枚举类字段

    private final String fieldName;
    private final SingularAttribute<?, ?> attributePath;
    private final JoinSource joinSource;
    private final boolean isStaticDictionary;
    private final String dictionaryCodeOrEntityFieldName; // 对于动态查询字段，这通常就是fieldName或JPA实体属性名

    FilterField(String fieldName, SingularAttribute<?, ?> attributePath, JoinSource joinSource, boolean isStaticDictionary, String dictionaryCodeOrEntityFieldName) {
        this.fieldName = fieldName;
        this.attributePath = attributePath;
        this.joinSource = joinSource;
        this.isStaticDictionary = isStaticDictionary;
        this.dictionaryCodeOrEntityFieldName = dictionaryCodeOrEntityFieldName;
    }

    // Getters...
    public String getFieldName() { return fieldName; }
    public SingularAttribute<?, ?> getAttributePath() { return attributePath; }
    public JoinSource getJoinSource() { return joinSource; }
    public boolean isStaticDictionary() { return isStaticDictionary; }
    public String getDictionaryCodeOrEntityFieldName() { return dictionaryCodeOrEntityFieldName; }


    public static FilterField fromFieldName(String fieldName) {
        for (FilterField ff : values()) {
            // 建议忽略大小写匹配，增加健壮性
            if (ff.fieldName.equalsIgnoreCase(fieldName)) {
                return ff;
            }
        }
        // 可以抛出自定义异常或返回一个表示不支持的特殊值/Optional.empty()
        throw new IllegalArgumentException("Unsupported filter field or fieldName not configured: " + fieldName);
    }
}

// 辅助枚举，用于指明字段来源
// @Getter // Lombok (optional)
enum JoinSource {
    ROOT, // 字段在 SalesOrder 实体上
    ITEM  // 字段在 SalesOrderItem 实体上
}


注意:

确保 JPA Metamodel 类 (SalesOrder_, SalesOrderItem_) 已正确生成。

dictionaryCodeOrEntityFieldName 对于静态字段是字典编码，对于动态字段可以存其实体属性名（如果和 fieldName 不完全一致时需要区分）。

2. 通用选项获取方法实现
// import javax.persistence.EntityManager;
// import javax.persistence.Tuple;
// import javax.persistence.criteria.*;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;
// import org.springframework.util.StringUtils;
// import java.util.ArrayList;
// import java.util.List;
// import java.util.stream.Collectors;
// import com.example.sales.dto.FilterOptionDTO;
// import com.example.sales.dto.SalesOrderQueryParamDTO;
// import com.example.sales.entity.SalesOrder;
// import com.example.sales.entity.SalesOrderItem;
// import com.example.sales.filter.FilterField; // 确保导入
// import com.example.sales.filter.JoinSource;  // 确保导入
// import com.example.service.DictionaryService; // 假设的字典服务

@Service
public class SalesOrderFilterService {

    private final EntityManager entityManager;
    private final DictionaryService dictionaryService; // 注入字典服务

    // 构造函数注入
    public SalesOrderFilterService(EntityManager entityManager, DictionaryService dictionaryService) {
        this.entityManager = entityManager;
        this.dictionaryService = dictionaryService;
    }

    @Transactional(readOnly = true)
    public List<FilterOptionDTO> getFilterOptions(String fieldName, String searchText, SalesOrderQueryParamDTO currentFilters) {
        FilterField meta;
        try {
            meta = FilterField.fromFieldName(fieldName);
        } catch (IllegalArgumentException e) {
            // log.warn("Unsupported filter field requested: {}", fieldName, e); // 使用日志记录
            System.err.println("Unsupported filter field requested: " + fieldName + ". " + e.getMessage()); // 临时输出
            return List.of();
        }

        // 处理静态字典字段
        if (meta.isStaticDictionary()) {
            // log.debug("Fetching static options for field: {}", fieldName);
            List<String> staticOptions = dictionaryService.getOptionsByCode(meta.getDictionaryCodeOrEntityFieldName());
            return staticOptions.stream()
                    .filter(opt -> !StringUtils.hasText(searchText) || opt.toLowerCase().contains(searchText.toLowerCase())) // 可选：如果静态选项也需要searchText过滤
                    .map(opt -> FilterOptionDTO.of(opt, 0L)) // 业务确认不需要动态计数，count可为0或null
                    .collect(Collectors.toList());
        }

        // log.debug("Fetching dynamic options for field: {} with searchText: '{}'", fieldName, searchText);
        // 处理动态查询字段
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> query = cb.createTupleQuery();
        Root<SalesOrder> orderRoot = query.from(SalesOrder.class);
        // 始终进行INNER JOIN，因为addOtherFilterConditions可能依赖itemJoin，并且大多数动态字段在Item上
        Join<SalesOrder, SalesOrderItem> itemJoin = orderRoot.join("items", JoinType.INNER);

        Path<?> fieldPath;
        if (meta.getJoinSource() == JoinSource.ROOT) {
            fieldPath = orderRoot.get(meta.getAttributePath().getName()); // 使用getName()获取属性名字符串
        } else { // ITEM
            fieldPath = itemJoin.get(meta.getAttributePath().getName());
        }

        query.multiselect(
            fieldPath.alias("value"),
            cb.countDistinct(orderRoot.get("id")).alias("count") // 假设计数是订单数
            // 如果是行项目数: cb.count(itemJoin).alias("count") 或 cb.countDistinct(itemJoin.get("id")).alias("count")
        );

        List<Predicate> predicates = new ArrayList<>();

        // 添加搜索文本条件 (针对当前请求的字段)
        if (StringUtils.hasText(searchText)) {
            // 需要确保 fieldPath 是可以转为 String 的类型，或数据库支持隐式转换
            predicates.add(cb.like(cb.lower(fieldPath.as(String.class)), "%" + searchText.toLowerCase() + "%"));
        }

        // 添加其他已应用的筛选条件 (排除当前字段自身的筛选)
        addOtherFilterConditions(cb, orderRoot, itemJoin, currentFilters, predicates, meta.getFieldName());

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        query.groupBy(fieldPath)
             .orderBy(cb.desc(cb.countDistinct(orderRoot.get("id")))); // 保持与 multiselect 中的 count 一致

        List<Tuple> results = entityManager.createQuery(query)
                .setMaxResults(50) // 限制返回的选项数量
                .getResultList();

        return results.stream()
                // 确保值不为null且不为空字符串 (如果字段本身就是String类型)
                .filter(tuple -> tuple.get("value") != null &&
                                 (!(tuple.get("value") instanceof String) || StringUtils.hasText((String)tuple.get("value"))))
                .map(tuple -> {
                    Object val = tuple.get("value");
                    // 对于枚举或其他非字符串类型，可能需要特殊转换 toString()
                    String displayValue = (val instanceof String) ? (String) val : String.valueOf(val);
                    return FilterOptionDTO.of(
                        displayValue,
                        tuple.get("count", Long.class)
                    );
                })
                .collect(Collectors.toList());
    }

    /**
     * 辅助方法，用于添加其他已应用的筛选条件。
     * @param cb CriteriaBuilder
     * @param orderRoot Root<SalesOrder>
     * @param itemJoin Join<SalesOrder, SalesOrderItem>
     * @param currentFilters 当前已应用的筛选参数DTO
     * @param predicates Predicate列表，用于添加新的条件
     * @param excludeField 当前正在获取选项的字段名，应排除对此字段自身的筛选，以实现级联效果
     */
    private void addOtherFilterConditions(CriteriaBuilder cb, Root<SalesOrder> orderRoot, Join<SalesOrder, SalesOrderItem> itemJoin,
                                          SalesOrderQueryParamDTO currentFilters, List<Predicate> predicates, String excludeField) {
        // 示例：生产单号筛选
        // 注意：这里要用 currentFilters 中的 filter 字段名 与 excludeField 进行比较
        if (!"productionOrderNo".equals(excludeField) && StringUtils.hasText(currentFilters.getFilterProductionOrderNo())) {
            predicates.add(cb.like(itemJoin.get(SalesOrderItem_.productionOrderNo), "%" + currentFilters.getFilterProductionOrderNo() + "%"));
        }

        // 示例：客户名称筛选 (多选)
        // 注意：这里要用 "customerName" (FilterField中定义的fieldName) 与 excludeField 进行比较
        if (!"customerName".equals(excludeField) && currentFilters.getFilterCustomerNames() != null && !currentFilters.getFilterCustomerNames().isEmpty()) {
            // 假设 customerName 在 SalesOrder 实体上
            predicates.add(orderRoot.get(SalesOrder_.customerName).in(currentFilters.getFilterCustomerNames()));
        }
        
        // 示例: 产品名称筛选
        if (!"productName".equals(excludeField) && currentFilters.getFilterProductNames() != null && !currentFilters.getFilterProductNames().isEmpty()) {
            predicates.add(itemJoin.get(SalesOrderItem_.productName).in(currentFilters.getFilterProductNames()));
        }
        
        // ... 实现 SalesOrderQueryParamDTO 中所有筛选参数的应用逻辑
        // 对于每个参数，都需要判断 !itsOwnFieldName.equals(excludeField)

        // 数字范围筛选 (以数量为例)
        // 数字和日期范围通常不需要排除自身，因为它们不是“选择”类型，而是直接输入
        if (currentFilters.getFilterQuantityMin() != null) {
            predicates.add(cb.greaterThanOrEqualTo(itemJoin.get(SalesOrderItem_.quantity), currentFilters.getFilterQuantityMin()));
        }
        if (currentFilters.getFilterQuantityMax() != null) {
            predicates.add(cb.lessThanOrEqualTo(itemJoin.get(SalesOrderItem_.quantity), currentFilters.getFilterQuantityMax()));
        }

        // 日期范围筛选 (以订单日期为例)
        if (currentFilters.getFilterOrderDateStart() != null) {
            predicates.add(cb.greaterThanOrEqualTo(orderRoot.get(SalesOrder_.orderDate), currentFilters.getFilterOrderDateStart()));
        }
        if (currentFilters.getFilterOrderDateEnd() != null) {
            predicates.add(cb.lessThanOrEqualTo(orderRoot.get(SalesOrder_.orderDate), currentFilters.getFilterOrderDateEnd()));
        }
        // ... 其他所有筛选条件
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END
3. DTOs

FilterOptionDTO: 保持不变，用于返回选项。

// package com.example.sales.dto;
// import lombok.AllArgsConstructor;
// import lombok.Data;
// import lombok.NoArgsConstructor;

// @Data
// @NoArgsConstructor
// @AllArgsConstructor(staticName = "of")
public class FilterOptionDTO {
    private String value; // 选项的显示值/实际值
    private Long count;   // 该选项在当前筛选条件下的计数 (对于静态字典字段，此值可能为0或null)

    public static FilterOptionDTO of(String value, Long count) {
        FilterOptionDTO dto = new FilterOptionDTO();
        dto.value = value;
        dto.count = count;
        return dto;
    }
    // getters and setters
    public String getValue() { return value; }
    public void setValue(String value) { this.value = value; }
    public Long getCount() { return count; }
    public void setCount(Long count) { this.count = count; }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

SalesOrderQueryParamDTO: 保持不变，包含所有可能的筛选参数。

4. 字典服务接口 (示例)
// package com.example.service;
// import java.util.List;

public interface DictionaryService {
    /**
     * 根据字典编码获取字典选项列表。
     * @param dictionaryCode 字典编码 (对应 FilterField 中的 dictionaryCodeOrEntityFieldName)
     * @return 选项字符串列表，如果未找到则返回空列表。
     */
    List<String> getOptionsByCode(String dictionaryCode);
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

你需要实现这个 DictionaryService，使其能够从你的业务字典数据源（数据库表、配置文件、缓存等）中获取数据。

关键点和注意事项:

JPA Metamodel: 务必确保项目已启用并正确生成了JPA Metamodel类（如 SalesOrder_ 和 SalesOrderItem_），以便在 FilterField 枚举和查询中使用类型安全的属性路径。

addOtherFilterConditions 的 excludeField 逻辑: 这是实现级联筛选的关键。当为字段A获取选项时，查询中不能包含字段A自身的筛选条件，但需要包含所有其他已选字段的筛选条件。

字段路径获取: Path<?> fieldPath = meta.getJoinSource() == JoinSource.ITEM ? itemJoin.get(meta.getAttributePath().getName()) : orderRoot.get(meta.getAttributePath().getName()); 这里使用 meta.getAttributePath().getName() 来获取属性的字符串名称，传递给 get() 方法。

错误处理: FilterField.fromFieldName() 中增加了对无效 fieldName 的处理。

静态字段的搜索文本过滤: 在示例代码中，为静态字段从字典服务获取选项后，增加了一个可选的 searchText 过滤。如果你的字典服务本身支持按文本搜索，则更佳。

事务管理: @Transactional(readOnly = true) 适用于查询操作。

日志: 在关键路径添加适当的日志记录，便于调试和问题追踪。

dictionaryCodeOrEntityFieldName 的用途:

当 isStaticDictionary 为 true 时，它存储的是字典服务中定义的字典编码。

当 isStaticDictionary 为 false 时，它可以存储实际的实体字段名字符串，尽管在当前 getFilterOptions 实现中，我们主要通过 meta.getAttributePath().getName() 来获取实体字段名。但如果 FilterField 的 fieldName (前端名) 与后端实体属性名有差异，这个字段可以作为记录实体属性名的地方。不过，JPA Metamodel 的 attributePath.getName() 通常就能满足需求。

这个文档应该能为后端开发人员提供清晰的指引。