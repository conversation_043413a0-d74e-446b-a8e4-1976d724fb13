package com.czerp.erpbackend.system.service.impl;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.system.dto.PaperSizeMatchingRequest;
import com.czerp.erpbackend.system.dto.PaperSizeMatchingResponse;
import com.czerp.erpbackend.system.dto.PaperSizeSettingDTO;
import com.czerp.erpbackend.system.entity.PaperSizeSetting;
import com.czerp.erpbackend.system.mapper.PaperSizeSettingMapper;
import com.czerp.erpbackend.system.repository.PaperSizeSettingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaperSizeMatchingServiceImplTest {

    @Mock
    private PaperSizeSettingRepository paperSizeSettingRepository;

    @Mock
    private PaperSizeSettingMapper paperSizeSettingMapper;

    @InjectMocks
    private PaperSizeMatchingServiceImpl paperSizeMatchingService;

    private List<PaperSizeSetting> mockPaperSizes;

    @BeforeEach
    void setUp() {
        // 创建模拟的纸度设置数据
        mockPaperSizes = new ArrayList<>();

        PaperSizeSetting size1 = new PaperSizeSetting();
        size1.setId(1L);
        size1.setPaperSizeInch(new BigDecimal("29"));
        size1.setPaperSizeCm(new BigDecimal("73.66"));

        PaperSizeSetting size2 = new PaperSizeSetting();
        size2.setId(2L);
        size2.setPaperSizeInch(new BigDecimal("31"));
        size2.setPaperSizeCm(new BigDecimal("78.74"));

        PaperSizeSetting size3 = new PaperSizeSetting();
        size3.setId(3L);
        size3.setPaperSizeInch(new BigDecimal("33"));
        size3.setPaperSizeCm(new BigDecimal("83.82"));

        mockPaperSizes.addAll(Arrays.asList(size1, size2, size3));

        // 模拟 mapper 的 toDto 方法
        when(paperSizeSettingMapper.toDto(any())).thenAnswer(invocation -> {
            PaperSizeSetting setting = invocation.getArgument(0);
            PaperSizeSettingDTO dto = new PaperSizeSettingDTO();
            dto.setId(setting.getId());
            dto.setPaperSizeInch(setting.getPaperSizeInch());
            dto.setPaperSizeCm(setting.getPaperSizeCm());
            return dto;
        });
    }

    @Test
    void findBestPaperSize_WithValidInput_ShouldReturnBestMatch() {
        // 准备测试数据
        PaperSizeMatchingRequest request = new PaperSizeMatchingRequest();
        request.setWidth(new BigDecimal("20"));
        request.setHeight(new BigDecimal("15"));

        // 模拟仓库返回
        when(paperSizeSettingRepository.findByIsDeletedFalseOrderByPaperSizeInchAsc()).thenReturn(mockPaperSizes);

        // 执行测试
        PaperSizeMatchingResponse response = paperSizeMatchingService.findBestPaperSize(request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getBestPaperSize());
        assertEquals(1, response.getMaxBoxesProducible());
        assertNotNull(response.getWastePercentage());
        assertNotNull(response.getWastePercentageFormatted());
        assertEquals("成功找到最佳纸度", response.getMessage());
    }

    @Test
    void findBestPaperSize_WithEmptyPaperSizes_ShouldReturnErrorMessage() {
        // 准备测试数据
        PaperSizeMatchingRequest request = new PaperSizeMatchingRequest();
        request.setWidth(new BigDecimal("20"));
        request.setHeight(new BigDecimal("15"));

        // 模拟仓库返回空列表
        when(paperSizeSettingRepository.findByIsDeletedFalseOrderByPaperSizeInchAsc()).thenReturn(Collections.emptyList());

        // 执行测试
        PaperSizeMatchingResponse response = paperSizeMatchingService.findBestPaperSize(request);

        // 验证结果
        assertNotNull(response);
        assertNull(response.getBestPaperSize());
        assertEquals("无可用纸度数据", response.getMessage());
    }

    @Test
    void findBestPaperSize_WithTooLargeBoxSize_ShouldReturnErrorMessage() {
        // 准备测试数据 - 非常大的纸盒尺寸
        PaperSizeMatchingRequest request = new PaperSizeMatchingRequest();
        request.setWidth(new BigDecimal("100"));
        request.setHeight(new BigDecimal("100"));

        // 模拟仓库返回
        when(paperSizeSettingRepository.findByIsDeletedFalseOrderByPaperSizeInchAsc()).thenReturn(mockPaperSizes);

        // 执行测试
        PaperSizeMatchingResponse response = paperSizeMatchingService.findBestPaperSize(request);

        // 验证结果
        assertNotNull(response);
        assertNull(response.getBestPaperSize());
        assertEquals("未找到合适的纸度，所需尺寸过大", response.getMessage());
    }

    @Test
    void findBestPaperSize_WithInvalidInput_ShouldThrowException() {
        // 准备测试数据 - 无效的宽度
        PaperSizeMatchingRequest request = new PaperSizeMatchingRequest();
        request.setWidth(new BigDecimal("-1"));
        request.setHeight(new BigDecimal("15"));

        // 验证异常
        assertThrows(BusinessException.class, () -> paperSizeMatchingService.findBestPaperSize(request));

        // 准备测试数据 - 无效的高度
        request.setWidth(new BigDecimal("20"));
        request.setHeight(new BigDecimal("-1"));

        // 验证异常
        assertThrows(BusinessException.class, () -> paperSizeMatchingService.findBestPaperSize(request));
    }

    @Test
    void findBestPaperSize_WithSameWasteRate_ShouldPreferMoreBoxes() {
        // 准备测试数据
        PaperSizeMatchingRequest request = new PaperSizeMatchingRequest();
        request.setWidth(new BigDecimal("10"));
        request.setHeight(new BigDecimal("5"));

        // 创建特殊的模拟数据，使得两个纸度有相同的浪费率但可生产的盒子数量不同
        List<PaperSizeSetting> specialMockSizes = new ArrayList<>();

        PaperSizeSetting size1 = new PaperSizeSetting();
        size1.setId(1L);
        size1.setPaperSizeInch(new BigDecimal("30"));
        size1.setPaperSizeCm(new BigDecimal("76.2"));

        PaperSizeSetting size2 = new PaperSizeSetting();
        size2.setId(2L);
        size2.setPaperSizeInch(new BigDecimal("45"));
        size2.setPaperSizeCm(new BigDecimal("114.3"));

        specialMockSizes.addAll(Arrays.asList(size1, size2));

        // 模拟仓库返回
        when(paperSizeSettingRepository.findByIsDeletedFalseOrderByPaperSizeInchAsc()).thenReturn(specialMockSizes);

        // 执行测试
        PaperSizeMatchingResponse response = paperSizeMatchingService.findBestPaperSize(request);

        // 验证结果 - 应该选择能生产更多盒子的纸度
        assertNotNull(response);
        assertNotNull(response.getBestPaperSize());
        assertTrue(response.getMaxBoxesProducible() > 1);
        assertEquals("成功找到最佳纸度", response.getMessage());
    }
}
