package com.czerp.erpbackend.product.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 产品规格DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSpecDTO {
    
    /**
     * 规格ID
     */
    private String id;
    
    /**
     * 规格名称
     */
    private String name;
    
    /**
     * 规格编码
     */
    private String code;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
