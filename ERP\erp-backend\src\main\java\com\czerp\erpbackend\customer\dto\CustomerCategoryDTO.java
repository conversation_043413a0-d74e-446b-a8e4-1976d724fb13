package com.czerp.erpbackend.customer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 客户分类DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerCategoryDTO {
    
    /**
     * 分类ID
     */
    private String id;
    
    /**
     * 分类编码
     */
    private String categoryCode;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 状态(active-启用,inactive-停用)
     */
    private String status;
    
    /**
     * 新建客户时默认属于此分类
     */
    private Boolean isDefault;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
