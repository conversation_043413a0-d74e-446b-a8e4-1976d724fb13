package com.czerp.erpbackend.customer.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.common.util.SecurityUtils;
import com.czerp.erpbackend.customer.dto.CreateCustomerCategoryRequest;
import com.czerp.erpbackend.customer.dto.CustomerCategoryDTO;
import com.czerp.erpbackend.customer.dto.CustomerCategoryQueryRequest;
import com.czerp.erpbackend.customer.dto.UpdateCustomerCategoryRequest;
import com.czerp.erpbackend.customer.entity.CustomerCategory;
import com.czerp.erpbackend.customer.mapper.CustomerCategoryMapper;
import com.czerp.erpbackend.customer.repository.CustomerCategoryRepository;
import com.czerp.erpbackend.customer.repository.CustomerRepository;
import com.czerp.erpbackend.customer.service.CustomerCategoryService;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 客户分类服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerCategoryServiceImpl implements CustomerCategoryService {

    private final CustomerCategoryRepository categoryRepository;
    private final CustomerRepository customerRepository;
    private final CustomerCategoryMapper categoryMapper;

    /**
     * 分页查询客户分类列表
     * @param request 查询请求
     * @return 客户分类分页列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<CustomerCategoryDTO> findCategories(CustomerCategoryQueryRequest request) {
        log.debug("Finding categories with request: {}", request);

        // 构建排序
        Sort sort = Sort.by(Sort.Direction.ASC, "sortOrder");
        if (StringUtils.hasText(request.getSortField())) {
            Sort.Direction direction = Sort.Direction.ASC;
            if (request.getSortDirection() != null && request.getSortDirection().equalsIgnoreCase("desc")) {
                direction = Sort.Direction.DESC;
            }
            sort = Sort.by(direction, request.getSortField());
        }

        // 构建分页
        int pageIndex = Math.max(0, request.getPage() - 1); // 确保页码不小于0
        Pageable pageable = PageRequest.of(pageIndex, request.getSize(), sort);

        // 构建查询条件
        Specification<CustomerCategory> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 添加未删除条件
            predicates.add(cb.equal(root.get("isDeleted"), false));

            // 添加分类编码条件
            if (StringUtils.hasText(request.getCategoryCode())) {
                predicates.add(cb.like(root.get("categoryCode"), "%" + request.getCategoryCode() + "%"));
            }

            // 添加分类名称条件
            if (StringUtils.hasText(request.getCategoryName())) {
                predicates.add(cb.like(root.get("categoryName"), "%" + request.getCategoryName() + "%"));
            }

            // 添加状态条件
            if (StringUtils.hasText(request.getStatus())) {
                predicates.add(cb.equal(root.get("status"), request.getStatus()));
            }

            // 添加是否默认条件
            if (request.getIsDefault() != null) {
                predicates.add(cb.equal(root.get("isDefault"), request.getIsDefault()));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 查询数据
        Page<CustomerCategory> resultPage = categoryRepository.findAll(spec, pageable);

        // 转换为DTO
        List<CustomerCategoryDTO> content = resultPage.getContent().stream()
                .map(categoryMapper::toDto)
                .toList();

        return new PageResponse<>(
                content,
                resultPage.getTotalElements(),
                resultPage.getTotalPages(),
                request.getPage(),
                request.getSize()
        );
    }

    /**
     * 查询所有客户分类
     * @return 客户分类列表
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "customer_categories", key = "'all'")
    public List<CustomerCategoryDTO> findAllCategories() {
        log.debug("Finding all categories");

        // 使用新的查询方法，只返回未删除的记录
        List<CustomerCategory> categories = categoryRepository.findByIsDeletedFalseOrderBySortOrderAsc();

        return categoryMapper.toDtoList(categories);
    }

    /**
     * 根据ID查询客户分类
     * @param id 客户分类ID
     * @return 客户分类信息
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "customer_categories", key = "#id")
    public CustomerCategoryDTO findCategoryById(String id) {
        log.debug("Finding category by id: {}", id);

        CustomerCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new BusinessException("客户分类不存在"));

        return categoryMapper.toDto(category);
    }

    /**
     * 根据分类编码查询客户分类
     * @param categoryCode 分类编码
     * @return 客户分类信息
     */
    @Override
    @Transactional(readOnly = true)
    public CustomerCategoryDTO findCategoryByCode(String categoryCode) {
        log.debug("Finding category by code: {}", categoryCode);

        CustomerCategory category = categoryRepository.findByCategoryCode(categoryCode)
                .orElseThrow(() -> new BusinessException("客户分类不存在"));

        return categoryMapper.toDto(category);
    }

    /**
     * 创建客户分类
     * @param request 创建请求
     * @return 客户分类信息
     */
    @Override
    @Transactional
    @CacheEvict(value = "customer_categories", allEntries = true)
    public CustomerCategoryDTO createCategory(CreateCustomerCategoryRequest request) {
        log.debug("Creating category with request: {}", request);

        // 检查分类编码是否已存在
        if (categoryRepository.existsByCategoryCode(request.getCategoryCode())) {
            throw new BusinessException("分类编码已存在");
        }

        // 如果设置为默认分类，需要将其他默认分类取消
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            resetDefaultCategories();
        }

        // 创建分类
        CustomerCategory category = categoryMapper.toEntity(request);

        // 设置ID
        category.setId(UUID.randomUUID().toString());

        // 设置默认值
        if (category.getSortOrder() == null) {
            category.setSortOrder(999);
        }
        if (category.getStatus() == null) {
            category.setStatus("active");
        }
        if (category.getIsDefault() == null) {
            category.setIsDefault(false);
        }

        // 设置审计字段
        String currentUser = SecurityUtils.getCurrentUsername();
        category.setCreatedBy(currentUser);
        category.setCreatedTime(LocalDateTime.now());

        // 保存分类
        category = categoryRepository.save(category);

        return categoryMapper.toDto(category);
    }

    /**
     * 更新客户分类
     * @param id 客户分类ID
     * @param request 更新请求
     * @return 客户分类信息
     */
    @Override
    @Transactional
    @CacheEvict(value = "customer_categories", allEntries = true)
    public CustomerCategoryDTO updateCategory(String id, UpdateCustomerCategoryRequest request) {
        log.debug("Updating category with id: {} and request: {}", id, request);

        // 查询分类
        CustomerCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new BusinessException("客户分类不存在"));

        // 如果设置为默认分类，需要将其他默认分类取消
        if (Boolean.TRUE.equals(request.getIsDefault()) && !Boolean.TRUE.equals(category.getIsDefault())) {
            resetDefaultCategories();
        }

        // 更新分类
        categoryMapper.updateEntity(request, category);

        // 设置审计字段
        String currentUser = SecurityUtils.getCurrentUsername();
        category.setUpdatedBy(currentUser);
        category.setUpdatedTime(LocalDateTime.now());

        // 保存分类
        category = categoryRepository.save(category);

        return categoryMapper.toDto(category);
    }

    /**
     * 删除客户分类
     * @param id 客户分类ID
     */
    @Override
    @Transactional
    @CacheEvict(value = "customer_categories", allEntries = true)
    public void deleteCategory(String id) {
        log.debug("Deleting category with id: {}", id);

        // 查询分类
        CustomerCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new BusinessException("客户分类不存在"));

        // 检查是否有客户使用该分类
        if (customerRepository.findByCategoryId(id, PageRequest.of(0, 1)).hasContent()) {
            throw new BusinessException("该分类下有客户，无法删除");
        }

        // 软删除
        category.setIsDeleted(true);
        category.setUpdatedBy(SecurityUtils.getCurrentUsername());
        category.setUpdatedTime(LocalDateTime.now());

        // 保存分类
        categoryRepository.save(category);
    }

    /**
     * 批量删除客户分类
     * @param ids 客户分类ID列表
     */
    @Override
    @Transactional
    @CacheEvict(value = "customer_categories", allEntries = true)
    public void batchDeleteCategories(List<String> ids) {
        log.debug("Batch deleting categories with ids: {}", ids);

        // 查询分类列表
        List<CustomerCategory> categories = categoryRepository.findAllById(ids);

        if (categories.isEmpty()) {
            return;
        }

        // 检查是否有客户使用这些分类
        for (CustomerCategory category : categories) {
            if (customerRepository.findByCategoryId(category.getId(), PageRequest.of(0, 1)).hasContent()) {
                throw new BusinessException("分类 [" + category.getCategoryName() + "] 下有客户，无法删除");
            }
        }

        // 软删除
        String currentUser = SecurityUtils.getCurrentUsername();
        LocalDateTime now = LocalDateTime.now();

        categories.forEach(category -> {
            category.setIsDeleted(true);
            category.setUpdatedBy(currentUser);
            category.setUpdatedTime(now);
        });

        // 保存分类
        categoryRepository.saveAll(categories);
    }

    /**
     * 获取默认客户分类
     * @return 默认客户分类
     */
    @Override
    @Transactional(readOnly = true)
    public CustomerCategoryDTO getDefaultCategory() {
        log.debug("Getting default category");

        List<CustomerCategory> defaultCategories = categoryRepository.findByIsDefaultTrue();

        if (defaultCategories.isEmpty()) {
            throw new BusinessException("未设置默认客户分类");
        }

        return categoryMapper.toDto(defaultCategories.get(0));
    }

    /**
     * 重置默认分类
     */
    private void resetDefaultCategories() {
        List<CustomerCategory> defaultCategories = categoryRepository.findByIsDefaultTrue();

        if (!defaultCategories.isEmpty()) {
            defaultCategories.forEach(category -> category.setIsDefault(false));
            categoryRepository.saveAll(defaultCategories);
        }
    }
}
