package com.czerp.erpbackend.material.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 纸质类别权限初始化器
 * 负责初始化纸质类别模块的权限并分配给管理员角色
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(10) // 在系统权限初始化之后执行
public class PaperTypePermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing paper type permissions...");

        // 初始化纸质类别模块权限
        initPaperTypePermissions();

        // 为管理员角色分配纸质类别模块权限
        assignPermissionsToAdminRole();

        log.info("Paper type permissions initialized successfully");
    }

    /**
     * 初始化纸质类别模块权限
     */
    private void initPaperTypePermissions() {
        log.info("Creating paper type permissions...");

        // 检查物料管理模块是否存在
        String materialModuleId = findPermissionIdByCode("material");
        if (materialModuleId == null) {
            log.info("Material module not found, creating it...");
            materialModuleId = createPermission("物料管理", "material", "menu", null, "/material", "material/index", "database", 50);
        }

        // 检查纸质类别模块是否已存在
        if (permissionRepository.existsByCode("material:paper-type")) {
            log.info("Paper type module already exists, skipping...");
            return;
        }

        // 创建纸质类别模块菜单
        String paperTypeModuleId = createPermission("纸质类别管理", "material:paper-type", "menu", materialModuleId, "/material/paper-types", "material/PaperType", "file-text", 20);

        // 创建纸质类别模块按钮权限
        createPermission("纸质类别列表", "material:paper-type:list", "button", paperTypeModuleId, null, null, null, 21);
        createPermission("纸质类别详情", "material:paper-type:read", "button", paperTypeModuleId, null, null, null, 22);
        createPermission("创建纸质类别", "material:paper-type:create", "button", paperTypeModuleId, null, null, null, 23);
        createPermission("更新纸质类别", "material:paper-type:update", "button", paperTypeModuleId, null, null, null, 24);
        createPermission("删除纸质类别", "material:paper-type:delete", "button", paperTypeModuleId, null, null, null, 25);

        log.info("Paper type permissions created successfully");
    }

    /**
     * 为管理员角色分配纸质类别模块权限
     */
    private void assignPermissionsToAdminRole() {
        log.info("Assigning paper type permissions to admin role...");

        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping permission assignment");
            return;
        }

        // 获取所有纸质类别相关权限
        List<String> permissionCodes = Arrays.asList(
                "material:paper-type",
                "material:paper-type:list",
                "material:paper-type:read",
                "material:paper-type:create",
                "material:paper-type:update",
                "material:paper-type:delete"
        );

        List<Permission> permissions = permissionRepository.findByCodeIn(permissionCodes);
        if (permissions.isEmpty()) {
            log.warn("No paper type permissions found, skipping assignment");
            return;
        }

        // 为管理员角色分配权限
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : permissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString());
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermission.setCreateBy("system");
                rolePermission.setCreateTime(LocalDateTime.now());
                rolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }

        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} permissions to admin role", rolePermissions.size());
        } else {
            log.info("All permissions already assigned to admin role, skipping");
        }
    }

    /**
     * 根据权限代码查找权限ID
     * @param code 权限代码
     * @return 权限ID
     */
    private String findPermissionIdByCode(String code) {
        return permissionRepository.findByCode(code)
                .map(Permission::getId)
                .orElse(null);
    }

    /**
     * 创建权限
     * @param name 权限名称
     * @param code 权限代码
     * @param type 权限类型
     * @param parentId 父权限ID
     * @param path 路径
     * @param component 组件
     * @param icon 图标
     * @param sort 排序
     * @return 权限ID
     */
    private String createPermission(String name, String code, String type, String parentId, String path, String component, String icon, Integer sort) {
        Permission permission = new Permission();
        permission.setId(UUID.randomUUID().toString());
        permission.setName(name);
        permission.setCode(code);
        permission.setType(type);
        permission.setParentId(parentId);
        permission.setPath(path);
        permission.setComponent(component);
        permission.setIcon(icon);
        permission.setSort(sort);
        permission.setStatus("active");
        permission.setCreatedBy("system");
        permission.setCreatedTime(LocalDateTime.now());
        permissionRepository.save(permission);
        log.info("Created permission: {}", code);
        return permission.getId();
    }
}
