package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.system.dto.CreateMeasurementUnitRequest;
import com.czerp.erpbackend.system.dto.MeasurementUnitDTO;
import com.czerp.erpbackend.system.dto.MeasurementUnitQueryRequest;
import com.czerp.erpbackend.system.dto.UpdateMeasurementUnitRequest;
import com.czerp.erpbackend.system.service.MeasurementUnitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 计量单位控制器
 */
@RestController
@RequestMapping("/measurement-units")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Measurement Unit Management", description = "计量单位管理相关接口")
public class MeasurementUnitController {

    private final MeasurementUnitService measurementUnitService;

    @GetMapping
    @Operation(summary = "获取计量单位列表", description = "分页查询计量单位列表，支持多条件筛选")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:list')")
    public ResponseEntity<ApiResponse<PageResponse<MeasurementUnitDTO>>> getMeasurementUnits(MeasurementUnitQueryRequest request) {
        log.debug("Getting measurement units with request: {}", request);
        PageResponse<MeasurementUnitDTO> units = measurementUnitService.findMeasurementUnits(request);
        return ResponseEntity.ok(ApiResponse.success(units));
    }

    @GetMapping("/all")
    @Operation(summary = "获取所有计量单位", description = "获取所有计量单位列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:list')")
    public ResponseEntity<ApiResponse<List<MeasurementUnitDTO>>> getAllMeasurementUnits() {
        log.debug("Getting all measurement units");
        List<MeasurementUnitDTO> units = measurementUnitService.findAllMeasurementUnits();
        return ResponseEntity.ok(ApiResponse.success(units));
    }

    @GetMapping("/active")
    @Operation(summary = "获取所有启用的计量单位", description = "获取所有启用的计量单位列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:list')")
    public ResponseEntity<ApiResponse<List<MeasurementUnitDTO>>> getAllActiveMeasurementUnits() {
        log.debug("Getting all active measurement units");
        List<MeasurementUnitDTO> units = measurementUnitService.findAllActiveMeasurementUnits();
        return ResponseEntity.ok(ApiResponse.success(units));
    }

    @GetMapping("/dimension")
    @Operation(summary = "获取所有尺寸单位", description = "获取所有尺寸单位列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:list')")
    public ResponseEntity<ApiResponse<List<MeasurementUnitDTO>>> getAllDimensionUnits() {
        log.debug("Getting all dimension units");
        List<MeasurementUnitDTO> units = measurementUnitService.findAllDimensionUnits();
        return ResponseEntity.ok(ApiResponse.success(units));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取计量单位详情", description = "根据ID获取计量单位详细信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:read')")
    public ResponseEntity<ApiResponse<MeasurementUnitDTO>> getMeasurementUnit(@PathVariable Long id) {
        log.debug("Getting measurement unit with id: {}", id);
        MeasurementUnitDTO unit = measurementUnitService.findMeasurementUnitById(id);
        return ResponseEntity.ok(ApiResponse.success(unit));
    }

    @PostMapping
    @Operation(summary = "创建计量单位", description = "创建新计量单位")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:create')")
    public ResponseEntity<ApiResponse<MeasurementUnitDTO>> createMeasurementUnit(@Valid @RequestBody CreateMeasurementUnitRequest request) {
        log.debug("Creating measurement unit with request: {}", request);
        MeasurementUnitDTO unit = measurementUnitService.createMeasurementUnit(request);
        return ResponseEntity.ok(ApiResponse.success(unit));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新计量单位", description = "更新计量单位信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:update')")
    public ResponseEntity<ApiResponse<MeasurementUnitDTO>> updateMeasurementUnit(
            @PathVariable Long id,
            @Valid @RequestBody UpdateMeasurementUnitRequest request) {
        log.debug("Updating measurement unit with id: {} and request: {}", id, request);
        MeasurementUnitDTO unit = measurementUnitService.updateMeasurementUnit(id, request);
        return ResponseEntity.ok(ApiResponse.success(unit));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除计量单位", description = "根据ID删除计量单位")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteMeasurementUnit(@PathVariable Long id) {
        log.debug("Deleting measurement unit with id: {}", id);
        measurementUnitService.deleteMeasurementUnit(id);
        return ResponseEntity.ok(ApiResponse.success());
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除计量单位", description = "批量删除计量单位")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:delete')")
    public ResponseEntity<ApiResponse<Void>> batchDeleteMeasurementUnits(@RequestBody List<Long> ids) {
        log.debug("Batch deleting measurement units with ids: {}", ids);
        measurementUnitService.batchDeleteMeasurementUnits(ids);
        return ResponseEntity.ok(ApiResponse.success());
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "切换计量单位状态", description = "启用或禁用计量单位")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:update')")
    public ResponseEntity<ApiResponse<MeasurementUnitDTO>> toggleMeasurementUnitStatus(
            @PathVariable Long id,
            @RequestParam boolean isActive) {
        log.debug("Toggling measurement unit status with id: {} and isActive: {}", id, isActive);
        MeasurementUnitDTO unit = measurementUnitService.toggleMeasurementUnitStatus(id, isActive);
        return ResponseEntity.ok(ApiResponse.success(unit));
    }

    @PutMapping("/{id}/default-for-new-material")
    @Operation(summary = "设置为默认物料单位", description = "设置计量单位为默认物料单位")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:update')")
    public ResponseEntity<ApiResponse<MeasurementUnitDTO>> setAsDefaultForNewMaterial(@PathVariable Long id) {
        log.debug("Setting measurement unit as default for new material with id: {}", id);
        MeasurementUnitDTO unit = measurementUnitService.setAsDefaultForNewMaterial(id);
        return ResponseEntity.ok(ApiResponse.success(unit));
    }

    @PutMapping("/{id}/default-dimension-unit")
    @Operation(summary = "设置为默认纸箱尺寸单位", description = "设置计量单位为默认纸箱尺寸单位")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:update')")
    public ResponseEntity<ApiResponse<MeasurementUnitDTO>> setAsDefaultDimensionUnit(@PathVariable Long id) {
        log.debug("Setting measurement unit as default dimension unit with id: {}", id);
        MeasurementUnitDTO unit = measurementUnitService.setAsDefaultDimensionUnit(id);
        return ResponseEntity.ok(ApiResponse.success(unit));
    }

    @PutMapping("/{id}/default-thickness-unit")
    @Operation(summary = "设置为默认纸度单位", description = "设置计量单位为默认纸度单位")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:update')")
    public ResponseEntity<ApiResponse<MeasurementUnitDTO>> setAsDefaultThicknessUnit(@PathVariable Long id) {
        log.debug("Setting measurement unit as default thickness unit with id: {}", id);
        MeasurementUnitDTO unit = measurementUnitService.setAsDefaultThicknessUnit(id);
        return ResponseEntity.ok(ApiResponse.success(unit));
    }

    @PutMapping("/{id}/default-length-unit")
    @Operation(summary = "设置为默认纸长单位", description = "设置计量单位为默认纸长单位")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:measurement-unit:update')")
    public ResponseEntity<ApiResponse<MeasurementUnitDTO>> setAsDefaultLengthUnit(@PathVariable Long id) {
        log.debug("Setting measurement unit as default length unit with id: {}", id);
        MeasurementUnitDTO unit = measurementUnitService.setAsDefaultLengthUnit(id);
        return ResponseEntity.ok(ApiResponse.success(unit));
    }
}
