package com.czerp.erpbackend.production.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.production.dto.CreateProcessRequest;
import com.czerp.erpbackend.production.dto.ProcessDTO;
import com.czerp.erpbackend.production.dto.ProcessQueryRequest;
import com.czerp.erpbackend.production.dto.UpdateProcessRequest;

import java.util.List;

/**
 * 工序服务接口
 */
public interface ProcessService {
    
    /**
     * 分页查询工序列表
     * @param request 查询请求
     * @return 工序分页列表
     */
    PageResponse<ProcessDTO> findProcesses(ProcessQueryRequest request);
    
    /**
     * 查询所有工序
     * @return 工序列表
     */
    List<ProcessDTO> findAllProcesses();
    
    /**
     * 根据ID查询工序
     * @param id 工序ID
     * @return 工序信息
     */
    ProcessDTO findProcessById(Long id);
    
    /**
     * 创建工序
     * @param request 创建请求
     * @return 工序信息
     */
    ProcessDTO createProcess(CreateProcessRequest request);
    
    /**
     * 更新工序
     * @param id 工序ID
     * @param request 更新请求
     * @return 工序信息
     */
    ProcessDTO updateProcess(Long id, UpdateProcessRequest request);
    
    /**
     * 删除工序
     * @param id 工序ID
     */
    void deleteProcess(Long id);
    
    /**
     * 批量删除工序
     * @param ids 工序ID列表
     */
    void batchDeleteProcesses(List<Long> ids);
}
