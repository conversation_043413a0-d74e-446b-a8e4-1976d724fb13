package com.czerp.erpbackend.purchase.dto;

import com.czerp.erpbackend.common.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 用于采购订单引用销售订单明细的查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SalesOrderItemForPurchaseQueryRequest extends PageRequest {
    /**
     * 关键字（生产单号、客户名称、品名等）
     */
    private String keyword;
    
    /**
     * 客户编码
     */
    private String customerCode;
    
    /**
     * 订单日期开始
     */
    private LocalDate orderDateStart;
    
    /**
     * 订单日期结束
     */
    private LocalDate orderDateEnd;
    
    /**
     * 交期开始
     */
    private LocalDate deliveryDateStart;
    
    /**
     * 交期结束
     */
    private LocalDate deliveryDateEnd;
    
    /**
     * 纸质
     */
    private String paperType;
    
    /**
     * 楞别
     */
    private String corrugationType;
}
