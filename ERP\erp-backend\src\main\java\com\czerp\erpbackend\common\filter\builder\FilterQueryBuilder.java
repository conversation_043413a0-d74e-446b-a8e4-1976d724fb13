package com.czerp.erpbackend.common.filter.builder;

import com.czerp.erpbackend.common.filter.metadata.FilterFieldMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 筛选查询构建器
 * 负责构建筛选查询的通用逻辑
 */
@Component
@Slf4j
public class FilterQueryBuilder {
    
    /**
     * 构建基础查询条件
     * @param cb CriteriaBuilder
     * @param root 根实体
     * @param metadata 字段元数据
     * @param searchText 搜索文本
     * @return 查询条件列表
     */
    public List<Predicate> buildBasicPredicates(CriteriaBuilder cb, Root<?> root, 
                                               FilterFieldMetadata metadata, String searchText) {
        List<Predicate> predicates = new ArrayList<>();
        
        // 获取字段路径
        Path<String> fieldPath = getFieldPath(root, metadata);
        
        // 添加搜索文本条件
        if (StringUtils.hasText(searchText) && metadata.getEnableSearch() != Boolean.FALSE) {
            predicates.add(cb.like(fieldPath, "%" + searchText + "%"));
        }
        
        // 过滤空值
        predicates.add(cb.isNotNull(fieldPath));
        predicates.add(cb.notEqual(fieldPath, ""));
        
        return predicates;
    }
    
    /**
     * 构建级联筛选条件
     * @param cb CriteriaBuilder
     * @param root 根实体
     * @param currentFilters 当前筛选条件
     * @param excludeField 排除的字段
     * @param moduleFieldsMap 模块字段映射
     * @return 查询条件列表
     */
    public List<Predicate> buildCascadePredicates(CriteriaBuilder cb, Root<?> root,
                                                 Map<String, Object> currentFilters,
                                                 String excludeField,
                                                 Map<String, FilterFieldMetadata> moduleFieldsMap) {
        List<Predicate> predicates = new ArrayList<>();
        
        if (currentFilters == null || currentFilters.isEmpty()) {
            return predicates;
        }
        
        for (Map.Entry<String, Object> entry : currentFilters.entrySet()) {
            String fieldName = entry.getKey();
            Object filterValue = entry.getValue();
            
            // 跳过当前字段和空值
            if (fieldName.equals(excludeField) || filterValue == null) {
                continue;
            }
            
            // 获取字段元数据
            FilterFieldMetadata metadata = moduleFieldsMap.get(fieldName);
            if (metadata == null) {
                log.warn("No metadata found for field: {}", fieldName);
                continue;
            }
            
            // 构建筛选条件
            Predicate predicate = buildFieldPredicate(cb, root, metadata, filterValue);
            if (predicate != null) {
                predicates.add(predicate);
            }
        }
        
        return predicates;
    }
    
    /**
     * 构建单个字段的筛选条件
     * @param cb CriteriaBuilder
     * @param root 根实体
     * @param metadata 字段元数据
     * @param filterValue 筛选值
     * @return 查询条件
     */
    private Predicate buildFieldPredicate(CriteriaBuilder cb, Root<?> root,
                                         FilterFieldMetadata metadata, Object filterValue) {
        try {
            Path<String> fieldPath = getFieldPath(root, metadata);
            
            if (filterValue instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> values = (List<String>) filterValue;
                if (!values.isEmpty()) {
                    return fieldPath.in(values);
                }
            } else if (filterValue instanceof String) {
                String value = (String) filterValue;
                if (StringUtils.hasText(value)) {
                    return cb.equal(fieldPath, value);
                }
            }
        } catch (Exception e) {
            log.warn("Failed to build predicate for field: {}, error: {}", 
                    metadata.getFieldName(), e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 获取字段路径
     * @param root 根实体
     * @param metadata 字段元数据
     * @return 字段路径
     */
    private Path<String> getFieldPath(Root<?> root, FilterFieldMetadata metadata) {
        String fieldPath = metadata.getFieldPath();
        if (fieldPath == null) {
            fieldPath = metadata.getEntityFieldName();
        }
        
        // 处理嵌套属性（如：orderItem.productName）
        if (fieldPath.contains(".")) {
            String[] parts = fieldPath.split("\\.");
            Path<String> path = root.get(parts[0]);
            for (int i = 1; i < parts.length; i++) {
                path = path.get(parts[i]);
            }
            return path;
        } else {
            return root.get(fieldPath);
        }
    }
    
    /**
     * 创建基础查询（不带级联筛选）
     * @param entityManager EntityManager
     * @param entityClass 实体类
     * @param metadata 字段元数据
     * @param searchText 搜索文本
     * @param maxResults 最大结果数
     * @return 查询结果列表
     */
    public List<String> executeBasicQuery(EntityManager entityManager, Class<?> entityClass,
                                         FilterFieldMetadata metadata, String searchText,
                                         Integer maxResults) {
        return executeQueryWithCascade(entityManager, entityClass, metadata, searchText,
                                     maxResults, null, null, null);
    }

    /**
     * 创建带级联筛选的查询
     * @param entityManager EntityManager
     * @param entityClass 实体类
     * @param metadata 字段元数据
     * @param searchText 搜索文本
     * @param maxResults 最大结果数
     * @param currentFilters 当前筛选条件
     * @param excludeField 排除的字段
     * @param moduleFieldsMap 模块字段映射
     * @return 查询结果列表
     */
    public List<String> executeQueryWithCascade(EntityManager entityManager, Class<?> entityClass,
                                               FilterFieldMetadata metadata, String searchText,
                                               Integer maxResults, Map<String, Object> currentFilters,
                                               String excludeField, Map<String, FilterFieldMetadata> moduleFieldsMap) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<?> root = query.from(entityClass);

        // 获取字段路径
        Path<String> fieldPath = getFieldPath(root, metadata);

        // 设置查询字段
        query.select(fieldPath).distinct(true);

        // 构建基础查询条件
        List<Predicate> predicates = buildBasicPredicates(cb, root, metadata, searchText);

        // 🔥 新增：添加级联筛选条件
        if (currentFilters != null && !currentFilters.isEmpty() && moduleFieldsMap != null) {
            List<Predicate> cascadePredicates = buildCascadePredicates(
                    cb, root, currentFilters, excludeField, moduleFieldsMap);
            predicates.addAll(cascadePredicates);
        }

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // 排序
        query.orderBy(cb.asc(fieldPath));

        // 执行查询
        return entityManager.createQuery(query)
                .setMaxResults(maxResults != null ? maxResults : 50)
                .getResultList();
    }
}
