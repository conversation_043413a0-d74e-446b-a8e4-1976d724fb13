package com.czerp.erpbackend.purchase.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 采购订单DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderDTO {

    /**
     * 采购订单ID
     */
    private Long id;

    /**
     * 采购单号
     */
    private String purchaseOrderNo;

    /**
     * 生产单号（关联销售订单明细的生产单号）
     */
    private String productionOrderNo;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 采购员
     */
    private String purchaser;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 采购类型
     */
    private String purchaseType;

    /**
     * 交易单位
     */
    private String tradingUnit;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 地址
     */
    private String address;

    /**
     * 电话
     */
    private String phone;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 订单明细
     */
    private List<PurchaseOrderItemDTO> items = new ArrayList<>();
}
