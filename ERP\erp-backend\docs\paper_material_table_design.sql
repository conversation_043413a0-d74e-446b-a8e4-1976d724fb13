-- 纸质资料数据库表设计
CREATE TABLE paper_material (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    paper_code VARCHAR(50) COMMENT '纸质编码',
    paper_name VARCHAR(50) COMMENT '纸质',
    paper_type VARCHAR(50) COMMENT '纸类',
    flute_type VARCHAR(50) COMMENT '楞别',
    is_standard TINYINT(1) COMMENT '标准纸质 (1-是, 0-否)',
    production_code VARCHAR(50) COMMENT '生产代号',
    face_paper VARCHAR(100) COMMENT '面纸',
    core_paper1 VARCHAR(100) COMMENT '芯纸1',
    middle_partition1 VARCHAR(100) COMMENT '中隔1',
    core_paper2 VARCHAR(100) COMMENT '芯纸2',
    middle_partition2 VARCHAR(100) COMMENT '中隔2',
    core_paper3 VARCHAR(100) COMMENT '芯纸3',
    liner_paper VARCHAR(100) COMMENT '里纸',
    layer_count INT COMMENT '层数',
    weight_kg_per_ksi DECIMAL(10, 6) COMMENT '重量(千克/千平方英寸)',
    weight_kg_per_sqm DECIMAL(10, 6) COMMENT '重量(千克/平方米)',
    edge_crush_strength VARCHAR(50) COMMENT '边压强度',
    bursting_strength VARCHAR(50) COMMENT '纸板耐破度',
    corresponding_standard VARCHAR(100) COMMENT '对应标准纸质',
    default_supplier VARCHAR(100) COMMENT '默认供应商名称',
    remarks VARCHAR(500) COMMENT '备注',
    is_disabled TINYINT(1) DEFAULT 0 COMMENT '停用 (1-是, 0-否)',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME COMMENT '更新时间',
    INDEX idx_paper_code (paper_code),
    INDEX idx_paper_name (paper_name),
    INDEX idx_paper_type (paper_type),
    INDEX idx_is_standard (is_standard),
    INDEX idx_is_disabled (is_disabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='纸质资料表';
