package com.czerp.erpbackend.sales.service.impl;

import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import com.czerp.erpbackend.sales.repository.SalesOrderItemRepository;
import com.czerp.erpbackend.sales.service.ProductionOrderNumberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 生产单号生成服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductionOrderNumberServiceImpl implements ProductionOrderNumberService {

    private final SalesOrderItemRepository salesOrderItemRepository;
    
    @Override
    public String generateProductionOrderNumber(SalesOrderItem orderItem) {
        log.debug("Generating production order number for order item: {}", orderItem.getId());
        
        // 生成格式：PO + 年月日 + 6位序号，例如：PO20250425000001
        LocalDate today = LocalDate.now();
        String datePrefix = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String productionOrderNoPrefix = "PO" + datePrefix;
        
        // 查询当天最大生产单号
        String maxProductionOrderNo = salesOrderItemRepository.findMaxProductionOrderNoByPrefix(productionOrderNoPrefix);
        
        int sequence = 1;
        if (maxProductionOrderNo != null && maxProductionOrderNo.length() >= productionOrderNoPrefix.length() + 6) {
            String sequenceStr = maxProductionOrderNo.substring(productionOrderNoPrefix.length());
            try {
                sequence = Integer.parseInt(sequenceStr) + 1;
            } catch (NumberFormatException e) {
                log.warn("Failed to parse sequence from production order no: {}", maxProductionOrderNo);
            }
        }
        
        // 格式化序号为6位数字
        return productionOrderNoPrefix + String.format("%06d", sequence);
    }
    
    @Override
    public Map<String, String> batchGenerateProductionOrderNumbers(List<SalesOrderItem> orderItems) {
        log.debug("Batch generating production order numbers for {} order items", orderItems.size());
        
        Map<String, String> productionOrderNumbers = new HashMap<>();
        
        // 生成格式：PO + 年月日 + 6位序号，例如：PO20250425000001
        LocalDate today = LocalDate.now();
        String datePrefix = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String productionOrderNoPrefix = "PO" + datePrefix;
        
        // 查询当天最大生产单号
        String maxProductionOrderNo = salesOrderItemRepository.findMaxProductionOrderNoByPrefix(productionOrderNoPrefix);
        
        int sequence = 1;
        if (maxProductionOrderNo != null && maxProductionOrderNo.length() >= productionOrderNoPrefix.length() + 6) {
            String sequenceStr = maxProductionOrderNo.substring(productionOrderNoPrefix.length());
            try {
                sequence = Integer.parseInt(sequenceStr) + 1;
            } catch (NumberFormatException e) {
                log.warn("Failed to parse sequence from production order no: {}", maxProductionOrderNo);
            }
        }
        
        // 为每个订单项生成唯一的生产单号
        for (SalesOrderItem orderItem : orderItems) {
            String productionOrderNo = productionOrderNoPrefix + String.format("%06d", sequence++);
            productionOrderNumbers.put(orderItem.getId(), productionOrderNo);
        }
        
        return productionOrderNumbers;
    }
}
