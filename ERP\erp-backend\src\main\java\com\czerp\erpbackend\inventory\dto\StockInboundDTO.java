package com.czerp.erpbackend.inventory.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 入库单DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockInboundDTO {

    /**
     * 入库单ID
     */
    private Long id;

    /**
     * 入库单号
     */
    private String inboundNo;

    /**
     * 入库日期
     */
    private LocalDate inboundDate;

    /**
     * 仓库
     */
    private String warehouse;

    /**
     * 备注
     */
    private String remark;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商送货单号
     */
    private String supplierDeliveryNo;

    /**
     * 送货单日期
     */
    private LocalDate deliveryDate;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 入库单明细
     */
    private List<StockInboundItemDTO> items = new ArrayList<>();
}
