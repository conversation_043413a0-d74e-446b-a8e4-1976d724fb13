-- 客户档案表结构
-- 创建日期：2023-11-15

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 客户档案表
CREATE TABLE IF NOT EXISTS `cus_customer` (
  `id` varchar(36) NOT NULL COMMENT '客户ID',
  `customer_code` varchar(50) NOT NULL COMMENT '客户编码',
  `customer_name` varchar(100) NOT NULL COMMENT '客户名称',
  `category_id` varchar(36) DEFAULT NULL COMMENT '客户分类ID',
  `full_name` varchar(200) DEFAULT NULL COMMENT '客户全称',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `fax` varchar(20) DEFAULT NULL COMMENT '传真',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `shipping_address` varchar(200) DEFAULT NULL COMMENT '收货地址',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '付款方式',
  `tax_rate` decimal(5,2) DEFAULT NULL COMMENT '税率%',
  `last_order_date` date DEFAULT NULL COMMENT '最近下单日期',
  `current_month_order_amount` decimal(15,2) DEFAULT 0.00 COMMENT '当月接单金额',
  `parent_company` varchar(100) DEFAULT NULL COMMENT '所属总公司',
  `region` varchar(50) DEFAULT NULL COMMENT '地区',
  `sales_person` varchar(50) DEFAULT NULL COMMENT '销售员',
  `order_tracker` varchar(50) DEFAULT NULL COMMENT '跟单员',
  `postal_code` varchar(20) DEFAULT NULL COMMENT '邮编',
  `receiver` varchar(50) DEFAULT NULL COMMENT '收货人',
  `currency` varchar(20) DEFAULT NULL COMMENT '币别',
  `default_quote_unit` varchar(20) DEFAULT NULL COMMENT '默认报价单位',
  `order_spare_ratio` decimal(5,2) DEFAULT NULL COMMENT '订单备品比例%',
  `price_decimal_places` int DEFAULT 2 COMMENT '单价小数位',
  `amount_decimal_places` int DEFAULT 2 COMMENT '金额小数位',
  `unit_weight_decimal_places` int DEFAULT 3 COMMENT '单重小数位',
  `total_weight_decimal_places` int DEFAULT 3 COMMENT '总重小数位',
  `unit_area_decimal_places` int DEFAULT 3 COMMENT '单位面积小数位',
  `total_area_decimal_places` int DEFAULT 3 COMMENT '总面积小数位',
  `special_monthly_settlement_date` int DEFAULT NULL COMMENT '特殊月结日期',
  `available_credit_amount` decimal(15,2) DEFAULT 0.00 COMMENT '可用额度金额',
  `last_delivery_date` date DEFAULT NULL COMMENT '最近送货日期',
  `days_without_order` int DEFAULT NULL COMMENT '未下单天数',
  `days_without_delivery` int DEFAULT NULL COMMENT '未送货天数',
  `first_order_date` date DEFAULT NULL COMMENT '首次订单日期',
  `first_delivery_date` date DEFAULT NULL COMMENT '首次送货日期',
  `print_header` varchar(200) DEFAULT NULL COMMENT '打印抬头',
  `disabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '停用',
  `created_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `industry` varchar(100) DEFAULT NULL COMMENT '行业',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customer_code` (`customer_code`),
  KEY `idx_customer_name` (`customer_name`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_region` (`region`),
  KEY `idx_sales_person` (`sales_person`),
  KEY `idx_disabled` (`disabled`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户档案表';

-- 删除已存在的外键约束以避免重复
ALTER TABLE `cus_customer` DROP FOREIGN KEY IF EXISTS `fk_customer_category`;

-- 添加外键约束
ALTER TABLE `cus_customer`
  ADD CONSTRAINT `fk_customer_category` FOREIGN KEY (`category_id`) REFERENCES `cus_customer_category` (`id`) ON DELETE SET NULL;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
