package com.czerp.erpbackend.system.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 纸度设置实体
 */
@Entity
@Table(name = "paper_size_setting")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PaperSizeSetting extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 纸度(inch)
     */
    @Column(name = "paper_size_inch", precision = 10, scale = 2)
    private BigDecimal paperSizeInch;

    /**
     * 纸度(cm)
     */
    @Column(name = "paper_size_cm", precision = 10, scale = 2)
    private BigDecimal paperSizeCm;

    /**
     * 最大损耗(inch)
     */
    @Column(name = "max_loss_inch", precision = 10, scale = 2)
    private BigDecimal maxLossInch;

    /**
     * 最大损耗(cm)
     */
    @Column(name = "max_loss_cm", precision = 10, scale = 2)
    private BigDecimal maxLossCm;
}
