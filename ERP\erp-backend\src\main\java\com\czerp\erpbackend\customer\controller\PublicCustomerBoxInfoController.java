package com.czerp.erpbackend.customer.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.customer.dto.CustomerBoxInfoDTO;
import com.czerp.erpbackend.customer.service.CustomerBoxInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共客户盒式信息接口控制器
 * 提供给其他模块使用的公共接口，不需要权限验证
 */
@RestController
@RequestMapping("/public/customer-box-infos")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Public Customer Box Info API", description = "公共客户盒式信息接口，不需要权限验证")
public class PublicCustomerBoxInfoController {

    private final CustomerBoxInfoService customerBoxInfoService;

    /**
     * 查询所有启用的客户盒式信息
     * @return 客户盒式信息列表
     */
    @GetMapping
    @Operation(summary = "查询所有启用的客户盒式信息", description = "查询所有启用的客户盒式信息，供其他模块使用")
    public ResponseEntity<ApiResponse<List<CustomerBoxInfoDTO>>> findActiveCustomerBoxInfos() {
        log.debug("Finding all active customer box infos (public)");
        List<CustomerBoxInfoDTO> response = customerBoxInfoService.findActiveCustomerBoxInfos();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 查询默认盒式
     * @return 默认盒式
     */
    @GetMapping("/default")
    @Operation(summary = "查询默认盒式", description = "查询默认盒式，供其他模块使用")
    public ResponseEntity<ApiResponse<CustomerBoxInfoDTO>> findDefaultCustomerBoxInfo() {
        log.debug("Finding default customer box info (public)");
        CustomerBoxInfoDTO response = customerBoxInfoService.findDefaultCustomerBoxInfo();
        return ResponseEntity.ok(ApiResponse.success(response));
    }
}
