package com.czerp.erpbackend.production.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.production.dto.ProcessDTO;
import com.czerp.erpbackend.production.service.ProcessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共工序接口控制器
 */
@RestController
@RequestMapping("/public/processes")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Public Process API", description = "公共工序接口")
public class PublicProcessController {
    
    private final ProcessService processService;
    
    @GetMapping
    @Operation(summary = "获取所有工序", description = "获取系统中所有工序列表")
    public ResponseEntity<ApiResponse<List<ProcessDTO>>> getAllProcesses() {
        log.info("Getting all processes (public)");
        List<ProcessDTO> processes = processService.findAllProcesses();
        return ResponseEntity.ok(ApiResponse.success(processes));
    }
}
