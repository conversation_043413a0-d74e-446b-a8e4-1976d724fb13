package com.czerp.erpbackend.purchase.service;

import com.czerp.erpbackend.purchase.dto.CreateSupplierCategoryRequest;
import com.czerp.erpbackend.purchase.dto.SupplierCategoryDTO;
import com.czerp.erpbackend.purchase.dto.UpdateSupplierCategoryRequest;

import java.util.List;

/**
 * 供应商分类服务接口
 */
public interface SupplierCategoryService {

    /**
     * 查询所有供应商分类
     * @return 供应商分类列表
     */
    List<SupplierCategoryDTO> findAllCategories();

    /**
     * 查询所有启用的供应商分类
     * @return 供应商分类列表
     */
    List<SupplierCategoryDTO> findAllActiveCategories();

    /**
     * 根据ID查询供应商分类
     * @param id 供应商分类ID
     * @return 供应商分类信息
     */
    SupplierCategoryDTO findCategoryById(String id);

    /**
     * 创建供应商分类
     * @param request 创建请求
     * @return 供应商分类信息
     */
    SupplierCategoryDTO createCategory(CreateSupplierCategoryRequest request);

    /**
     * 更新供应商分类
     * @param id 供应商分类ID
     * @param request 更新请求
     * @return 供应商分类信息
     */
    SupplierCategoryDTO updateCategory(String id, UpdateSupplierCategoryRequest request);

    /**
     * 删除供应商分类
     * @param id 供应商分类ID
     */
    void deleteCategory(String id);
}
