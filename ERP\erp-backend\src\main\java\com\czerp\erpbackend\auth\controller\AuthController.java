package com.czerp.erpbackend.auth.controller;

import com.czerp.erpbackend.auth.dto.LoginRequest;
import com.czerp.erpbackend.auth.dto.LoginResponse;
import com.czerp.erpbackend.auth.dto.LogoutRequest;
import com.czerp.erpbackend.auth.service.AuthService;
import com.czerp.erpbackend.common.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Authentication", description = "认证相关接口")
public class AuthController {

    private final AuthService authService;

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "使用用户名和密码进行登录，成功返回JWT令牌")
    public ResponseEntity<ApiResponse<LoginResponse>> login(@Valid @RequestBody LoginRequest loginRequest) {
        LoginResponse loginResponse = authService.login(loginRequest);
        return ResponseEntity.ok(ApiResponse.success(loginResponse));
    }

    @PostMapping("/logout")
    @Operation(summary = "用户退出", description = "使用令牌进行退出登录")
    @SecurityRequirement(name = "Bearer Authentication")
    public ResponseEntity<ApiResponse<Void>> logout(@Valid @RequestBody LogoutRequest logoutRequest) {
        // log.info("User logout request received");
        authService.logout(logoutRequest.getToken());
        return ResponseEntity.ok(ApiResponse.success());
    }

    // TODO: Add endpoints for refreshToken, changePassword etc. later
}