package com.czerp.erpbackend.system.service.impl;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.system.dto.PermissionDTO;
import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.mapper.PermissionMapper;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.UserRoleRepository;
import com.czerp.erpbackend.system.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PermissionServiceImpl implements PermissionService {

    private final PermissionRepository permissionRepository;
    private final RolePermissionRepository rolePermissionRepository;
    private final UserRoleRepository userRoleRepository;
    private final PermissionMapper permissionMapper;

    /**
     * 获取权限树
     * @return 权限树
     */
    @Override
    public List<PermissionDTO> findPermissionTree() {
        // 获取所有权限
        List<Permission> permissions = permissionRepository.findByIsDeletedFalseOrderBySortAsc();

        // 转换为DTO
        List<PermissionDTO> permissionDTOs = permissions.stream()
                .map(permissionMapper::toDto)
                .collect(Collectors.toList());

        // 构建权限树
        return buildPermissionTree(permissionDTOs);
    }

    /**
     * 查询所有权限
     * @return 权限列表
     */
    @Override
    public List<PermissionDTO> findAllPermissions() {
        List<Permission> permissions = permissionRepository.findByIsDeletedFalseOrderBySortAsc();
        return permissions.stream()
                .map(permissionMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询权限列表
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Override
    public List<PermissionDTO> findPermissionsByRoleId(String roleId) {
        List<String> permissionIds = rolePermissionRepository.findPermissionIdsByRoleId(roleId);
        if (permissionIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<Permission> permissions = permissionRepository.findByIdInAndIsDeletedFalse(permissionIds);
        return permissions.stream()
                .map(permissionMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID查询权限
     * @param id 权限ID
     * @return 权限信息
     */
    @Override
    public PermissionDTO findPermissionById(String id) {
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new BusinessException("权限不存在"));
        return permissionMapper.toDto(permission);
    }

    /**
     * 根据角色ID查询权限ID列表
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<String> findPermissionIdsByRoleId(String roleId) {
        return rolePermissionRepository.findPermissionIdsByRoleId(roleId);
    }

    /**
     * 根据用户ID查询权限编码列表
     * @param userId 用户ID
     * @return 权限编码列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<String> findPermissionCodesByUserId(String userId) {
        // 获取用户角色ID列表
        List<String> roleIds = userRoleRepository.findRoleIdsByUserId(userId);
        if (roleIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取角色权限编码列表
        return permissionRepository.findPermissionCodesByRoleIds(roleIds);
    }

    /**
     * 根据角色ID列表查询权限编码列表
     * @param roleIds 角色ID列表
     * @return 权限编码列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<String> findPermissionCodesByRoleIds(List<String> roleIds) {
        if (roleIds.isEmpty()) {
            return new ArrayList<>();
        }

        return permissionRepository.findPermissionCodesByRoleIds(roleIds);
    }

    /**
     * 构建权限树
     * @param permissions 权限列表
     * @return 权限树
     */
    private List<PermissionDTO> buildPermissionTree(List<PermissionDTO> permissions) {
        // 按父ID分组
        Map<String, List<PermissionDTO>> permissionMap = permissions.stream()
                .collect(Collectors.groupingBy(p -> p.getParentId() == null ? "root" : p.getParentId()));

        // 获取根权限
        List<PermissionDTO> rootPermissions = permissionMap.getOrDefault("root", new ArrayList<>());

        // 递归构建树
        rootPermissions.forEach(root -> buildChildren(root, permissionMap));

        return rootPermissions;
    }

    /**
     * 递归构建子权限
     * @param parent 父权限
     * @param permissionMap 权限映射
     */
    private void buildChildren(PermissionDTO parent, Map<String, List<PermissionDTO>> permissionMap) {
        List<PermissionDTO> children = permissionMap.getOrDefault(parent.getId(), new ArrayList<>());
        if (!children.isEmpty()) {
            parent.setChildren(children);
            children.forEach(child -> buildChildren(child, permissionMap));
        }
    }

    /**
     * 根据编码查询权限
     * @param code 权限编码
     * @return 权限
     */
    @Override
    @Transactional(readOnly = true)
    public java.util.Optional<Permission> findByCode(String code) {
        return permissionRepository.findByCode(code);
    }

    /**
     * 根据编码列表查询权限列表
     * @param codes 编码列表
     * @return 权限列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<Permission> findByCodes(List<String> codes) {
        if (codes == null || codes.isEmpty()) {
            return new ArrayList<>();
        }
        return permissionRepository.findByCodeIn(codes);
    }

    /**
     * 保存权限，如果不存在
     * @param permission 权限
     * @return 权限
     */
    @Override
    @Transactional
    public Permission saveIfNotExists(Permission permission) {
        if (permission == null) {
            throw new IllegalArgumentException("权限不能为空");
        }

        // 检查权限是否存在
        if (permissionRepository.existsByCode(permission.getCode())) {
            return permissionRepository.findByCode(permission.getCode()).orElse(permission);
        }

        // 保存权限
        return permissionRepository.save(permission);
    }

    /**
     * 批量保存权限，如果不存在
     * @param permissions 权限列表
     * @return 权限列表
     */
    @Override
    @Transactional
    public List<Permission> saveAllIfNotExist(List<Permission> permissions) {
        if (permissions == null || permissions.isEmpty()) {
            return new ArrayList<>();
        }

        List<Permission> result = new ArrayList<>();
        for (Permission permission : permissions) {
            result.add(saveIfNotExists(permission));
        }

        return result;
    }
}
