package com.czerp.erpbackend.customer.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.customer.dto.CreateCustomerCategoryRequest;
import com.czerp.erpbackend.customer.dto.CustomerCategoryDTO;
import com.czerp.erpbackend.customer.dto.CustomerCategoryQueryRequest;
import com.czerp.erpbackend.customer.dto.UpdateCustomerCategoryRequest;
import com.czerp.erpbackend.customer.service.CustomerCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户分类控制器
 */
@RestController
@RequestMapping("/customer-categories")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Customer Category API", description = "客户分类接口")
public class CustomerCategoryController {
    
    private final CustomerCategoryService categoryService;
    
    /**
     * 分页查询客户分类列表
     * @param request 查询请求
     * @return 客户分类分页列表
     */
    @GetMapping
    @Operation(summary = "分页查询客户分类列表", description = "分页查询客户分类列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:read')")
    public ResponseEntity<ApiResponse<PageResponse<CustomerCategoryDTO>>> findCategories(CustomerCategoryQueryRequest request) {
        log.info("Finding categories with request: {}", request);
        PageResponse<CustomerCategoryDTO> categories = categoryService.findCategories(request);
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
    
    /**
     * 查询所有客户分类
     * @return 客户分类列表
     */
    @GetMapping("/all")
    @Operation(summary = "查询所有客户分类", description = "查询所有客户分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:read')")
    public ResponseEntity<ApiResponse<List<CustomerCategoryDTO>>> findAllCategories() {
        log.info("Finding all categories");
        List<CustomerCategoryDTO> categories = categoryService.findAllCategories();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
    
    /**
     * 根据ID查询客户分类
     * @param id 客户分类ID
     * @return 客户分类信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询客户分类", description = "根据ID查询客户分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:read')")
    public ResponseEntity<ApiResponse<CustomerCategoryDTO>> findCategoryById(@PathVariable String id) {
        log.info("Finding category by id: {}", id);
        CustomerCategoryDTO category = categoryService.findCategoryById(id);
        return ResponseEntity.ok(ApiResponse.success(category));
    }
    
    /**
     * 根据分类编码查询客户分类
     * @param code 分类编码
     * @return 客户分类信息
     */
    @GetMapping("/code/{code}")
    @Operation(summary = "根据分类编码查询客户分类", description = "根据分类编码查询客户分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:read')")
    public ResponseEntity<ApiResponse<CustomerCategoryDTO>> findCategoryByCode(@PathVariable String code) {
        log.info("Finding category by code: {}", code);
        CustomerCategoryDTO category = categoryService.findCategoryByCode(code);
        return ResponseEntity.ok(ApiResponse.success(category));
    }
    
    /**
     * 创建客户分类
     * @param request 创建请求
     * @return 客户分类信息
     */
    @PostMapping
    @Operation(summary = "创建客户分类", description = "创建客户分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:create')")
    public ResponseEntity<ApiResponse<CustomerCategoryDTO>> createCategory(@Valid @RequestBody CreateCustomerCategoryRequest request) {
        log.info("Creating category with request: {}", request);
        CustomerCategoryDTO category = categoryService.createCategory(request);
        return ResponseEntity.ok(ApiResponse.success(category));
    }
    
    /**
     * 更新客户分类
     * @param id 客户分类ID
     * @param request 更新请求
     * @return 客户分类信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新客户分类", description = "更新客户分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:update')")
    public ResponseEntity<ApiResponse<CustomerCategoryDTO>> updateCategory(
            @PathVariable String id,
            @Valid @RequestBody UpdateCustomerCategoryRequest request) {
        log.info("Updating category with id: {} and request: {}", id, request);
        CustomerCategoryDTO category = categoryService.updateCategory(id, request);
        return ResponseEntity.ok(ApiResponse.success(category));
    }
    
    /**
     * 删除客户分类
     * @param id 客户分类ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除客户分类", description = "删除客户分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteCategory(@PathVariable String id) {
        log.info("Deleting category with id: {}", id);
        categoryService.deleteCategory(id);
        return ResponseEntity.ok(ApiResponse.success());
    }
    
    /**
     * 批量删除客户分类
     * @param ids 客户分类ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除客户分类", description = "批量删除客户分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:delete')")
    public ResponseEntity<ApiResponse<Void>> batchDeleteCategories(@RequestBody List<String> ids) {
        log.info("Batch deleting categories with ids: {}", ids);
        categoryService.batchDeleteCategories(ids);
        return ResponseEntity.ok(ApiResponse.success());
    }
    
    /**
     * 获取默认客户分类
     * @return 默认客户分类
     */
    @GetMapping("/default")
    @Operation(summary = "获取默认客户分类", description = "获取默认客户分类")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:read')")
    public ResponseEntity<ApiResponse<CustomerCategoryDTO>> getDefaultCategory() {
        log.info("Getting default category");
        CustomerCategoryDTO category = categoryService.getDefaultCategory();
        return ResponseEntity.ok(ApiResponse.success(category));
    }
}
