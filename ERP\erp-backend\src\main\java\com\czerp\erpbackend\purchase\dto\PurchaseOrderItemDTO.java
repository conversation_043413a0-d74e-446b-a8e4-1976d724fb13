package com.czerp.erpbackend.purchase.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 采购订单明细DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderItemDTO {

    /**
     * 采购订单明细ID
     */
    private Long id;

    /**
     * 采购订单ID
     */
    private Long purchaseOrderId;

    /**
     * 纸质
     */
    private String paperQuality;

    /**
     * 纸板类别
     */
    private String paperBoardCategory;

    /**
     * 楞别
     */
    private String corrugationType;

    /**
     * 纸度
     */
    private BigDecimal paperWidth;

    /**
     * 纸长
     */
    private BigDecimal paperLength;

    /**
     * 合订
     */
    private Boolean bindingMethod;

    /**
     * 合订规格
     */
    private String bindingSpecification;

    /**
     * 用料异动
     */
    private Boolean materialChange;

    /**
     * 报价特价
     */
    private String specialQuotation;

    /**
     * 纸质报价
     */
    private BigDecimal paperQuotation;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 压线尺寸(纸度)
     */
    private String creasingSize;

    /**
     * 压线方式
     */
    private String creasingMethod;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 折度规格
     */
    private String foldingSpecification;

    /**
     * 客户名称 - 从sales_order表获取
     */
    private String customerName;

    /**
     * 销售员 - 从sales_order表获取
     */
    private String salesPerson;

    /**
     * 客户订单号 - 从sales_order_item表获取
     */
    private String customerOrderNo;

    /**
     * 客方货号 - 从sales_order_item表获取
     */
    private String customerProductCode;

    /**
     * 品名 - 从sales_order_item表获取
     */
    private String productName;

    /**
     * 工艺要求 - 从sales_order_item表获取
     */
    private String processRequirements;

    /**
     * 盒式 - 从sales_order_item表获取
     */
    private String boxType;

    /**
     * 订单纸质 - 从sales_order_item表获取的paper_type
     */
    private String orderPaperType;

    /**
     * 产品规格 - 从sales_order_item表获取的length x width x height + size_unit
     */
    private String productSpecification;

    /**
     * 生产规格 - 从sales_order_item表获取的production_length x production_width x production_height + size_unit
     */
    private String productionSpecification;

    /**
     * 模开数 - 从sales_order_material表获取
     */
    private Integer dieOpenCount;

    /**
     * 纸板数 - 从sales_order_material表获取
     */
    private Integer boardCount;

    /**
     * 长度(米)
     */
    private BigDecimal lengthMeters;

    /**
     * 面积(平米)
     */
    private BigDecimal areaSquareMeters;

    /**
     * 体积(立方米)
     */
    private BigDecimal volumeCubicMeters;

    /**
     * 单重
     */
    private BigDecimal unitWeight;

    /**
     * 总重(KG)
     */
    private BigDecimal totalWeightKg;

    /**
     * 加工费
     */
    private BigDecimal processingFee;

    /**
     * 币种
     */
    private String currency;

    /**
     * 交期
     */
    private LocalDate deliveryDate;

    /**
     * 销售订单交期 - 从sales_order_item表获取
     */
    private LocalDate salesOrderDeliveryDate;

    /**
     * 销售订单创建时间 - 从sales_order_item表获取
     */
    private LocalDateTime salesOrderCreatedTime;

    /**
     * 销售订单明细生产备注 - 从sales_order_item表获取
     */
    private String salesOrderProductionRemark;

    /**
     * 销售订单明细备注 - 从sales_order_item表获取
     */
    private String salesOrderRemark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 来源销售订单明细ID
     */
    private String sourceSalesOrderItemId;

    /**
     * 生产单号（关联销售订单明细的生产单号）
     */
    private String productionOrderNo;

    // ========== 采购订单基本信息字段（用于明细级别查询） ==========

    /**
     * 采购单号
     */
    private String purchaseOrderNo;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 采购员
     */
    private String purchaser;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 采购类型
     */
    private String purchaseType;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 电话
     */
    private String phone;

    /**
     * 地址
     */
    private String address;
}
