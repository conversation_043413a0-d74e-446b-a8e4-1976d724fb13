package com.czerp.erpbackend.product.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

/**
 * 货品管理权限初始化
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(3) // 在系统权限初始化之后执行
public class ProductPermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing product permissions...");

        // 初始化货品管理权限
        createProductPermissions();

        // 初始化产品分类管理权限
        createProductCategoryPermissions();

        // 初始化产品规格管理权限
        createProductSpecPermissions();

        log.info("Product permissions initialized successfully");
    }

    /**
     * 创建货品管理权限
     */
    private void createProductPermissions() {
        // 货品管理菜单
        createPermissionIfNotExists(
                "product",
                "货品管理",
                "menu",
                null,
                "/products",
                "product/index",
                "shopping",
                40
        );

        // 货品列表按钮
        createPermissionIfNotExists(
                "product:list",
                "货品列表",
                "button",
                findPermissionIdByCode("product"),
                null,
                null,
                null,
                41
        );

        // 货品详情按钮
        createPermissionIfNotExists(
                "product:read",
                "货品详情",
                "button",
                findPermissionIdByCode("product"),
                null,
                null,
                null,
                42
        );

        // 创建货品按钮
        createPermissionIfNotExists(
                "product:create",
                "创建货品",
                "button",
                findPermissionIdByCode("product"),
                null,
                null,
                null,
                43
        );

        // 更新货品按钮
        createPermissionIfNotExists(
                "product:update",
                "更新货品",
                "button",
                findPermissionIdByCode("product"),
                null,
                null,
                null,
                44
        );

        // 删除货品按钮
        createPermissionIfNotExists(
                "product:delete",
                "删除货品",
                "button",
                findPermissionIdByCode("product"),
                null,
                null,
                null,
                45
        );

        // 导入货品按钮
        createPermissionIfNotExists(
                "product:import",
                "导入货品",
                "button",
                findPermissionIdByCode("product"),
                null,
                null,
                null,
                46
        );
    }

    /**
     * 创建产品分类管理权限
     */
    private void createProductCategoryPermissions() {
        // 产品分类菜单
        createPermissionIfNotExists(
                "product-category",
                "产品分类",
                "menu",
                null,
                "/product-categories",
                "product/category/index",
                "appstore",
                50
        );

        // 分类列表按钮
        createPermissionIfNotExists(
                "product-category:list",
                "分类列表",
                "button",
                findPermissionIdByCode("product-category"),
                null,
                null,
                null,
                51
        );

        // 分类详情按钮
        createPermissionIfNotExists(
                "product-category:read",
                "分类详情",
                "button",
                findPermissionIdByCode("product-category"),
                null,
                null,
                null,
                52
        );

        // 创建分类按钮
        createPermissionIfNotExists(
                "product-category:create",
                "创建分类",
                "button",
                findPermissionIdByCode("product-category"),
                null,
                null,
                null,
                53
        );

        // 更新分类按钮
        createPermissionIfNotExists(
                "product-category:update",
                "更新分类",
                "button",
                findPermissionIdByCode("product-category"),
                null,
                null,
                null,
                54
        );

        // 删除分类按钮
        createPermissionIfNotExists(
                "product-category:delete",
                "删除分类",
                "button",
                findPermissionIdByCode("product-category"),
                null,
                null,
                null,
                55
        );
    }

    /**
     * 创建产品规格管理权限
     */
    private void createProductSpecPermissions() {
        // 产品规格菜单
        createPermissionIfNotExists(
                "product-spec",
                "产品规格",
                "menu",
                null,
                "/product-specs",
                "product/spec/index",
                "tag",
                60
        );

        // 规格列表按钮
        createPermissionIfNotExists(
                "product-spec:list",
                "规格列表",
                "button",
                findPermissionIdByCode("product-spec"),
                null,
                null,
                null,
                61
        );

        // 规格详情按钮
        createPermissionIfNotExists(
                "product-spec:read",
                "规格详情",
                "button",
                findPermissionIdByCode("product-spec"),
                null,
                null,
                null,
                62
        );

        // 创建规格按钮
        createPermissionIfNotExists(
                "product-spec:create",
                "创建规格",
                "button",
                findPermissionIdByCode("product-spec"),
                null,
                null,
                null,
                63
        );

        // 更新规格按钮
        createPermissionIfNotExists(
                "product-spec:update",
                "更新规格",
                "button",
                findPermissionIdByCode("product-spec"),
                null,
                null,
                null,
                64
        );

        // 删除规格按钮
        createPermissionIfNotExists(
                "product-spec:delete",
                "删除规格",
                "button",
                findPermissionIdByCode("product-spec"),
                null,
                null,
                null,
                65
        );
    }

    /**
     * 创建权限（如果不存在）
     * @param code 权限编码
     * @param name 权限名称
     * @param type 权限类型
     * @param parentId 父权限ID
     * @param path 路径
     * @param component 组件
     * @param icon 图标
     * @param sort 排序
     */
    private void createPermissionIfNotExists(String code, String name, String type, String parentId, String path, String component, String icon, Integer sort) {
        if (!permissionRepository.existsByCode(code)) {
            Permission permission = new Permission();
            permission.setId(UUID.randomUUID().toString());
            permission.setCode(code);
            permission.setName(name);
            permission.setType(type);
            permission.setParentId(parentId);
            permission.setPath(path);
            permission.setComponent(component);
            permission.setIcon(icon);
            permission.setSort(sort);
            permission.setStatus("active");

            permissionRepository.save(permission);
            log.info("Created permission: {}", code);
        }
    }

    /**
     * 根据权限编码查询权限ID
     * @param code 权限编码
     * @return 权限ID
     */
    private String findPermissionIdByCode(String code) {
        return permissionRepository.findByCode(code)
                .map(Permission::getId)
                .orElse(null);
    }
}
