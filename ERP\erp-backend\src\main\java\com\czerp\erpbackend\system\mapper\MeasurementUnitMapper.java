package com.czerp.erpbackend.system.mapper;

import com.czerp.erpbackend.system.dto.CreateMeasurementUnitRequest;
import com.czerp.erpbackend.system.dto.MeasurementUnitDTO;
import com.czerp.erpbackend.system.dto.UpdateMeasurementUnitRequest;
import com.czerp.erpbackend.system.entity.MeasurementUnit;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * 计量单位Mapper
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeasurementUnitMapper {

    /**
     * 实体转DTO
     * @param entity 实体
     * @return DTO
     */
    @Mapping(target = "isActive", expression = "java(entity.getStatus() != null && entity.getStatus().equals(\"active\"))")
    @Mapping(target = "createdAt", source = "createdTime")
    @Mapping(target = "updatedAt", source = "updatedTime")
    MeasurementUnitDTO toDto(MeasurementUnit entity);

    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    MeasurementUnit toEntity(CreateMeasurementUnitRequest request);

    /**
     * 更新实体
     * @param request 更新请求
     * @param entity 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateMeasurementUnitRequest request, @MappingTarget MeasurementUnit entity);
}
