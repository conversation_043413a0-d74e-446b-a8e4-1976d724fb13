# 客户盒式关联API文档

## 目录

- [1. 概述](#1-概述)
- [2. 通用数据结构](#2-通用数据结构)
- [3. 客户盒式关联API](#3-客户盒式关联api)
  - [3.1 查询客户关联的盒式信息列表](#31-查询客户关联的盒式信息列表)
  - [3.2 创建客户盒式关联](#32-创建客户盒式关联)
  - [3.3 批量创建客户盒式关联](#33-批量创建客户盒式关联)
  - [3.4 删除客户盒式关联](#34-删除客户盒式关联)
  - [3.5 批量删除客户盒式关联](#35-批量删除客户盒式关联)
  - [3.6 删除客户的所有盒式关联](#36-删除客户的所有盒式关联)
- [4. 前端适配指南](#4-前端适配指南)
  - [4.1 数据模型映射](#41-数据模型映射)
  - [4.2 请求参数适配](#42-请求参数适配)
  - [4.3 响应数据适配](#43-响应数据适配)

## 1. 概述

客户盒式关联API提供了对客户与盒式信息关联关系的管理功能，包括查询客户关联的盒式信息列表、创建客户盒式关联、批量创建客户盒式关联、删除客户盒式关联、批量删除客户盒式关联和删除客户的所有盒式关联等操作。本文档详细描述了后端API的接口定义和数据结构，以及前端适配的相关指南。

## 2. 通用数据结构

### 2.1 API响应结构

所有API响应都遵循以下统一格式：

```typescript
interface ApiResponse<T> {
  success: boolean;       // 请求是否成功
  code: string;           // 状态码
  message: string;        // 提示信息
  data: T;                // 响应数据
}
```

### 2.2 客户盒式关联数据结构

```typescript
interface CustomerBoxRelationDTO {
  id: string;                           // 主键ID
  customerId: string;                   // 客户ID
  customerCode: string;                 // 客户编码
  customerName: string;                 // 客户名称
  boxInfoId: string;                    // 盒式信息ID
  boxCode: string;                      // 盒式编码
  boxName: string;                      // 盒式名称
  quoteFormula: string;                 // 报价公式
  calculationUnit: string;              // 算价单位
  quoteUnit: string;                    // 报价单位
  connectionMethod: string;             // 连接方式
  forbidDoubleFluting: boolean;         // 禁止双驳
  doubleFlutingLengthThreshold: number; // 长度大于此值双驳
  pitchTolerance: string;               // 跳度公差
  hasBoxGraphic: boolean;               // 盒式图形
  remark: string;                       // 备注
  createdBy: string;                    // 创建人
  createdTime: string;                  // 创建时间
  updatedBy: string;                    // 更新人
  updatedTime: string;                  // 更新时间
}
```

### 2.3 创建客户盒式关联请求

```typescript
interface CreateCustomerBoxRelationRequest {
  boxInfoId: string;                    // 盒式信息ID
  remark?: string;                      // 备注
}
```

## 3. 客户盒式关联API

### 3.1 查询客户关联的盒式信息列表

#### 请求信息

- **URL**: `/api/customers/{customerId}/box-infos`
- **方法**: GET
- **描述**: 查询客户关联的盒式信息列表
- **权限**: `customer:read`

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| customerId | string | 是 | 客户ID |

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "customerId": "customer-001",
      "customerCode": "C001",
      "customerName": "测试客户",
      "boxInfoId": "box-001",
      "boxCode": "BOX001",
      "boxName": "标准盒式",
      "quoteFormula": "(L+W)*2*H*单价",
      "calculationUnit": "mm",
      "quoteUnit": "元/平方米",
      "connectionMethod": "胶粘",
      "forbidDoubleFluting": false,
      "doubleFlutingLengthThreshold": 1000.00,
      "pitchTolerance": "±2mm",
      "hasBoxGraphic": true,
      "remark": "测试备注",
      "createdBy": "admin",
      "createdTime": "2023-01-01T00:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-01-01T00:00:00"
    }
  ]
}
```

### 3.2 创建客户盒式关联

#### 请求信息

- **URL**: `/api/customers/{customerId}/box-infos`
- **方法**: POST
- **描述**: 创建客户盒式关联
- **权限**: `customer:update`

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| customerId | string | 是 | 客户ID |

#### 请求体

```json
{
  "boxInfoId": "box-001",
  "remark": "测试备注"
}
```

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": "1",
    "customerId": "customer-001",
    "customerCode": "C001",
    "customerName": "测试客户",
    "boxInfoId": "box-001",
    "boxCode": "BOX001",
    "boxName": "标准盒式",
    "quoteFormula": "(L+W)*2*H*单价",
    "calculationUnit": "mm",
    "quoteUnit": "元/平方米",
    "connectionMethod": "胶粘",
    "forbidDoubleFluting": false,
    "doubleFlutingLengthThreshold": 1000.00,
    "pitchTolerance": "±2mm",
    "hasBoxGraphic": true,
    "remark": "测试备注",
    "createdBy": "admin",
    "createdTime": "2023-01-01T00:00:00",
    "updatedBy": "admin",
    "updatedTime": "2023-01-01T00:00:00"
  }
}
```

### 3.3 批量创建客户盒式关联

#### 请求信息

- **URL**: `/api/customers/{customerId}/box-infos/batch`
- **方法**: POST
- **描述**: 批量创建客户盒式关联
- **权限**: `customer:update`

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| customerId | string | 是 | 客户ID |

#### 请求体

```json
[
  {
    "boxInfoId": "box-001",
    "remark": "测试备注1"
  },
  {
    "boxInfoId": "box-002",
    "remark": "测试备注2"
  }
]
```

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "customerId": "customer-001",
      "customerCode": "C001",
      "customerName": "测试客户",
      "boxInfoId": "box-001",
      "boxCode": "BOX001",
      "boxName": "标准盒式",
      "quoteFormula": "(L+W)*2*H*单价",
      "calculationUnit": "mm",
      "quoteUnit": "元/平方米",
      "connectionMethod": "胶粘",
      "forbidDoubleFluting": false,
      "doubleFlutingLengthThreshold": 1000.00,
      "pitchTolerance": "±2mm",
      "hasBoxGraphic": true,
      "remark": "测试备注1",
      "createdBy": "admin",
      "createdTime": "2023-01-01T00:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-01-01T00:00:00"
    },
    {
      "id": "2",
      "customerId": "customer-001",
      "customerCode": "C001",
      "customerName": "测试客户",
      "boxInfoId": "box-002",
      "boxCode": "BOX002",
      "boxName": "高级盒式",
      "quoteFormula": "(L+W)*2*H*单价*1.2",
      "calculationUnit": "mm",
      "quoteUnit": "元/平方米",
      "connectionMethod": "胶粘",
      "forbidDoubleFluting": true,
      "doubleFlutingLengthThreshold": 800.00,
      "pitchTolerance": "±1mm",
      "hasBoxGraphic": true,
      "remark": "测试备注2",
      "createdBy": "admin",
      "createdTime": "2023-01-01T00:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-01-01T00:00:00"
    }
  ]
}
```

### 3.4 删除客户盒式关联

#### 请求信息

- **URL**: `/api/customers/{customerId}/box-infos/{boxInfoId}`
- **方法**: DELETE
- **描述**: 删除客户盒式关联
- **权限**: `customer:update`

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| customerId | string | 是 | 客户ID |
| boxInfoId | string | 是 | 盒式信息ID |

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

### 3.5 批量删除客户盒式关联

#### 请求信息

- **URL**: `/api/customers/{customerId}/box-infos/batch`
- **方法**: DELETE
- **描述**: 批量删除客户盒式关联
- **权限**: `customer:update`

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| customerId | string | 是 | 客户ID |

#### 请求体

```json
["box-001", "box-002"]
```

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

### 3.6 删除客户的所有盒式关联

#### 请求信息

- **URL**: `/api/customers/{customerId}/box-infos`
- **方法**: DELETE
- **描述**: 删除客户的所有盒式关联
- **权限**: `customer:update`

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| customerId | string | 是 | 客户ID |

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

## 4. 前端适配指南

### 4.1 数据模型映射

前端可以定义以下接口来映射后端的数据结构：

```typescript
// 客户盒式关联接口
export interface CustomerBoxRelation {
  id: string;
  customerId: string;                   // 客户ID
  customerCode: string;                 // 客户编码
  customerName: string;                 // 客户名称
  boxInfoId: string;                    // 盒式信息ID
  boxCode: string;                      // 盒式编码
  boxName: string;                      // 盒式名称
  quoteFormula: string;                 // 报价公式
  calculationUnit: string;              // 算价单位
  quoteUnit: string;                    // 报价单位
  connectionMethod: string;             // 连接方式
  forbidDoubleFluting: boolean;         // 禁止双驳
  doubleFlutingLengthThreshold: number; // 长度大于此值双驳
  pitchTolerance: string;               // 跳度公差
  hasBoxGraphic: boolean;               // 盒式图形
  remark: string;                       // 备注
  createdBy: string;                    // 创建人
  createdTime: string;                  // 创建时间
  updatedBy: string;                    // 更新人
  updatedTime: string;                  // 更新时间
}

// 创建客户盒式关联请求接口
export interface CreateCustomerBoxRelationRequest {
  boxInfoId: string;                    // 盒式信息ID
  remark?: string;                      // 备注
}
```

### 4.2 请求参数适配

#### 创建客户盒式关联

前端需要将自己的创建请求适配为后端接受的格式：

```typescript
// 前端创建请求
const createRequest: CreateCustomerBoxRelationRequest = {
  boxInfoId: 'box-001',
  remark: '测试备注'
};

// 调用API
const response = await api.post(`/api/customers/${customerId}/box-infos`, createRequest);
```

### 4.3 响应数据适配

前端需要将后端返回的数据适配为前端使用的格式：

```typescript
// 处理单个对象响应
function handleSingleResponse(response: any): CustomerBoxRelation {
  return response.data;
}

// 处理列表响应
function handleListResponse(response: any): CustomerBoxRelation[] {
  return response.data;
}
```

### 4.4 API服务示例

```typescript
import { get, post, del } from './request';
import type { CustomerBoxRelation, CreateCustomerBoxRelationRequest } from '@/types/customer';
import type { ApiResponse } from '@/types/api';

/**
 * 查询客户关联的盒式信息列表
 * @param customerId 客户ID
 */
export function getCustomerBoxRelations(customerId: string): Promise<ApiResponse<CustomerBoxRelation[]>> {
  return get<CustomerBoxRelation[]>(`/customers/${customerId}/box-infos`);
}

/**
 * 创建客户盒式关联
 * @param customerId 客户ID
 * @param request 创建请求
 */
export function createCustomerBoxRelation(customerId: string, request: CreateCustomerBoxRelationRequest): Promise<ApiResponse<CustomerBoxRelation>> {
  return post<CustomerBoxRelation>(`/customers/${customerId}/box-infos`, request);
}

/**
 * 批量创建客户盒式关联
 * @param customerId 客户ID
 * @param requests 创建请求列表
 */
export function batchCreateCustomerBoxRelations(customerId: string, requests: CreateCustomerBoxRelationRequest[]): Promise<ApiResponse<CustomerBoxRelation[]>> {
  return post<CustomerBoxRelation[]>(`/customers/${customerId}/box-infos/batch`, requests);
}

/**
 * 删除客户盒式关联
 * @param customerId 客户ID
 * @param boxInfoId 盒式信息ID
 */
export function deleteCustomerBoxRelation(customerId: string, boxInfoId: string): Promise<ApiResponse<void>> {
  return del<void>(`/customers/${customerId}/box-infos/${boxInfoId}`);
}

/**
 * 批量删除客户盒式关联
 * @param customerId 客户ID
 * @param boxInfoIds 盒式信息ID列表
 */
export function batchDeleteCustomerBoxRelations(customerId: string, boxInfoIds: string[]): Promise<ApiResponse<void>> {
  return del<void>(`/customers/${customerId}/box-infos/batch`, boxInfoIds);
}

/**
 * 删除客户的所有盒式关联
 * @param customerId 客户ID
 */
export function deleteAllCustomerBoxRelations(customerId: string): Promise<ApiResponse<void>> {
  return del<void>(`/customers/${customerId}/box-infos`);
}
```
