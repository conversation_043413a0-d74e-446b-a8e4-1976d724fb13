package com.czerp.erpbackend.common.filter.metadata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 筛选字段元数据
 * 定义字段的筛选配置信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterFieldMetadata {
    
    /**
     * 字段名称（前端传递的字段名）
     */
    private String fieldName;
    
    /**
     * 实体字段名称（数据库字段名）
     */
    private String entityFieldName;
    
    /**
     * 实体类
     */
    private Class<?> entityClass;
    
    /**
     * 字段路径（支持嵌套属性，如：orderItem.productName）
     */
    private String fieldPath;
    
    /**
     * 连接源类型
     */
    private JoinSource joinSource;
    
    /**
     * 筛选类型
     */
    private FilterType filterType;
    
    /**
     * 字典编码（仅静态字典字段使用）
     */
    private String dictionaryCode;
    
    /**
     * 自定义查询处理器类名（用于复杂查询逻辑）
     */
    private String customHandlerClass;

    /**
     * 自定义查询处理器Bean名称（用于复杂查询逻辑）
     */
    private String customHandlerBean;
    
    /**
     * 字段描述
     */
    private String description;
    
    /**
     * 是否启用搜索文本过滤
     */
    private Boolean enableSearch;
    
    /**
     * 最大选项数量
     */
    private Integer maxOptions;
    
    /**
     * 连接源枚举
     */
    public enum JoinSource {
        /**
         * 主表（根实体）
         */
        ROOT,

        /**
         * 关联表（通过Join查询）
         */
        JOIN,

        /**
         * 子查询（需要通过其他表关联查询）
         */
        SUBQUERY,
        
        /**
         * 动态字段（需要特殊处理的计算字段）
         */
        DYNAMIC
    }
    
    /**
     * 筛选类型枚举
     */
    public enum FilterType {
        /**
         * 动态查询 - 从数据库查询获取选项
         */
        DYNAMIC_QUERY,
        
        /**
         * 静态字典 - 从字典服务获取选项
         */
        STATIC_DICTIONARY,
        
        /**
         * 自定义处理 - 使用自定义处理器
         */
        CUSTOM_HANDLER
    }
    
    /**
     * 检查字段是否为动态查询类型
     * @return 是否为动态查询类型
     */
    public boolean isDynamicQuery() {
        return FilterType.DYNAMIC_QUERY.equals(this.filterType);
    }
    
    /**
     * 检查字段是否为静态字典类型
     * @return 是否为静态字典类型
     */
    public boolean isStaticDictionary() {
        return FilterType.STATIC_DICTIONARY.equals(this.filterType);
    }
    
    /**
     * 检查字段是否为自定义处理类型
     * @return 是否为自定义处理类型
     */
    public boolean isCustomHandler() {
        return FilterType.CUSTOM_HANDLER.equals(this.filterType);
    }
}
