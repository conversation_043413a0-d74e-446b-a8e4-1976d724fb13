package com.czerp.erpbackend.inventory.repository;

import com.czerp.erpbackend.inventory.entity.StockInboundItem;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 入库单明细Repository
 */
@Repository
public interface StockInboundItemRepository extends JpaRepository<StockInboundItem, Long>, JpaSpecificationExecutor<StockInboundItem> {

    /**
     * 根据入库单ID查询明细列表
     * @param inboundId 入库单ID
     * @return 明细列表
     */
    List<StockInboundItem> findByStockInboundId(Long inboundId);

    /**
     * 根据采购订单明细ID查询入库明细列表
     * @param purchaseOrderItemId 采购订单明细ID
     * @return 入库明细列表
     */
    List<StockInboundItem> findByPurchaseOrderItemId(Long purchaseOrderItemId);

    /**
     * 根据采购订单明细ID列表批量查询入库明细列表
     * @param purchaseOrderItemIds 采购订单明细ID列表
     * @return 入库明细列表
     */
    List<StockInboundItem> findByPurchaseOrderItemIdIn(List<Long> purchaseOrderItemIds);

    /**
     * 优化查询：使用JOIN一次性获取所有关联数据，避免N+1查询问题
     * 返回Object[]数组，包含：StockInboundItem, StockInbound, PurchaseOrderItem, PurchaseOrder, SalesOrderItem, SalesOrder
     */
    @Query("SELECT si, sb, poi, po, soi, so " +
           "FROM StockInboundItem si " +
           "JOIN si.stockInbound sb " +
           "LEFT JOIN si.purchaseOrderItem poi " +
           "LEFT JOIN poi.purchaseOrder po " +
           "LEFT JOIN SalesOrderItem soi ON poi.sourceSalesOrderItemId = soi.id " +
           "LEFT JOIN soi.order so " +
           "WHERE si.isDeleted = false AND sb.isDeleted = false " +
           "AND (:keyword IS NULL OR :keyword = '' OR " +
           "     sb.inboundNo LIKE %:keyword% OR " +
           "     sb.supplierName LIKE %:keyword% OR " +
           "     sb.warehouse LIKE %:keyword% OR " +
           "     sb.supplierDeliveryNo LIKE %:keyword% OR " +
           "     po.purchaseOrderNo LIKE %:keyword% OR " +
           "     so.orderNo LIKE %:keyword% OR " +
           "     so.customerName LIKE %:keyword%) " +
           "AND (:inboundDateStart IS NULL OR sb.inboundDate >= :inboundDateStart) " +
           "AND (:inboundDateEnd IS NULL OR sb.inboundDate <= :inboundDateEnd) " +
           "AND (:supplierCode IS NULL OR :supplierCode = '' OR sb.supplierCode = :supplierCode) " +
           "AND (:supplierName IS NULL OR :supplierName = '' OR sb.supplierName LIKE %:supplierName%) " +
           "AND (:warehouse IS NULL OR :warehouse = '' OR sb.warehouse LIKE %:warehouse%) " +
           "AND (:supplierDeliveryNo IS NULL OR :supplierDeliveryNo = '' OR sb.supplierDeliveryNo LIKE %:supplierDeliveryNo%) " +
           "AND (:deliveryDateStart IS NULL OR sb.deliveryDate >= :deliveryDateStart) " +
           "AND (:deliveryDateEnd IS NULL OR sb.deliveryDate <= :deliveryDateEnd) " +
           "AND (:createdBy IS NULL OR :createdBy = '' OR sb.createdBy = :createdBy) " +
           "ORDER BY sb.inboundDate DESC, sb.inboundNo DESC, si.id DESC")
    Page<Object[]> findStockInboundItemsWithJoin(
            @Param("keyword") String keyword,
            @Param("inboundDateStart") LocalDate inboundDateStart,
            @Param("inboundDateEnd") LocalDate inboundDateEnd,
            @Param("supplierCode") String supplierCode,
            @Param("supplierName") String supplierName,
            @Param("warehouse") String warehouse,
            @Param("supplierDeliveryNo") String supplierDeliveryNo,
            @Param("deliveryDateStart") LocalDate deliveryDateStart,
            @Param("deliveryDateEnd") LocalDate deliveryDateEnd,
            @Param("createdBy") String createdBy,
            Pageable pageable);
}
