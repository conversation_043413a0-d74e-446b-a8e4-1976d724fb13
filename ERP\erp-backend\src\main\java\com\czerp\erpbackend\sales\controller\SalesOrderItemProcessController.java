package com.czerp.erpbackend.sales.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.sales.dto.SalesOrderProcessDTO;
import com.czerp.erpbackend.sales.service.SalesOrderProcessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售订单明细项工序Controller
 */
@RestController
@RequestMapping("/sales/order-items/{orderItemId}/processes")
@Tag(name = "销售订单明细项工序管理", description = "销售订单明细项工序管理相关接口")
@RequiredArgsConstructor
@Slf4j
public class SalesOrderItemProcessController {

    private final SalesOrderProcessService salesOrderProcessService;

    /**
     * 查询订单明细项工序列表
     * @param orderItemId 订单明细项ID
     * @return 工序列表
     */
    @GetMapping
    @Operation(summary = "查询订单明细项工序列表", description = "查询订单明细项工序列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<List<SalesOrderProcessDTO>>> getProcesses(@PathVariable String orderItemId) {
        log.info("Getting processes for order item: {}", orderItemId);
        List<SalesOrderProcessDTO> processes = salesOrderProcessService.getProcessesByOrderItemId(orderItemId);
        return ResponseEntity.ok(ApiResponse.success(processes));
    }

    /**
     * 添加订单明细项工序
     * @param orderItemId 订单明细项ID
     * @param processDTO 工序DTO
     * @return 添加的工序
     */
    @PostMapping
    @Operation(summary = "添加订单明细项工序", description = "添加订单明细项工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<SalesOrderProcessDTO>> addProcess(
            @PathVariable String orderItemId,
            @Valid @RequestBody SalesOrderProcessDTO processDTO) {
        log.info("Adding process for order item: {}, process: {}", orderItemId, processDTO);
        
        // 确保订单明细项ID一致
        processDTO.setOrderItemId(orderItemId);
        
        SalesOrderProcessDTO addedProcess = salesOrderProcessService.addProcess(processDTO);
        return ResponseEntity.ok(ApiResponse.success(addedProcess));
    }

    /**
     * 批量添加订单明细项工序
     * @param orderItemId 订单明细项ID
     * @param processDTOs 工序DTO列表
     * @return 添加的工序列表
     */
    @PostMapping("/batch")
    @Operation(summary = "批量添加订单明细项工序", description = "批量添加订单明细项工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<List<SalesOrderProcessDTO>>> addProcesses(
            @PathVariable String orderItemId,
            @Valid @RequestBody List<SalesOrderProcessDTO> processDTOs) {
        log.info("Adding {} processes for order item: {}", processDTOs.size(), orderItemId);
        
        // 确保所有工序的订单明细项ID一致
        processDTOs.forEach(dto -> dto.setOrderItemId(orderItemId));
        
        List<SalesOrderProcessDTO> addedProcesses = salesOrderProcessService.addProcesses(processDTOs);
        return ResponseEntity.ok(ApiResponse.success(addedProcesses));
    }

    /**
     * 更新订单明细项工序
     * @param orderItemId 订单明细项ID
     * @param id 工序ID
     * @param processDTO 工序DTO
     * @return 更新后的工序
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新订单明细项工序", description = "更新订单明细项工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<SalesOrderProcessDTO>> updateProcess(
            @PathVariable String orderItemId,
            @PathVariable String id,
            @Valid @RequestBody SalesOrderProcessDTO processDTO) {
        log.info("Updating process: {} for order item: {}, process: {}", id, orderItemId, processDTO);
        
        // 确保订单明细项ID一致
        processDTO.setOrderItemId(orderItemId);
        
        SalesOrderProcessDTO updatedProcess = salesOrderProcessService.updateProcess(id, processDTO);
        return ResponseEntity.ok(ApiResponse.success(updatedProcess));
    }

    /**
     * 删除订单明细项工序
     * @param orderItemId 订单明细项ID
     * @param id 工序ID
     * @return 无内容
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除订单明细项工序", description = "删除订单明细项工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<Void>> deleteProcess(
            @PathVariable String orderItemId,
            @PathVariable String id) {
        log.info("Deleting process: {} for order item: {}", id, orderItemId);
        salesOrderProcessService.deleteProcess(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 删除订单明细项所有工序
     * @param orderItemId 订单明细项ID
     * @return 无内容
     */
    @DeleteMapping
    @Operation(summary = "删除订单明细项所有工序", description = "删除订单明细项所有工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:update')")
    public ResponseEntity<ApiResponse<Void>> deleteAllProcesses(@PathVariable String orderItemId) {
        log.info("Deleting all processes for order item: {}", orderItemId);
        salesOrderProcessService.deleteProcessesByOrderItemId(orderItemId);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
}
