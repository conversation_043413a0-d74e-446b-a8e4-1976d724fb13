package com.czerp.erpbackend.system.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 系统权限实体
 */
@Entity
@Table(name = "sys_permission")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Permission extends BaseEntity {

    /**
     * 权限ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 权限名称
     */
    @Column(name = "name", length = 50, nullable = false)
    private String name;

    /**
     * 权限编码
     */
    @Column(name = "code", length = 100, nullable = false, unique = true)
    private String code;

    /**
     * 类型(menu-菜单,button-按钮,api-接口)
     */
    @Column(name = "type", length = 20, nullable = false)
    private String type;

    /**
     * 父权限ID
     */
    @Column(name = "parent_id", length = 36)
    private String parentId;

    /**
     * 路径(菜单路径或API路径)
     */
    @Column(name = "path", length = 255)
    private String path;

    /**
     * 组件路径(前端组件)
     */
    @Column(name = "component", length = 255)
    private String component;

    /**
     * 图标
     */
    @Column(name = "icon", length = 50)
    private String icon;

    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort = 0;

    /**
     * 状态(active-启用,inactive-停用)
     */
    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";
}