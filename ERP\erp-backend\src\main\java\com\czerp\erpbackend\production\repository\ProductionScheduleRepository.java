package com.czerp.erpbackend.production.repository;

import com.czerp.erpbackend.production.entity.ProductionSchedule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 生产排程单Repository
 */
@Repository
public interface ProductionScheduleRepository extends JpaRepository<ProductionSchedule, String>, JpaSpecificationExecutor<ProductionSchedule> {

    /**
     * 根据排程单号查询生产排程单
     * @param scheduleNo 排程单号
     * @return 生产排程单
     */
    ProductionSchedule findByScheduleNo(String scheduleNo);

    /**
     * 检查排程单号是否存在
     * @param scheduleNo 排程单号
     * @return 是否存在
     */
    boolean existsByScheduleNo(String scheduleNo);

    /**
     * 查询指定前缀的最大排程单号
     * @param prefix 前缀
     * @return 最大排程单号
     */
    @Query("SELECT MAX(p.scheduleNo) FROM ProductionSchedule p WHERE p.scheduleNo LIKE :prefix%")
    String findMaxScheduleNoByPrefix(@Param("prefix") String prefix);

    /**
     * 根据ID查询生产排程单（包含明细）
     * @param id 排程单ID
     * @return 生产排程单
     */
    @Query("SELECT p FROM ProductionSchedule p LEFT JOIN FETCH p.items WHERE p.id = :id")
    ProductionSchedule findByIdWithItems(@Param("id") String id);
}
