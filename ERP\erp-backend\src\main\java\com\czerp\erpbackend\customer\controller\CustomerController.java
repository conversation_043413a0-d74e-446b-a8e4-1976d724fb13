package com.czerp.erpbackend.customer.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.customer.dto.CreateCustomerRequest;
import com.czerp.erpbackend.customer.dto.CustomerDTO;
import com.czerp.erpbackend.customer.dto.CustomerQueryRequest;
import com.czerp.erpbackend.customer.dto.UpdateCustomerRequest;
import com.czerp.erpbackend.customer.service.CustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户控制器
 */
@RestController
@RequestMapping("/customers")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Customer API", description = "客户接口")
public class CustomerController {

    private final CustomerService customerService;

    /**
     * 分页查询客户列表
     * @param request 查询请求
     * @return 客户分页列表
     */
    @GetMapping
    @Operation(summary = "分页查询客户列表", description = "分页查询客户列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:read')")
    public ResponseEntity<ApiResponse<PageResponse<CustomerDTO>>> findCustomers(CustomerQueryRequest request) {
        log.info("Finding customers with request: {}", request);
        try {
            PageResponse<CustomerDTO> customers = customerService.findCustomers(request);
            log.info("Found {} customers", customers.getContent().size());
            return ResponseEntity.ok(ApiResponse.success(customers));
        } catch (Exception e) {
            log.error("Error finding customers: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据ID查询客户
     * @param id 客户ID
     * @return 客户信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询客户", description = "根据ID查询客户")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:read')")
    public ResponseEntity<ApiResponse<CustomerDTO>> findCustomerById(@PathVariable String id) {
        log.info("Finding customer by id: {}", id);
        CustomerDTO customer = customerService.findCustomerById(id);
        return ResponseEntity.ok(ApiResponse.success(customer));
    }

    /**
     * 根据客户编码查询客户
     * @param code 客户编码
     * @return 客户信息
     */
    @GetMapping("/code/{code}")
    @Operation(summary = "根据客户编码查询客户", description = "根据客户编码查询客户")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:read')")
    public ResponseEntity<ApiResponse<CustomerDTO>> findCustomerByCode(@PathVariable String code) {
        log.info("Finding customer by code: {}", code);
        CustomerDTO customer = customerService.findCustomerByCode(code);
        return ResponseEntity.ok(ApiResponse.success(customer));
    }

    /**
     * 创建客户
     * @param request 创建请求
     * @return 客户信息
     */
    @PostMapping
    @Operation(summary = "创建客户", description = "创建客户")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:create')")
    public ResponseEntity<ApiResponse<CustomerDTO>> createCustomer(@Valid @RequestBody CreateCustomerRequest request) {
        log.info("Creating customer with request: {}", request);
        CustomerDTO customer = customerService.createCustomer(request);
        return ResponseEntity.ok(ApiResponse.success(customer));
    }

    /**
     * 更新客户
     * @param id 客户ID
     * @param request 更新请求
     * @return 客户信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新客户", description = "更新客户")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:update')")
    public ResponseEntity<ApiResponse<CustomerDTO>> updateCustomer(
            @PathVariable String id,
            @Valid @RequestBody UpdateCustomerRequest request) {
        log.info("Updating customer with id: {} and request: {}", id, request);
        CustomerDTO customer = customerService.updateCustomer(id, request);
        return ResponseEntity.ok(ApiResponse.success(customer));
    }

    /**
     * 删除客户
     * @param id 客户ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除客户", description = "删除客户")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteCustomer(@PathVariable String id) {
        log.info("Deleting customer with id: {}", id);
        customerService.deleteCustomer(id);
        return ResponseEntity.ok(ApiResponse.success());
    }

    /**
     * 批量删除客户
     * @param ids 客户ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除客户", description = "批量删除客户")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('customer:delete')")
    public ResponseEntity<ApiResponse<Void>> batchDeleteCustomers(@RequestBody List<String> ids) {
        log.info("Batch deleting customers with ids: {}", ids);
        customerService.batchDeleteCustomers(ids);
        return ResponseEntity.ok(ApiResponse.success());
    }
}
