package com.czerp.erpbackend.system.config;

import com.czerp.erpbackend.system.dto.LengthUnit;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 长度单位反序列化器
 * 支持大小写不敏感的单位转换，如"cm"、"CM"、"Cm"都会转换为LengthUnit.CM
 */
public class LengthUnitDeserializer extends JsonDeserializer<LengthUnit> {
    
    private static final Logger log = LoggerFactory.getLogger(LengthUnitDeserializer.class);
    
    @Override
    public LengthUnit deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        if (value == null || value.isEmpty()) {
            log.debug("Empty length unit value, using default: CM");
            return LengthUnit.CM; // 默认使用厘米
        }
        
        try {
            LengthUnit unit = LengthUnit.valueOf(value.toUpperCase());
            log.debug("Deserialized length unit: {} -> {}", value, unit);
            return unit;
        } catch (IllegalArgumentException e) {
            log.warn("Invalid length unit: {}, using default: CM", value);
            return LengthUnit.CM; // 如果无法匹配，返回默认值
        }
    }
}
