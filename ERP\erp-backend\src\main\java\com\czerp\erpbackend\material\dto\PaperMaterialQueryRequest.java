package com.czerp.erpbackend.material.dto;

import com.czerp.erpbackend.common.dto.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 纸质资料查询请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PaperMaterialQueryRequest extends PageRequest {

    /**
     * 纸质编码
     */
    private String paperCode;

    /**
     * 纸质
     */
    private String paperName;

    /**
     * 纸类
     */
    private String paperType;

    /**
     * 楞别
     */
    private String fluteType;

    /**
     * 标准纸质
     */
    private Boolean isStandard;

    /**
     * 停用
     */
    private Boolean isDisabled;
}
