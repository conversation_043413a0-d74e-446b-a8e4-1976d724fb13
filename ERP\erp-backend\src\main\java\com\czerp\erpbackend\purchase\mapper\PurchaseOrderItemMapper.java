package com.czerp.erpbackend.purchase.mapper;

import com.czerp.erpbackend.purchase.dto.CreatePurchaseOrderItemRequest;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemDTO;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 采购订单明细Mapper
 * 注意：此接口有自定义实现类PurchaseOrderItemMapperImpl，用于处理字符串到BigDecimal的转换
 */
public interface PurchaseOrderItemMapper {

    /**
     * 实体转DTO
     * @param entity 实体
     * @return DTO
     */
    @Mapping(source = "purchaseOrder.id", target = "purchaseOrderId")
    PurchaseOrderItemDTO toDto(PurchaseOrderItem entity);

    /**
     * 实体列表转DTO列表
     * @param entities 实体列表
     * @return DTO列表
     */
    List<PurchaseOrderItemDTO> toDto(List<PurchaseOrderItem> entities);

    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "purchaseOrder", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    PurchaseOrderItem toEntity(CreatePurchaseOrderItemRequest request);

    /**
     * 更新实体
     * @param request 创建请求
     * @param entity 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "purchaseOrder", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateEntity(CreatePurchaseOrderItemRequest request, @MappingTarget PurchaseOrderItem entity);
}
