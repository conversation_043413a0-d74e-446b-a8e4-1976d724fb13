package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.system.dto.RoleDTO;
import com.czerp.erpbackend.system.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色控制器
 */
@RestController
@RequestMapping("/system/roles")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Role Management", description = "角色管理相关接口")
public class RoleController {
    
    private final RoleService roleService;
    
    @GetMapping
    @Operation(summary = "获取角色列表", description = "获取所有角色列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('role:list')")
    public ResponseEntity<ApiResponse<List<RoleDTO>>> getAllRoles() {
        log.info("Getting all roles");
        List<RoleDTO> roles = roleService.findAllRoles();
        return ResponseEntity.ok(ApiResponse.success(roles));
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取角色详情", description = "根据ID获取角色详情")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('role:read')")
    public ResponseEntity<ApiResponse<RoleDTO>> getRole(@PathVariable String id) {
        log.info("Getting role with id: {}", id);
        RoleDTO role = roleService.findRoleById(id);
        return ResponseEntity.ok(ApiResponse.success(role));
    }
    
    @GetMapping("/page")
    @Operation(summary = "分页获取角色列表", description = "分页获取角色列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('role:list')")
    public ResponseEntity<ApiResponse<PageResponse<RoleDTO>>> getRoles(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("Getting roles with page: {}, size: {}", page, size);
        PageResponse<RoleDTO> roles = roleService.findRoles(page, size);
        return ResponseEntity.ok(ApiResponse.success(roles));
    }
}
