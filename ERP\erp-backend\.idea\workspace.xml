<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9e0c620b-f700-41fe-9c1f-abc62adad2c4" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/inventory/repository/StockInboundItemRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/inventory/repository/StockInboundItemRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/production/dto/ProductionScheduleDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/production/dto/ProductionScheduleDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/production/dto/ProductionScheduleItemDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/production/dto/ProductionScheduleItemDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/production/entity/ProductionSchedule.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/production/entity/ProductionSchedule.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/production/mapper/ProductionScheduleMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/production/mapper/ProductionScheduleMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/production/service/impl/ProductionScheduleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/czerp/erpbackend/production/service/impl/ProductionScheduleServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../.." />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2voYnGYkiwUvtdldw9lV3AnUmFs" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "JUnit.ProductServiceImportTest.executor": "Run",
    "JUnit.ProductionOrderNumberServiceTest.executor": "Run",
    "JUnit.PurchaseOrderForInboundServiceConsistencyTest.executor": "Run",
    "JUnit.SalesOrderQueryServiceTest.executor": "Run",
    "JUnit.SalesOrderServiceTest.executor": "Run",
    "Maven.erp-backend [clean,install,-U].executor": "Run",
    "Maven.erp-backend [clean,install].executor": "Run",
    "Maven.erp-backend [clean].executor": "Run",
    "Maven.erp-backend [test].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.ErpBackendApplication.executor": "Run",
    "git-widget-placeholder": "main",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.0",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn clean install" />
      <command value="mvn clean install -U" />
      <command value="mvn test" />
      <command value="mvn clean" />
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.ErpBackendApplication">
    <configuration name="ProductServiceImportTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="erp-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.czerp.erpbackend.product.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.czerp.erpbackend.product.service" />
      <option name="MAIN_CLASS_NAME" value="com.czerp.erpbackend.product.service.ProductServiceImportTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProductionOrderNumberServiceTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="erp-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.czerp.erpbackend.sales.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.czerp.erpbackend.sales.service" />
      <option name="MAIN_CLASS_NAME" value="com.czerp.erpbackend.sales.service.ProductionOrderNumberServiceTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PurchaseOrderForInboundServiceConsistencyTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="erp-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.czerp.erpbackend.purchase.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.czerp.erpbackend.purchase.service" />
      <option name="MAIN_CLASS_NAME" value="com.czerp.erpbackend.purchase.service.PurchaseOrderForInboundServiceConsistencyTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SalesOrderQueryServiceTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="erp-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.czerp.erpbackend.sales.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.czerp.erpbackend.sales.service" />
      <option name="MAIN_CLASS_NAME" value="com.czerp.erpbackend.sales.service.SalesOrderQueryServiceTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SalesOrderServiceTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="erp-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.czerp.erpbackend.sales.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.czerp.erpbackend.sales.service" />
      <option name="MAIN_CLASS_NAME" value="com.czerp.erpbackend.sales.service.SalesOrderServiceTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ErpBackendApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="erp-backend" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.czerp.erpbackend.ErpBackendApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.PurchaseOrderForInboundServiceConsistencyTest" />
        <item itemvalue="JUnit.ProductServiceImportTest" />
        <item itemvalue="JUnit.SalesOrderQueryServiceTest" />
        <item itemvalue="JUnit.ProductionOrderNumberServiceTest" />
        <item itemvalue="JUnit.SalesOrderServiceTest" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.21565.193" />
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-IU-243.21565.193" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="9e0c620b-f700-41fe-9c1f-abc62adad2c4" name="更改" comment="" />
      <created>1744813063959</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744813063959</updated>
      <workItem from="1744813065050" duration="3712000" />
      <workItem from="1744880788776" duration="30718000" />
      <workItem from="1745028523755" duration="25031000" />
      <workItem from="1745227005899" duration="27465000" />
      <workItem from="1745391942392" duration="4024000" />
      <workItem from="1745458734201" duration="102277000" />
      <workItem from="1746856020179" duration="1333000" />
      <workItem from="1747018426861" duration="15775000" />
      <workItem from="1747206256563" duration="20751000" />
      <workItem from="1747790473204" duration="14592000" />
      <workItem from="1747980980667" duration="51534000" />
      <workItem from="1749002938613" duration="27490000" />
      <workItem from="1749283687562" duration="13190000" />
      <workItem from="1749798164841" duration="10698000" />
      <workItem from="1750037962987" duration="4654000" />
      <workItem from="1750312753429" duration="1271000" />
      <workItem from="1750747730422" duration="1857000" />
      <workItem from="1750906976476" duration="5868000" />
      <workItem from="1751253055073" duration="3892000" />
      <workItem from="1751279108767" duration="2406000" />
      <workItem from="1753950100271" duration="2335000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>