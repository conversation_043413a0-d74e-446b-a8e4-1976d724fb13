package com.czerp.erpbackend.purchase.dto;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新供应商分类请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSupplierCategoryRequest {
    
    /**
     * 分类名称
     */
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String categoryName;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 状态(active-启用,inactive-停用)
     */
    @Pattern(regexp = "^(active|inactive)$", message = "状态值不正确，可选值：active, inactive")
    private String status;
    
    /**
     * 备注
     */
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;
}
