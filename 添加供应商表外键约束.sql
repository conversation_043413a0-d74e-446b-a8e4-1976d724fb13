-- 添加供应商表与供应商分类表的外键约束
-- 注意：执行此脚本前，请确保已经执行了供应商分类表和供应商表的字段名称修改脚本

-- 首先检查是否已存在外键约束
SELECT 
    CONSTRAINT_NAME 
FROM 
    INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
WHERE 
    TABLE_NAME = 'pur_supplier' 
    AND CONSTRAINT_TYPE = 'FOREIGN KEY' 
    AND CONSTRAINT_SCHEMA = DATABASE();

-- 添加外键约束
ALTER TABLE pur_supplier
ADD CONSTRAINT fk_supplier_category
FOREIGN KEY (category_id) REFERENCES pur_supplier_category(id)
ON DELETE SET NULL  -- 当分类被删除时，将供应商的category_id设为NULL
ON UPDATE CASCADE;  -- 当分类ID更新时，自动更新供应商的category_id

-- 验证外键约束是否创建成功
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME, 
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE 
    REFERENCED_TABLE_NAME = 'pur_supplier_category'
    AND TABLE_SCHEMA = DATABASE();
