package com.czerp.erpbackend.warehouse.config;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 仓库管理角色权限初始化器
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(101)
public class WarehouseRolePermissionInitializer implements CommandLineRunner {

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    public void run(String... args) throws Exception {
        log.info("Initializing warehouse management role permissions...");
        initializeAdminRolePermissions();
        log.info("Warehouse management role permissions initialized successfully.");
    }

    /**
     * 为管理员角色初始化仓库管理权限
     */
    private void initializeAdminRolePermissions() {
        // 查找管理员角色
        Optional<Role> adminRoleOpt = roleRepository.findByCode("admin");
        if (adminRoleOpt.isEmpty()) {
            log.warn("Admin role not found, skipping warehouse permissions initialization");
            return;
        }

        Role adminRole = adminRoleOpt.get();

        // 定义仓库管理权限代码列表
        List<String> warehousePermissionCodes = Arrays.asList(
                "warehouse",
                "warehouse:read",
                "warehouse:create",
                "warehouse:update",
                "warehouse:delete"
        );

        // 为管理员角色分配仓库管理权限
        for (String permissionCode : warehousePermissionCodes) {
            assignPermissionToRole(adminRole, permissionCode);
        }
    }

    /**
     * 为角色分配权限
     */
    private void assignPermissionToRole(Role role, String permissionCode) {
        // 查找权限
        Optional<Permission> permissionOpt = permissionRepository.findByCode(permissionCode);
        if (permissionOpt.isEmpty()) {
            log.warn("Permission not found: {}", permissionCode);
            return;
        }

        Permission permission = permissionOpt.get();

        // 检查角色权限关联是否已存在
        boolean exists = rolePermissionRepository.existsByRoleIdAndPermissionId(role.getId(), permission.getId());
        if (exists) {
            log.debug("Role permission already exists: {} - {}", role.getCode(), permissionCode);
            return;
        }

        // 创建角色权限关联
        RolePermission rolePermission = new RolePermission();
        rolePermission.setId(java.util.UUID.randomUUID().toString());
        rolePermission.setRoleId(role.getId());
        rolePermission.setPermissionId(permission.getId());

        rolePermissionRepository.save(rolePermission);
        log.info("Assigned permission {} to role {}", permissionCode, role.getCode());
    }
}
