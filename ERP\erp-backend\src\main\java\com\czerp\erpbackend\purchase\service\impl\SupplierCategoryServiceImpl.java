package com.czerp.erpbackend.purchase.service.impl;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.purchase.dto.CreateSupplierCategoryRequest;
import com.czerp.erpbackend.purchase.dto.SupplierCategoryDTO;
import com.czerp.erpbackend.purchase.dto.UpdateSupplierCategoryRequest;
import com.czerp.erpbackend.purchase.entity.SupplierCategory;
import com.czerp.erpbackend.purchase.mapper.SupplierCategoryMapper;
import com.czerp.erpbackend.purchase.repository.SupplierCategoryRepository;
import com.czerp.erpbackend.purchase.service.SupplierCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 供应商分类服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SupplierCategoryServiceImpl implements SupplierCategoryService {

    private final SupplierCategoryRepository categoryRepository;
    private final SupplierCategoryMapper categoryMapper;

    /**
     * 查询所有供应商分类
     * @return 供应商分类列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<SupplierCategoryDTO> findAllCategories() {
        log.debug("Finding all supplier categories");

        List<SupplierCategory> categories = categoryRepository.findAll(Sort.by(Sort.Direction.ASC, "sortOrder"));

        return categories.stream()
                .map(categoryMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 查询所有启用的供应商分类
     * @return 供应商分类列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<SupplierCategoryDTO> findAllActiveCategories() {
        log.debug("Finding all active supplier categories");

        List<SupplierCategory> categories = categoryRepository.findByStatusOrderBySortOrderAsc("active");

        return categories.stream()
                .map(categoryMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID查询供应商分类
     * @param id 供应商分类ID
     * @return 供应商分类信息
     */
    @Override
    @Transactional(readOnly = true)
    public SupplierCategoryDTO findCategoryById(String id) {
        log.debug("Finding supplier category by id: {}", id);

        SupplierCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new BusinessException("供应商分类不存在"));

        return categoryMapper.toDto(category);
    }

    /**
     * 创建供应商分类
     * @param request 创建请求
     * @return 供应商分类信息
     */
    @Override
    @Transactional
    public SupplierCategoryDTO createCategory(CreateSupplierCategoryRequest request) {
        log.debug("Creating supplier category with request: {}", request);

        // 检查分类编码是否已存在
        if (categoryRepository.existsByCategoryCode(request.getCategoryCode())) {
            throw new BusinessException("分类编码已存在");
        }

        // 创建分类
        SupplierCategory category = categoryMapper.toEntity(request);
        category.setId(UUID.randomUUID().toString());

        // 设置默认值
        if (category.getStatus() == null) {
            category.setStatus("active");
        }

        // 保存分类
        category = categoryRepository.save(category);

        return categoryMapper.toDto(category);
    }

    /**
     * 更新供应商分类
     * @param id 供应商分类ID
     * @param request 更新请求
     * @return 供应商分类信息
     */
    @Override
    @Transactional
    public SupplierCategoryDTO updateCategory(String id, UpdateSupplierCategoryRequest request) {
        log.debug("Updating supplier category with id: {} and request: {}", id, request);

        // 查询分类
        SupplierCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new BusinessException("供应商分类不存在"));

        // 更新分类
        categoryMapper.updateEntity(request, category);

        // 保存分类
        category = categoryRepository.save(category);

        return categoryMapper.toDto(category);
    }

    /**
     * 删除供应商分类
     * @param id 供应商分类ID
     */
    @Override
    @Transactional
    public void deleteCategory(String id) {
        log.debug("Deleting supplier category with id: {}", id);

        // 查询分类
        SupplierCategory category = categoryRepository.findById(id)
                .orElseThrow(() -> new BusinessException("供应商分类不存在"));

        // 删除分类
        categoryRepository.delete(category);
    }
}
