package com.czerp.erpbackend.common.filter.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.filter.config.FilterFieldRegistry;
import com.czerp.erpbackend.common.filter.service.ColumnFilterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 筛选功能测试控制器
 * 用于验证列筛选功能是否正常工作
 */
@RestController
@RequestMapping("/test/filter")
@RequiredArgsConstructor
@Slf4j
public class FilterTestController {
    
    private final ColumnFilterService columnFilterService;
    private final FilterFieldRegistry filterFieldRegistry;
    
    /**
     * 测试获取已注册的模块
     */
    @GetMapping("/modules")
    public ApiResponse<List<String>> getRegisteredModules() {
        List<String> modules = filterFieldRegistry.getRegisteredModules();
        log.info("Registered modules: {}", modules);
        return ApiResponse.success(modules);
    }
    
    /**
     * 测试获取模块支持的字段
     */
    @GetMapping("/fields/{moduleCode}")
    public ApiResponse<List<String>> getSupportedFields(@PathVariable String moduleCode) {
        List<String> fields = columnFilterService.getSupportedFields(moduleCode);
        log.info("Supported fields for module {}: {}", moduleCode, fields);
        return ApiResponse.success(fields);
    }
    
    /**
     * 测试检查字段是否支持
     */
    @GetMapping("/supported/{moduleCode}/{fieldName}")
    public ApiResponse<Boolean> isFieldSupported(@PathVariable String moduleCode, @PathVariable String fieldName) {
        boolean supported = columnFilterService.isFieldSupported(moduleCode, fieldName);
        log.info("Field {} in module {} is supported: {}", fieldName, moduleCode, supported);
        return ApiResponse.success(supported);
    }
    
    /**
     * 测试获取筛选选项
     */
    @GetMapping("/options/{moduleCode}/{fieldName}")
    public ApiResponse<Object> getFilterOptions(@PathVariable String moduleCode, @PathVariable String fieldName) {
        try {
            var options = columnFilterService.getFilterOptions(moduleCode, fieldName, null, null);
            log.info("Filter options for {}.{}: {} items", moduleCode, fieldName, options.size());
            return ApiResponse.success(Map.of(
                "fieldName", fieldName,
                "moduleCode", moduleCode,
                "optionCount", options.size(),
                "options", options.size() > 10 ? options.subList(0, 10) : options,
                "message", options.size() > 10 ? "Showing first 10 options" : "All options"
            ));
        } catch (Exception e) {
            log.error("Failed to get filter options for {}.{}: {}", moduleCode, fieldName, e.getMessage(), e);
            return ApiResponse.error("Failed to get filter options: " + e.getMessage());
        }
    }
}
