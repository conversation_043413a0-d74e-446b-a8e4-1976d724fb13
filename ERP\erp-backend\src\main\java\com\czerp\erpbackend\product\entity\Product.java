package com.czerp.erpbackend.product.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 货品实体
 */
@Entity
@Table(name = "prd_product")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Product extends BaseEntity {
    
    /**
     * 货品ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;
    
    /**
     * 货品名称
     */
    @Column(name = "name", length = 100, nullable = false)
    private String name;
    
    /**
     * 货品编码
     */
    @Column(name = "code", length = 50, nullable = false, unique = true)
    private String code;
    
    /**
     * 分类ID
     */
    @Column(name = "category_id", length = 36)
    private String categoryId;
    
    /**
     * 分类
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", insertable = false, updatable = false)
    private ProductCategory category;
    
    /**
     * 规格ID
     */
    @Column(name = "spec_id", length = 36)
    private String specId;
    
    /**
     * 规格
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "spec_id", insertable = false, updatable = false)
    private ProductSpec spec;
    
    /**
     * 单位
     */
    @Column(name = "unit", length = 20, nullable = false)
    private String unit;
    
    /**
     * 价格
     */
    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price;
    
    /**
     * 描述
     */
    @Column(name = "description", length = 500)
    private String description;
    
    /**
     * 图片URL
     */
    @Column(name = "image_url", length = 255)
    private String imageUrl;
    
    /**
     * 是否禁用
     */
    @Column(name = "disabled", nullable = false)
    private Boolean disabled = false;
}
