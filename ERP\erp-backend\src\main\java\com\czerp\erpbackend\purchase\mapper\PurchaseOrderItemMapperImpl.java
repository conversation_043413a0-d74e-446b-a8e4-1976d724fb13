package com.czerp.erpbackend.purchase.mapper;

import com.czerp.erpbackend.purchase.dto.CreatePurchaseOrderItemRequest;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemDTO;
import com.czerp.erpbackend.purchase.entity.PurchaseOrder;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 采购订单明细Mapper实现类
 * 自定义实现，处理字符串到BigDecimal的转换
 */
@Component
public class PurchaseOrderItemMapperImpl implements PurchaseOrderItemMapper {

    @Override
    public PurchaseOrderItemDTO toDto(PurchaseOrderItem entity) {
        if (entity == null) {
            return null;
        }

        PurchaseOrderItemDTO dto = new PurchaseOrderItemDTO();

        dto.setId(entity.getId());
        if (entity.getPurchaseOrder() != null) {
            dto.setPurchaseOrderId(entity.getPurchaseOrder().getId());
        }
        dto.setPaperQuality(entity.getPaperQuality());
        dto.setPaperBoardCategory(entity.getPaperBoardCategory());
        dto.setCorrugationType(entity.getCorrugationType());
        dto.setPaperWidth(entity.getPaperWidth());
        dto.setPaperLength(entity.getPaperLength());
        dto.setBindingMethod(entity.getBindingMethod());
        dto.setBindingSpecification(entity.getBindingSpecification());
        dto.setMaterialChange(entity.getMaterialChange());
        dto.setSpecialQuotation(entity.getSpecialQuotation());
        dto.setPaperQuotation(entity.getPaperQuotation());
        dto.setDiscount(entity.getDiscount());
        dto.setQuantity(entity.getQuantity());
        dto.setPrice(entity.getPrice());
        dto.setAmount(entity.getAmount());
        dto.setCreasingSize(entity.getCreasingSize());
        dto.setCreasingMethod(entity.getCreasingMethod());
        dto.setRemarks(entity.getRemarks());
        dto.setFoldingSpecification(entity.getFoldingSpecification());
        dto.setLengthMeters(entity.getLengthMeters());
        dto.setAreaSquareMeters(entity.getAreaSquareMeters());
        dto.setVolumeCubicMeters(entity.getVolumeCubicMeters());
        dto.setUnitWeight(entity.getUnitWeight());
        dto.setTotalWeightKg(entity.getTotalWeightKg());
        dto.setProcessingFee(entity.getProcessingFee());
        dto.setCurrency(entity.getCurrency());
        dto.setDeliveryDate(entity.getDeliveryDate());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedTime(entity.getCreatedTime());
        dto.setUpdatedBy(entity.getUpdatedBy());
        dto.setUpdatedTime(entity.getUpdatedTime());
        dto.setSourceSalesOrderItemId(entity.getSourceSalesOrderItemId());

        return dto;
    }

    @Override
    public List<PurchaseOrderItemDTO> toDto(List<PurchaseOrderItem> entities) {
        if (entities == null) {
            return null;
        }

        List<PurchaseOrderItemDTO> list = new ArrayList<>(entities.size());
        for (PurchaseOrderItem entity : entities) {
            list.add(toDto(entity));
        }

        return list;
    }

    @Override
    public PurchaseOrderItem toEntity(CreatePurchaseOrderItemRequest request) {
        if (request == null) {
            return null;
        }

        PurchaseOrderItem entity = new PurchaseOrderItem();

        entity.setPaperQuality(request.getPaperQuality());
        entity.setPaperBoardCategory(request.getPaperBoardCategory());
        entity.setCorrugationType(request.getCorrugationType());

        // 处理纸度字段，将字符串转换为BigDecimal
        if (request.getPaperWidth() != null) {
            try {
                entity.setPaperWidth(new BigDecimal(request.getPaperWidth()));
            } catch (NumberFormatException e) {
                // 如果转换失败，设置为null
                entity.setPaperWidth(null);
            }
        }

        // 处理纸长字段，将字符串转换为BigDecimal
        if (request.getPaperLength() != null) {
            try {
                entity.setPaperLength(new BigDecimal(request.getPaperLength()));
            } catch (NumberFormatException e) {
                // 如果转换失败，设置为null
                entity.setPaperLength(null);
            }
        }

        entity.setBindingMethod(request.getBindingMethod());
        entity.setBindingSpecification(request.getBindingSpecification());
        entity.setMaterialChange(request.getMaterialChange());
        entity.setSpecialQuotation(request.getSpecialQuotation());
        entity.setPaperQuotation(request.getPaperQuotation());
        entity.setDiscount(request.getDiscount());
        entity.setQuantity(request.getQuantity());
        entity.setPrice(request.getPrice());
        entity.setAmount(request.getAmount());
        entity.setCreasingSize(request.getCreasingSize());
        entity.setCreasingMethod(request.getCreasingMethod());
        entity.setRemarks(request.getRemarks());
        entity.setFoldingSpecification(request.getFoldingSpecification());
        entity.setLengthMeters(request.getLengthMeters());
        entity.setAreaSquareMeters(request.getAreaSquareMeters());
        entity.setVolumeCubicMeters(request.getVolumeCubicMeters());
        entity.setUnitWeight(request.getUnitWeight());
        entity.setTotalWeightKg(request.getTotalWeightKg());
        entity.setProcessingFee(request.getProcessingFee());
        entity.setCurrency(request.getCurrency());
        entity.setDeliveryDate(request.getDeliveryDate());

        return entity;
    }

    @Override
    public void updateEntity(CreatePurchaseOrderItemRequest request, PurchaseOrderItem entity) {
        if (request == null) {
            return;
        }

        entity.setPaperQuality(request.getPaperQuality());
        entity.setPaperBoardCategory(request.getPaperBoardCategory());
        entity.setCorrugationType(request.getCorrugationType());

        // 处理纸度字段，将字符串转换为BigDecimal
        if (request.getPaperWidth() != null) {
            try {
                entity.setPaperWidth(new BigDecimal(request.getPaperWidth()));
            } catch (NumberFormatException e) {
                // 如果转换失败，设置为null
                entity.setPaperWidth(null);
            }
        } else {
            entity.setPaperWidth(null);
        }

        // 处理纸长字段，将字符串转换为BigDecimal
        if (request.getPaperLength() != null) {
            try {
                entity.setPaperLength(new BigDecimal(request.getPaperLength()));
            } catch (NumberFormatException e) {
                // 如果转换失败，设置为null
                entity.setPaperLength(null);
            }
        } else {
            entity.setPaperLength(null);
        }

        entity.setBindingMethod(request.getBindingMethod());
        entity.setBindingSpecification(request.getBindingSpecification());
        entity.setMaterialChange(request.getMaterialChange());
        entity.setSpecialQuotation(request.getSpecialQuotation());
        entity.setPaperQuotation(request.getPaperQuotation());
        entity.setDiscount(request.getDiscount());
        entity.setQuantity(request.getQuantity());
        entity.setPrice(request.getPrice());
        entity.setAmount(request.getAmount());
        entity.setCreasingSize(request.getCreasingSize());
        entity.setCreasingMethod(request.getCreasingMethod());
        entity.setRemarks(request.getRemarks());
        entity.setFoldingSpecification(request.getFoldingSpecification());
        entity.setLengthMeters(request.getLengthMeters());
        entity.setAreaSquareMeters(request.getAreaSquareMeters());
        entity.setVolumeCubicMeters(request.getVolumeCubicMeters());
        entity.setUnitWeight(request.getUnitWeight());
        entity.setTotalWeightKg(request.getTotalWeightKg());
        entity.setProcessingFee(request.getProcessingFee());
        entity.setCurrency(request.getCurrency());
        entity.setDeliveryDate(request.getDeliveryDate());
    }
}
