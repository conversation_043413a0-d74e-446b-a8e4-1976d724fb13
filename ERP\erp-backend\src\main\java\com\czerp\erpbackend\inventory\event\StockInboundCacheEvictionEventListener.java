package com.czerp.erpbackend.inventory.event;

import com.czerp.erpbackend.purchase.service.PurchaseOrderItemCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 入库单缓存失效事件监听器
 * 在事务提交后异步处理缓存清除，确保数据一致性
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class StockInboundCacheEvictionEventListener {

    private final PurchaseOrderItemCacheService cacheService;

    /**
     * 处理入库单缓存失效事件
     * 使用 @TransactionalEventListener 确保在事务提交后执行
     * 使用 @Async 异步执行，避免影响主业务流程性能
     * 
     * @param event 缓存失效事件
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    @Async
    @EventListener
    public void handleStockInboundCacheEvictionEvent(StockInboundCacheEvictionEvent event) {
        try {
            log.info("Processing cache eviction event: {}", event);

            // 清除相关采购订单明细的缓存
            if (event.getAffectedPurchaseOrderItemIds() != null && 
                !event.getAffectedPurchaseOrderItemIds().isEmpty()) {
                
                cacheService.evictPurchaseOrderItemCaches(event.getAffectedPurchaseOrderItemIds());
                
                log.info("Successfully processed cache eviction for stock inbound {} ({}), " +
                        "cleared cache for {} purchase order items", 
                        event.getStockInboundId(), 
                        event.getEventType(), 
                        event.getAffectedPurchaseOrderItemIds().size());
            } else {
                log.debug("No purchase order items affected for stock inbound {} ({})", 
                         event.getStockInboundId(), event.getEventType());
            }

        } catch (Exception e) {
            log.error("Failed to process cache eviction event: {}", event, e);
            // 缓存清除失败不应该影响业务流程，只记录错误日志
            // 可以考虑添加重试机制或告警通知
        }
    }

    /**
     * 处理缓存预热事件（可选）
     * 在大批量操作后预热缓存，提高后续查询性能
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    @Async
    public void handleCacheWarmUpEvent(StockInboundCacheEvictionEvent event) {
        // 只在创建或更新事件后进行缓存预热
        if (event.getEventType() == StockInboundCacheEvictionEvent.EventType.CREATED ||
            event.getEventType() == StockInboundCacheEvictionEvent.EventType.UPDATED) {
            
            try {
                if (event.getAffectedPurchaseOrderItemIds() != null && 
                    !event.getAffectedPurchaseOrderItemIds().isEmpty()) {
                    
                    // 延迟一段时间后预热缓存，避免与缓存清除操作冲突
                    Thread.sleep(1000);
                    
                    cacheService.warmUpCaches(
                        event.getAffectedPurchaseOrderItemIds().stream().toList()
                    );
                    
                    log.debug("Cache warm-up completed for stock inbound {} after {}", 
                             event.getStockInboundId(), event.getEventType());
                }
            } catch (Exception e) {
                log.warn("Cache warm-up failed for stock inbound {} after {}: {}", 
                        event.getStockInboundId(), event.getEventType(), e.getMessage());
            }
        }
    }
}
