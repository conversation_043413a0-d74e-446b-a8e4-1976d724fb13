package com.czerp.erpbackend.common.enums;

import lombok.Getter;

/**
 * 单据状态枚举
 */
@Getter
public enum DocumentStatus {
    
    /**
     * 草稿
     */
    DRAFT("草稿"),
    
    /**
     * 待审核
     */
    PENDING_APPROVAL("待审核"),
    
    /**
     * 已审核
     */
    APPROVED("已审核"),
    
    /**
     * 已拒绝
     */
    REJECTED("已拒绝"),
    
    /**
     * 已作废
     */
    CANCELLED("已作废");
    
    private final String description;
    
    DocumentStatus(String description) {
        this.description = description;
    }
}
