package com.czerp.erpbackend.system.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentDTO {
    
    /**
     * 部门ID
     */
    private String id;
    
    /**
     * 部门名称
     */
    private String name;
    
    /**
     * 部门编码
     */
    private String code;
    
    /**
     * 父部门ID
     */
    private String parentId;
    
    /**
     * 父部门名称
     */
    private String parentName;
    
    /**
     * 层级
     */
    private Integer level;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 部门负责人ID
     */
    private String managerId;
    
    /**
     * 部门负责人姓名
     */
    private String managerName;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 子部门列表
     */
    private List<DepartmentDTO> children;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 