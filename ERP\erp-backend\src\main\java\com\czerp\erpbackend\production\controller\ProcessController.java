package com.czerp.erpbackend.production.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.production.dto.CreateProcessRequest;
import com.czerp.erpbackend.production.dto.ProcessDTO;
import com.czerp.erpbackend.production.dto.ProcessQueryRequest;
import com.czerp.erpbackend.production.dto.UpdateProcessRequest;
import com.czerp.erpbackend.production.service.ProcessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工序控制器
 */
@RestController
@RequestMapping("/processes")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Process Management", description = "工序管理相关接口")
public class ProcessController {
    
    private final ProcessService processService;
    
    @GetMapping
    @Operation(summary = "获取工序列表", description = "分页查询工序列表，支持多条件筛选")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('process:list')")
    public ResponseEntity<ApiResponse<PageResponse<ProcessDTO>>> getProcesses(ProcessQueryRequest request) {
        log.info("Getting processes with request: {}", request);
        PageResponse<ProcessDTO> processes = processService.findProcesses(request);
        return ResponseEntity.ok(ApiResponse.success(processes));
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "获取工序详情", description = "根据ID获取工序详情")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('process:view')")
    public ResponseEntity<ApiResponse<ProcessDTO>> getProcess(@PathVariable Long id) {
        log.info("Getting process with id: {}", id);
        ProcessDTO process = processService.findProcessById(id);
        return ResponseEntity.ok(ApiResponse.success(process));
    }
    
    @PostMapping
    @Operation(summary = "创建工序", description = "创建新的工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('process:create')")
    public ResponseEntity<ApiResponse<ProcessDTO>> createProcess(@Valid @RequestBody CreateProcessRequest request) {
        log.info("Creating process with request: {}", request);
        ProcessDTO process = processService.createProcess(request);
        return ResponseEntity.ok(ApiResponse.success(process));
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "更新工序", description = "根据ID更新工序信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('process:update')")
    public ResponseEntity<ApiResponse<ProcessDTO>> updateProcess(
            @PathVariable Long id,
            @Valid @RequestBody UpdateProcessRequest request) {
        log.info("Updating process with id: {} and request: {}", id, request);
        ProcessDTO process = processService.updateProcess(id, request);
        return ResponseEntity.ok(ApiResponse.success(process));
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "删除工序", description = "根据ID删除工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('process:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteProcess(@PathVariable Long id) {
        log.info("Deleting process with id: {}", id);
        processService.deleteProcess(id);
        return ResponseEntity.ok(ApiResponse.success());
    }
    
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除工序", description = "根据ID列表批量删除工序")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('process:delete')")
    public ResponseEntity<ApiResponse<Void>> batchDeleteProcesses(@RequestBody List<Long> ids) {
        log.info("Batch deleting processes with ids: {}", ids);
        processService.batchDeleteProcesses(ids);
        return ResponseEntity.ok(ApiResponse.success());
    }
}
