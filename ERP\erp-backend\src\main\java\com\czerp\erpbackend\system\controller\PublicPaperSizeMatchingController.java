package com.czerp.erpbackend.system.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.system.dto.PaperSizeMatchingRequest;
import com.czerp.erpbackend.system.dto.PaperSizeMatchingResponse;
import com.czerp.erpbackend.system.service.PaperSizeMatchingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 纸度匹配公共控制器
 * 提供给其他模块使用的公共接口，不需要权限验证
 */
@RestController
@RequestMapping("/public/system/paper-size-matching")
@Tag(name = "纸度匹配公共接口", description = "纸度匹配公共接口，不需要权限验证")
@RequiredArgsConstructor
@Slf4j
public class PublicPaperSizeMatchingController {
    
    private final PaperSizeMatchingService paperSizeMatchingService;
    
    /**
     * 匹配最佳纸度
     * @param request 纸度匹配请求
     * @return 纸度匹配响应
     */
    @PostMapping
    @Operation(summary = "匹配最佳纸度", description = "根据纸盒宽度和高度匹配最佳纸度")
    public ResponseEntity<ApiResponse<PaperSizeMatchingResponse>> matchPaperSize(
            @Valid @RequestBody PaperSizeMatchingRequest request) {
        log.info("Matching paper size with request (public): {}", request);
        PaperSizeMatchingResponse response = paperSizeMatchingService.findBestPaperSize(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }
}
