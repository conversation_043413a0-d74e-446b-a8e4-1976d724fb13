package com.czerp.erpbackend.system.dto;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 更新部门请求
 */
@Data
public class UpdateDepartmentRequest {
    
    /**
     * 部门名称
     */
    @Size(max = 50, message = "部门名称长度不能超过50个字符")
    private String name;
    
    /**
     * 父部门ID
     */
    private String parentId;
    
    /**
     * 部门负责人ID
     */
    private String managerId;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 状态
     */
    @Pattern(regexp = "^(active|inactive)?$", message = "状态值不正确，可选值：active, inactive")
    private String status;
} 