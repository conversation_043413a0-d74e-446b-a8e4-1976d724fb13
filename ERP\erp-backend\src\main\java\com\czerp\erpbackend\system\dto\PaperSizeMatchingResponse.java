package com.czerp.erpbackend.system.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 纸度匹配响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaperSizeMatchingResponse {
    
    /**
     * 最佳纸度设置
     */
    private PaperSizeSettingDTO bestPaperSize;
    
    /**
     * 最佳纸度下可生产的盒子数量
     */
    private Integer maxBoxesProducible;
    
    /**
     * 浪费率（小数形式，如0.05表示5%）
     */
    private BigDecimal wastePercentage;
    
    /**
     * 浪费率（百分比形式，如"5%"）
     */
    private String wastePercentageFormatted;
    
    /**
     * 提示信息
     */
    private String message;
}
