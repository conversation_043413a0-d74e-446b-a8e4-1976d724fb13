package com.czerp.erpbackend.customer.repository;

import com.czerp.erpbackend.customer.entity.CustomerBoxInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 客户盒式信息存储库
 */
@Repository
public interface CustomerBoxInfoRepository extends JpaRepository<CustomerBoxInfo, String>, JpaSpecificationExecutor<CustomerBoxInfo> {

    /**
     * 根据盒式编码查找盒式信息
     * @param boxCode 盒式编码
     * @return 盒式信息
     */
    Optional<CustomerBoxInfo> findByBoxCode(String boxCode);

    /**
     * 判断盒式编码是否存在
     * @param boxCode 盒式编码
     * @return 是否存在
     */
    boolean existsByBoxCode(String boxCode);

    /**
     * 根据状态查询盒式信息列表
     * @param status 状态
     * @return 盒式信息列表
     */
    List<CustomerBoxInfo> findByStatus(String status);

    /**
     * 查询默认盒式
     * @return 默认盒式列表
     */
    List<CustomerBoxInfo> findByIsDefaultTrue();
}
