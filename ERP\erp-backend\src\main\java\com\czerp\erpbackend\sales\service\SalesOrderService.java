package com.czerp.erpbackend.sales.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.sales.dto.SalesOrderDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderDetailDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderItemDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryDTO;

import java.util.List;

/**
 * 销售订单Service接口
 */
public interface SalesOrderService {

    /**
     * 创建销售订单
     * @param orderDTO 销售订单DTO
     * @return 销售订单DTO
     */
    SalesOrderDetailDTO createOrder(SalesOrderDetailDTO orderDTO);

    /**
     * 更新销售订单
     * @param id 订单ID
     * @param orderDTO 销售订单DTO
     * @return 销售订单DTO
     */
    SalesOrderDetailDTO updateOrder(String id, SalesOrderDetailDTO orderDTO);

    /**
     * 根据ID查询销售订单
     * @param id 订单ID
     * @return 销售订单DTO
     */
    SalesOrderDetailDTO getOrderById(String id);

    /**
     * 根据订单号查询销售订单
     * @param orderNo 订单号
     * @return 销售订单DTO
     */
    SalesOrderDetailDTO getOrderByOrderNo(String orderNo);

    /**
     * 分页查询销售订单
     * @param queryDTO 查询条件
     * @return 销售订单分页列表
     */
    PageResponse<SalesOrderDTO> getOrders(SalesOrderQueryDTO queryDTO);

    /**
     * 删除销售订单
     * @param id 订单ID
     */
    void deleteOrder(String id);

    /**
     * 根据客户编码查询销售订单
     * @param customerCode 客户编码
     * @return 销售订单列表
     */
    List<SalesOrderDTO> getOrdersByCustomerCode(String customerCode);

    /**
     * 生成订单号
     * @return 订单号
     */
    String generateOrderNo();

    /**
     * 打印订单
     * @param id 订单ID
     * @return 销售订单DTO
     */
    SalesOrderDTO printOrder(String id);

    /**
     * 根据客户ID查询历史订单明细
     * @param customerId 客户ID
     * @param keyword 关键词（可选，用于搜索客方货号或品名）
     * @return 历史订单明细列表
     */
    List<SalesOrderItemDTO> getHistoricalItemsByCustomerId(String customerId, String keyword);
}
