package com.czerp.erpbackend.common.filter.service;

import com.czerp.erpbackend.common.filter.dto.FilterOptionDTO;
import com.czerp.erpbackend.common.filter.dto.FilterRequest;

import java.util.List;
import java.util.Map;

/**
 * 通用列筛选服务接口
 * 提供跨模块的列筛选功能支持
 */
public interface ColumnFilterService {
    
    /**
     * 获取指定字段的筛选选项
     * @param moduleCode 模块编码（如：sales-order, purchase-order）
     * @param fieldName 字段名称
     * @param searchText 搜索文本（可选）
     * @param currentFilters 当前筛选条件（可选）
     * @return 筛选选项列表
     */
    List<FilterOptionDTO> getFilterOptions(String moduleCode, String fieldName, 
                                         String searchText, Map<String, Object> currentFilters);
    
    /**
     * 获取指定字段的筛选选项（使用FilterRequest）
     * @param request 筛选请求对象
     * @return 筛选选项列表
     */
    List<FilterOptionDTO> getFilterOptions(FilterRequest request);
    
    /**
     * 批量获取多个字段的筛选选项
     * @param moduleCode 模块编码
     * @param fieldNames 字段名称列表
     * @param currentFilters 当前筛选条件
     * @return 字段名称到筛选选项列表的映射
     */
    Map<String, List<FilterOptionDTO>> getBatchFilterOptions(String moduleCode, 
                                                            List<String> fieldNames, 
                                                            Map<String, Object> currentFilters);
    
    /**
     * 检查模块是否支持指定字段的筛选
     * @param moduleCode 模块编码
     * @param fieldName 字段名称
     * @return 是否支持
     */
    boolean isFieldSupported(String moduleCode, String fieldName);
    
    /**
     * 获取模块支持的所有筛选字段
     * @param moduleCode 模块编码
     * @return 支持的字段名称列表
     */
    List<String> getSupportedFields(String moduleCode);
}
