package com.czerp.erpbackend.product.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.product.dto.ProductCategoryDTO;
import com.czerp.erpbackend.product.dto.ProductDTO;
import com.czerp.erpbackend.product.dto.ProductSpecDTO;
import com.czerp.erpbackend.product.service.ProductCategoryService;
import com.czerp.erpbackend.product.service.ProductService;
import com.czerp.erpbackend.product.service.ProductSpecService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共产品接口控制器
 */
@RestController
@RequestMapping("/public/products")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Public Product API", description = "公共产品接口")
public class PublicProductController {

    private final ProductCategoryService categoryService;
    private final ProductSpecService specService;
    private final ProductService productService;

    @GetMapping("/categories")
    @Operation(summary = "获取所有分类", description = "获取系统中所有产品分类列表")
    public ResponseEntity<ApiResponse<List<ProductCategoryDTO>>> getAllCategories() {
        log.info("Getting all categories (public)");
        List<ProductCategoryDTO> categories = categoryService.findAllCategories();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }

    @GetMapping("/categories/tree")
    @Operation(summary = "获取分类树", description = "获取产品分类树结构")
    public ResponseEntity<ApiResponse<List<ProductCategoryDTO>>> getCategoryTree() {
        log.info("Getting category tree (public)");
        List<ProductCategoryDTO> categoryTree = categoryService.findCategoryTree();
        return ResponseEntity.ok(ApiResponse.success(categoryTree));
    }

    @GetMapping("/specs")
    @Operation(summary = "获取所有规格", description = "获取系统中所有产品规格列表")
    public ResponseEntity<ApiResponse<List<ProductSpecDTO>>> getAllSpecs() {
        log.info("Getting all specs (public)");
        List<ProductSpecDTO> specs = specService.findAllSpecs();
        return ResponseEntity.ok(ApiResponse.success(specs));
    }

    @GetMapping
    @Operation(summary = "获取所有启用的货品", description = "获取系统中所有启用的货品列表")
    public ResponseEntity<ApiResponse<List<ProductDTO>>> getAllActiveProducts() {
        log.info("Getting all active products (public)");
        List<ProductDTO> products = productService.findActiveProducts();
        return ResponseEntity.ok(ApiResponse.success(products));
    }
}
