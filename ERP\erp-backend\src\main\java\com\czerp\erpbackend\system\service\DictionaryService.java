package com.czerp.erpbackend.system.service;

import java.util.List;

/**
 * 字典服务接口
 * 用于获取系统字典数据，支持筛选选项功能
 */
public interface DictionaryService {
    
    /**
     * 根据字典编码获取字典选项列表
     * @param dictionaryCode 字典编码
     * @return 选项字符串列表，如果未找到则返回空列表
     */
    List<String> getOptionsByCode(String dictionaryCode);
    
    /**
     * 根据字典编码和搜索文本获取字典选项列表
     * @param dictionaryCode 字典编码
     * @param searchText 搜索文本（可选）
     * @return 选项字符串列表，如果未找到则返回空列表
     */
    List<String> getOptionsByCode(String dictionaryCode, String searchText);
    
    /**
     * 检查字典编码是否存在
     * @param dictionaryCode 字典编码
     * @return 是否存在
     */
    boolean existsByCode(String dictionaryCode);
}
