package com.czerp.erpbackend.system.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.system.dto.CreateMeasurementUnitRequest;
import com.czerp.erpbackend.system.dto.MeasurementUnitDTO;
import com.czerp.erpbackend.system.dto.MeasurementUnitQueryRequest;
import com.czerp.erpbackend.system.dto.UpdateMeasurementUnitRequest;

import java.util.List;

/**
 * 计量单位服务接口
 */
public interface MeasurementUnitService {
    
    /**
     * 分页查询计量单位列表
     * @param request 查询请求
     * @return 计量单位分页列表
     */
    PageResponse<MeasurementUnitDTO> findMeasurementUnits(MeasurementUnitQueryRequest request);
    
    /**
     * 查询所有计量单位
     * @return 计量单位列表
     */
    List<MeasurementUnitDTO> findAllMeasurementUnits();
    
    /**
     * 查询所有启用的计量单位
     * @return 计量单位列表
     */
    List<MeasurementUnitDTO> findAllActiveMeasurementUnits();
    
    /**
     * 查询所有尺寸单位
     * @return 计量单位列表
     */
    List<MeasurementUnitDTO> findAllDimensionUnits();
    
    /**
     * 根据ID查询计量单位
     * @param id 计量单位ID
     * @return 计量单位信息
     */
    MeasurementUnitDTO findMeasurementUnitById(Long id);
    
    /**
     * 创建计量单位
     * @param request 创建请求
     * @return 计量单位信息
     */
    MeasurementUnitDTO createMeasurementUnit(CreateMeasurementUnitRequest request);
    
    /**
     * 更新计量单位
     * @param id 计量单位ID
     * @param request 更新请求
     * @return 计量单位信息
     */
    MeasurementUnitDTO updateMeasurementUnit(Long id, UpdateMeasurementUnitRequest request);
    
    /**
     * 删除计量单位
     * @param id 计量单位ID
     */
    void deleteMeasurementUnit(Long id);
    
    /**
     * 批量删除计量单位
     * @param ids 计量单位ID列表
     */
    void batchDeleteMeasurementUnits(List<Long> ids);
    
    /**
     * 切换计量单位状态
     * @param id 计量单位ID
     * @param isActive 是否启用
     * @return 计量单位信息
     */
    MeasurementUnitDTO toggleMeasurementUnitStatus(Long id, boolean isActive);
    
    /**
     * 设置为默认物料单位
     * @param id 计量单位ID
     * @return 计量单位信息
     */
    MeasurementUnitDTO setAsDefaultForNewMaterial(Long id);
    
    /**
     * 设置为默认纸箱尺寸单位
     * @param id 计量单位ID
     * @return 计量单位信息
     */
    MeasurementUnitDTO setAsDefaultDimensionUnit(Long id);
    
    /**
     * 设置为默认纸度单位
     * @param id 计量单位ID
     * @return 计量单位信息
     */
    MeasurementUnitDTO setAsDefaultThicknessUnit(Long id);
    
    /**
     * 设置为默认纸长单位
     * @param id 计量单位ID
     * @return 计量单位信息
     */
    MeasurementUnitDTO setAsDefaultLengthUnit(Long id);
}
