# 销售订单管理API接口文档

## 基础信息

- 基础路径: `/api/sales/orders`
- 认证方式: <PERSON><PERSON>
- 响应格式: 所有接口返回统一的ApiResponse格式
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {} // 实际返回数据
  }
  ```

## 数据结构

### SalesOrderDTO (销售订单基本信息)

```json
{
  "id": "order1",
  "orderNo": "SO2023060100001",
  "productionOrderNo": "PO2023060100001",
  "customerId": "customer1",
  "customerName": "测试客户",
  "customerOrderNo": "CUS-001",
  "orderDate": "2023-06-01",
  "deliveryDate": "2023-06-15",
  "currency": "CNY",
  "salesPerson": "张三",
  "totalAmount": 10500.00,
  "totalWeight": 0,
  "totalArea": 0,
  "totalVolume": 0,
  "taxRate": 0,
  "isTaxed": false,
  "orderStatus": "draft",
  "productionStatus": "pending",
  "inventoryStatus": "pending",
  "deliveryStatus": "pending",
  "lastDeliveryDate": null,
  "lastInventoryDate": null,
  "remark": "测试订单",
  "productionRemark": null,
  "isTerminated": false,
  "createdBy": "admin",
  "createdByName": "管理员",
  "createdTime": "2023-06-01T10:00:00",
  "updatedBy": "admin",
  "updatedByName": "管理员",
  "updatedTime": "2023-06-01T10:00:00",
  "lastPrintTime": null
}
```

### SalesOrderDetailDTO (包含订单明细的完整订单信息)

```json
{
  // 包含SalesOrderDTO的所有字段
  "id": "order1",
  "orderNo": "SO2023060100001",
  // ... 其他订单基本字段

  // 订单明细列表
  "items": [
    {
      "id": "item1",
      "orderId": "order1",
      "productId": "product1",
      "customerProductCode": "CP001",
      "productName": "产品A",
      "processRequirements": "工艺要求",
      "boxType": "盒式A",
      "paperType": "铜版纸",
      "productionPaperType": "生产纸质",
      "specification": "规格A",
      "productionSpecification": "生产规格",
      "quantity": 1000,
      "spareQuantity": 50,
      "unit": "个",
      "price": 10.5,
      "amount": 10500.00,
      "isSpecialPrice": false,
      "spareRatio": null,
      "useInventory": 0,
      "process": null,
      "connectionMethod": null,
      "boardType": null,
      "lineSizeWidth": null,
      "lineSizeLength": null,
      "dieCuttingCount": null,
      "useBoardInventory": 0,
      "unitWeight": null,
      "productArea": null,
      "remark": "明细备注",
      "createdBy": "admin",
      "createdByName": "管理员",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": "admin",
      "updatedByName": "管理员",
      "updatedTime": "2023-06-01T10:00:00"
    }
  ]
}
```

## 1. 订单基础管理

### 1.1 创建销售订单

- **URL**: `/api/sales/orders`
- **方法**: `POST`
- **权限**: `sales:order:create`
- **请求体**:
  ```json
  {
    "orderNo": "SO2023060100001", // 可选，不提供则自动生成
    "productionOrderNo": "PO2023060100001", // 可选
    "customerId": "customer1",
    "customerName": "测试客户",
    "customerOrderNo": "CUS-001", // 可选
    "orderDate": "2023-06-01",
    "deliveryDate": "2023-06-15",
    "currency": "CNY",
    "salesPerson": "张三",
    "remark": "测试订单", // 可选
    "items": [
      {
        "productId": "product1", // 可选
        "customerProductCode": "CP001", // 可选
        "productName": "产品A",
        "processRequirements": "工艺要求", // 可选
        "boxType": "盒式A", // 可选
        "paperType": "铜版纸", // 可选
        "productionPaperType": "生产纸质", // 可选
        "specification": "规格A", // 可选
        "productionSpecification": "生产规格", // 可选
        "quantity": 1000,
        "spareQuantity": 50, // 可选
        "unit": "个",
        "price": 10.5,
        "remark": "明细备注" // 可选
      }
    ]
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": "order1",
      "orderNo": "SO2023060100001",
      "productionOrderNo": "PO2023060100001",
      "customerId": "customer1",
      "customerName": "测试客户",
      "customerOrderNo": "CUS-001",
      "orderDate": "2023-06-01",
      "deliveryDate": "2023-06-15",
      "currency": "CNY",
      "salesPerson": "张三",
      "totalAmount": 10500.00,
      "totalWeight": 0,
      "totalArea": 0,
      "totalVolume": 0,
      "taxRate": 0,
      "taxAmount": 0,
      "totalAmountWithTax": 10500.00,
      "orderStatus": "draft",
      "productionStatus": "pending",
      "inventoryStatus": "pending",
      "deliveryStatus": "pending",
      "remark": "测试订单",
      "createdBy": "admin",
      "createdByName": "管理员",
      "createdTime": "2023-06-01T10:00:00",
      "updatedBy": "admin",
      "updatedByName": "管理员",
      "updatedTime": "2023-06-01T10:00:00",
      "items": [
        {
          "id": "item1",
          "orderId": "order1",
          "productId": "product1",
          "customerProductCode": "CP001",
          "productName": "产品A",
          "processRequirements": "工艺要求",
          "boxType": "盒式A",
          "paperType": "铜版纸",
          "productionPaperType": "生产纸质",
          "specification": "规格A",
          "productionSpecification": "生产规格",
          "quantity": 1000,
          "spareQuantity": 50,
          "unit": "个",
          "price": 10.5,
          "amount": 10500.00,
          "weight": 0,
          "area": 0,
          "volume": 0,
          "remark": "明细备注",
          "createdBy": "admin",
          "createdByName": "管理员",
          "createdTime": "2023-06-01T10:00:00",
          "updatedBy": "admin",
          "updatedByName": "管理员",
          "updatedTime": "2023-06-01T10:00:00"
        }
      ]
    }
  }
  ```

### 1.2 更新销售订单

- **URL**: `/api/sales/orders/{id}`
- **方法**: `PUT`
- **权限**: `sales:order:update`
- **路径参数**: `id` - 订单ID
- **请求体**: 同创建订单，但只能更新草稿状态的订单
- **响应**: 同创建订单

### 1.3 根据ID查询销售订单

- **URL**: `/api/sales/orders/{id}`
- **方法**: `GET`
- **权限**: `sales:order:read`
- **路径参数**: `id` - 订单ID
- **响应**: 同创建订单的响应

### 1.4 根据订单号查询销售订单

- **URL**: `/api/sales/orders/by-order-no/{orderNo}`
- **方法**: `GET`
- **权限**: `sales:order:read`
- **路径参数**: `orderNo` - 订单号
- **响应**: 同创建订单的响应

### 1.5 分页查询销售订单

- **URL**: `/api/sales/orders`
- **方法**: `GET`
- **权限**: `sales:order:read`
- **查询参数**:
  - `page`: 页码，默认1
  - `pageSize`: 每页大小，默认10
  - `keyword`: 关键字搜索（单号、客户名称等）
  - `orderNo`: 订单号
  - `productionOrderNo`: 生产单号
  - `customerId`: 客户ID
  - `customerName`: 客户名称
  - `customerOrderNo`: 客户订单号
  - `salesPerson`: 销售员
  - `orderStatus`: 订单状态
  - `productionStatus`: 生产状态
  - `inventoryStatus`: 入库状态
  - `deliveryStatus`: 送货状态
  - `startDate`: 开始日期
  - `endDate`: 结束日期
  - `sortField`: 排序字段
  - `sortOrder`: 排序方式（asc/desc）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "content": [
        {
          "id": "order1",
          "orderNo": "SO2023060100001",
          "productionOrderNo": "PO2023060100001",
          "customerId": "customer1",
          "customerName": "测试客户",
          "customerOrderNo": "CUS-001",
          "orderDate": "2023-06-01",
          "deliveryDate": "2023-06-15",
          "currency": "CNY",
          "salesPerson": "张三",
          "totalAmount": 10500.00,
          "totalWeight": 0,
          "totalArea": 0,
          "totalVolume": 0,
          "taxRate": 0,
          "taxAmount": 0,
          "totalAmountWithTax": 10500.00,
          "orderStatus": "draft",
          "productionStatus": "pending",
          "inventoryStatus": "pending",
          "deliveryStatus": "pending",
          "remark": "测试订单",
          "createdBy": "admin",
          "createdByName": "管理员",
          "createdTime": "2023-06-01T10:00:00",
          "updatedBy": "admin",
          "updatedByName": "管理员",
          "updatedTime": "2023-06-01T10:00:00"
        }
      ],
      "totalElements": 1,
      "totalPages": 1,
      "size": 10,
      "number": 1,
      "first": true,
      "last": true,
      "empty": false
    }
  }
  ```

### 1.6 删除销售订单

- **URL**: `/api/sales/orders/{id}`
- **方法**: `DELETE`
- **权限**: `sales:order:delete`
- **路径参数**: `id` - 订单ID
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": null
  }
  ```

### 1.7 中止销售订单

- **URL**: `/api/sales/orders/{id}/terminate`
- **方法**: `PUT`
- **权限**: `sales:order:update`
- **路径参数**: `id` - 订单ID
- **响应**: 返回更新后的订单DTO

### 1.8 恢复销售订单

- **URL**: `/api/sales/orders/{id}/restore`
- **方法**: `PUT`
- **权限**: `sales:order:update`
- **路径参数**: `id` - 订单ID
- **响应**: 返回更新后的订单DTO

### 1.9 根据客户ID查询销售订单

- **URL**: `/api/sales/orders/by-customer/{customerId}`
- **方法**: `GET`
- **权限**: `sales:order:read`
- **路径参数**: `customerId` - 客户ID
- **响应**: 返回订单DTO列表

### 1.10 生成订单号

- **URL**: `/api/sales/orders/generate-order-no`
- **方法**: `GET`
- **权限**: `sales:order:create`
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": "SO2023060100001"
  }
  ```

### 1.11 打印订单

- **URL**: `/api/sales/orders/{id}/print`
- **方法**: `PUT`
- **权限**: `sales:order:read`
- **路径参数**: `id` - 订单ID
- **响应**: 返回更新后的订单DTO

## 2. 订单状态管理

### 2.1 更新订单状态

- **URL**: `/api/sales/orders/status/{id}`
- **方法**: `PUT`
- **权限**: `sales:order:update`
- **路径参数**: `id` - 订单ID
- **请求参数**:
  - `status`: 订单状态，例如 "pending"
- **响应**: 返回更新后的订单DTO

### 2.2 更新生产状态

- **URL**: `/api/sales/orders/status/{id}/production`
- **方法**: `PUT`
- **权限**: `sales:order:update`
- **路径参数**: `id` - 订单ID
- **请求参数**:
  - `productionStatus`: 生产状态，例如 "producing"
- **响应**: 返回更新后的订单DTO

### 2.3 提交订单审核

- **URL**: `/api/sales/orders/status/{id}/submit`
- **方法**: `PUT`
- **权限**: `sales:order:update`
- **路径参数**: `id` - 订单ID
- **响应**: 返回更新后的订单DTO

### 2.4 审核通过订单

- **URL**: `/api/sales/orders/status/{id}/approve`
- **方法**: `PUT`
- **权限**: `sales:order:approve`
- **路径参数**: `id` - 订单ID
- **请求参数**:
  - `remark`: 审核通过备注（可选）
- **响应**: 返回更新后的订单DTO

### 2.5 拒绝订单

- **URL**: `/api/sales/orders/status/{id}/reject`
- **方法**: `PUT`
- **权限**: `sales:order:approve`
- **路径参数**: `id` - 订单ID
- **请求参数**:
  - `remark`: 拒绝原因（可选）
- **响应**: 返回更新后的订单DTO

### 2.6 取消订单

- **URL**: `/api/sales/orders/status/{id}/cancel`
- **方法**: `PUT`
- **权限**: `sales:order:update`
- **路径参数**: `id` - 订单ID
- **请求参数**:
  - `remark`: 取消原因（可选）
- **响应**: 返回更新后的订单DTO

### 2.7 开始处理订单

- **URL**: `/api/sales/orders/status/{id}/start-processing`
- **方法**: `PUT`
- **权限**: `sales:order:update`
- **路径参数**: `id` - 订单ID
- **响应**: 返回更新后的订单DTO

### 2.8 完成订单

- **URL**: `/api/sales/orders/status/{id}/complete`
- **方法**: `PUT`
- **权限**: `sales:order:update`
- **路径参数**: `id` - 订单ID
- **响应**: 返回更新后的订单DTO

### 2.9 验证状态转换是否合法

- **URL**: `/api/sales/orders/status/validate`
- **方法**: `GET`
- **权限**: `sales:order:read`
- **请求参数**:
  - `currentStatus`: 当前状态
  - `newStatus`: 新状态
- **响应**: 返回是否合法的布尔值

## 3. 状态枚举值

### 3.1 订单状态(OrderStatus)

- `draft`: 草稿
- `pending`: 待审核
- `approved`: 已审核
- `processing`: 处理中
- `completed`: 已完成
- `canceled`: 已取消
- `rejected`: 已拒绝

### 3.2 生产状态(ProductionStatus)

- `pending`: 待生产
- `producing`: 生产中
- `completed`: 已完成

### 3.3 入库状态(InventoryStatus)

- `pending`: 待入库
- `partial`: 部分入库
- `completed`: 已入库

### 3.4 送货状态(DeliveryStatus)

- `pending`: 待送货
- `partial`: 部分送货
- `completed`: 已送货

## 4. 状态转换规则

### 4.1 订单状态转换规则

- `draft` -> `pending`: 提交审核
- `pending` -> `approved`/`rejected`: 审核通过/拒绝
- `approved` -> `processing`: 开始处理(生产)
- `processing` -> `completed`: 完成处理
- `draft`/`pending` -> `canceled`: 取消订单

### 4.2 生产状态转换规则

- `pending` -> `producing`: 开始生产
- `producing` -> `completed`: 生产完成

### 4.3 入库状态转换规则

- `pending` -> `partial`: 部分入库
- `pending`/`partial` -> `completed`: 完全入库

### 4.4 送货状态转换规则

- `pending` -> `partial`: 部分送货
- `pending`/`partial` -> `completed`: 完全送货
