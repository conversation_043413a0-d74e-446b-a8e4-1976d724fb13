package com.czerp.erpbackend.purchase.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.purchase.dto.CreatePurchaseOrderRequest;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderItemDTO;
import com.czerp.erpbackend.purchase.dto.PurchaseOrderQueryRequest;
import com.czerp.erpbackend.purchase.dto.UpdatePurchaseOrderRequest;

import java.util.List;

/**
 * 采购订单服务接口
 */
public interface PurchaseOrderService {

    /**
     * 分页查询采购订单
     * @param request 查询请求
     * @return 采购订单分页列表
     */
    PageResponse<PurchaseOrderDTO> findPurchaseOrders(PurchaseOrderQueryRequest request);

    /**
     * 分页查询采购订单明细（按明细行级别分页）
     * @param request 查询请求
     * @return 采购订单明细分页列表
     */
    PageResponse<PurchaseOrderItemDTO> findPurchaseOrderItems(PurchaseOrderQueryRequest request);

    /**
     * 根据ID查询采购订单
     * @param id 采购订单ID
     * @return 采购订单
     */
    PurchaseOrderDTO findPurchaseOrderById(Long id);

    /**
     * 创建采购订单
     * @param request 创建请求
     * @return 采购订单
     */
    PurchaseOrderDTO createPurchaseOrder(CreatePurchaseOrderRequest request);

    /**
     * 生成采购单号
     * @return 采购单号
     */
    String generatePurchaseOrderNo();

    /**
     * 更新采购订单
     * @param id 采购订单ID
     * @param request 更新请求
     * @return 采购订单
     */
    PurchaseOrderDTO updatePurchaseOrder(Long id, UpdatePurchaseOrderRequest request);
}
