package com.czerp.erpbackend.system.mapper;

import com.czerp.erpbackend.system.dto.CreateDepartmentRequest;
import com.czerp.erpbackend.system.dto.DepartmentDTO;
import com.czerp.erpbackend.system.dto.UpdateDepartmentRequest;
import com.czerp.erpbackend.system.entity.Department;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 部门映射器
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DepartmentMapper {

    /**
     * 部门实体转DTO
     * @param department 部门实体
     * @return 部门DTO
     */
    @Mapping(target = "createTime", source = "createdTime")
    @Mapping(target = "updateTime", source = "updatedTime")
    @Mapping(target = "parentName", expression = "java(department.getParentId() != null ? department.getParent() != null ? department.getParent().getName() : null : null)")
    @Mapping(target = "managerName", expression = "java(department.getManagerId() != null ? department.getManager() != null ? department.getManager().getNickname() : null : null)")
    @Mapping(target = "children", ignore = true)
    DepartmentDTO toDto(Department department);

    /**
     * 创建部门请求转部门实体
     * @param request 创建部门请求
     * @return 部门实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "level", constant = "1")
    @Mapping(target = "manager", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    Department toEntity(CreateDepartmentRequest request);

    /**
     * 更新部门
     * @param request 更新部门请求
     * @param department 部门实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "level", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "manager", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateDepartmentRequest request, @MappingTarget Department department);

    /**
     * String类型的ID转换为Department对象
     * @param id 部门ID
     * @return 部门对象
     */
    default Department toDepartment(String id) {
        if (id == null) {
            return null;
        }
        Department department = new Department();
        department.setId(id);
        return department;
    }
}