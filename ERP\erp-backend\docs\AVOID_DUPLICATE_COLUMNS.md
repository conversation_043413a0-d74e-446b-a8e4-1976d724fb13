# 避免数据库重复列的最佳实践

## 问题描述

我们在项目中遇到了一个问题：数据库表中出现了重复的列，例如同时存在 `created_by` 和 `CreatedBy`、`is_deleted` 和 `IsDeleted` 等。这些重复列不仅浪费存储空间，还可能导致数据不一致和应用程序错误。

## 问题原因

这个问题的根本原因是：

1. **命名策略配置**：我们使用了 `PhysicalNamingStrategyStandardImpl`，它不会对实体类的字段名进行任何转换，而是直接使用 `@Column` 注解指定的名称作为数据库列名。

2. **重写审计字段**：在某些实体类中，我们重写了 `BaseEntity` 类中的审计字段方法（如 `getCreatedBy()`、`getIsDeleted()` 等），并使用了不同的列名。

3. **Hibernate 自动创建表**：当 Hibernate 自动创建或更新表结构时（`ddl-auto: update`），它会检测到这些不同的列名，并创建多个独立的列。

## 解决方案

为了避免这个问题，我们应该遵循以下最佳实践：

### 1. 统一命名规范

- 所有数据库列名都应该使用小写字母，单词之间使用下划线分隔（snake_case）。
- 所有实体类字段名都应该使用驼峰命名法（camelCase）。
- 在 `@Column` 注解中，使用 `name` 属性明确指定列名，例如：
  ```java
  @Column(name = "created_by", nullable = false, length = 50)
  private String createdBy;
  ```

### 2. 不要重写审计字段

- 不要在实体类中重写 `BaseEntity` 类中的审计字段方法，如 `getCreatedBy()`、`getIsDeleted()` 等。
- 如果需要自定义审计字段的行为，应该在 `BaseEntity` 类中进行修改，而不是在子类中重写。

### 3. 使用数据库迁移工具

- 使用 Flyway 或 Liquibase 等数据库迁移工具来管理数据库结构变更，而不是依赖 Hibernate 的自动创建表功能。
- 在迁移脚本中明确指定列名，确保它们符合命名规范。

### 4. 检查现有代码

- 定期检查实体类，确保没有重写审计字段。
- 定期检查数据库表结构，确保没有重复的列。

## 示例：正确的实体类定义

```java
@Entity
@Table(name = "paper_size_setting")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PaperSizeSetting extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 纸度(inch)
     */
    @Column(name = "paper_size_inch", precision = 10, scale = 2)
    private BigDecimal paperSizeInch;

    // 其他字段...
}
```

## 示例：错误的实体类定义（不要这样做）

```java
@Entity
@Table(name = "paper_size_setting")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PaperSizeSetting extends BaseEntity {

    // ... 其他字段 ...

    /**
     * 重写isDeleted字段，映射到deleted列 - 这会导致重复列！
     */
    @Column(name = "deleted", nullable = false)
    @Override
    public Boolean getIsDeleted() {
        return super.getIsDeleted();
    }
}
```

## 结论

通过遵循这些最佳实践，我们可以避免数据库中出现重复列的问题，确保数据库结构的一致性和应用程序的稳定性。
