+-------------------------------------------+---------------+------+-----+-------------------+-----------------------------+
| Field                                     | Type          | Null | Key | Default           | Extra                       |
+-------------------------------------------+---------------+------+-----+-------------------+-----------------------------+
| id                                        | varchar(36)   | NO   | PRI | NULL              |                             |
| code                                      | varchar(50)   | NO   | UNI | NULL              |                             |
| name                                      | varchar(100)  | NO   |     | NULL              |                             |
| category_id                               | varchar(36)   | YES  | MUL | NULL              |                             |
| spec_id                                   | varchar(36)   | YES  | MUL | NULL              |                             |
| formula                                   | varchar(500)  | YES  |     | NULL              |                             |
| calc_unit                                 | varchar(20)   | YES  |     | NULL              |                             |
| quote_unit                                | varchar(20)   | YES  |     | NULL              |                             |
| connection_method                         | varchar(50)   | YES  |     | NULL              |                             |
| prohibit_double_berth                     | tinyint(1)    | NO   |     | 0                 |                             |
| double_berth_length_threshold             | decimal(10,2) | YES  |     | NULL              |                             |
| jump_tolerance                            | varchar(50)   | YES  |     | NULL              |                             |
| customer_code                             | varchar(50)   | YES  | MUL | NULL              |                             |
| customer_name                             | varchar(100)  | YES  |     | NULL              |                             |
| disabled                                  | tinyint(1)    | NO   | MUL | 0                 |                             |
| graphic                                   | varchar(255)  | YES  |     | NULL              |                             |
| description                               | varchar(500)  | YES  |     | NULL              |                             |
| price                                     | decimal(10,2) | YES  |     | 0.00              |                             |
| created_by                                | varchar(50)   | NO   |     | NULL              |                             |
| created_time                              | datetime      | NO   | MUL | CURRENT_TIMESTAMP | DEFAULT_GENERATED           |
| updated_by                                | varchar(50)   | YES  |     | NULL              |                             |
| updated_time                              | datetime      | YES  |     | NULL              | on update CURRENT_TIMESTAMP |
| is_deleted                                | tinyint(1)    | NO   |     | 0                 |                             |
| version                                   | int           | YES  |     | NULL              |                             |
| image_url                                 | varchar(255)  | YES  |     | NULL              |                             |
| unit                                      | varchar(20)   | NO   |     | NULL              |                             |
| weight_formula                            | varchar(500)  | YES  |     | NULL              |                             |
| area_formula                              | varchar(500)  | YES  |     | NULL              |                             |
| calculate_area_by_formula                 | tinyint(1)    | NO   |     | 0                 |                             |
| order_default_size_unit                   | varchar(20)   | YES  |     | NULL              |                             |
| default_measurement_unit                  | varchar(20)   | YES  |     | NULL              |                             |
| order_quantity_threshold_for_double_berth | int           | YES  |     | NULL              |                             |
| box_type_minimum_price                    | decimal(10,2) | YES  |     | NULL              |                             |
| CreatedBy                                 | varchar(50)   | NO   |     | NULL              |                             |
| IsDeleted                                 | bit(1)        | NO   |     | NULL              |                             |
| UpdatedBy                                 | varchar(50)   | YES  |     | NULL              |                             |
| UpdatedTime                               | datetime(6)   | YES  |     | NULL              |                             |
+-------------------------------------------+---------------+------+-----+-------------------+-----------------------------+