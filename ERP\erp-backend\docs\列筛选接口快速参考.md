# 列筛选接口快速参考

## ⚠️ 重要说明
**销售订单模块继续使用原有架构**，本文档主要用于新模块的筛选功能开发。

## 🚀 快速开始

### 测试接口（验证功能）
```http
# 查看已注册的模块
GET /api/test/filter/modules

# 查看销售订单支持的字段
GET /api/test/filter/fields/sales-order

# 测试获取客户名称筛选选项
GET /api/test/filter/options/sales-order/customerName
```

### 通用筛选接口
```http
# 基础用法
GET /api/common/filter/options?moduleCode=sales-order&fieldName=customerName

# 带搜索
GET /api/common/filter/options?moduleCode=sales-order&fieldName=customerName&searchText=华为

# 批量获取
GET /api/common/filter/batch-options?moduleCode=sales-order&fieldNames=customerName,productName
```

### 销售订单接口（继续使用原有架构）
```http
# 销售订单使用原有接口（稳定架构）
GET /api/sales/orders/query/filter-options?fieldName=customerName

# 注意：销售订单不使用新的通用架构
```

## 📊 销售订单支持的字段

### 主表字段
- `customerName` - 客户名称
- `salesPerson` - 销售员
- `orderNo` - 销售单号

### 明细表字段
- `productName` - 产品名称
- `productionOrderNo` - 生产单号
- `customerOrderNo` - 客户订单号
- `customerProductCode` - 客户产品编码
- `paperType` - 纸质
- `productionPaperType` - 生产纸质
- `processRequirements` - 工艺要求
- `boxType` - 箱型
- `corrugationType` - 瓦楞类型
- `connectionMethod` - 连接方式
- `staplePosition` - 钉位
- `unit` - 单位
- `currency` - 币种

### 子查询字段
- `supplierName` - 采购供应商
- `paperTypeName` - 纸板类别
- `purchaseOrderNo` - 采购单号
- `bindingSpecification` - 合订规格
- `pressSizeWidth` - 压线尺寸
- `processes` - 工序

### 动态字段
- `specification` - 规格（长×宽×高 单位）
- `productionSpecification` - 生产规格（带回退机制）

## 🔧 为新模块集成

### 1. 创建配置类
```java
@Configuration
@RequiredArgsConstructor
public class YourModuleFilterConfig {
    
    private final FilterFieldRegistry filterFieldRegistry;
    public static final String MODULE_CODE = "your-module";
    
    @PostConstruct
    public void registerFields() {
        List<FilterFieldMetadata> fields = List.of(
            FilterFieldMetadata.builder()
                .fieldName("fieldName")
                .entityClass(YourEntity.class)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .enableSearch(true)
                .maxOptions(50)
                .build()
        );
        filterFieldRegistry.registerModuleFields(MODULE_CODE, fields);
    }
}
```

### 2. 创建适配器
```java
@Component
@RequiredArgsConstructor
public class YourModuleFilterAdapter {
    
    private final ColumnFilterService columnFilterService;
    
    public List<YourFilterOptionDTO> getFilterOptions(
            String fieldName, String searchText, YourQueryParamDTO currentFilters) {
        
        Map<String, Object> filterMap = convertToFilterMap(currentFilters);
        List<FilterOptionDTO> options = columnFilterService.getFilterOptions(
                YourModuleFilterConfig.MODULE_CODE, fieldName, searchText, filterMap);
        return convertToYourFilterOptions(options);
    }
}
```

### 3. 创建控制器
```java
@RestController
@RequestMapping("/your-module/filter")
@RequiredArgsConstructor
public class YourModuleFilterController {
    
    private final YourModuleFilterAdapter filterAdapter;
    
    @GetMapping("/options")
    public ApiResponse<List<YourFilterOptionDTO>> getFilterOptions(
            @RequestParam String fieldName,
            @RequestParam(required = false) String searchText,
            YourQueryParamDTO currentFilters) {
        
        List<YourFilterOptionDTO> options = filterAdapter.getFilterOptions(
                fieldName, searchText, currentFilters);
        return ApiResponse.success(options);
    }
}
```

## 📝 响应格式

### 单个字段筛选选项
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "label": "华为技术有限公司",
      "value": "华为技术有限公司"
    },
    {
      "label": "小米科技有限公司",
      "value": "小米科技有限公司"
    }
  ]
}
```

### 批量筛选选项
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "customerName": [
      {"label": "华为技术有限公司", "value": "华为技术有限公司"}
    ],
    "productName": [
      {"label": "手机包装盒", "value": "手机包装盒"}
    ]
  }
}
```

## 🐛 常见问题

### 启动错误
- **路径冲突**: 检查控制器路径映射是否重复
- **包名错误**: 使用`jakarta.persistence`而非`javax.persistence`
- **Bean注入失败**: 确保组件添加了正确的注解

### 运行时问题
- **筛选选项为空**: 检查数据库数据和字段配置
- **级联筛选不生效**: 验证筛选条件传递和字段关联
- **性能问题**: 启用搜索功能，设置maxOptions限制

### 调试方法
```http
# 检查模块注册
GET /api/test/filter/modules

# 检查字段支持
GET /api/test/filter/supported/{moduleCode}/{fieldName}

# 测试筛选选项
GET /api/test/filter/options/{moduleCode}/{fieldName}
```

## 📚 相关文档

- [通用列筛选接口使用指南](./通用列筛选接口使用指南.md) - 完整文档
- [销售订单筛选选项接口使用说明](./销售订单筛选选项接口使用说明.md) - 销售订单专用文档

---

**提示**: 该功能完全向后兼容，不影响现有业务。新模块可直接使用通用接口。
