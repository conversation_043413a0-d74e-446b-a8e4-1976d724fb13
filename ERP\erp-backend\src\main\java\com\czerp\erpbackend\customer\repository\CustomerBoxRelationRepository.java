package com.czerp.erpbackend.customer.repository;

import com.czerp.erpbackend.customer.entity.CustomerBoxRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 客户盒式关联存储库
 */
@Repository
public interface CustomerBoxRelationRepository extends JpaRepository<CustomerBoxRelation, String>, JpaSpecificationExecutor<CustomerBoxRelation> {

    /**
     * 根据客户ID查询关联列表
     * @param customerId 客户ID
     * @return 关联列表
     */
    List<CustomerBoxRelation> findByCustomerId(String customerId);

    /**
     * 根据盒式信息ID查询关联列表
     * @param boxInfoId 盒式信息ID
     * @return 关联列表
     */
    List<CustomerBoxRelation> findByBoxInfoId(String boxInfoId);

    /**
     * 根据客户ID和盒式信息ID查询关联
     * @param customerId 客户ID
     * @param boxInfoId 盒式信息ID
     * @return 关联
     */
    Optional<CustomerBoxRelation> findByCustomerIdAndBoxInfoId(String customerId, String boxInfoId);

    /**
     * 判断客户ID和盒式信息ID的关联是否存在
     * @param customerId 客户ID
     * @param boxInfoId 盒式信息ID
     * @return 是否存在
     */
    boolean existsByCustomerIdAndBoxInfoId(String customerId, String boxInfoId);

    /**
     * 根据客户ID删除关联
     * @param customerId 客户ID
     */
    void deleteByCustomerId(String customerId);

    /**
     * 根据盒式信息ID删除关联
     * @param boxInfoId 盒式信息ID
     */
    void deleteByBoxInfoId(String boxInfoId);

    /**
     * 根据客户ID和盒式信息ID删除关联
     * @param customerId 客户ID
     * @param boxInfoId 盒式信息ID
     */
    void deleteByCustomerIdAndBoxInfoId(String customerId, String boxInfoId);
}
