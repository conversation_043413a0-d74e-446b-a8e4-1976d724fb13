# 前端筛选功能集成指南

## 📋 概述

本文档为前端开发人员提供了完整的销售订单筛选功能集成指南。后端已实现元数据驱动的筛选架构，支持 **17 个字段** 的全量筛选功能。

## 🎯 功能特性

### ✅ 已支持的功能
- **17 个筛选字段**：从原来的 3 个扩展到 17 个
- **动态查询**：所有字段都从实际数据库数据获取选项，返回真实的业务数据
- **级联筛选**：选择一个字段后，其他字段的选项会动态更新
- **搜索功能**：支持在筛选选项中搜索
- **向后兼容**：现有前端代码无需修改

### 🆕 新增字段
除了原有的 `customerName`、`productName`、`salesPerson` 外，新增了：

**新增动态查询字段（14个）：**
- `productionOrderNo` - 生产单号
- `orderNo` - 销售单号
- `customerOrderNo` - 客户订单号
- `customerProductCode` - 客方货号
- `paperType` - 纸质
- `processRequirements` - 工艺要求
- `boxType` - 盒式（返回实际盒式名称，如"天地盖"、"飞机盒"）
- `corrugationType` - 楞别（返回实际楞别，如"A楞"、"B楞"）
- `connectionMethod` - 连接方式（返回实际连接方式）
- `staplePosition` - 钉位（返回实际钉位信息）
- `unit` - 单位（返回实际使用的单位）
- `currency` - 币种（返回实际使用的币种）

**重要说明**：所有字段都返回数据库中实际存储的业务数据，而不是预定义的编码。

## 🔌 API 接口

### 1. 获取筛选选项（主要接口）

```http
GET /api/sales/orders/query/filter-options
```

**请求参数：**
```typescript
interface FilterOptionsRequest {
  fieldName: string;        // 字段名称
  searchText?: string;      // 搜索文本（可选）
  // 当前筛选条件（用于级联筛选）
  filterCustomerNames?: string[];
  filterProductNames?: string[];
  filterSalesPersons?: string[];
  filterProductionOrderNo?: string;
  filterOrderNo?: string;
  filterCustomerOrderNo?: string;
  filterCustomerProductCode?: string;
  // ... 其他筛选条件
}
```

**响应格式：**
```typescript
interface FilterOptionDTO {
  label: string;    // 显示文本
  value: string;    // 选项值
  count: null;      // 计数字段已废弃，固定为null
}

interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}
```

### 2. 使用示例

#### 获取客户名称选项
```javascript
// 获取所有客户名称选项
const response = await fetch('/api/sales/orders/query/filter-options?fieldName=customerName');
const result = await response.json();

// 响应示例
{
  "code": 200,
  "message": "success",
  "data": [
    { "label": "测试客户A", "value": "测试客户A", "count": null },
    { "label": "测试客户B", "value": "测试客户B", "count": null },
    { "label": "测试客户C", "value": "测试客户C", "count": null }
  ]
}
```

#### 获取盒式选项（动态查询）
```javascript
const response = await fetch('/api/sales/orders/query/filter-options?fieldName=boxType');
const result = await response.json();

// 响应示例
{
  "code": 200,
  "message": "success",
  "data": [
    { "label": "天地盖", "value": "天地盖", "count": null },
    { "label": "飞机盒", "value": "飞机盒", "count": null },
    { "label": "手提袋", "value": "手提袋", "count": null }
  ]
}
```

#### 带搜索的选项获取
```javascript
const response = await fetch('/api/sales/orders/query/filter-options?fieldName=customerName&searchText=测试');
```

#### 级联筛选示例
```javascript
// 当用户选择了客户后，获取该客户的产品选项
const currentFilters = {
  filterCustomerNames: ['测试客户A']
};

const response = await fetch('/api/sales/orders/query/filter-options', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    fieldName: 'productName',
    ...currentFilters
  })
});
```

## 🎨 前端实现建议

### 1. 筛选字段配置

```typescript
// 筛选字段配置
export const FILTER_FIELDS = {
  // 动态查询字段
  customerName: { 
    label: '客户名称', 
    type: 'dynamic', 
    searchable: true 
  },
  productName: { 
    label: '产品名称', 
    type: 'dynamic', 
    searchable: true 
  },
  salesPerson: { 
    label: '销售员', 
    type: 'dynamic', 
    searchable: true 
  },
  productionOrderNo: { 
    label: '生产单号', 
    type: 'dynamic', 
    searchable: true 
  },
  orderNo: { 
    label: '销售单号', 
    type: 'dynamic', 
    searchable: true 
  },
  customerOrderNo: { 
    label: '客户订单号', 
    type: 'dynamic', 
    searchable: true 
  },
  customerProductCode: { 
    label: '客方货号', 
    type: 'dynamic', 
    searchable: true 
  },
  paperType: { 
    label: '纸质', 
    type: 'dynamic', 
    searchable: true 
  },
  processRequirements: { 
    label: '工艺要求', 
    type: 'dynamic', 
    searchable: true 
  },
  
  // 静态字典字段
  boxType: { 
    label: '盒式', 
    type: 'dictionary', 
    searchable: true 
  },
  corrugationType: { 
    label: '楞别', 
    type: 'dictionary', 
    searchable: true 
  },
  connectionMethod: { 
    label: '连接方式', 
    type: 'dictionary', 
    searchable: false 
  },
  staplePosition: { 
    label: '钉位', 
    type: 'dictionary', 
    searchable: false 
  },
  unit: { 
    label: '单位', 
    type: 'dictionary', 
    searchable: false 
  },
  currency: { 
    label: '币种', 
    type: 'dictionary', 
    searchable: false 
  }
};
```

### 2. 筛选组件实现

```vue
<template>
  <div class="filter-dropdown">
    <!-- 搜索框 -->
    <a-input 
      v-if="config.searchable"
      v-model:value="searchText"
      placeholder="搜索..."
      @input="onSearch"
      class="filter-search"
    />
    
    <!-- 选项列表 -->
    <a-checkbox-group 
      v-model:value="selectedValues"
      @change="onSelectionChange"
    >
      <div class="filter-options">
        <a-checkbox 
          v-for="option in filteredOptions" 
          :key="option.value"
          :value="option.value"
          class="filter-option"
        >
          <span class="option-label">{{ option.value }}</span>
          <!-- 计数显示已移除 -->
        </a-checkbox>
      </div>
    </a-checkbox-group>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { FILTER_FIELDS } from './config';

interface Props {
  fieldName: string;
  currentFilters: Record<string, any>;
}

const props = defineProps<Props>();
const emit = defineEmits(['change']);

const searchText = ref('');
const selectedValues = ref<string[]>([]);
const options = ref<FilterOptionDTO[]>([]);
const loading = ref(false);

const config = computed(() => FILTER_FIELDS[props.fieldName]);

// 获取筛选选项
const fetchOptions = async () => {
  loading.value = true;
  try {
    const params = new URLSearchParams({
      fieldName: props.fieldName,
      ...(searchText.value && { searchText: searchText.value })
    });
    
    const response = await fetch(`/api/sales/orders/query/filter-options?${params}`);
    const result = await response.json();
    
    if (result.code === 200) {
      options.value = result.data;
    }
  } catch (error) {
    console.error('获取筛选选项失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const onSearch = debounce(() => {
  fetchOptions();
}, 300);

// 选择变化处理
const onSelectionChange = (values: string[]) => {
  emit('change', {
    fieldName: props.fieldName,
    values
  });
};

// 监听当前筛选条件变化，重新获取选项（级联筛选）
watch(() => props.currentFilters, () => {
  fetchOptions();
}, { deep: true });

// 初始化
onMounted(() => {
  fetchOptions();
});
</script>
```

### 3. 表格列筛选集成

```vue
<template>
  <a-table 
    :columns="columns" 
    :data-source="dataSource"
    :loading="loading"
  >
    <!-- 自定义筛选列头 -->
    <template #customFilterDropdown="{ column }">
      <FilterDropdown
        :field-name="column.dataIndex"
        :current-filters="currentFilters"
        @change="onFilterChange"
      />
    </template>
  </a-table>
</template>

<script setup lang="ts">
// 表格列配置
const columns = [
  {
    title: '客户名称',
    dataIndex: 'customerName',
    customFilterDropdown: true,
    onFilter: (value, record) => record.customerName.includes(value),
  },
  {
    title: '产品名称', 
    dataIndex: 'productName',
    customFilterDropdown: true,
    onFilter: (value, record) => record.productName.includes(value),
  },
  {
    title: '盒式',
    dataIndex: 'boxType', 
    customFilterDropdown: true,
    onFilter: (value, record) => record.boxType === value,
  },
  // ... 其他列
];

const currentFilters = ref({});

const onFilterChange = (filterData) => {
  // 更新筛选条件
  const filterKey = `filter${capitalize(filterData.fieldName)}s`;
  currentFilters.value[filterKey] = filterData.values;
  
  // 重新查询数据
  fetchTableData();
};
</script>
```

## 📝 注意事项

### 1. **性能优化**
- 使用防抖处理搜索输入
- 限制选项数量显示（后端已限制50个）
- 考虑虚拟滚动处理大量选项

### 2. **用户体验**
- 显示加载状态
- 提供清空筛选功能
- 显示当前筛选条件数量

### 3. **错误处理**
```javascript
try {
  const response = await fetch('/api/sales/orders/query/filter-options?fieldName=customerName');
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const result = await response.json();
  
  if (result.code !== 200) {
    throw new Error(result.message || '获取筛选选项失败');
  }
  
  return result.data;
} catch (error) {
  console.error('筛选选项获取失败:', error);
  // 显示用户友好的错误信息
  message.error('获取筛选选项失败，请稍后重试');
  return [];
}
```

### 4. **类型定义**
```typescript
// 筛选选项类型
interface FilterOptionDTO {
  value: string;
  count: number;
}

// 筛选参数类型
interface SalesOrderQueryParamDTO {
  // 文本筛选
  filterProductionOrderNo?: string;
  filterOrderNo?: string;
  filterCustomerOrderNo?: string;
  filterCustomerProductCode?: string;
  
  // 多选筛选
  filterCustomerNames?: string[];
  filterProductNames?: string[];
  filterSalesPersons?: string[];
  
  // 数字范围筛选
  filterQuantityMin?: number;
  filterQuantityMax?: number;
  filterTotalAmountMin?: number;
  filterTotalAmountMax?: number;
  
  // 日期范围筛选
  filterOrderDateStart?: string;
  filterOrderDateEnd?: string;
  filterDeliveryDateStart?: string;
  filterDeliveryDateEnd?: string;
}
```

## 🚀 快速开始

1. **更新现有筛选组件**：在现有的客户名称、产品名称、销售员筛选基础上，添加新的字段支持

2. **测试新字段**：使用测试接口验证新字段的筛选选项获取

3. **逐步迁移**：可以逐个字段添加筛选功能，不需要一次性全部实现

4. **性能监控**：关注筛选选项获取的响应时间，必要时添加缓存

## 🔧 实用工具函数

### 1. 筛选选项获取工具

```typescript
// utils/filterUtils.ts
export class FilterUtils {
  private static baseUrl = '/api/sales/orders/query';

  /**
   * 获取筛选选项
   */
  static async getFilterOptions(
    fieldName: string,
    searchText?: string,
    currentFilters?: Record<string, any>
  ): Promise<FilterOptionDTO[]> {
    try {
      const params = new URLSearchParams({ fieldName });
      if (searchText) params.append('searchText', searchText);

      const url = `${this.baseUrl}/filter-options?${params}`;
      const response = await fetch(url, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result.code === 200 ? result.data : [];
    } catch (error) {
      console.error(`获取${fieldName}筛选选项失败:`, error);
      return [];
    }
  }

  /**
   * 批量获取多个字段的筛选选项
   */
  static async getBatchFilterOptions(
    fieldNames: string[],
    currentFilters?: Record<string, any>
  ): Promise<Record<string, FilterOptionDTO[]>> {
    const promises = fieldNames.map(fieldName =>
      this.getFilterOptions(fieldName, undefined, currentFilters)
        .then(options => ({ fieldName, options }))
    );

    const results = await Promise.allSettled(promises);
    const optionsMap: Record<string, FilterOptionDTO[]> = {};

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        optionsMap[result.value.fieldName] = result.value.options;
      } else {
        console.error(`获取${fieldNames[index]}选项失败:`, result.reason);
        optionsMap[fieldNames[index]] = [];
      }
    });

    return optionsMap;
  }

  /**
   * 格式化筛选参数
   */
  static formatFilterParams(filters: Record<string, string[]>): Record<string, any> {
    const formatted: Record<string, any> = {};

    Object.entries(filters).forEach(([fieldName, values]) => {
      if (values && values.length > 0) {
        // 转换为后端期望的参数名格式
        const paramName = `filter${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}s`;
        formatted[paramName] = values;
      }
    });

    return formatted;
  }
}
```

### 2. 防抖工具

```typescript
// utils/debounce.ts
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
```

## 📊 完整示例：销售订单筛选页面

```vue
<template>
  <div class="sales-order-filter-page">
    <!-- 筛选区域 -->
    <a-card title="筛选条件" class="filter-card">
      <a-row :gutter="[16, 16]">
        <a-col
          v-for="(config, fieldName) in FILTER_FIELDS"
          :key="fieldName"
          :span="6"
        >
          <div class="filter-item">
            <label class="filter-label">{{ config.label }}</label>
            <a-select
              v-model:value="filters[fieldName]"
              mode="multiple"
              :placeholder="`选择${config.label}`"
              :loading="loadingStates[fieldName]"
              :show-search="config.searchable"
              :filter-option="false"
              @search="(value) => onSearch(fieldName, value)"
              @change="onFilterChange"
              class="filter-select"
            >
              <a-select-option
                v-for="option in filterOptions[fieldName]"
                :key="option.value"
                :value="option.value"
              >
                {{ option.value }}
                <!-- 计数显示已移除 -->
              </a-select-option>
            </a-select>
          </div>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <div class="filter-actions">
        <a-button type="primary" @click="applyFilters" :loading="tableLoading">
          应用筛选
        </a-button>
        <a-button @click="clearFilters">
          清空筛选
        </a-button>
        <a-tag v-if="activeFilterCount > 0" color="blue">
          已选择 {{ activeFilterCount }} 个筛选条件
        </a-tag>
      </div>
    </a-card>

    <!-- 数据表格 -->
    <a-card title="销售订单列表" class="table-card">
      <a-table
        :columns="tableColumns"
        :data-source="tableData"
        :loading="tableLoading"
        :pagination="pagination"
        @change="onTableChange"
        row-key="id"
      >
        <!-- 自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'specification'">
            <span>{{ record.specification || '-' }}</span>
          </template>
          <template v-if="column.dataIndex === 'amount'">
            <span>{{ formatCurrency(record.amount, record.currency) }}</span>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { FilterUtils, debounce } from '@/utils';
import { FILTER_FIELDS } from './config';

// 响应式数据
const filters = reactive<Record<string, string[]>>({});
const filterOptions = reactive<Record<string, FilterOptionDTO[]>>({});
const loadingStates = reactive<Record<string, boolean>>({});
const tableData = ref([]);
const tableLoading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 计算属性
const activeFilterCount = computed(() => {
  return Object.values(filters).filter(values => values && values.length > 0).length;
});

// 初始化筛选选项
const initFilterOptions = async () => {
  const fieldNames = Object.keys(FILTER_FIELDS);

  // 设置加载状态
  fieldNames.forEach(fieldName => {
    loadingStates[fieldName] = true;
    filters[fieldName] = [];
  });

  try {
    const optionsMap = await FilterUtils.getBatchFilterOptions(fieldNames);
    Object.assign(filterOptions, optionsMap);
  } catch (error) {
    message.error('初始化筛选选项失败');
  } finally {
    fieldNames.forEach(fieldName => {
      loadingStates[fieldName] = false;
    });
  }
};

// 搜索处理（防抖）
const onSearch = debounce(async (fieldName: string, searchText: string) => {
  if (!FILTER_FIELDS[fieldName]?.searchable) return;

  loadingStates[fieldName] = true;
  try {
    const currentFilterParams = FilterUtils.formatFilterParams(filters);
    const options = await FilterUtils.getFilterOptions(fieldName, searchText, currentFilterParams);
    filterOptions[fieldName] = options;
  } catch (error) {
    message.error(`搜索${FILTER_FIELDS[fieldName].label}失败`);
  } finally {
    loadingStates[fieldName] = false;
  }
}, 300);

// 筛选变化处理
const onFilterChange = async () => {
  // 级联更新其他字段的选项
  const currentFilterParams = FilterUtils.formatFilterParams(filters);
  const fieldNames = Object.keys(FILTER_FIELDS);

  for (const fieldName of fieldNames) {
    if (loadingStates[fieldName]) continue;

    loadingStates[fieldName] = true;
    try {
      const options = await FilterUtils.getFilterOptions(fieldName, undefined, currentFilterParams);
      filterOptions[fieldName] = options;
    } catch (error) {
      console.error(`更新${fieldName}选项失败:`, error);
    } finally {
      loadingStates[fieldName] = false;
    }
  }
};

// 应用筛选
const applyFilters = async () => {
  pagination.current = 1; // 重置到第一页
  await fetchTableData();
};

// 清空筛选
const clearFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = [];
  });
  applyFilters();
};

// 获取表格数据
const fetchTableData = async () => {
  tableLoading.value = true;
  try {
    const filterParams = FilterUtils.formatFilterParams(filters);
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filterParams
    };

    const response = await fetch('/api/sales/orders/query', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params)
    });

    const result = await response.json();
    if (result.code === 200) {
      tableData.value = result.data.records;
      pagination.total = result.data.totalElements;
    } else {
      message.error(result.message || '查询失败');
    }
  } catch (error) {
    message.error('查询销售订单失败');
    console.error('查询失败:', error);
  } finally {
    tableLoading.value = false;
  }
};

// 表格变化处理
const onTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchTableData();
};

// 格式化货币
const formatCurrency = (amount: number, currency: string) => {
  if (!amount) return '-';
  return `${amount.toLocaleString()} ${currency || 'CNY'}`;
};

// 表格列配置
const tableColumns = [
  { title: '销售单号', dataIndex: 'orderNo', width: 120 },
  { title: '生产单号', dataIndex: 'productionOrderNo', width: 120 },
  { title: '客户名称', dataIndex: 'customerName', width: 150 },
  { title: '产品名称', dataIndex: 'productName', width: 200 },
  { title: '规格', dataIndex: 'specification', width: 150 },
  { title: '数量', dataIndex: 'quantity', width: 80 },
  { title: '金额', dataIndex: 'amount', width: 120 },
  { title: '销售员', dataIndex: 'salesPerson', width: 100 },
  { title: '订单日期', dataIndex: 'orderDate', width: 120 },
];

// 生命周期
onMounted(() => {
  initFilterOptions();
  fetchTableData();
});
</script>

<style scoped>
.sales-order-filter-page {
  padding: 16px;
}

.filter-card {
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-label {
  margin-bottom: 8px;
  font-weight: 500;
}

.filter-select {
  width: 100%;
}

.filter-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
  align-items: center;
}

.option-count {
  color: #999;
  font-size: 12px;
}

.table-card {
  min-height: 400px;
}
</style>
```

## 🎯 最佳实践总结

1. **渐进式实现**：先实现核心字段，再逐步添加其他字段
2. **性能优化**：使用防抖、批量请求、虚拟滚动等技术
3. **用户体验**：提供加载状态、错误提示、清空功能
4. **类型安全**：使用 TypeScript 确保类型正确性
5. **错误处理**：完善的异常捕获和用户友好提示

现在你可以为销售订单查询页面提供强大的全量筛选功能了！🎉
