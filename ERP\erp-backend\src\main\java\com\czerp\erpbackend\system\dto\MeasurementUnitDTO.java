package com.czerp.erpbackend.system.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 计量单位DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeasurementUnitDTO {

    /**
     * 单位ID
     */
    private Long id;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 新建物料时默认此单位
     */
    private Boolean isDefaultForNewMaterial;

    /**
     * 纸箱/纸板尺寸单位
     */
    private Boolean isDimensionUnit;

    /**
     * 默认纸箱尺寸单位
     */
    private Boolean isDefaultDimensionUnit;

    /**
     * 默认纸度单位
     */
    private Boolean isDefaultThicknessUnit;

    /**
     * 默认纸长单位
     */
    private Boolean isDefaultLengthUnit;

    /**
     * 状态(active-启用,inactive-停用)
     */
    private String status;

    /**
     * 是否启用（兼容前端）
     */
    private Boolean isActive;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 创建时间（兼容前端）
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间（兼容前端）
     */
    private LocalDateTime updatedAt;
}
