package com.czerp.erpbackend.sales.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 销售订单明细DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SalesOrderItemDTO {

    /**
     * 明细ID
     */
    private String id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 生产单号
     */
    private String productionOrderNo;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 客方货号
     */
    private String customerProductCode;

    /**
     * 品名
     */
    private String productName;

    /**
     * 工艺要求
     */
    private String processRequirements;

    /**
     * 是否含税
     */
    private Boolean isTaxed;

    /**
     * 盒式
     */
    private String boxType;

    /**
     * 纸质
     */
    private String paperType;

    /**
     * 楞别
     */
    private String corrugationType;

    /**
     * 生产纸质
     */
    private String productionPaperType;

    /**
     * 长
     */
    private BigDecimal length;

    /**
     * 宽
     */
    private BigDecimal width;

    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 尺寸单位
     */
    private String sizeUnit;

    /**
     * 规格（长×宽×高）
     */
    private String specification;

    /**
     * 生产规格（生产长×生产宽×生产高）
     */
    private String productionSpecification;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 备品数
     */
    private Integer spareQuantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 送货日期
     */
    private LocalDate deliveryDate;

    /**
     * 特价
     */
    private Boolean isSpecialPrice;

    /**
     * 纸质报价
     */
    private BigDecimal paperQuotation;

    /**
     * 连接方式
     */
    private String connectionMethod;

    /**
     * 钉口
     */
    private String staplePosition;

    /**
     * 包装数
     */
    private Integer packagingCount;

    /**
     * 生产备注
     */
    private String productionRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 生产长
     */
    private BigDecimal productionLength;

    /**
     * 生产宽
     */
    private BigDecimal productionWidth;

    /**
     * 生产高
     */
    private BigDecimal productionHeight;

    /**
     * 成套比例
     */
    private BigDecimal spareRatio;

    /**
     * 成套数量
     */
    private Integer spareQuantityTotal;

    /**
     * 当前库存
     */
    private Integer currentInventory;

    /**
     * 可用库存
     */
    private Integer availableInventory;

    /**
     * 使用库存
     */
    private Integer useInventory;

    /**
     * 特殊规格
     */
    private Boolean isSpecialSpecification;

    /**
     * 单重
     */
    private BigDecimal unitWeight;

    /**
     * 总重(KG)
     */
    private BigDecimal totalWeight;

    /**
     * 产品面积
     */
    private BigDecimal productArea;

    /**
     * 总面积(平米)
     */
    private BigDecimal totalArea;

    /**
     * 产品体积
     */
    private BigDecimal productVolume;

    /**
     * 总体积(立方米)
     */
    private BigDecimal totalVolume;

    /**
     * 部件
     */
    private String component;

    /**
     * 样品
     */
    private Boolean isSample;

    /**
     * 已送货数
     */
    private Integer deliveredQuantity;

    /**
     * 已送备品数
     */
    private Integer deliveredSpareQuantity;

    /**
     * 退货数
     */
    private Integer returnedQuantity;

    /**
     * 安全库存数
     */
    private Integer safetyStock;

    /**
     * 已对账数
     */
    private Integer reconciliationQuantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 币种
     */
    private String currency;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新人姓名
     */
    private String updatedByName;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 材料信息列表
     * 非数据库字段，用于前端展示和提交
     */
    private List<SalesOrderMaterialDTO> materials = new ArrayList<>();

    /**
     * 工序信息列表
     * 非数据库字段，用于前端展示和提交
     */
    private List<SalesOrderProcessDTO> processes = new ArrayList<>();
}
