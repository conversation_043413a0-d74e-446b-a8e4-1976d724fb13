package com.czerp.erpbackend.system.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.system.dto.CreateDepartmentRequest;
import com.czerp.erpbackend.system.dto.DepartmentDTO;
import com.czerp.erpbackend.system.dto.UpdateDepartmentRequest;
import com.czerp.erpbackend.system.dto.UserDTO;

import java.util.List;

/**
 * 部门服务接口
 */
public interface DepartmentService {
    
    /**
     * 获取部门树
     * @return 部门树
     */
    List<DepartmentDTO> getDepartmentTree();
    
    /**
     * 根据ID查询部门
     * @param id 部门ID
     * @return 部门信息
     */
    DepartmentDTO findDepartmentById(String id);
    
    /**
     * 创建部门
     * @param request 创建部门请求
     * @return 部门信息
     */
    DepartmentDTO createDepartment(CreateDepartmentRequest request);
    
    /**
     * 更新部门
     * @param id 部门ID
     * @param request 更新部门请求
     * @return 部门信息
     */
    DepartmentDTO updateDepartment(String id, UpdateDepartmentRequest request);
    
    /**
     * 删除部门
     * @param id 部门ID
     */
    void deleteDepartment(String id);
    
    /**
     * 更改部门状态
     * @param id 部门ID
     * @param status 状态
     * @return 部门信息
     */
    DepartmentDTO changeStatus(String id, String status);
    
    /**
     * 获取部门成员
     * @param id 部门ID
     * @param page 页码
     * @param size 每页大小
     * @return 用户分页列表
     */
    PageResponse<UserDTO> getDepartmentMembers(String id, int page, int size);
} 