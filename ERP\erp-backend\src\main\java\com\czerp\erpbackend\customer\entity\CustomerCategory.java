package com.czerp.erpbackend.customer.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 客户分类实体
 */
@Entity
@Table(name = "cus_customer_category")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerCategory extends BaseEntity {

    /**
     * 分类ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 分类编码
     */
    @Column(name = "category_code", length = 50, nullable = false, unique = true)
    private String categoryCode;

    /**
     * 分类名称
     */
    @Column(name = "category_name", length = 100, nullable = false)
    private String categoryName;

    /**
     * 排序
     */
    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    /**
     * 备注
     */
    @Column(name = "remark", length = 255)
    private String remark;

    /**
     * 状态(active-启用,inactive-停用)
     */
    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";

    /**
     * 新建客户时默认属于此分类
     */
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;
}
