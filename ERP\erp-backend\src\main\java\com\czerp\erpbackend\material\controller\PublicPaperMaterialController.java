package com.czerp.erpbackend.material.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.material.dto.PaperMaterialDTO;
import com.czerp.erpbackend.material.service.PaperMaterialService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 纸质资料公共控制器
 * 提供给其他模块使用的公共接口，不需要权限验证
 */
@RestController
@RequestMapping("/public/material/paper-materials")
@Tag(name = "纸质资料公共接口", description = "纸质资料公共接口，不需要权限验证")
@RequiredArgsConstructor
@Slf4j
public class PublicPaperMaterialController {

    private final PaperMaterialService paperMaterialService;

    /**
     * 查询所有未停用的纸质资料
     * @return 纸质资料列表
     */
    @GetMapping
    @Operation(summary = "查询所有未停用的纸质资料", description = "查询所有未停用的纸质资料，供其他模块使用")
    public ResponseEntity<ApiResponse<List<PaperMaterialDTO>>> findActivePaperMaterials() {
        log.debug("Finding all active paper materials (public)");
        List<PaperMaterialDTO> response = paperMaterialService.findActivePaperMaterials();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 查询所有标准纸质资料
     * @return 纸质资料列表
     */
    @GetMapping("/standard")
    @Operation(summary = "查询所有标准纸质资料", description = "查询所有标准纸质资料，供其他模块使用")
    public ResponseEntity<ApiResponse<List<PaperMaterialDTO>>> findStandardPaperMaterials() {
        log.debug("Finding all standard paper materials (public)");
        List<PaperMaterialDTO> response = paperMaterialService.findStandardPaperMaterials();
        return ResponseEntity.ok(ApiResponse.success(response));
    }
}
