package com.czerp.erpbackend.purchase.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

/**
 * 供应商实体
 */
@Entity
@Table(name = "pur_supplier")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Supplier extends BaseEntity {

    /**
     * 供应商ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 供应商编码
     */
    @Column(name = "supplier_code", length = 50, nullable = false, unique = true)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Column(name = "supplier_name", length = 100, nullable = false)
    private String supplierName;

    /**
     * 供应商全称
     */
    @Column(name = "full_name", length = 200)
    private String fullName;

    /**
     * 供应商分类ID
     */
    @Column(name = "category_id", length = 36)
    private String categoryId;

    /**
     * 供应商分类
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", insertable = false, updatable = false)
    private SupplierCategory category;

    /**
     * 行业
     */
    @Column(name = "industry", length = 100)
    private String industry;

    /**
     * 电话
     */
    @Column(name = "phone", length = 20)
    private String phone;

    /**
     * 传真
     */
    @Column(name = "fax", length = 20)
    private String fax;

    /**
     * 联系人
     */
    @Column(name = "contact_person", length = 50)
    private String contactPerson;

    /**
     * 手机
     */
    @Column(name = "mobile", length = 20)
    private String mobile;

    /**
     * 地址
     */
    @Column(name = "address", length = 200)
    private String address;

    /**
     * 地区
     */
    @Column(name = "region", length = 100)
    private String region;

    /**
     * 最近下单日期
     */
    @Column(name = "last_order_date")
    private LocalDate lastOrderDate;

    /**
     * 最近收货日期
     */
    @Column(name = "last_receive_date")
    private LocalDate lastReceiveDate;

    /**
     * 单价小数位
     */
    @Column(name = "price_decimal_places", nullable = false)
    private Integer priceDecimalPlaces = 2;

    /**
     * 金额小数位
     */
    @Column(name = "amount_decimal_places", nullable = false)
    private Integer amountDecimalPlaces = 2;

    /**
     * 单重小数位
     */
    @Column(name = "unit_weight_decimal_places", nullable = false)
    private Integer unitWeightDecimalPlaces = 3;

    /**
     * 总重小数位
     */
    @Column(name = "total_weight_decimal_places", nullable = false)
    private Integer totalWeightDecimalPlaces = 3;

    /**
     * 未下单天数
     */
    @Column(name = "days_without_order")
    private Integer daysWithoutOrder;

    /**
     * 未收货天数
     */
    @Column(name = "days_without_receive")
    private Integer daysWithoutReceive;

    /**
     * 税号
     */
    @Column(name = "tax_number", length = 50)
    private String taxNumber;

    /**
     * 开户行
     */
    @Column(name = "bank_name", length = 100)
    private String bankName;

    /**
     * 银行账号
     */
    @Column(name = "bank_account", length = 50)
    private String bankAccount;

    /**
     * 付款条件
     */
    @Column(name = "payment_terms", length = 50)
    private String paymentTerms;

    /**
     * 状态(active-启用,inactive-停用)
     */
    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";

    /**
     * 备注
     */
    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;
}
