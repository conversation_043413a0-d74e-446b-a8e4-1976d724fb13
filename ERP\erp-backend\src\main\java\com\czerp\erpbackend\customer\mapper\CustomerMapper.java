package com.czerp.erpbackend.customer.mapper;

import com.czerp.erpbackend.customer.dto.CreateCustomerRequest;
import com.czerp.erpbackend.customer.dto.CustomerDTO;
import com.czerp.erpbackend.customer.dto.UpdateCustomerRequest;
import com.czerp.erpbackend.customer.entity.Customer;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 客户Mapper
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomerMapper {

    /**
     * 实体转DTO
     * @param customer 实体
     * @return DTO
     */
    @Mapping(source = "category.categoryName", target = "categoryName")
    CustomerDTO toDto(Customer customer);

    /**
     * 创建请求转实体
     * @param request 创建请求
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "lastOrderDate", ignore = true)
    @Mapping(target = "currentMonthOrderAmount", ignore = true)
    @Mapping(target = "lastDeliveryDate", ignore = true)
    @Mapping(target = "daysWithoutOrder", ignore = true)
    @Mapping(target = "daysWithoutDelivery", ignore = true)
    @Mapping(target = "firstOrderDate", ignore = true)
    @Mapping(target = "firstDeliveryDate", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    Customer toEntity(CreateCustomerRequest request);

    /**
     * 更新实体
     * @param request 更新请求
     * @param customer 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "customerCode", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "lastOrderDate", ignore = true)
    @Mapping(target = "currentMonthOrderAmount", ignore = true)
    @Mapping(target = "lastDeliveryDate", ignore = true)
    @Mapping(target = "daysWithoutOrder", ignore = true)
    @Mapping(target = "daysWithoutDelivery", ignore = true)
    @Mapping(target = "firstOrderDate", ignore = true)
    @Mapping(target = "firstDeliveryDate", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    void updateEntity(UpdateCustomerRequest request, @MappingTarget Customer customer);
}
