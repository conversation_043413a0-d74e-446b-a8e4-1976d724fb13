package com.czerp.erpbackend.customer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 客户DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerDTO {

    /**
     * 客户ID
     */
    private String id;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户分类ID
     */
    private String categoryId;

    /**
     * 客户分类名称
     */
    private String categoryName;

    /**
     * 客户全称
     */
    private String fullName;

    /**
     * 电话
     */
    private String phone;

    /**
     * 传真
     */
    private String fax;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 地址
     */
    private String address;

    /**
     * 收货地址
     */
    private String shippingAddress;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 税率%
     */
    private BigDecimal taxRate;

    /**
     * 最近下单日期
     */
    private LocalDate lastOrderDate;

    /**
     * 当月接单金额
     */
    private BigDecimal currentMonthOrderAmount;

    /**
     * 所属总公司
     */
    private String parentCompany;

    /**
     * 地区
     */
    private String region;

    /**
     * 销售员
     */
    private String salesPerson;

    /**
     * 跟单员
     */
    private String orderTracker;

    /**
     * 邮编
     */
    private String postalCode;

    /**
     * 收货人
     */
    private String receiver;

    /**
     * 币别
     */
    private String currency;

    /**
     * 默认报价单位
     */
    private String defaultQuoteUnit;

    /**
     * 订单备品比例%
     */
    private BigDecimal orderSpareRatio;

    /**
     * 单价小数位
     */
    private Integer priceDecimalPlaces;

    /**
     * 金额小数位
     */
    private Integer amountDecimalPlaces;

    /**
     * 单重小数位
     */
    private Integer unitWeightDecimalPlaces;

    /**
     * 总重小数位
     */
    private Integer totalWeightDecimalPlaces;

    /**
     * 单位面积小数位
     */
    private Integer unitAreaDecimalPlaces;

    /**
     * 总面积小数位
     */
    private Integer totalAreaDecimalPlaces;

    /**
     * 特殊月结日期
     */
    private Integer specialMonthlySettlementDate;

    /**
     * 可用额度金额
     */
    private BigDecimal availableCreditAmount;

    /**
     * 最近送货日期
     */
    private LocalDate lastDeliveryDate;

    /**
     * 未下单天数
     */
    private Integer daysWithoutOrder;

    /**
     * 未送货天数
     */
    private Integer daysWithoutDelivery;

    /**
     * 首次订单日期
     */
    private LocalDate firstOrderDate;

    /**
     * 首次送货日期
     */
    private LocalDate firstDeliveryDate;

    /**
     * 打印抬头
     */
    private String printHeader;

    /**
     * 停用
     */
    private Boolean disabled;

    /**
     * 行业
     */
    private String industry;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
