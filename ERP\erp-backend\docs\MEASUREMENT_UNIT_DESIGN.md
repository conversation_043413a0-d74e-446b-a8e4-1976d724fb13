# 计量单位表设计和迁移方案

## 表设计

根据需求，我们设计了一个使用小写下划线命名的计量单位表 `measurement_unit`，包含以下字段：

| 字段名                      | 类型         | 说明                     | 是否必填 | 默认值    |
|----------------------------|--------------|-------------------------|---------|-----------|
| id                         | BIGINT       | 主键ID                   | 是      | 自增      |
| unit_name                  | VARCHAR(50)  | 单位名称                 | 是      | 无        |
| sort_order                 | INT          | 排序                     | 是      | 999       |
| is_default_for_new_material| BOOLEAN      | 新建物料默认单位         | 是      | FALSE     |
| is_dimension_unit          | BOOLEAN      | 纸箱/纸板尺寸单位        | 是      | FALSE     |
| is_default_dimension_unit  | BOOLEAN      | 默认纸箱尺寸单位         | 是      | FALSE     |
| is_default_thickness_unit  | BOOLEAN      | 默认纸度单位             | 是      | FALSE     |
| is_default_length_unit     | BOOLEAN      | 默认纸长单位             | 是      | FALSE     |
| status                     | VARCHAR(20)  | 状态                     | 是      | 'active'  |
| created_by                 | VARCHAR(50)  | 创建人                   | 是      | 无        |
| created_time               | DATETIME     | 创建时间                 | 是      | 无        |
| updated_by                 | VARCHAR(50)  | 更新人                   | 否      | NULL      |
| updated_time               | DATETIME     | 更新时间                 | 否      | NULL      |
| version                    | INT          | 版本号                   | 否      | 0         |
| is_deleted                 | BOOLEAN      | 是否删除                 | 是      | FALSE     |

## 索引设计

为了提高查询性能和确保数据一致性，我们设计了以下索引：

1. 主键索引：`id`
2. 唯一索引：`unit_name`
3. 普通索引：`sort_order`、`status`

## 触发器设计

由于MySQL 8.0以下版本不支持部分索引（带WHERE子句的索引），我们使用触发器来确保唯一性约束：

1. `trg_check_default_material_unit`：确保只有一个默认物料单位
2. `trg_check_default_dimension_unit`：确保只有一个默认纸箱尺寸单位
3. `trg_check_default_thickness_unit`：确保只有一个默认纸度单位
4. `trg_check_default_length_unit`：确保只有一个默认纸长单位

每个约束都有两个触发器：一个用于INSERT操作，一个用于UPDATE操作。触发器会检查是否已经存在一个设置为默认的记录，如果存在，则阻止插入或更新操作。

## 迁移方案

为了从旧的 `measurementunits` 表迁移到新的 `measurement_unit` 表，我们设计了以下迁移方案：

1. 创建新表 `measurement_unit`
2. 检查旧表 `measurementunits` 是否存在
3. 如果旧表存在，将数据从旧表迁移到新表
4. 如果新表中没有数据，插入默认数据
5. 如果旧表存在，将旧表重命名为 `measurementunits_backup`（备份）

## 实体类设计

为了与新表结构保持一致，我们设计了一个新的实体类 `MeasurementUnit`：

```java
@Entity
@Table(name = "measurement_unit")
public class MeasurementUnit extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "unit_name", length = 50, nullable = false, unique = true)
    private String unitName;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 999;

    @Column(name = "is_default_for_new_material", nullable = false)
    private Boolean isDefaultForNewMaterial = false;

    @Column(name = "is_dimension_unit", nullable = false)
    private Boolean isDimensionUnit = false;

    @Column(name = "is_default_dimension_unit", nullable = false)
    private Boolean isDefaultDimensionUnit = false;

    @Column(name = "is_default_thickness_unit", nullable = false)
    private Boolean isDefaultThicknessUnit = false;

    @Column(name = "is_default_length_unit", nullable = false)
    private Boolean isDefaultLengthUnit = false;

    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";
}
```

## 注意事项

1. 由于我们使用了自定义命名策略 `CustomPhysicalNamingStrategy`，实体类中的驼峰命名字段会自动转换为数据库中的小写下划线命名列。
2. 为了确保只有一个默认单位，我们使用了触发器来实现唯一性约束，因为 MySQL 8.0 以下版本不支持部分索引（带 WHERE 子句的索引）。
3. 迁移脚本会自动处理数据迁移，无需手动操作。
4. 旧表会被重命名为备份表，以防万一需要恢复数据。

## 默认数据

如果没有从旧表迁移数据，系统会自动插入以下默认数据：

1. 毫米（默认纸箱尺寸单位）
2. 厘米
3. 英寸（默认纸度单位）
4. 米（默认纸长单位）
5. 千克
6. 克
7. 吨
8. 个
9. 箱
10. 包
