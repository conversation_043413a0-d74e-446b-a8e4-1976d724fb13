# 货品档案导入API文档

## 概述

本文档描述了货品档案导入相关的API接口，包括导入模板下载和货品数据导入功能。

## API基础信息

- **基础路径**: `/api/products`
- **认证方式**: Bearer Token
- **权限要求**: 需要`product:create`权限

## 接口列表

### 1. 下载货品导入模板

#### 请求信息

- **URL**: `/api/products/import/template`
- **方法**: GET
- **描述**: 下载货品导入模板Excel文件
- **权限**: `product:create`

#### 响应信息

- **内容类型**: `application/octet-stream`
- **文件名**: `product_import_template.xlsx`
- **响应体**: Excel文件二进制数据

#### 模板字段说明

| 字段名 | 描述 | 是否必填 |
| ----- | ---- | ------- |
| 盒式编码 | 必填，唯一标识 | 是 |
| 盒式名称 | 必填，盒式名称 | 是 |
| 分类名称 | 选填，分类名称 | 否 |
| 规格名称 | 选填，规格名称 | 否 |
| 报价公式 | 必填，报价公式 | 是 |
| 算价单位 | 必填，算价单位 | 是 |
| 报价单位 | 必填，报价单位 | 是 |
| 连接方式 | 必填，连接方式 | 是 |
| 禁止双驳 | 选填，true表示禁止，false表示允许 | 否 |
| 长度大于此值双驳 | 选填，数字，单位mm | 否 |
| 跳度公差 | 选填，跳度公差 | 否 |
| 客户编码 | 选填，客户编码 | 否 |
| 客户名称 | 选填，客户名称 | 否 |
| 价格 | 选填，货品单价 | 否 |
| 图片URL | 选填，货品图片URL | 否 |
| 是否禁用 | 选填，true表示禁用，false表示启用 | 否 |

#### 示例请求

```http
GET /api/products/import/template HTTP/1.1
Host: localhost:8080
Authorization: Bearer {token}
```

#### 响应码

- **200**: 成功，返回Excel文件
- **401**: 未授权，需要登录
- **403**: 禁止访问，没有权限
- **500**: 服务器内部错误

### 2. 导入货品数据

#### 请求信息

- **URL**: `/api/products/import`
- **方法**: POST
- **描述**: 通过Excel文件批量导入货品
- **权限**: `product:create`
- **内容类型**: `multipart/form-data`

#### 请求参数

| 参数名 | 类型 | 描述 | 是否必填 |
| ----- | ---- | ---- | ------- |
| file | File | Excel文件(.xlsx或.xls) | 是 |

#### 响应信息

- **内容类型**: `application/json`
- **响应体**: JSON对象

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": 5,
    "fail": 2,
    "errors": [
      "导入失败: P001 - 测试货品, 原因: 货品编码已存在",
      "导入失败: P002 - , 原因: 货品名称不能为空"
    ]
  }
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| success | Integer | 成功导入的记录数 |
| fail | Integer | 导入失败的记录数 |
| errors | Array | 导入失败的错误信息列表 |

#### 示例请求

```http
POST /api/products/import HTTP/1.1
Host: localhost:8080
Authorization: Bearer {token}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="product_import.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

(二进制文件内容)
------WebKitFormBoundary7MA4YWxkTrZu0gW--
```

#### 响应码

- **200**: 成功，返回导入结果
- **400**: 请求错误，如文件格式不正确
- **401**: 未授权，需要登录
- **403**: 禁止访问，没有权限
- **500**: 服务器内部错误

## 业务规则

1. **盒式编码唯一性**:
   - 系统会检查导入的盒式编码是否已存在，如果存在则导入失败

2. **必填字段验证**:
   - 盒式编码、盒式名称、报价公式、算价单位、报价单位和连接方式为必填字段，如果为空则导入失败

3. **分类和规格验证**:
   - 如果提供了分类ID或规格ID，系统会验证其是否存在，不存在则导入失败

4. **前端特有字段处理**:
   - 系统会将报价公式、连接方式、跳度公差、禁止双驳、长度大于此值双驳、客户编码和客户名称等前端特有字段存储在描述字段中
   - 前端在显示时会解析描述字段，提取这些特有字段的值

4. **批量处理**:
   - 系统会逐行处理Excel文件中的数据，成功的记录会被保存，失败的记录会被记录错误信息
   - 导入结果会返回成功数量、失败数量和错误信息列表

## 错误处理

导入过程中可能出现的错误包括但不限于：

1. 文件格式不正确（非Excel文件）
2. 必填字段为空（盒式编码、盒式名称、报价公式、算价单位、报价单位、连接方式）
3. 盒式编码已存在
4. 分类ID或规格ID不存在
5. Excel文件解析错误

每个错误都会被记录并返回给前端，方便用户了解导入失败的原因。

## 注意事项

1. 导入前建议先下载模板，按照模板格式填写数据
2. 大批量导入可能需要较长时间，请耐心等待
3. 导入完成后，建议检查导入结果，特别是失败记录的错误信息
