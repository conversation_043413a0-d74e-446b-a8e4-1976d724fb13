package com.czerp.erpbackend.purchase.dto;

import com.czerp.erpbackend.common.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 采购订单查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrderQueryRequest extends PageRequest {

    /**
     * 关键字（采购单号、供应商名称、采购员）
     */
    private String keyword;

    /**
     * 采购日期开始
     */
    private LocalDate purchaseDateStart;

    /**
     * 采购日期结束
     */
    private LocalDate purchaseDateEnd;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 采购类型
     */
    private String purchaseType;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 生产单号
     */
    private String productionOrderNo;
}
