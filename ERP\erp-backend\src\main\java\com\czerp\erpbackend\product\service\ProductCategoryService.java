package com.czerp.erpbackend.product.service;

import com.czerp.erpbackend.product.dto.CreateCategoryRequest;
import com.czerp.erpbackend.product.dto.ProductCategoryDTO;
import com.czerp.erpbackend.product.dto.UpdateCategoryRequest;

import java.util.List;

/**
 * 产品分类服务接口
 */
public interface ProductCategoryService {
    
    /**
     * 查询所有分类
     * @return 分类列表
     */
    List<ProductCategoryDTO> findAllCategories();
    
    /**
     * 查询分类树
     * @return 分类树
     */
    List<ProductCategoryDTO> findCategoryTree();
    
    /**
     * 根据ID查询分类
     * @param id 分类ID
     * @return 分类信息
     */
    ProductCategoryDTO findCategoryById(String id);
    
    /**
     * 创建分类
     * @param request 创建请求
     * @return 分类信息
     */
    ProductCategoryDTO createCategory(CreateCategoryRequest request);
    
    /**
     * 更新分类
     * @param id 分类ID
     * @param request 更新请求
     * @return 分类信息
     */
    ProductCategoryDTO updateCategory(String id, UpdateCategoryRequest request);
    
    /**
     * 删除分类
     * @param id 分类ID
     */
    void deleteCategory(String id);
}
