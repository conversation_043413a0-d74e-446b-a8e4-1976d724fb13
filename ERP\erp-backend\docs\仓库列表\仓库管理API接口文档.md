# 仓库管理API接口文档

## 概述

仓库管理模块提供完整的仓库信息增删改查功能，支持分页查询、条件筛选、批量操作等功能。

**基础路径**: `/api/warehouses`

**权限要求**: 所有接口都需要用户登录并具有相应权限

## 权限说明

| 权限代码 | 权限名称 | 说明 |
|---------|---------|------|
| `warehouse:read` | 查看仓库 | 查询仓库列表和详情 |
| `warehouse:create` | 创建仓库 | 创建新仓库 |
| `warehouse:update` | 更新仓库 | 修改仓库信息 |
| `warehouse:delete` | 删除仓库 | 删除仓库（逻辑删除） |

## 数据模型

### WarehouseDTO（仓库信息）

| 字段名 | 类型 | 说明 | 示例 |
|-------|------|------|------|
| `id` | Long | 仓库ID | 1 |
| `warehouseName` | String | 仓库名称 | "主仓库" |
| `isMaterialWarehouse` | Boolean | 是否备料仓 | true |
| `createdBy` | String | 创建人 | "admin" |
| `createdTime` | LocalDateTime | 创建时间 | "2024-01-01T10:00:00" |
| `updatedBy` | String | 更新人 | "admin" |
| `updatedTime` | LocalDateTime | 更新时间 | "2024-01-01T10:00:00" |
| `version` | Integer | 版本号（乐观锁） | 1 |

## API接口

### 1. 分页查询仓库列表

**接口地址**: `GET /api/warehouses`

**权限要求**: `warehouse:read`

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| `keyword` | String | 否 | - | 关键词（仓库名称模糊查询） |
| `isMaterialWarehouse` | Boolean | 否 | - | 备料仓筛选（true-备料仓，false-非备料仓，null-全部） |
| `page` | Integer | 否 | 1 | 页码（从1开始） |
| `size` | Integer | 否 | 10 | 每页大小 |
| `sortBy` | String | 否 | "createdTime" | 排序字段 |
| `sortDirection` | String | 否 | "desc" | 排序方向（asc-升序，desc-降序） |

**请求示例**:
```
GET /api/warehouses?keyword=主仓&isMaterialWarehouse=true&page=1&size=10
```

**响应格式**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "content": [
      {
        "id": 1,
        "warehouseName": "主仓库",
        "isMaterialWarehouse": true,
        "createdBy": "admin",
        "createdTime": "2024-01-01T10:00:00",
        "updatedBy": "admin",
        "updatedTime": "2024-01-01T10:00:00",
        "version": 1
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "page": 1,
    "size": 10,
    "first": true,
    "last": true,
    "empty": false
  }
}
```

### 2. 获取所有仓库列表（不分页）

**接口地址**: `GET /api/warehouses/all`

**权限要求**: `warehouse:read`

**请求参数**: 无

**响应格式**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "warehouseName": "主仓库",
      "isMaterialWarehouse": true,
      "createdBy": "admin",
      "createdTime": "2024-01-01T10:00:00",
      "updatedBy": "admin",
      "updatedTime": "2024-01-01T10:00:00",
      "version": 1
    }
  ]
}
```

### 3. 查询仓库详情

**接口地址**: `GET /api/warehouses/{id}`

**权限要求**: `warehouse:read`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| `id` | Long | 是 | 仓库ID |

**请求示例**:
```
GET /api/warehouses/1
```

**响应格式**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 1,
    "warehouseName": "主仓库",
    "isMaterialWarehouse": true,
    "createdBy": "admin",
    "createdTime": "2024-01-01T10:00:00",
    "updatedBy": "admin",
    "updatedTime": "2024-01-01T10:00:00",
    "version": 1
  }
}
```

### 4. 创建仓库

**接口地址**: `POST /api/warehouses`

**权限要求**: `warehouse:create`

**请求体**:
```json
{
  "warehouseName": "新仓库",
  "isMaterialWarehouse": false
}
```

**请求参数说明**:

| 字段名 | 类型 | 必填 | 说明 | 验证规则 |
|-------|------|------|------|----------|
| `warehouseName` | String | 是 | 仓库名称 | 不能为空，最大长度100 |
| `isMaterialWarehouse` | Boolean | 是 | 是否备料仓 | 不能为空 |

**响应格式**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": 2,
    "warehouseName": "新仓库",
    "isMaterialWarehouse": false,
    "createdBy": "admin",
    "createdTime": "2024-01-01T10:00:00",
    "updatedBy": "admin",
    "updatedTime": "2024-01-01T10:00:00",
    "version": 1
  }
}
```

### 5. 更新仓库

**接口地址**: `PUT /api/warehouses/{id}`

**权限要求**: `warehouse:update`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| `id` | Long | 是 | 仓库ID |

**请求体**:
```json
{
  "warehouseName": "更新后的仓库名称",
  "isMaterialWarehouse": true
}
```

**请求参数说明**:

| 字段名 | 类型 | 必填 | 说明 | 验证规则 |
|-------|------|------|------|----------|
| `warehouseName` | String | 否 | 仓库名称 | 最大长度100 |
| `isMaterialWarehouse` | Boolean | 否 | 是否备料仓 | - |

**响应格式**: 同创建仓库响应格式

### 6. 删除仓库

**接口地址**: `DELETE /api/warehouses/{id}`

**权限要求**: `warehouse:delete`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| `id` | Long | 是 | 仓库ID |

**响应格式**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

### 7. 批量删除仓库

**接口地址**: `DELETE /api/warehouses/batch`

**权限要求**: `warehouse:delete`

**请求体**:
```json
[1, 2, 3]
```

**响应格式**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

### 8. 检查仓库名称是否存在

**接口地址**: `GET /api/warehouses/check-name`

**权限要求**: `warehouse:read`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| `warehouseName` | String | 是 | 仓库名称 |
| `excludeId` | Long | 否 | 排除的仓库ID（用于更新时检查） |

**请求示例**:
```
GET /api/warehouses/check-name?warehouseName=主仓库&excludeId=1
```

**响应格式**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": false
}
```

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 400 | 请求参数错误 |
| 401 | 未登录或认证失败 |
| 403 | 权限不足 |
| 404 | 仓库不存在 |
| 500 | 服务器内部错误 |

## 业务规则

1. **仓库名称唯一性**: 仓库名称在系统中必须唯一
2. **逻辑删除**: 删除操作为逻辑删除，不会物理删除数据
3. **权限控制**: 所有操作都需要相应的权限
4. **审计字段**: 系统自动维护创建人、创建时间、更新人、更新时间等审计字段
5. **乐观锁**: 使用版本号实现乐观锁，防止并发更新冲突

## 注意事项

1. 所有时间字段使用ISO 8601格式：`yyyy-MM-ddTHH:mm:ss`
2. 分页查询的页码从1开始
3. 排序字段支持实体类中的所有字段名
4. 关键词查询支持仓库名称的模糊匹配
5. 备料仓筛选参数为null时返回所有仓库
