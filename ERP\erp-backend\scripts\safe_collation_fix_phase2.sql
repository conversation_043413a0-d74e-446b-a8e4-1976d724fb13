-- 安全的字符集修复方案 - 第二阶段
-- 修复核心业务表

-- 设置安全模式
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';

-- ========================================
-- 第二阶段：修复核心业务表
-- ========================================

-- 检查第一阶段是否成功
SELECT 
    '第一阶段检查' as '检查项目',
    COUNT(*) as '应该修复的表数',
    SUM(CASE WHEN table_collation = 'utf8mb4_0900_ai_ci' THEN 1 ELSE 0 END) as '已修复表数',
    CASE 
        WHEN COUNT(*) = SUM(CASE WHEN table_collation = 'utf8mb4_0900_ai_ci' THEN 1 ELSE 0 END)
        THEN '✓ 第一阶段成功，可以继续'
        ELSE '✗ 第一阶段未完成，请先完成第一阶段'
    END as '状态'
FROM information_schema.tables 
WHERE table_schema = 'czerp_web' 
    AND table_name IN ('sys_role_permission', 'sys_user_role', 'sys_permission_backup', 'example_entity', 'paper_size_setting');

-- ========================================
-- 修复核心问题表（解决报错的关键表）
-- ========================================

-- 1. 修复采购订单表（核心问题表）
SELECT '开始修复 purchase_order 表...' as '状态';
ALTER TABLE purchase_order CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
SELECT '✓ purchase_order 表修复完成' as '状态';

-- 2. 修复采购订单明细表（核心问题表）
SELECT '开始修复 purchase_order_item 表...' as '状态';
ALTER TABLE purchase_order_item CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
SELECT '✓ purchase_order_item 表修复完成' as '状态';

-- ========================================
-- 立即测试核心功能
-- ========================================

-- 测试之前失败的查询
SELECT 
    '核心问题测试' as '测试项目',
    '测试之前失败的查询是否能正常执行' as '说明';

-- 这个查询之前会失败，现在应该能正常执行
SELECT 
    COUNT(*) as '查询结果',
    '如果能看到数字说明核心问题已解决' as '说明'
FROM sales_order so1_0 
JOIN sales_order_item i1_0 ON so1_0.id = i1_0.order_id 
WHERE i1_0.id IN (
    SELECT poi1_0.source_sales_order_item_id 
    FROM purchase_order_item poi1_0 
    JOIN purchase_order po1_0 ON po1_0.id = poi1_0.purchase_order_id 
    WHERE po1_0.purchase_order_no IN ('CG2025051900003')
);

-- ========================================
-- 验证核心修复结果
-- ========================================
SELECT 
    '核心表修复验证' as '检查项目',
    table_name as '表名',
    table_collation as '当前字符集',
    CASE 
        WHEN table_collation = 'utf8mb4_0900_ai_ci' THEN '✓ 修复成功'
        ELSE '✗ 修复失败'
    END as '状态'
FROM information_schema.tables 
WHERE table_schema = 'czerp_web' 
    AND table_name IN ('purchase_order', 'purchase_order_item')
ORDER BY table_name;

-- 验证关键字段
SELECT 
    '关键字段验证' as '检查项目',
    TABLE_NAME as '表名',
    COLUMN_NAME as '字段名',
    COLLATION_NAME as '字符集',
    CASE 
        WHEN COLLATION_NAME = 'utf8mb4_0900_ai_ci' THEN '✓ 正确'
        ELSE '✗ 错误'
    END as '状态'
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'czerp_web' 
    AND TABLE_NAME IN ('sales_order_item', 'purchase_order_item', 'purchase_order')
    AND COLUMN_NAME IN ('id', 'source_sales_order_item_id', 'purchase_order_no')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 恢复安全模式
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 第二阶段执行说明
-- ========================================
/*
第二阶段特点：
1. 修复导致报错的核心表
2. 立即测试核心功能是否恢复
3. 如果成功，原始报错应该消失

执行后检查：
1. 核心问题测试应该能正常执行
2. 关键字段验证都应该显示 "✓ 正确"
3. 重启应用程序，测试销售订单查询功能

如果第二阶段成功：
- 原始的字符集冲突错误应该消失
- 可以选择继续第三阶段，或暂停观察

如果第二阶段失败：
- 立即恢复备份
- 不要继续后续阶段
*/
