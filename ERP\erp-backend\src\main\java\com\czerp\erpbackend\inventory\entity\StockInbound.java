package com.czerp.erpbackend.inventory.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 入库单实体
 */
@Entity
@Table(name = "stock_inbound")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class StockInbound extends BaseEntity {

    /**
     * 入库单ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 入库单号
     */
    @Column(name = "inbound_no", length = 50, nullable = false, unique = true)
    private String inboundNo;

    /**
     * 入库日期
     */
    @Column(name = "inbound_date", nullable = false)
    private LocalDate inboundDate;

    /**
     * 仓库
     */
    @Column(name = "warehouse", length = 100)
    private String warehouse;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 供应商编码
     */
    @Column(name = "supplier_code", length = 50)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Column(name = "supplier_name", length = 100)
    private String supplierName;

    /**
     * 供应商送货单号
     */
    @Column(name = "supplier_delivery_no", length = 100)
    private String supplierDeliveryNo;

    /**
     * 送货单日期
     */
    @Column(name = "delivery_date")
    private LocalDate deliveryDate;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;

    /**
     * 入库单明细
     */
    @OneToMany(mappedBy = "stockInbound", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<StockInboundItem> items = new ArrayList<>();

    /**
     * 添加入库单明细
     * @param item 入库单明细
     */
    public void addItem(StockInboundItem item) {
        items.add(item);
        item.setStockInbound(this);
    }

    /**
     * 移除入库单明细
     * @param item 入库单明细
     */
    public void removeItem(StockInboundItem item) {
        items.remove(item);
        item.setStockInbound(null);
    }
}
