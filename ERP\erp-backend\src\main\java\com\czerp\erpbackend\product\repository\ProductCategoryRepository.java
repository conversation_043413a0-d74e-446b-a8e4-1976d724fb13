package com.czerp.erpbackend.product.repository;

import com.czerp.erpbackend.product.entity.ProductCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 产品分类存储库
 */
@Repository
public interface ProductCategoryRepository extends JpaRepository<ProductCategory, String> {
    
    /**
     * 根据编码查找分类
     * @param code 编码
     * @return 分类
     */
    Optional<ProductCategory> findByCode(String code);
    
    /**
     * 判断编码是否存在
     * @param code 编码
     * @return 是否存在
     */
    boolean existsByCode(String code);
    
    /**
     * 查询根分类列表
     * @return 根分类列表
     */
    List<ProductCategory> findByParentIdIsNullOrderBySortAsc();
    
    /**
     * 根据父分类ID查询子分类列表
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<ProductCategory> findByParentIdOrderBySortAsc(String parentId);
    
    /**
     * 根据父分类ID查询子分类数量
     * @param parentId 父分类ID
     * @return 子分类数量
     */
    long countByParentId(String parentId);
}
