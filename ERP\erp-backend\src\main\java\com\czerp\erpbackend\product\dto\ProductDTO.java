package com.czerp.erpbackend.product.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 货品DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductDTO {
    
    /**
     * 货品ID
     */
    private String id;
    
    /**
     * 货品名称
     */
    private String name;
    
    /**
     * 货品编码
     */
    private String code;
    
    /**
     * 分类ID
     */
    private String categoryId;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 规格ID
     */
    private String specId;
    
    /**
     * 规格名称
     */
    private String specName;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 图片URL
     */
    private String imageUrl;
    
    /**
     * 是否禁用
     */
    private Boolean disabled;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
