package com.czerp.erpbackend.production.dto;

import com.czerp.erpbackend.common.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 生产排程单查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductionScheduleQueryRequest extends PageRequest {

    /**
     * 关键字（排程单号、客户名称、产品名称）
     */
    private String keyword;

    /**
     * 排程日期开始
     */
    private LocalDate scheduleDateStart;

    /**
     * 排程日期结束
     */
    private LocalDate scheduleDateEnd;

    /**
     * 计划完成日期开始
     */
    private LocalDate plannedCompletionDateStart;

    /**
     * 计划完成日期结束
     */
    private LocalDate plannedCompletionDateEnd;

    /**
     * 是否急单
     */
    private Boolean isUrgent;

    /**
     * 是否已打印
     */
    private Boolean isPrinted;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 生产单号
     */
    private String productionOrderNo;

    /**
     * 销售订单号
     */
    private String salesOrderNo;

    /**
     * 产品名称
     */
    private String productName;
}
