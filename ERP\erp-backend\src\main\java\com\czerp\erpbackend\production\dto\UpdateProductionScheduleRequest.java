package com.czerp.erpbackend.production.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 更新生产排程单请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateProductionScheduleRequest {

    /**
     * 版本号（乐观锁）
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;

    /**
     * 排程日期
     */
    private LocalDate scheduleDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 生产排程明细列表
     */
    @Valid
    @NotEmpty(message = "生产排程明细不能为空")
    private List<UpdateProductionScheduleItemRequest> items;
}
