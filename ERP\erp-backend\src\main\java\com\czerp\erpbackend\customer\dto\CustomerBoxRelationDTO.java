package com.czerp.erpbackend.customer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 客户盒式关联DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerBoxRelationDTO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 盒式信息ID
     */
    private String boxInfoId;

    /**
     * 盒式编码
     */
    private String boxCode;

    /**
     * 盒式名称
     */
    private String boxName;

    /**
     * 报价公式
     */
    private String quoteFormula;

    /**
     * 算价单位
     */
    private String calculationUnit;

    /**
     * 报价单位
     */
    private String quoteUnit;

    /**
     * 连接方式
     */
    private String connectionMethod;

    /**
     * 禁止双驳
     */
    private Boolean forbidDoubleFluting;

    /**
     * 长度大于此值双驳
     */
    private Double doubleFlutingLengthThreshold;

    /**
     * 跳度公差
     */
    private String pitchTolerance;

    /**
     * 盒式图形
     */
    private Boolean hasBoxGraphic;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
