package com.czerp.erpbackend.inventory.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 入库单明细DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockInboundItemDTO {

    /**
     * 入库单明细ID
     */
    private Long id;

    /**
     * 入库单号
     */
    private String inboundId;

    /**
     * 来源采购订单明细ID
     */
    private Long purchaseOrderItemId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 收备品数
     */
    private Integer spareQuantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 面积(平米)
     */
    private BigDecimal areaSquareMeters;

    /**
     * 单重
     */
    private BigDecimal unitWeight;

    /**
     * 重量(KG)
     */
    private BigDecimal weightKg;

    /**
     * 每板数
     */
    private Integer quantityPerBoard;

    /**
     * 税率%
     */
    private BigDecimal taxRate;

    /**
     * 币别
     */
    private String currency;

    /**
     * 供应商送货单号
     */
    private String supplierDeliveryNo;

    /**
     * 折度规格
     */
    private String foldingSpecification;

    /**
     * 折算数量
     */
    private BigDecimal conversionQuantity;

    /**
     * 折算单价
     */
    private BigDecimal conversionPrice;

    /**
     * 折算金额
     */
    private BigDecimal conversionAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 版本号
     */
    private Integer version;

    // ==================== 采购单相关字段 ====================

    /**
     * 采购单号
     */
    private String purchaseOrderNo;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 交易单位
     */
    private String tradingUnit;

    /**
     * 采购订单数量
     */
    private Integer purchaseOrderQuantity;

    /**
     * 纸质
     */
    private String paperQuality;

    /**
     * 纸板类别
     */
    private String paperBoardCategory;

    /**
     * 楞别
     */
    private String corrugationType;

    /**
     * 规格 (纸度×纸长)
     */
    private String specification;

    /**
     * 纸质报价
     */
    private BigDecimal paperQuotation;

    /**
     * 折扣
     */
    private BigDecimal discount;

    // ==================== 销售单相关字段 ====================

    /**
     * 生产单号
     */
    private String productionOrderNo;

    /**
     * 销售单号
     */
    private String salesOrderNo;

    /**
     * 销售日期
     */
    private LocalDate salesOrderDate;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 客方货号
     */
    private String customerProductCode;

    /**
     * 产品 (盒式+订单纸质)
     */
    private String product;

    /**
     * 产品规格
     */
    private String productSpecification;

    /**
     * 品名
     */
    private String productName;

    /**
     * 工艺要求 (格式：分纸→开槽→粘箱→包装)
     */
    private String processRequirements;

    /**
     * 订单数
     */
    private Integer orderQuantity;

    /**
     * 产品单价
     */
    private BigDecimal productPrice;

    /**
     * 产品金额
     */
    private BigDecimal productAmount;

    /**
     * 销售备注
     */
    private String salesRemark;

    /**
     * 销售员
     */
    private String salesPerson;

    // ==================== 计算字段 ====================

    /**
     * 金额 (价格×数量)
     */
    private BigDecimal amount;
}
