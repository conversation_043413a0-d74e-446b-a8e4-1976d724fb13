package com.czerp.erpbackend.product.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 产品权限角色分配初始化器
 * 负责将产品相关权限分配给管理员角色
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(4) // 在产品权限初始化之后执行
public class ProductRolePermissionInitializer implements CommandLineRunner {

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing product role permissions...");

        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping product role permissions initialization");
            return;
        }

        // 获取所有产品相关权限
        List<String> productPermissionCodes = Arrays.asList(
                "product", "product:list", "product:read", "product:create", "product:update", "product:delete", "product:import",
                "product-category", "product-category:list", "product-category:read", "product-category:create", "product-category:update", "product-category:delete",
                "product-spec", "product-spec:list", "product-spec:read", "product-spec:create", "product-spec:update", "product-spec:delete"
        );

        List<Permission> productPermissions = permissionRepository.findByCodeIn(productPermissionCodes);

        // 为管理员角色分配产品权限
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : productPermissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString());
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }

        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} product permissions to admin role", rolePermissions.size());
        } else {
            log.info("All product permissions already assigned to admin role, skipping");
        }

        log.info("Product role permissions initialized successfully");
    }
}
