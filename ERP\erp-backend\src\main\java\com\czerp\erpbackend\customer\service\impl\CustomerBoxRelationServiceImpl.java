package com.czerp.erpbackend.customer.service.impl;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.customer.dto.CreateCustomerBoxRelationRequest;
import com.czerp.erpbackend.customer.dto.CustomerBoxRelationDTO;
import com.czerp.erpbackend.customer.entity.Customer;
import com.czerp.erpbackend.customer.entity.CustomerBoxInfo;
import com.czerp.erpbackend.customer.entity.CustomerBoxRelation;
import com.czerp.erpbackend.customer.repository.CustomerBoxInfoRepository;
import com.czerp.erpbackend.customer.repository.CustomerBoxRelationRepository;
import com.czerp.erpbackend.customer.repository.CustomerRepository;
import com.czerp.erpbackend.customer.service.CustomerBoxRelationService;
import com.czerp.erpbackend.product.entity.Product;
import com.czerp.erpbackend.product.repository.ProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 客户盒式关联服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerBoxRelationServiceImpl implements CustomerBoxRelationService {

    private final CustomerRepository customerRepository;
    private final CustomerBoxInfoRepository customerBoxInfoRepository;
    private final CustomerBoxRelationRepository customerBoxRelationRepository;
    private final ProductRepository productRepository;

    /**
     * 查询客户关联的盒式信息列表
     * @param customerId 客户ID
     * @return 盒式信息列表
     */
    @Override
    public List<CustomerBoxRelationDTO> findCustomerBoxRelations(String customerId) {
        log.debug("Finding customer box relations for customer id: {}", customerId);

        // 检查客户是否存在
        Customer customer = customerRepository.findById(customerId)
                .orElseThrow(() -> new BusinessException("客户不存在"));

        // 查询关联列表
        List<CustomerBoxRelation> relations = customerBoxRelationRepository.findByCustomerId(customerId);

        // 转换为DTO，并填充完整信息
        return relations.stream()
                .map(relation -> {
                    // 获取基本DTO
                    CustomerBoxRelationDTO dto = convertToDTO(relation);

                    // 填充客户信息
                    dto.setCustomerCode(customer.getCustomerCode());
                    dto.setCustomerName(customer.getCustomerName());

                    // 填充盒式信息
                    try {
                        CustomerBoxInfo boxInfo = customerBoxInfoRepository.findById(relation.getBoxInfoId())
                                .orElse(null);

                        if (boxInfo != null) {
                            dto.setBoxCode(boxInfo.getBoxCode());
                            dto.setBoxName(boxInfo.getBoxName());
                            dto.setQuoteFormula(boxInfo.getQuoteFormula());
                            dto.setCalculationUnit(boxInfo.getCalculationUnit());
                            dto.setQuoteUnit(boxInfo.getQuoteUnit());
                            dto.setConnectionMethod(boxInfo.getConnectionMethod());
                            dto.setForbidDoubleFluting(boxInfo.getForbidDoubleFluting());
                            dto.setDoubleFlutingLengthThreshold(boxInfo.getDoubleFlutingLengthThreshold() != null ?
                                    boxInfo.getDoubleFlutingLengthThreshold().doubleValue() : null);
                            dto.setPitchTolerance(boxInfo.getPitchTolerance());
                            dto.setHasBoxGraphic(boxInfo.getHasBoxGraphic());
                        }
                    } catch (Exception e) {
                        log.warn("Failed to get box info for relation: {}", relation.getId(), e);
                    }

                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 查询盒式信息关联的客户列表
     * @param boxInfoId 盒式信息ID
     * @return 客户列表
     */
    @Override
    public List<CustomerBoxRelationDTO> findBoxInfoCustomerRelations(String boxInfoId) {
        log.debug("Finding box info customer relations for box info id: {}", boxInfoId);

        // 检查盒式信息是否存在
        CustomerBoxInfo boxInfo = customerBoxInfoRepository.findById(boxInfoId)
                .orElseThrow(() -> new BusinessException("盒式信息不存在"));

        // 查询关联列表
        List<CustomerBoxRelation> relations = customerBoxRelationRepository.findByBoxInfoId(boxInfoId);

        // 转换为DTO，并填充完整信息
        return relations.stream()
                .map(relation -> {
                    // 获取基本DTO
                    CustomerBoxRelationDTO dto = convertToDTO(relation);

                    // 填充盒式信息
                    dto.setBoxCode(boxInfo.getBoxCode());
                    dto.setBoxName(boxInfo.getBoxName());
                    dto.setQuoteFormula(boxInfo.getQuoteFormula());
                    dto.setCalculationUnit(boxInfo.getCalculationUnit());
                    dto.setQuoteUnit(boxInfo.getQuoteUnit());
                    dto.setConnectionMethod(boxInfo.getConnectionMethod());
                    dto.setForbidDoubleFluting(boxInfo.getForbidDoubleFluting());
                    dto.setDoubleFlutingLengthThreshold(boxInfo.getDoubleFlutingLengthThreshold() != null ?
                            boxInfo.getDoubleFlutingLengthThreshold().doubleValue() : null);
                    dto.setPitchTolerance(boxInfo.getPitchTolerance());
                    dto.setHasBoxGraphic(boxInfo.getHasBoxGraphic());

                    // 填充客户信息
                    try {
                        Customer customer = customerRepository.findById(relation.getCustomerId())
                                .orElse(null);

                        if (customer != null) {
                            dto.setCustomerCode(customer.getCustomerCode());
                            dto.setCustomerName(customer.getCustomerName());
                        }
                    } catch (Exception e) {
                        log.warn("Failed to get customer for relation: {}", relation.getId(), e);
                    }

                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建客户盒式关联
     * @param customerId 客户ID
     * @param request 创建请求
     * @return 关联信息
     */
    @Override
    @Transactional
    public CustomerBoxRelationDTO createCustomerBoxRelation(String customerId, CreateCustomerBoxRelationRequest request) {
        log.debug("Creating customer box relation for customer id: {} and request: {}", customerId, request);

        // 检查客户是否存在
        Customer customer = customerRepository.findById(customerId)
                .orElseThrow(() -> new BusinessException("客户不存在"));

        CustomerBoxInfo boxInfo;

        // 根据请求中的字段选择处理方式
        if (request.getBoxInfoId() != null && !request.getBoxInfoId().isEmpty()) {
            // 使用已有的盒式信息
            boxInfo = customerBoxInfoRepository.findById(request.getBoxInfoId())
                    .orElseThrow(() -> new BusinessException("盒式信息不存在"));
        } else if (request.getProductId() != null && !request.getProductId().isEmpty()) {
            // 根据货品ID创建盒式信息
            boxInfo = createBoxInfoFromProduct(request.getProductId());
        } else {
            throw new BusinessException("盒式信息ID和货品ID不能同时为空");
        }

        // 检查关联是否已存在
        if (customerBoxRelationRepository.existsByCustomerIdAndBoxInfoId(customerId, boxInfo.getId())) {
            throw new BusinessException("客户已关联该盒式信息");
        }

        // 创建关联
        CustomerBoxRelation relation = new CustomerBoxRelation();
        relation.setId(UUID.randomUUID().toString());
        relation.setCustomerId(customerId);
        relation.setBoxInfoId(boxInfo.getId());
        relation.setRemark(request.getRemark());

        // 保存关联
        CustomerBoxRelation savedRelation = customerBoxRelationRepository.save(relation);

        // 转换为DTO
        CustomerBoxRelationDTO dto = convertToDTO(savedRelation);
        dto.setCustomerCode(customer.getCustomerCode());
        dto.setCustomerName(customer.getCustomerName());
        dto.setBoxCode(boxInfo.getBoxCode());
        dto.setBoxName(boxInfo.getBoxName());
        dto.setQuoteFormula(boxInfo.getQuoteFormula());
        dto.setCalculationUnit(boxInfo.getCalculationUnit());
        dto.setQuoteUnit(boxInfo.getQuoteUnit());
        dto.setConnectionMethod(boxInfo.getConnectionMethod());
        dto.setForbidDoubleFluting(boxInfo.getForbidDoubleFluting());
        dto.setDoubleFlutingLengthThreshold(boxInfo.getDoubleFlutingLengthThreshold() != null ? boxInfo.getDoubleFlutingLengthThreshold().doubleValue() : null);
        dto.setPitchTolerance(boxInfo.getPitchTolerance());
        dto.setHasBoxGraphic(boxInfo.getHasBoxGraphic());

        return dto;
    }

    /**
     * 根据货品ID创建盒式信息
     * @param productId 货品ID
     * @return 盒式信息
     */
    @Transactional
    private CustomerBoxInfo createBoxInfoFromProduct(String productId) {
        log.debug("Creating customer box info from product id: {}", productId);

        // 查询货品
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new BusinessException("货品不存在"));

        // 检查盒式编码是否已存在
        if (customerBoxInfoRepository.existsByBoxCode(product.getCode())) {
            // 如果已存在，直接返回
            return customerBoxInfoRepository.findByBoxCode(product.getCode())
                    .orElseThrow(() -> new BusinessException("盒式信息不存在"));
        }

        // 从货品描述中提取信息
        String quoteFormula = "";
        String connectionMethod = "";
        String pitchTolerance = "";
        Boolean hasBoxGraphic = false;

        if (product.getDescription() != null) {
            // 尝试从描述中提取报价公式
            if (product.getDescription().contains("报价公式:")) {
                quoteFormula = extractValue(product.getDescription(), "报价公式:");
            }

            // 尝试从描述中提取连接方式
            if (product.getDescription().contains("连接方式:")) {
                connectionMethod = extractValue(product.getDescription(), "连接方式:");
            }

            // 尝试从描述中提取跳度公差
            if (product.getDescription().contains("跳度公差:")) {
                pitchTolerance = extractValue(product.getDescription(), "跳度公差:");
            }
        }

        // 创建盒式信息
        CustomerBoxInfo boxInfo = new CustomerBoxInfo();
        boxInfo.setId(UUID.randomUUID().toString());
        boxInfo.setBoxCode(product.getCode());
        boxInfo.setBoxName(product.getName());
        boxInfo.setQuoteFormula(quoteFormula);
        boxInfo.setCalculationUnit(product.getUnit());
        boxInfo.setQuoteUnit(product.getUnit());
        boxInfo.setConnectionMethod(connectionMethod);
        boxInfo.setForbidDoubleFluting(false);
        boxInfo.setDoubleFlutingLengthThreshold(null);
        boxInfo.setPitchTolerance(pitchTolerance);
        boxInfo.setHasBoxGraphic(product.getImageUrl() != null && !product.getImageUrl().isEmpty());
        boxInfo.setIsDefault(false);
        boxInfo.setStatus("active");

        // 保存盒式信息
        return customerBoxInfoRepository.save(boxInfo);
    }

    /**
     * 从字符串中提取指定前缀后的值
     * @param source 源字符串
     * @param prefix 前缀
     * @return 提取的值
     */
    private String extractValue(String source, String prefix) {
        int start = source.indexOf(prefix) + prefix.length();
        int end = source.indexOf(",", start);
        if (end == -1) {
            end = source.length();
        }
        return source.substring(start, end).trim();
    }

    /**
     * 批量创建客户盒式关联
     * @param customerId 客户ID
     * @param requests 创建请求列表
     * @return 关联信息列表
     */
    @Override
    @Transactional
    public List<CustomerBoxRelationDTO> batchCreateCustomerBoxRelations(String customerId, List<CreateCustomerBoxRelationRequest> requests) {
        log.debug("Batch creating customer box relations for customer id: {} and requests: {}", customerId, requests);

        // 检查客户是否存在
        if (!customerRepository.existsById(customerId)) {
            throw new BusinessException("客户不存在");
        }

        List<CustomerBoxRelationDTO> result = new ArrayList<>();
        for (CreateCustomerBoxRelationRequest request : requests) {
            try {
                CustomerBoxRelationDTO dto = createCustomerBoxRelation(customerId, request);
                result.add(dto);
            } catch (BusinessException e) {
                log.warn("Failed to create customer box relation: {}", e.getMessage());
                // 继续处理下一个请求
            }
        }

        return result;
    }

    /**
     * 删除客户盒式关联
     * @param customerId 客户ID
     * @param boxInfoId 盒式信息ID
     */
    @Override
    @Transactional
    public void deleteCustomerBoxRelation(String customerId, String boxInfoId) {
        log.debug("Deleting customer box relation for customer id: {} and box info id: {}", customerId, boxInfoId);

        // 检查关联是否存在
        CustomerBoxRelation relation = customerBoxRelationRepository.findByCustomerIdAndBoxInfoId(customerId, boxInfoId)
                .orElseThrow(() -> new BusinessException("客户未关联该盒式信息"));

        // 删除关联
        customerBoxRelationRepository.delete(relation);
    }

    /**
     * 批量删除客户盒式关联
     * @param customerId 客户ID
     * @param boxInfoIds 盒式信息ID列表
     */
    @Override
    @Transactional
    public void batchDeleteCustomerBoxRelations(String customerId, List<String> boxInfoIds) {
        log.debug("Batch deleting customer box relations for customer id: {} and box info ids: {}", customerId, boxInfoIds);

        // 检查客户是否存在
        if (!customerRepository.existsById(customerId)) {
            throw new BusinessException("客户不存在");
        }

        for (String boxInfoId : boxInfoIds) {
            try {
                deleteCustomerBoxRelation(customerId, boxInfoId);
            } catch (BusinessException e) {
                log.warn("Failed to delete customer box relation: {}", e.getMessage());
                // 继续处理下一个请求
            }
        }
    }

    /**
     * 删除客户的所有盒式关联
     * @param customerId 客户ID
     */
    @Override
    @Transactional
    public void deleteAllCustomerBoxRelations(String customerId) {
        log.debug("Deleting all customer box relations for customer id: {}", customerId);

        // 检查客户是否存在
        if (!customerRepository.existsById(customerId)) {
            throw new BusinessException("客户不存在");
        }

        // 删除关联
        customerBoxRelationRepository.deleteByCustomerId(customerId);
    }

    /**
     * 将实体转换为DTO
     * @param relation 关联实体
     * @return 关联DTO
     */
    private CustomerBoxRelationDTO convertToDTO(CustomerBoxRelation relation) {
        return CustomerBoxRelationDTO.builder()
                .id(relation.getId())
                .customerId(relation.getCustomerId())
                .boxInfoId(relation.getBoxInfoId())
                .remark(relation.getRemark())
                .createdBy(relation.getCreatedBy())
                .createdTime(relation.getCreatedTime())
                .updatedBy(relation.getUpdatedBy())
                .updatedTime(relation.getUpdatedTime())
                .build();
    }
}
