package com.czerp.erpbackend.sales.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售订单查询参数DTO
 * 用于接收前端查询条件
 * 支持传统搜索参数和新的列筛选功能
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderQueryParamDTO {
    // ========== 传统搜索参数（保持向后兼容） ==========
    private String keyword; // 关键字搜索
    private String orderNo; // 销售单号
    private String productionOrderNo; // 生产单号
    private String customerName; // 客户名称
    private String customerOrderNo; // 客户订单号
    private String customerProductCode; // 客方货号
    private String productName; // 品名
    private LocalDate startDate; // 开始日期
    private LocalDate endDate; // 结束日期
    private String salesPerson; // 销售员
    private Integer page = 1; // 页码
    private Integer pageSize = 10; // 每页大小
    private String sortField; // 排序字段
    private String sortOrder; // 排序方向

    // ========== 第一阶段：核心列筛选参数 ==========
    // 文本筛选（单值，用于搜索栏）
    private String filterProductionOrderNo; // 生产单号筛选
    private String filterOrderNo; // 销售单号筛选
    private String filterCustomerOrderNo; // 客户订单号筛选
    private String filterCustomerProductCode; // 客方货号筛选

    // 选择筛选（多值，用于列头筛选）
    private List<String> filterCustomerNames; // 客户名称筛选（多选）
    private List<String> filterProductNames; // 产品名称筛选（多选）
    private List<String> filterSalesPersons; // 销售员筛选（多选）

    // 🔥 新增：单号类字段的多值筛选（用于列头筛选）
    private List<String> filterProductionOrderNos; // 生产单号筛选（多选）
    private List<String> filterOrderNos; // 销售单号筛选（多选）
    private List<String> filterCustomerOrderNos; // 客户订单号筛选（多选）
    private List<String> filterCustomerProductCodes; // 客方货号筛选（多选）
    private List<String> filterPurchaseOrderNos; // 采购单号筛选（多选）

    // 🔥 新增：规格类字段的多值筛选（用于列头筛选）
    private List<String> filterBindingSpecifications; // 合订规格筛选（多选）
    private List<String> filterSpecifications; // 规格筛选（多选）
    private List<String> filterProductionSpecifications; // 生产规格筛选（多选）
    private List<String> filterPressSizeWidths; // 压线尺寸筛选（多选）

    // 🔥 新增：工艺和人员字段的多值筛选（用于列头筛选）
    private List<String> filterPaperTypes; // 纸质筛选（多选）
    private List<String> filterProcessRequirements; // 工艺要求筛选（多选）
    private List<String> filterProcesses; // 工序筛选（多选）
    private List<String> filterReceivingUnits; // 收货单位筛选（多选）
    private List<String> filterReceivingPersons; // 收货人筛选（多选）

    // 🔥 新增：采购相关字段的多值筛选（用于列头筛选）
    private List<String> filterSupplierNames; // 采购供应商筛选（多选，通过子查询）
    private List<String> filterPaperTypeNames; // 纸板类别筛选（多选，通过子查询）

    // 🔥 新增：字典类字段的多值筛选（用于列头筛选）
    private List<String> filterBoxTypes; // 箱型筛选（多选）
    private List<String> filterCorrugationTypes; // 瓦楞类型筛选（多选）

    // 🔥 新增：生产和工艺相关字段的多值筛选
    private List<String> filterProductionPaperTypes; // 生产纸质筛选（多选）
    private List<String> filterConnectionMethods; // 连接方式筛选（多选）
    private List<String> filterUnits; // 单位筛选（多选）
    private List<String> filterCurrencies; // 币种筛选（多选）
    private List<String> filterTaxRates; // 税率筛选（多选）

    // 🔥 新增：人员和地址相关字段的多值筛选
    private List<String> filterReceiverPhones; // 收货人电话筛选（多选）
    private List<String> filterCreatedBys; // 创建人筛选（多选）

    // 🔥 新增：文本字段的筛选
    private List<String> filterProductionRemarks; // 生产备注筛选（多选）
    private List<String> filterRemarks; // 备注筛选（多选）
    private List<String> filterReceivingAddresses; // 收货地址筛选（多选）

    // 🔥 新增：特殊标识字段的筛选
    private List<Boolean> filterIsSpecialPrices; // 特价标识筛选（多选）

    // 数字范围筛选
    private BigDecimal filterQuantityMin; // 数量最小值
    private BigDecimal filterQuantityMax; // 数量最大值
    private BigDecimal filterTotalAmountMin; // 金额最小值
    private BigDecimal filterTotalAmountMax; // 金额最大值

    // 🔥 新增：数字范围筛选字段
    private Integer filterSpareQuantityMin; // 备品数最小值
    private Integer filterSpareQuantityMax; // 备品数最大值
    private BigDecimal filterPriceMin; // 单价最小值
    private BigDecimal filterPriceMax; // 单价最大值
    private Integer filterPurchasedQuantityMin; // 已采购数最小值
    private Integer filterPurchasedQuantityMax; // 已采购数最大值
    private BigDecimal filterPaperQuotationMin; // 纸质报价最小值
    private BigDecimal filterPaperQuotationMax; // 纸质报价最大值
    private BigDecimal filterUnitWeightMin; // 单重最小值
    private BigDecimal filterUnitWeightMax; // 单重最大值
    private BigDecimal filterTotalWeightMin; // 总重最小值
    private BigDecimal filterTotalWeightMax; // 总重最大值
    private BigDecimal filterProductAreaMin; // 产品面积最小值
    private BigDecimal filterProductAreaMax; // 产品面积最大值
    private BigDecimal filterTotalAreaMin; // 总面积最小值
    private BigDecimal filterTotalAreaMax; // 总面积最大值
    private BigDecimal filterProductVolumeMin; // 产品体积最小值
    private BigDecimal filterProductVolumeMax; // 产品体积最大值
    private BigDecimal filterTotalVolumeMin; // 总体积最小值
    private BigDecimal filterTotalVolumeMax; // 总体积最大值

    // 日期范围筛选
    private LocalDate filterOrderDateStart; // 订单日期开始
    private LocalDate filterOrderDateEnd; // 订单日期结束
    private LocalDate filterDeliveryDateStart; // 交期开始
    private LocalDate filterDeliveryDateEnd; // 交期结束

    // 🔥 新增：日期范围筛选字段
    private LocalDate filterCreatedTimeStart; // 创建时间开始
    private LocalDate filterCreatedTimeEnd; // 创建时间结束

    // ========== 筛选选项请求标识 ==========
    private Boolean includeFilterOptions; // 是否返回筛选选项
    private List<String> filterOptionFields; // 需要返回选项的字段列表
}
