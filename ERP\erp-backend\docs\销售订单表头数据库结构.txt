+--------------------+--------------+--------------------+------+-----+---------+-------+---------------------------------+-----------+
| Field              | Type         | Collation          | Null | Key | Default | Extra | Privileges                      | Comment   |
+--------------------+--------------+--------------------+------+-----+---------+-------+---------------------------------+-----------+
| id                 | varchar(36)  | utf8mb4_0900_ai_ci | NO   | PRI | NULL    |       | select,insert,update,references |           |
| order_no           | varchar(50)  | utf8mb4_0900_ai_ci | NO   | UNI | NULL    |       | select,insert,update,references | ???????   |
| order_date         | date         | NULL               | YES  |     | NULL    |       | select,insert,update,references | ???????   |
| payment_method     | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ?????     |
| customer_code      | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ??????    |
| customer_name      | varchar(200) | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ????      |
| sales_person       | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ?????     |
| customer_purchaser | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ???????   |
| receiving_unit     | varchar(200) | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ?????λ   |
| receiver           | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ??        |
| receiver_phone     | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ?????绰   |
| receiving_address  | varchar(500) | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ??????    |
| remark             | varchar(500) | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ???       |
| order_type         | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ???????   |
| created_by         | varchar(50)  | utf8mb4_0900_ai_ci | NO   |     | NULL    |       | select,insert,update,references |           |
| created_time       | datetime     | NULL               | YES  |     | NULL    |       | select,insert,update,references | ??????    |
| updated_by         | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references |           |
| updated_time       | datetime     | NULL               | YES  |     | NULL    |       | select,insert,update,references | ??????    |
| created_by_name    | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ????????? |
| updated_by_name    | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references | ????????? |
| is_deleted         | bit(1)       | NULL               | NO   |     | NULL    |       | select,insert,update,references |           |
| version            | int          | NULL               | YES  |     | NULL    |       | select,insert,update,references |           |
| CreatedBy          | varchar(50)  | utf8mb4_0900_ai_ci | NO   |     | NULL    |       | select,insert,update,references |           |
| UpdatedBy          | varchar(50)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |       | select,insert,update,references |           |
+--------------------+--------------+--------------------+------+-----+---------+-------+---------------------------------+-----------+
24 <USER> <GROUP> set (0.01 sec)