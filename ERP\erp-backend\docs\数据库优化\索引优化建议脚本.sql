-- 索引优化建议脚本
-- 基于动态计算已入库数量的需求，优化stock_inbound_item表的索引

-- ==========================================
-- 第一步：分析现有索引重复情况
-- ==========================================

-- 查看重复的purchase_order_item_id索引
SELECT 
    '=== 重复索引分析 ===' as analysis_type;

SELECT 
    index_name,
    GROUP_CONCAT(column_name ORDER BY seq_in_index) as columns,
    index_type,
    CASE WHEN non_unique = 0 THEN 'UNIQUE' ELSE 'NON-UNIQUE' END as uniqueness
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name = 'stock_inbound_item'
AND index_name IN ('idx_purchase_order_item_id', 'idx_stock_inbound_item_purchase_order_item_id')
GROUP BY index_name, index_type, non_unique;

-- ==========================================
-- 第二步：检查我们需要的复合索引
-- ==========================================

SELECT 
    '=== 动态计算所需索引检查 ===' as analysis_type;

-- 检查核心复合索引是否存在
SELECT 
    CASE 
        WHEN COUNT(*) >= 3 THEN 'EXISTS'
        ELSE 'NOT EXISTS'
    END as status,
    'idx_stock_inbound_item_calculation' as index_name,
    'purchase_order_item_id, is_deleted, quantity' as required_columns,
    COUNT(*) as column_count
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name = 'stock_inbound_item'
AND index_name = 'idx_stock_inbound_item_calculation'
AND column_name IN ('purchase_order_item_id', 'is_deleted', 'quantity');

-- ==========================================
-- 第三步：索引优化建议
-- ==========================================

-- 建议1：移除重复索引
SELECT 
    '=== 优化建议 ===' as analysis_type;

SELECT 
    'REMOVE_DUPLICATE' as action,
    'idx_stock_inbound_item_purchase_order_item_id' as index_to_remove,
    'idx_purchase_order_item_id' as keep_index,
    '两个索引功能重复，保留较短名称的索引' as reason;

-- 建议2：创建动态计算所需的复合索引
SELECT 
    'CREATE_COMPOSITE' as action,
    'idx_stock_inbound_item_calculation' as index_to_create,
    'purchase_order_item_id, is_deleted, quantity' as columns,
    '优化动态计算已入库数量的查询性能' as reason;

-- ==========================================
-- 第四步：执行优化SQL语句
-- ==========================================

-- 注意：请在业务低峰期执行以下操作

-- 1. 移除重复索引（如果确认idx_purchase_order_item_id存在且功能相同）
-- DROP INDEX idx_stock_inbound_item_purchase_order_item_id ON stock_inbound_item;

-- 2. 创建动态计算所需的复合索引
-- CREATE INDEX IF NOT EXISTS idx_stock_inbound_item_calculation 
-- ON stock_inbound_item(purchase_order_item_id, is_deleted, quantity);

-- ==========================================
-- 第五步：验证优化效果
-- ==========================================

-- 测试动态计算查询的执行计划
SELECT 
    '=== 查询性能测试 ===' as test_type;

-- 单个采购订单明细的已入库数量计算
EXPLAIN FORMAT=JSON
SELECT COALESCE(SUM(quantity), 0) 
FROM stock_inbound_item 
WHERE purchase_order_item_id = 1 
AND is_deleted = false;

-- 批量采购订单明细的已入库数量计算
EXPLAIN FORMAT=JSON
SELECT purchase_order_item_id, COALESCE(SUM(quantity), 0) 
FROM stock_inbound_item 
WHERE purchase_order_item_id IN (1, 2, 3, 4, 5) 
AND is_deleted = false 
GROUP BY purchase_order_item_id;

-- ==========================================
-- 第六步：最终索引状态检查
-- ==========================================

-- 查看优化后的索引列表
SELECT 
    '=== 最终索引状态 ===' as final_status;

SELECT 
    index_name,
    GROUP_CONCAT(column_name ORDER BY seq_in_index) as columns,
    index_type,
    CASE WHEN non_unique = 0 THEN 'UNIQUE' ELSE 'NON-UNIQUE' END as uniqueness,
    CASE 
        WHEN index_name = 'PRIMARY' THEN '主键索引'
        WHEN index_name = 'idx_created_time' THEN '创建时间查询'
        WHEN index_name = 'idx_inbound_id' THEN '入库单关联'
        WHEN index_name = 'idx_purchase_order_item_id' THEN '采购订单明细关联'
        WHEN index_name = 'idx_stock_inbound_item_is_deleted' THEN '软删除过滤'
        WHEN index_name = 'idx_supplier_delivery_no' THEN '供应商送货单号'
        WHEN index_name = 'idx_stock_inbound_item_calculation' THEN '动态计算复合索引'
        ELSE '其他用途'
    END as purpose
FROM information_schema.statistics 
WHERE table_schema = DATABASE()
AND table_name = 'stock_inbound_item'
GROUP BY index_name, index_type, non_unique
ORDER BY 
    CASE 
        WHEN index_name = 'PRIMARY' THEN 1
        WHEN index_name LIKE 'idx_stock_inbound_item_calculation' THEN 2
        WHEN index_name LIKE 'idx_purchase_order_item_id' THEN 3
        ELSE 4
    END, index_name;
