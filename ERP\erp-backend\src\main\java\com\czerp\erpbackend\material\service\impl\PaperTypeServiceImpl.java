package com.czerp.erpbackend.material.service.impl;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.material.dto.PaperTypeDTO;
import com.czerp.erpbackend.material.dto.request.CreatePaperTypeRequest;
import com.czerp.erpbackend.material.dto.request.UpdatePaperTypeRequest;
import com.czerp.erpbackend.material.entity.PaperType;
import com.czerp.erpbackend.material.mapper.PaperTypeMapper;
import com.czerp.erpbackend.material.mapper.PaperTypeMapperHelper;
import com.czerp.erpbackend.material.repository.PaperTypeRepository;
import com.czerp.erpbackend.material.service.PaperTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 纸质类别服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaperTypeServiceImpl implements PaperTypeService {

    private final PaperTypeRepository paperTypeRepository;
    private final PaperTypeMapper paperTypeMapper;
    private final PaperTypeMapperHelper paperTypeMapperHelper;

    /**
     * 查询所有纸质类别
     * @return 纸质类别列表
     */
    @Override
    public List<PaperTypeDTO> findAllPaperTypes() {
        log.debug("Finding all paper types");
        List<PaperType> paperTypes = paperTypeRepository.findAll();
        // 使用辅助类进行映射，确保addMarginMm字段被正确映射
        return paperTypeMapperHelper.manualToDtoList(paperTypes);
    }

    /**
     * 根据ID查询纸质类别
     * @param id 纸质类别ID
     * @return 纸质类别信息
     */
    @Override
    public PaperTypeDTO findPaperTypeById(Integer id) {
        log.debug("Finding paper type by id: {}", id);
        PaperType paperType = paperTypeRepository.findById(id)
                .orElseThrow(() -> new BusinessException("纸质类别不存在"));
        // 使用辅助类进行映射，确保addMarginMm字段被正确映射
        return paperTypeMapperHelper.manualToDto(paperType);
    }

    /**
     * 根据纸板类别名称查询纸质类别
     * @param paperTypeName 纸板类别名称
     * @return 纸质类别信息
     */
    @Override
    public PaperTypeDTO findPaperTypeByName(String paperTypeName) {
        log.debug("Finding paper type by name: {}", paperTypeName);
        PaperType paperType = paperTypeRepository.findByPaperTypeName(paperTypeName)
                .orElseThrow(() -> new BusinessException("纸质类别不存在"));
        // 使用辅助类进行映射，确保addMarginMm字段被正确映射
        return paperTypeMapperHelper.manualToDto(paperType);
    }

    /**
     * 创建纸质类别
     * @param request 创建请求
     * @return 纸质类别信息
     */
    @Override
    @Transactional
    public PaperTypeDTO createPaperType(CreatePaperTypeRequest request) {
        log.debug("Creating paper type with request: {}", request);

        // 检查纸质类别名称是否已存在
        if (paperTypeRepository.existsByPaperTypeName(request.getPaperTypeName())) {
            throw new BusinessException("纸质类别名称已存在");
        }

        // 创建纸质类别
        PaperType paperType = new PaperType();
        paperTypeMapper.updateEntityFromCreateRequest(request, paperType);

        // 保存纸质类别
        paperType = paperTypeRepository.save(paperType);

        // 使用辅助类进行映射，确保addMarginMm字段被正确映射
        return paperTypeMapperHelper.manualToDto(paperType);
    }

    /**
     * 更新纸质类别
     * @param id 纸质类别ID
     * @param request 更新请求
     * @return 纸质类别信息
     */
    @Override
    @Transactional
    public PaperTypeDTO updatePaperType(Integer id, UpdatePaperTypeRequest request) {
        log.debug("Updating paper type with id: {} and request: {}", id, request);

        // 查询纸质类别
        PaperType paperType = paperTypeRepository.findById(id)
                .orElseThrow(() -> new BusinessException("纸质类别不存在"));

        // 检查纸质类别名称是否已存在
        if (StringUtils.hasText(request.getPaperTypeName()) &&
                !request.getPaperTypeName().equals(paperType.getPaperTypeName()) &&
                paperTypeRepository.existsByPaperTypeName(request.getPaperTypeName())) {
            throw new BusinessException("纸质类别名称已存在");
        }

        // 更新纸质类别
        paperTypeMapper.updateEntityFromUpdateRequest(request, paperType);

        // 保存纸质类别
        paperType = paperTypeRepository.save(paperType);

        // 使用辅助类进行映射，确保addMarginMm字段被正确映射
        return paperTypeMapperHelper.manualToDto(paperType);
    }

    /**
     * 删除纸质类别
     * @param id 纸质类别ID
     */
    @Override
    @Transactional
    public void deletePaperType(Integer id) {
        log.debug("Deleting paper type with id: {}", id);

        // 查询纸质类别
        PaperType paperType = paperTypeRepository.findById(id)
                .orElseThrow(() -> new BusinessException("纸质类别不存在"));

        // 逻辑删除
        paperType.setIsDeleted(true);
        paperTypeRepository.save(paperType);
    }

    /**
     * 批量删除纸质类别
     * @param ids 纸质类别ID列表
     */
    @Override
    @Transactional
    public void batchDeletePaperTypes(List<Integer> ids) {
        log.debug("Batch deleting paper types with ids: {}", ids);

        // 查询纸质类别
        List<PaperType> paperTypes = paperTypeRepository.findAllById(ids);

        // 逻辑删除
        paperTypes.forEach(paperType -> paperType.setIsDeleted(true));
        paperTypeRepository.saveAll(paperTypes);
    }
}
