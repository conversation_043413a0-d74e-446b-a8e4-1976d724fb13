package com.czerp.erpbackend.purchase.service.impl;

import com.czerp.erpbackend.purchase.service.PurchaseOrderItemCacheService;
import com.czerp.erpbackend.purchase.service.PurchaseOrderItemCalculationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 采购订单明细缓存管理服务实现
 * 提供统一的缓存失效和管理功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PurchaseOrderItemCacheServiceImpl implements PurchaseOrderItemCacheService {

    private final CacheManager cacheManager;
    private final PurchaseOrderItemCalculationService calculationService;

    private static final String RECEIVED_QUANTITY_CACHE = "receivedQuantity";
    private static final String AVAILABILITY_CACHE = "purchaseOrderItemAvailability";

    @Override
    public void evictPurchaseOrderItemCache(Long purchaseOrderItemId) {
        if (purchaseOrderItemId == null) {
            return;
        }

        try {
            evictReceivedQuantityCache(purchaseOrderItemId);
            evictAvailabilityCache(purchaseOrderItemId);
            log.debug("Evicted all caches for purchase order item: {}", purchaseOrderItemId);
        } catch (Exception e) {
            log.error("Failed to evict caches for purchase order item: {}", purchaseOrderItemId, e);
        }
    }

    @Override
    public void evictPurchaseOrderItemCaches(Set<Long> purchaseOrderItemIds) {
        if (purchaseOrderItemIds == null || purchaseOrderItemIds.isEmpty()) {
            return;
        }

        try {
            evictReceivedQuantityCaches(purchaseOrderItemIds);
            evictAvailabilityCaches(purchaseOrderItemIds);
            log.info("Evicted all caches for {} purchase order items", purchaseOrderItemIds.size());
        } catch (Exception e) {
            log.error("Failed to evict caches for purchase order items: {}", purchaseOrderItemIds, e);
        }
    }

    @Override
    public void evictReceivedQuantityCache(Long purchaseOrderItemId) {
        if (purchaseOrderItemId == null) {
            return;
        }

        Cache cache = cacheManager.getCache(RECEIVED_QUANTITY_CACHE);
        if (cache != null) {
            cache.evict(purchaseOrderItemId);
            log.debug("Evicted receivedQuantity cache for item: {}", purchaseOrderItemId);
        }
    }

    @Override
    public void evictReceivedQuantityCaches(Set<Long> purchaseOrderItemIds) {
        if (purchaseOrderItemIds == null || purchaseOrderItemIds.isEmpty()) {
            return;
        }

        Cache cache = cacheManager.getCache(RECEIVED_QUANTITY_CACHE);
        if (cache != null) {
            for (Long itemId : purchaseOrderItemIds) {
                cache.evict(itemId);
            }
            log.debug("Evicted receivedQuantity cache for {} items", purchaseOrderItemIds.size());
        }
    }

    @Override
    public void evictAvailabilityCache(Long purchaseOrderItemId) {
        if (purchaseOrderItemId == null) {
            return;
        }

        Cache cache = cacheManager.getCache(AVAILABILITY_CACHE);
        if (cache != null) {
            cache.evict(purchaseOrderItemId);
            log.debug("Evicted availability cache for item: {}", purchaseOrderItemId);
        }
    }

    @Override
    public void evictAvailabilityCaches(Set<Long> purchaseOrderItemIds) {
        if (purchaseOrderItemIds == null || purchaseOrderItemIds.isEmpty()) {
            return;
        }

        Cache cache = cacheManager.getCache(AVAILABILITY_CACHE);
        if (cache != null) {
            for (Long itemId : purchaseOrderItemIds) {
                cache.evict(itemId);
            }
            log.debug("Evicted availability cache for {} items", purchaseOrderItemIds.size());
        }
    }

    @Override
    public void evictAllPurchaseOrderItemCaches() {
        try {
            Cache receivedQuantityCache = cacheManager.getCache(RECEIVED_QUANTITY_CACHE);
            if (receivedQuantityCache != null) {
                receivedQuantityCache.clear();
                log.info("Cleared all receivedQuantity cache");
            }

            Cache availabilityCache = cacheManager.getCache(AVAILABILITY_CACHE);
            if (availabilityCache != null) {
                availabilityCache.clear();
                log.info("Cleared all availability cache");
            }

            log.warn("Cleared ALL purchase order item caches - this should only be done during maintenance");
        } catch (Exception e) {
            log.error("Failed to clear all purchase order item caches", e);
        }
    }

    @Override
    public void warmUpCaches(List<Long> purchaseOrderItemIds) {
        if (purchaseOrderItemIds == null || purchaseOrderItemIds.isEmpty()) {
            return;
        }

        log.info("Starting cache warm-up for {} purchase order items", purchaseOrderItemIds.size());

        // 异步预热缓存，避免阻塞主线程
        CompletableFuture.runAsync(() -> {
            try {
                // 批量计算已入库数量，触发缓存加载
                calculationService.calculateReceivedQuantitiesBatch(purchaseOrderItemIds);
                log.info("Cache warm-up completed for {} items", purchaseOrderItemIds.size());
            } catch (Exception e) {
                log.error("Cache warm-up failed for items: {}", purchaseOrderItemIds, e);
            }
        });
    }

    @Override
    public CacheStatistics getCacheStatistics() {
        CacheStatistics stats = new CacheStatistics();

        try {
            // 获取已入库数量缓存统计
            Cache receivedQuantityCache = cacheManager.getCache(RECEIVED_QUANTITY_CACHE);
            if (receivedQuantityCache != null) {
                // 注意：ConcurrentMapCacheManager 不提供详细统计信息
                // 如果需要详细统计，建议使用 Caffeine 或 Redis 缓存
                stats.setReceivedQuantityCacheSize(0); // 无法获取准确大小
            }

            // 获取可用性缓存统计
            Cache availabilityCache = cacheManager.getCache(AVAILABILITY_CACHE);
            if (availabilityCache != null) {
                stats.setAvailabilityCacheSize(0); // 无法获取准确大小
            }

            log.debug("Retrieved cache statistics: {}", stats);
        } catch (Exception e) {
            log.error("Failed to retrieve cache statistics", e);
        }

        return stats;
    }
}
