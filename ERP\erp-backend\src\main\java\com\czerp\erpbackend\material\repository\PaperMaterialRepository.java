package com.czerp.erpbackend.material.repository;

import com.czerp.erpbackend.material.entity.PaperMaterial;
import com.czerp.erpbackend.material.entity.PaperType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 纸质资料存储库
 */
@Repository
public interface PaperMaterialRepository extends JpaRepository<PaperMaterial, Long>, JpaSpecificationExecutor<PaperMaterial> {

    /**
     * 根据纸质编码查找纸质资料
     * @param paperCode 纸质编码
     * @return 纸质资料
     */
    Optional<PaperMaterial> findByPaperCode(String paperCode);

    /**
     * 判断纸质编码是否存在
     * @param paperCode 纸质编码
     * @return 是否存在
     */
    boolean existsByPaperCode(String paperCode);

    /**
     * 判断纸质编码是否存在（排除指定ID）
     * @param paperCode 纸质编码
     * @param id ID
     * @return 是否存在
     */
    boolean existsByPaperCodeAndIdNot(String paperCode, Long id);

    /**
     * 根据纸质名称查找纸质资料
     * @param paperName 纸质名称
     * @return 纸质资料
     */
    Optional<PaperMaterial> findByPaperName(String paperName);

    /**
     * 根据纸质名称列表批量查找纸质资料
     * @param paperNames 纸质名称列表
     * @return 纸质资料列表
     */
    List<PaperMaterial> findByPaperNameIn(List<String> paperNames);

    /**
     * 判断纸质名称是否存在
     * @param paperName 纸质名称
     * @return 是否存在
     */
    boolean existsByPaperName(String paperName);

    /**
     * 判断纸质名称是否存在（排除指定ID）
     * @param paperName 纸质名称
     * @param id ID
     * @return 是否存在
     */
    boolean existsByPaperNameAndIdNot(String paperName, Long id);

    /**
     * 查询所有未停用的纸质资料
     * @return 纸质资料列表
     */
    List<PaperMaterial> findByIsDisabledFalse();

    /**
     * 查询所有标准纸质资料
     * @return 纸质资料列表
     */
    List<PaperMaterial> findByIsStandardTrue();

    /**
     * 查询所有标准且未停用的纸质资料
     * @return 纸质资料列表
     */
    List<PaperMaterial> findByIsStandardTrueAndIsDisabledFalse();

    /**
     * 根据纸类查询纸质资料
     * @param paperType 纸类
     * @return 纸质资料列表
     */
    List<PaperMaterial> findByPaperType(PaperType paperType);

    /**
     * 根据纸类ID查询纸质资料
     * @param paperTypeId 纸类ID
     * @return 纸质资料列表
     */
    List<PaperMaterial> findByPaperType_Id(Integer paperTypeId);

    /**
     * 根据纸类名称查询纸质资料
     * @param paperTypeName 纸类名称
     * @return 纸质资料列表
     */
    List<PaperMaterial> findByPaperType_PaperTypeName(String paperTypeName);

    /**
     * 根据纸类和楞别查询纸质资料
     * @param paperType 纸类
     * @param fluteType 楞别
     * @return 纸质资料列表
     */
    List<PaperMaterial> findByPaperTypeAndFluteType(PaperType paperType, String fluteType);

    /**
     * 根据纸类名称和楞别查询纸质资料
     * @param paperTypeName 纸类名称
     * @param fluteType 楞别
     * @return 纸质资料列表
     */
    List<PaperMaterial> findByPaperType_PaperTypeNameAndFluteType(String paperTypeName, String fluteType);
}
