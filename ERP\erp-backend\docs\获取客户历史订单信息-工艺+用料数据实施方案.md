# 获取客户历史订单信息 - 工艺+用料数据实施方案

## 需求概述

扩展现有的客户历史订单明细查询API，添加工艺和用料数据字段，以便前端可以在查询客户历史订单明细时，同时获取相关的工艺和用料数据。

## 实施方案

### 1. 修改SalesOrderItemDTO类

在SalesOrderItemDTO类中添加processes字段，用于存储工序信息：

```java
/**
 * 工序信息列表
 * 非数据库字段，用于前端展示和提交
 */
private List<SalesOrderProcessDTO> processes = new ArrayList<>();
```

SalesOrderItemDTO类已经包含了materials字段，用于存储材料信息：

```java
/**
 * 材料信息列表
 * 非数据库字段，用于前端展示和提交
 */
private List<SalesOrderMaterialDTO> materials = new ArrayList<>();
```

### 2. 修改SalesOrderServiceImpl.getHistoricalItemsByCustomerId方法

扩展现有方法，为每个订单明细项加载工序和材料信息：

```java
/**
 * 根据客户ID查询历史订单明细
 * @param customerId 客户ID
 * @param keyword 关键词（可选，用于搜索客方货号或品名）
 * @return 历史订单明细列表
 */
@Override
@Transactional(readOnly = true)
public List<SalesOrderItemDTO> getHistoricalItemsByCustomerId(String customerId, String keyword) {
    log.info("Getting historical items by customer id: {}, keyword: {}", customerId, keyword);

    // 1. 先查询该客户是否存在
    Customer customer = customerRepository.findById(customerId)
            .orElseThrow(() -> new BusinessException("客户不存在"));

    // 2. 构建查询条件
    Specification<SalesOrderItem> spec = (root, query, cb) -> {
        Join<SalesOrderItem, SalesOrder> orderJoin = root.join("order", JoinType.INNER);

        List<Predicate> predicates = new ArrayList<>();

        // 客户编码条件
        predicates.add(cb.equal(orderJoin.get("customerCode"), customer.getCustomerCode()));

        // 关键词搜索（如果提供）
        if (StringUtils.hasText(keyword)) {
            predicates.add(cb.or(
                cb.like(root.get("customerProductCode"), "%" + keyword + "%"),
                cb.like(root.get("productName"), "%" + keyword + "%")
            ));
        }

        // 排除已删除的订单
        predicates.add(cb.equal(orderJoin.get("isDeleted"), false));

        return cb.and(predicates.toArray(new Predicate[0]));
    };

    // 3. 构建排序（按创建时间降序，最新的在前面）
    Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");

    // 4. 执行查询
    List<SalesOrderItem> items = salesOrderItemRepository.findAll(spec, sort);

    // 5. 去重处理（按客方货号去重，保留最新的记录）
    Map<String, SalesOrderItem> uniqueItems = new LinkedHashMap<>();
    for (SalesOrderItem item : items) {
        if (StringUtils.hasText(item.getCustomerProductCode())) {
            uniqueItems.putIfAbsent(item.getCustomerProductCode(), item);
        }
    }

    // 6. 转换为DTO
    List<SalesOrderItemDTO> itemDTOs = uniqueItems.values().stream()
            .map(this::convertToHistoricalItemDTO)
            .collect(Collectors.toList());
            
    // 7. 批量加载工序信息
    if (!itemDTOs.isEmpty()) {
        // 获取所有订单ID
        Set<String> orderIds = itemDTOs.stream()
                .map(SalesOrderItemDTO::getOrderId)
                .collect(Collectors.toSet());
                
        // 批量查询工序信息
        Map<String, List<SalesOrderProcessDTO>> processesByOrderId = new HashMap<>();
        for (String orderId : orderIds) {
            List<SalesOrderProcessDTO> processes = salesOrderProcessService.getProcessesByOrderId(orderId);
            processesByOrderId.put(orderId, processes);
        }
        
        // 设置工序信息到每个订单明细DTO
        for (SalesOrderItemDTO itemDTO : itemDTOs) {
            List<SalesOrderProcessDTO> processes = processesByOrderId.getOrDefault(itemDTO.getOrderId(), new ArrayList<>());
            itemDTO.setProcesses(processes);
        }
    }
    
    // 8. 批量加载材料信息
    if (!itemDTOs.isEmpty()) {
        // 获取所有订单明细ID
        List<String> itemIds = itemDTOs.stream()
                .map(SalesOrderItemDTO::getId)
                .collect(Collectors.toList());
                
        // 批量查询材料信息
        Map<String, List<SalesOrderMaterialDTO>> materialsByItemId = new HashMap<>();
        for (String itemId : itemIds) {
            List<SalesOrderMaterialDTO> materials = salesOrderMaterialService.getMaterialsByOrderItemId(itemId);
            materialsByItemId.put(itemId, materials);
        }
        
        // 设置材料信息到每个订单明细DTO
        for (SalesOrderItemDTO itemDTO : itemDTOs) {
            List<SalesOrderMaterialDTO> materials = materialsByItemId.getOrDefault(itemDTO.getId(), new ArrayList<>());
            itemDTO.setMaterials(materials);
        }
    }

    return itemDTOs;
}
```

### 3. 更新API文档

更新`获取客户历史订单货品信息API接口说明.txt`文档，添加工序和材料信息的字段说明、响应示例和前端实现建议。

## 性能优化考虑

1. **批量查询**：使用批量查询工序和材料信息，而不是逐个查询，减少数据库访问次数
2. **延迟加载**：可以考虑只有在前端明确请求时才加载工序和材料信息，通过增加查询参数控制
3. **数据量控制**：考虑限制返回的历史记录数量，避免数据量过大影响性能

## 注意事项

1. 确保SalesOrderItemDTO类中添加processes字段
2. 考虑数据量和性能问题，可能需要添加分页或限制返回记录数
3. 考虑添加查询参数控制是否加载工序和材料信息，以便前端可以根据需要选择是否加载这些额外数据
4. 确保工序和材料信息的关联关系正确，避免数据混乱
5. 工序信息是按订单ID关联的，而材料信息是按订单明细ID关联的
6. 复制工序和材料信息时，需要注意调整相关ID关联关系

## 后续优化方向

1. 添加查询参数控制是否加载工序和材料信息，以提高性能
2. 实现分页查询，避免数据量过大
3. 优化批量查询逻辑，减少数据库访问次数
4. 考虑缓存常用客户的历史订单明细数据，提高查询性能
