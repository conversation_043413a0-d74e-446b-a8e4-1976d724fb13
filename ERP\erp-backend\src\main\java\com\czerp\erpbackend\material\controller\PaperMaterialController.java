package com.czerp.erpbackend.material.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.ImportResult;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.util.ExcelTemplateUtil;
import com.czerp.erpbackend.material.dto.CreatePaperMaterialRequest;
import com.czerp.erpbackend.material.dto.PaperMaterialDTO;
import com.czerp.erpbackend.material.dto.PaperMaterialQueryRequest;
import com.czerp.erpbackend.material.dto.UpdatePaperMaterialRequest;
import com.czerp.erpbackend.material.service.PaperMaterialService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;

import java.util.List;

/**
 * 纸质资料控制器
 */
@RestController
@RequestMapping("/material/paper-materials")
@Tag(name = "纸质资料管理", description = "纸质资料管理相关接口")
@RequiredArgsConstructor
@Slf4j
public class PaperMaterialController {

    private final PaperMaterialService paperMaterialService;

    /**
     * 分页查询纸质资料列表
     * @param request 查询请求
     * @return 纸质资料分页列表
     */
    @GetMapping
    @Operation(summary = "分页查询纸质资料列表", description = "分页查询纸质资料列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:list')")
    public ResponseEntity<ApiResponse<PageResponse<PaperMaterialDTO>>> findPaperMaterials(PaperMaterialQueryRequest request) {
        log.debug("Finding paper materials with request: {}", request);
        PageResponse<PaperMaterialDTO> response = paperMaterialService.findPaperMaterials(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 查询所有纸质资料
     * @return 纸质资料列表
     */
    @GetMapping("/all")
    @Operation(summary = "查询所有纸质资料", description = "查询所有纸质资料")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:list')")
    public ResponseEntity<ApiResponse<List<PaperMaterialDTO>>> findAllPaperMaterials() {
        log.debug("Finding all paper materials");
        List<PaperMaterialDTO> response = paperMaterialService.findAllPaperMaterials();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 查询所有未停用的纸质资料
     * @return 纸质资料列表
     */
    @GetMapping("/active")
    @Operation(summary = "查询所有未停用的纸质资料", description = "查询所有未停用的纸质资料")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:list')")
    public ResponseEntity<ApiResponse<List<PaperMaterialDTO>>> findActivePaperMaterials() {
        log.debug("Finding all active paper materials");
        List<PaperMaterialDTO> response = paperMaterialService.findActivePaperMaterials();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 查询所有标准纸质资料
     * @return 纸质资料列表
     */
    @GetMapping("/standard")
    @Operation(summary = "查询所有标准纸质资料", description = "查询所有标准纸质资料")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:list')")
    public ResponseEntity<ApiResponse<List<PaperMaterialDTO>>> findStandardPaperMaterials() {
        log.debug("Finding all standard paper materials");
        List<PaperMaterialDTO> response = paperMaterialService.findStandardPaperMaterials();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据ID查询纸质资料
     * @param id 纸质资料ID
     * @return 纸质资料信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询纸质资料", description = "根据ID查询纸质资料")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:read')")
    public ResponseEntity<ApiResponse<PaperMaterialDTO>> findPaperMaterialById(@PathVariable Long id) {
        log.debug("Finding paper material by id: {}", id);
        PaperMaterialDTO response = paperMaterialService.findPaperMaterialById(id);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 创建纸质资料
     * @param request 创建请求
     * @return 纸质资料信息
     */
    @PostMapping
    @Operation(summary = "创建纸质资料", description = "创建纸质资料")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:create')")
    public ResponseEntity<ApiResponse<PaperMaterialDTO>> createPaperMaterial(@Valid @RequestBody CreatePaperMaterialRequest request) {
        log.debug("Creating paper material with request: {}", request);
        PaperMaterialDTO response = paperMaterialService.createPaperMaterial(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 更新纸质资料
     * @param id 纸质资料ID
     * @param request 更新请求
     * @return 纸质资料信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新纸质资料", description = "更新纸质资料")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:update')")
    public ResponseEntity<ApiResponse<PaperMaterialDTO>> updatePaperMaterial(@PathVariable Long id, @Valid @RequestBody UpdatePaperMaterialRequest request) {
        log.debug("Updating paper material with id: {} and request: {}", id, request);
        PaperMaterialDTO response = paperMaterialService.updatePaperMaterial(id, request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 删除纸质资料
     * @param id 纸质资料ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除纸质资料", description = "删除纸质资料")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:delete')")
    public ResponseEntity<ApiResponse<Void>> deletePaperMaterial(@PathVariable Long id) {
        log.debug("Deleting paper material with id: {}", id);
        paperMaterialService.deletePaperMaterial(id);
        return ResponseEntity.ok(ApiResponse.success());
    }

    /**
     * 批量删除纸质资料
     * @param ids 纸质资料ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除纸质资料", description = "批量删除纸质资料")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:delete')")
    public ResponseEntity<ApiResponse<Void>> batchDeletePaperMaterials(@RequestBody List<Long> ids) {
        log.debug("Batch deleting paper materials with ids: {}", ids);
        paperMaterialService.batchDeletePaperMaterials(ids);
        return ResponseEntity.ok(ApiResponse.success());
    }

    /**
     * 导入纸质资料
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "导入纸质资料", description = "通过Excel文件批量导入纸质资料")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:create')")
    public ResponseEntity<ApiResponse<ImportResult>> importPaperMaterials(@RequestParam("file") MultipartFile file) {
        log.debug("Importing paper materials from file: {}", file.getOriginalFilename());
        ImportResult result = paperMaterialService.importPaperMaterials(file);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    /**
     * 下载纸质资料导入模板
     * @return Excel模板文件
     */
    @GetMapping("/import/template")
    @Operation(summary = "下载纸质资料导入模板", description = "下载纸质资料导入模板Excel文件")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-material:create')")
    public ResponseEntity<byte[]> downloadImportTemplate() {
        log.debug("Downloading paper material import template");

        try {
            // 定义表头
            List<String> headers = Arrays.asList(
                    "纸质编码*", "纸质*", "纸类", "楞别", "标准纸质", "生产代号",
                    "面纸", "芯纸1", "中隔1", "芯纸2", "中隔2", "芯纸3", "里纸",
                    "层数", "重量(千克/千平方英寸)", "重量(千克/平方米)", "边压强度", "纸板耐破度",
                    "对应标准纸质", "默认供应商名称", "备注", "停用"
            );

            // 定义必填列
            List<Integer> requiredColumns = Arrays.asList(0, 1);

            // 生成模板
            byte[] templateBytes = ExcelTemplateUtil.createImportTemplate("纸质资料", headers, requiredColumns);

            // 设置响应头
            HttpHeaders headers2 = new HttpHeaders();
            headers2.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers2.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=paper_material_import_template.xlsx");

            return ResponseEntity.ok()
                    .headers(headers2)
                    .body(templateBytes);
        } catch (IOException e) {
            log.error("Error creating import template: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
