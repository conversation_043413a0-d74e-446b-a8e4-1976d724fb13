package com.czerp.erpbackend.sales.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 销售订单材料信息实体
 */
@Entity
@Table(name = "sales_order_material")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderMaterial extends BaseEntity {

    /**
     * 创建人姓名
     */
    @Column(name = "created_by_name", length = 50)
    private String createdByName;

    /**
     * 更新人姓名
     */
    @Column(name = "updated_by_name", length = 50)
    private String updatedByName;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 销售订单行项目ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_item_id", nullable = false)
    private SalesOrderItem orderItem;

    /**
     * 序号
     */
    @Column(name = "serial_no")
    private Integer serialNo;

    /**
     * 纸质
     */
    @Column(name = "paper_quality", length = 100)
    private String paperQuality;

    /**
     * 纸度
     */
    @Column(name = "paper_width", precision = 10, scale = 2)
    private BigDecimal paperWidth;

    /**
     * 纸长
     */
    @Column(name = "paper_length", precision = 10, scale = 2)
    private BigDecimal paperLength;

    /**
     * 度开
     */
    @Column(name = "width_open", precision = 10, scale = 2)
    private BigDecimal widthOpen;

    /**
     * 长开
     */
    @Column(name = "length_open", precision = 10, scale = 2)
    private BigDecimal lengthOpen;

    /**
     * 纸板数
     */
    @Column(name = "board_count")
    private Integer boardCount;

    /**
     * 纸板损耗
     */
    @Column(name = "board_loss", precision = 10, scale = 2)
    private BigDecimal boardLoss;

    /**
     * 原料用量
     */
    @Column(name = "material_usage", precision = 10, scale = 2)
    private BigDecimal materialUsage;

    /**
     * 用量
     */
    @Column(name = "material_usage_count", precision = 10, scale = 2)
    private BigDecimal usage;

    /**
     * 方式
     */
    @Column(name = "method", length = 50)
    private String method;

    /**
     * 压线尺寸(纸度)
     */
    @Column(name = "press_size_width", length = 100)
    private String pressSizeWidth;

    /**
     * 压线方式
     */
    @Column(name = "press_method", length = 50)
    private String pressMethod;

    /**
     * 实际用料宽
     */
    @Column(name = "actual_material_width", precision = 10, scale = 4)
    private BigDecimal actualMaterialWidth;

    /**
     * 实际用料长
     */
    @Column(name = "actual_material_length", precision = 10, scale = 4)
    private BigDecimal actualMaterialLength;

    /**
     * 啤模
     */
    @Column(name = "die_model", length = 100)
    private String dieModel;

    /**
     * 啤模编号
     */
    @Column(name = "die_model_no", length = 50)
    private String dieModelNo;

    /**
     * 模开数
     */
    @Column(name = "die_open_count")
    private Integer dieOpenCount;

    /**
     * 啤模位置
     */
    @Column(name = "die_model_position", length = 100)
    private String dieModelPosition;

    /**
     * 啤模选中状态
     */
    @Column(name = "die_model_checked")
    private Boolean dieModelChecked;

    /**
     * 单位
     */
    @Column(name = "unit", length = 20)
    private String unit;

    /**
     * 当前库存
     */
    @Column(name = "current_inventory")
    private Integer currentInventory;

    /**
     * 可用库存
     */
    @Column(name = "available_inventory")
    private Integer availableInventory;

    /**
     * 使用库存数
     */
    @Column(name = "use_inventory_count")
    private Integer useInventoryCount;

    /**
     * 实际用料长(转换)
     */
    @Column(name = "actual_material_length_converted", precision = 10, scale = 4)
    private BigDecimal actualMaterialLengthConverted;

    /**
     * 实际用料宽(转换)
     */
    @Column(name = "actual_material_width_converted", precision = 10, scale = 4)
    private BigDecimal actualMaterialWidthConverted;

    /**
     * 供应商
     */
    @Column(name = "supplier", length = 100)
    private String supplier;

    /**
     * 材料备注
     */
    @Column(name = "material_remark", length = 500)
    private String materialRemark;

    /**
     * 已采购数
     */
    @Column(name = "purchased_count")
    private Integer purchasedCount;

    /**
     * 啤模压线
     */
    @Column(name = "die_press_line", length = 100)
    private String diePressLine;

    /**
     * 压线尺寸(纸长)
     */
    @Column(name = "press_size_length", precision = 10, scale = 2)
    private BigDecimal pressSizeLength;

    /**
     * 已入库数
     */
    @Column(name = "inbound_count")
    private Integer inboundCount;

    /**
     * 已领料数
     */
    @Column(name = "materials_received_count")
    private Integer materialsReceivedCount;
}
