package com.czerp.erpbackend.customer.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 客户盒式信息实体
 */
@Entity
@Table(name = "customer_box_info")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerBoxInfo extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 盒式编码
     */
    @Column(name = "box_code", length = 50, nullable = false)
    private String boxCode;

    /**
     * 盒式名称
     */
    @Column(name = "box_name", length = 100, nullable = false)
    private String boxName;

    /**
     * 报价公式
     */
    @Column(name = "quote_formula", length = 255)
    private String quoteFormula;

    /**
     * 算价单位
     */
    @Column(name = "calculation_unit", length = 50)
    private String calculationUnit;

    /**
     * 报价单位
     */
    @Column(name = "quote_unit", length = 50)
    private String quoteUnit;

    /**
     * 连接方式
     */
    @Column(name = "connection_method", length = 100)
    private String connectionMethod;

    /**
     * 禁止双驳
     */
    @Column(name = "forbid_double_fluting", nullable = false)
    private Boolean forbidDoubleFluting = false;

    /**
     * 长度大于此值双驳
     */
    @Column(name = "double_fluting_length_threshold", precision = 10, scale = 2)
    private BigDecimal doubleFlutingLengthThreshold;

    /**
     * 跳度公差
     */
    @Column(name = "pitch_tolerance", length = 100)
    private String pitchTolerance;

    /**
     * 盒式图形
     */
    @Column(name = "has_box_graphic", nullable = false)
    private Boolean hasBoxGraphic = false;

    /**
     * 默认盒式
     */
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    /**
     * 状态(active-启用,inactive-禁用)
     */
    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";
}
