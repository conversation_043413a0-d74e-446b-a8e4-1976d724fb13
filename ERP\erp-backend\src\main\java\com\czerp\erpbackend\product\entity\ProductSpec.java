package com.czerp.erpbackend.product.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 产品规格实体
 */
@Entity
@Table(name = "prd_spec")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProductSpec extends BaseEntity {
    
    /**
     * 规格ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;
    
    /**
     * 规格名称
     */
    @Column(name = "name", length = 50, nullable = false)
    private String name;
    
    /**
     * 规格编码
     */
    @Column(name = "code", length = 50, nullable = false, unique = true)
    private String code;
    
    /**
     * 单位
     */
    @Column(name = "unit", length = 20, nullable = false)
    private String unit;
    
    /**
     * 描述
     */
    @Column(name = "description", length = 255)
    private String description;
}
