# 货品档案公共接口文档

## 目录

- [1. 概述](#1-概述)
- [2. 通用数据结构](#2-通用数据结构)
- [3. 公共接口](#3-公共接口)
  - [3.1 获取所有启用的货品](#31-获取所有启用的货品)
  - [3.2 获取所有分类](#32-获取所有分类)
  - [3.3 获取分类树](#33-获取分类树)
  - [3.4 获取所有规格](#34-获取所有规格)
- [4. 前端适配指南](#4-前端适配指南)
  - [4.1 数据模型映射](#41-数据模型映射)
  - [4.2 API服务示例](#42-api服务示例)

## 1. 概述

货品档案公共接口提供了查询货品、分类和规格的功能，这些接口不需要权限验证，可以被其他模块直接调用。本文档详细描述了后端API的接口定义和数据结构，以及前端适配的相关指南。

## 2. 通用数据结构

### 2.1 API响应结构

所有API响应都遵循以下统一格式：

```typescript
interface ApiResponse<T> {
  success: boolean;       // 请求是否成功
  code: string;           // 状态码
  message: string;        // 提示信息
  data: T;                // 响应数据
}
```

### 2.2 货品数据结构

```typescript
interface ProductDTO {
  id: string;                 // 主键ID
  code: string;               // 货品编码
  name: string;               // 货品名称
  categoryId: string;         // 分类ID
  categoryName: string;       // 分类名称
  specId: string;             // 规格ID
  specName: string;           // 规格名称
  unit: string;               // 单位
  price: number;              // 价格
  description: string;        // 描述
  imageUrl: string;           // 图片URL
  disabled: boolean;          // 是否禁用
  createdBy: string;          // 创建人
  createdTime: string;        // 创建时间
  updatedBy: string;          // 更新人
  updatedTime: string;        // 更新时间
}
```

### 2.3 分类数据结构

```typescript
interface ProductCategoryDTO {
  id: string;                 // 主键ID
  code: string;               // 分类编码
  name: string;               // 分类名称
  parentId: string;           // 父分类ID
  parentName: string;         // 父分类名称
  sort: number;               // 排序
  children: ProductCategoryDTO[]; // 子分类列表
}
```

### 2.4 规格数据结构

```typescript
interface ProductSpecDTO {
  id: string;                 // 主键ID
  code: string;               // 规格编码
  name: string;               // 规格名称
  description: string;        // 描述
}
```

## 3. 公共接口

### 3.1 获取所有启用的货品

#### 请求信息

- **URL**: `/api/public/products`
- **方法**: GET
- **描述**: 获取系统中所有启用的货品列表
- **权限**: 无需权限

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "code": "P001",
      "name": "测试货品",
      "categoryId": "1",
      "categoryName": "测试分类",
      "specId": "1",
      "specName": "测试规格",
      "unit": "个",
      "price": 100.00,
      "description": "报价公式: (L+W)*2*H*单价, 连接方式: 胶粘, 跳度公差: ±2mm",
      "imageUrl": "http://example.com/image.jpg",
      "disabled": false,
      "createdBy": "admin",
      "createdTime": "2023-01-01T00:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-01-01T00:00:00"
    }
  ]
}
```

### 3.2 获取所有分类

#### 请求信息

- **URL**: `/api/public/products/categories`
- **方法**: GET
- **描述**: 获取系统中所有产品分类列表
- **权限**: 无需权限

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "code": "C001",
      "name": "测试分类",
      "parentId": null,
      "parentName": null,
      "sort": 1,
      "children": []
    }
  ]
}
```

### 3.3 获取分类树

#### 请求信息

- **URL**: `/api/public/products/categories/tree`
- **方法**: GET
- **描述**: 获取产品分类树结构
- **权限**: 无需权限

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "code": "C001",
      "name": "测试分类",
      "parentId": null,
      "parentName": null,
      "sort": 1,
      "children": [
        {
          "id": "2",
          "code": "C002",
          "name": "子分类",
          "parentId": "1",
          "parentName": "测试分类",
          "sort": 1,
          "children": []
        }
      ]
    }
  ]
}
```

### 3.4 获取所有规格

#### 请求信息

- **URL**: `/api/public/products/specs`
- **方法**: GET
- **描述**: 获取系统中所有产品规格列表
- **权限**: 无需权限

#### 响应信息

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "code": "S001",
      "name": "测试规格",
      "description": "测试规格描述"
    }
  ]
}
```

## 4. 前端适配指南

### 4.1 数据模型映射

前端可以定义以下接口来映射后端的数据结构：

```typescript
// 货品接口
export interface Product {
  id: string;
  code: string;
  name: string;
  categoryId: string;
  categoryName: string;
  specId: string;
  specName: string;
  unit: string;
  price: number;
  description: string;
  imageUrl: string;
  disabled: boolean;
  createdBy: string;
  createdTime: string;
  updatedBy: string;
  updatedTime: string;
}

// 分类接口
export interface ProductCategory {
  id: string;
  code: string;
  name: string;
  parentId: string;
  parentName: string;
  sort: number;
  children: ProductCategory[];
}

// 规格接口
export interface ProductSpec {
  id: string;
  code: string;
  name: string;
  description: string;
}
```

### 4.2 API服务示例

```typescript
import { get } from './request';
import type { Product, ProductCategory, ProductSpec } from '@/types/product';
import type { ApiResponse } from '@/types/api';

/**
 * 获取所有启用的货品
 */
export function getAllActiveProducts(): Promise<ApiResponse<Product[]>> {
  return get<Product[]>('/public/products');
}

/**
 * 获取所有分类
 */
export function getAllCategories(): Promise<ApiResponse<ProductCategory[]>> {
  return get<ProductCategory[]>('/public/products/categories');
}

/**
 * 获取分类树
 */
export function getCategoryTree(): Promise<ApiResponse<ProductCategory[]>> {
  return get<ProductCategory[]>('/public/products/categories/tree');
}

/**
 * 获取所有规格
 */
export function getAllSpecs(): Promise<ApiResponse<ProductSpec[]>> {
  return get<ProductSpec[]>('/public/products/specs');
}
```
