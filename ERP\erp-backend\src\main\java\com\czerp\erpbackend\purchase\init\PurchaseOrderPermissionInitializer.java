package com.czerp.erpbackend.purchase.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 采购订单权限初始化
 */
@Component
@RequiredArgsConstructor
@Order(300)
@Slf4j
public class PurchaseOrderPermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing purchase order permissions...");

        // 创建采购订单权限
        List<Permission> permissions = new ArrayList<>();

        // 采购订单父权限
        Permission purchaseOrderParent = createPermissionIfNotExists(
                "purchase:order",
                "采购订单管理",
                "menu",
                1,
                "purchase",
                null
        );
        permissions.add(purchaseOrderParent);

        // 采购订单子权限
        permissions.add(createPermissionIfNotExists(
                "purchase:order:read",
                "查看采购订单",
                "button",
                1,
                null,
                purchaseOrderParent.getId()
        ));
        permissions.add(createPermissionIfNotExists(
                "purchase:order:create",
                "创建采购订单",
                "button",
                2,
                null,
                purchaseOrderParent.getId()
        ));
        permissions.add(createPermissionIfNotExists(
                "purchase:order:update",
                "更新采购订单",
                "button",
                3,
                null,
                purchaseOrderParent.getId()
        ));
        permissions.add(createPermissionIfNotExists(
                "purchase:order:delete",
                "删除采购订单",
                "button",
                4,
                null,
                purchaseOrderParent.getId()
        ));

        // 为管理员角色分配权限
        roleRepository.findByCode("admin").ifPresent(adminRole -> {
            List<RolePermission> rolePermissions = new ArrayList<>();
            for (Permission permission : permissions) {
                if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.getId(), permission.getId())) {
                    RolePermission rolePermission = new RolePermission();
                    rolePermission.setId(UUID.randomUUID().toString());
                    rolePermission.setRoleId(adminRole.getId());
                    rolePermission.setPermissionId(permission.getId());

                    rolePermissions.add(rolePermission);
                    log.info("Assigning permission {} to admin role", permission.getCode());
                }
            }

            if (!rolePermissions.isEmpty()) {
                rolePermissionRepository.saveAll(rolePermissions);
                log.info("Assigned {} permissions to admin role", rolePermissions.size());
            }
        });

        log.info("Purchase order permissions initialized successfully");
    }

    /**
     * 创建权限（如果不存在）
     * @param code 权限编码
     * @param name 权限名称
     * @param type 权限类型
     * @param sort 排序
     * @param module 模块
     * @param parentId 父权限ID
     * @return 权限
     */
    private Permission createPermissionIfNotExists(String code, String name, String type, Integer sort, String module, String parentId) {
        return permissionRepository.findByCode(code).orElseGet(() -> {
            Permission permission = new Permission();
            permission.setId(UUID.randomUUID().toString());
            permission.setCode(code);
            permission.setName(name);
            permission.setType(type);
            permission.setSort(sort);
            permission.setParentId(parentId);
            permission.setStatus("active");
            permission.setIsDeleted(false);
            Permission savedPermission = permissionRepository.save(permission);
            log.info("Created permission: {}", code);
            return savedPermission;
        });
    }
}
