package com.czerp.erpbackend.material.mapper;

import com.czerp.erpbackend.material.dto.PaperTypeDTO;
import com.czerp.erpbackend.material.dto.request.CreatePaperTypeRequest;
import com.czerp.erpbackend.material.dto.request.UpdatePaperTypeRequest;
import com.czerp.erpbackend.material.entity.PaperType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * 纸质类别Mapper
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PaperTypeMapper {

    /**
     * 实体转DTO
     * @param entity 实体
     * @return DTO
     */
    PaperTypeDTO toDto(PaperType entity);

    /**
     * 实体列表转DTO列表
     * @param entities 实体列表
     * @return DTO列表
     */
    List<PaperTypeDTO> toDtoList(List<PaperType> entities);

    /**
     * DTO转实体
     * @param dto DTO
     * @return 实体
     */
    PaperType toEntity(PaperTypeDTO dto);

    /**
     * 创建请求更新实体
     * @param request 创建请求
     * @param entity 实体
     */
    void updateEntityFromCreateRequest(CreatePaperTypeRequest request, @MappingTarget PaperType entity);

    /**
     * 更新请求更新实体
     * @param request 更新请求
     * @param entity 实体
     */
    void updateEntityFromUpdateRequest(UpdatePaperTypeRequest request, @MappingTarget PaperType entity);
}
