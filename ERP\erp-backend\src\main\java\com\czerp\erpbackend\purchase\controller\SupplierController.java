package com.czerp.erpbackend.purchase.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.purchase.dto.CreateSupplierRequest;
import com.czerp.erpbackend.purchase.dto.SupplierDTO;
import com.czerp.erpbackend.purchase.dto.SupplierQueryRequest;
import com.czerp.erpbackend.purchase.dto.UpdateSupplierRequest;
import com.czerp.erpbackend.purchase.service.SupplierService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商控制器
 */
@RestController
@RequestMapping("/suppliers")
@Tag(name = "供应商管理", description = "供应商管理相关接口")
@RequiredArgsConstructor
@Slf4j
public class SupplierController {

    private final SupplierService supplierService;

    /**
     * 分页查询供应商列表
     * @param request 查询请求
     * @return 供应商分页列表
     */
    @GetMapping
    @Operation(summary = "分页查询供应商列表", description = "分页查询供应商列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:read')")
    public ResponseEntity<ApiResponse<PageResponse<SupplierDTO>>> findSuppliers(SupplierQueryRequest request) {
        log.info("Finding suppliers with request: {}", request);
        try {
            PageResponse<SupplierDTO> suppliers = supplierService.findSuppliers(request);
            log.info("Found {} suppliers", suppliers.getContent().size());
            return ResponseEntity.ok(ApiResponse.success(suppliers));
        } catch (Exception e) {
            log.error("Error finding suppliers: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 查询所有供应商
     * @return 供应商列表
     */
    @GetMapping("/all")
    @Operation(summary = "查询所有供应商", description = "查询所有供应商")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:read')")
    public ResponseEntity<ApiResponse<List<SupplierDTO>>> findAllSuppliers() {
        log.info("Finding all suppliers");
        List<SupplierDTO> suppliers = supplierService.findAllSuppliers();
        return ResponseEntity.ok(ApiResponse.success(suppliers));
    }

    /**
     * 根据ID查询供应商
     * @param id 供应商ID
     * @return 供应商信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询供应商", description = "根据ID查询供应商")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:read')")
    public ResponseEntity<ApiResponse<SupplierDTO>> findSupplierById(@PathVariable Long id) {
        log.info("Finding supplier by id: {}", id);
        SupplierDTO supplier = supplierService.findSupplierById(id);
        return ResponseEntity.ok(ApiResponse.success(supplier));
    }

    /**
     * 创建供应商
     * @param request 创建请求
     * @return 供应商信息
     */
    @PostMapping
    @Operation(summary = "创建供应商", description = "创建供应商")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:create')")
    public ResponseEntity<ApiResponse<SupplierDTO>> createSupplier(@Valid @RequestBody CreateSupplierRequest request) {
        log.info("Creating supplier with request: {}", request);
        SupplierDTO supplier = supplierService.createSupplier(request);
        return ResponseEntity.ok(ApiResponse.success(supplier));
    }

    /**
     * 更新供应商
     * @param id 供应商ID
     * @param request 更新请求
     * @return 供应商信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新供应商", description = "更新供应商")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:update')")
    public ResponseEntity<ApiResponse<SupplierDTO>> updateSupplier(
            @PathVariable Long id,
            @Valid @RequestBody UpdateSupplierRequest request) {
        log.info("Updating supplier with id: {} and request: {}", id, request);
        SupplierDTO supplier = supplierService.updateSupplier(id, request);
        return ResponseEntity.ok(ApiResponse.success(supplier));
    }

    /**
     * 删除供应商
     * @param id 供应商ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除供应商", description = "删除供应商")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteSupplier(@PathVariable Long id) {
        log.info("Deleting supplier with id: {}", id);
        supplierService.deleteSupplier(id);
        return ResponseEntity.ok(ApiResponse.success());
    }

    /**
     * 批量删除供应商
     * @param ids 供应商ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除供应商", description = "批量删除供应商")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:delete')")
    public ResponseEntity<ApiResponse<Void>> batchDeleteSuppliers(@RequestBody List<Long> ids) {
        log.info("Batch deleting suppliers with ids: {}", ids);
        supplierService.batchDeleteSuppliers(ids);
        return ResponseEntity.ok(ApiResponse.success());
    }

    /**
     * 切换供应商状态
     * @param id 供应商ID
     * @param status 状态
     * @return 供应商信息
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "切换供应商状态", description = "切换供应商状态")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('supplier:update')")
    public ResponseEntity<ApiResponse<SupplierDTO>> toggleSupplierStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        log.info("Toggling supplier status with id: {} and status: {}", id, status);
        SupplierDTO supplier = supplierService.toggleSupplierStatus(id, status);
        return ResponseEntity.ok(ApiResponse.success(supplier));
    }
}
