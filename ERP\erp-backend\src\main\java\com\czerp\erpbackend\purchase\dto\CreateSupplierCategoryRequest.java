package com.czerp.erpbackend.purchase.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建供应商分类请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateSupplierCategoryRequest {
    
    /**
     * 分类编码
     */
    @NotBlank(message = "分类编码不能为空")
    @Size(max = 50, message = "分类编码长度不能超过50个字符")
    private String categoryCode;
    
    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String categoryName;
    
    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    private Integer sortOrder;
    
    /**
     * 状态(active-启用,inactive-停用)
     */
    @Pattern(regexp = "^(active|inactive)$", message = "状态值不正确，可选值：active, inactive")
    private String status;
    
    /**
     * 备注
     */
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;
}
