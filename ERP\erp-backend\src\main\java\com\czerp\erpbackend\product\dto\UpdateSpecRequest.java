package com.czerp.erpbackend.product.dto;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新产品规格请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSpecRequest {
    
    /**
     * 规格名称
     */
    @Size(max = 50, message = "规格名称长度不能超过50个字符")
    private String name;
    
    /**
     * 单位
     */
    @Size(max = 20, message = "单位长度不能超过20个字符")
    private String unit;
    
    /**
     * 描述
     */
    @Size(max = 255, message = "描述长度不能超过255个字符")
    private String description;
}
