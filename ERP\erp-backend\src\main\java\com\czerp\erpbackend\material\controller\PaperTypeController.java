package com.czerp.erpbackend.material.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.material.dto.PaperTypeDTO;
import com.czerp.erpbackend.material.dto.request.CreatePaperTypeRequest;
import com.czerp.erpbackend.material.dto.request.UpdatePaperTypeRequest;
import com.czerp.erpbackend.material.service.PaperTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 纸质类别控制器
 */
@RestController
@RequestMapping("/material/paper-types")
@Tag(name = "纸质类别", description = "纸质类别相关接口")
@RequiredArgsConstructor
@Slf4j
public class PaperTypeController {

    private final PaperTypeService paperTypeService;

    /**
     * 查询所有纸质类别
     * @return 纸质类别列表
     */
    @GetMapping
    @Operation(summary = "查询所有纸质类别", description = "查询所有纸质类别")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-type:list')")
    public ResponseEntity<ApiResponse<List<PaperTypeDTO>>> findAllPaperTypes() {
        log.debug("Finding all paper types");
        List<PaperTypeDTO> response = paperTypeService.findAllPaperTypes();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据ID查询纸质类别
     * @param id 纸质类别ID
     * @return 纸质类别信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询纸质类别", description = "根据ID查询纸质类别")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-type:read')")
    public ResponseEntity<ApiResponse<PaperTypeDTO>> findPaperTypeById(@PathVariable Integer id) {
        log.debug("Finding paper type by id: {}", id);
        PaperTypeDTO response = paperTypeService.findPaperTypeById(id);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 创建纸质类别
     * @param request 创建请求
     * @return 纸质类别信息
     */
    @PostMapping
    @Operation(summary = "创建纸质类别", description = "创建纸质类别")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-type:create')")
    public ResponseEntity<ApiResponse<PaperTypeDTO>> createPaperType(@Valid @RequestBody CreatePaperTypeRequest request) {
        log.debug("Creating paper type with request: {}", request);
        PaperTypeDTO response = paperTypeService.createPaperType(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 更新纸质类别
     * @param id 纸质类别ID
     * @param request 更新请求
     * @return 纸质类别信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新纸质类别", description = "更新纸质类别")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-type:update')")
    public ResponseEntity<ApiResponse<PaperTypeDTO>> updatePaperType(
            @PathVariable Integer id,
            @Valid @RequestBody UpdatePaperTypeRequest request) {
        log.debug("Updating paper type with id: {} and request: {}", id, request);
        PaperTypeDTO response = paperTypeService.updatePaperType(id, request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 删除纸质类别
     * @param id 纸质类别ID
     * @return 无内容
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除纸质类别", description = "删除纸质类别")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-type:delete')")
    public ResponseEntity<ApiResponse<Void>> deletePaperType(@PathVariable Integer id) {
        log.debug("Deleting paper type with id: {}", id);
        paperTypeService.deletePaperType(id);
        return ResponseEntity.ok(ApiResponse.success());
    }

    /**
     * 批量删除纸质类别
     * @param ids 纸质类别ID列表
     * @return 无内容
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除纸质类别", description = "批量删除纸质类别")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('material:paper-type:delete')")
    public ResponseEntity<ApiResponse<Void>> batchDeletePaperTypes(@RequestBody List<Integer> ids) {
        log.debug("Batch deleting paper types with ids: {}", ids);
        paperTypeService.batchDeletePaperTypes(ids);
        return ResponseEntity.ok(ApiResponse.success());
    }
}
