# 通用列筛选接口使用指南

## 📋 概述

本文档介绍了ERP系统中通用列筛选功能的使用方法。该功能基于销售订单模块的筛选逻辑设计，形成了一个可复用的筛选服务框架，**专门为新的业务模块提供标准化的筛选解决方案**。

## ⚠️ 重要说明

**销售订单模块继续使用原有的稳定架构**，本通用筛选架构主要用于：
- 新开发的业务模块
- 需要筛选功能的新页面
- 其他非销售订单的查询场景

## 🏗️ 架构概览

### 核心组件

- **`ColumnFilterService`** - 通用列筛选服务
- **`FilterFieldRegistry`** - 筛选字段注册中心
- **`FilterQueryBuilder`** - 筛选查询构建器
- **`CascadeFilterBuilder`** - 级联筛选构建器
- **自定义筛选处理器** - 处理复杂筛选逻辑

### 支持的筛选类型

1. **动态查询** (`DYNAMIC_QUERY`) - 基于数据库实时查询
2. **静态字典** (`STATIC_DICTIONARY`) - 基于预定义字典
3. **自定义处理器** (`CUSTOM_HANDLER`) - 复杂业务逻辑处理

## 🚀 快速开始

### 1. 测试接口验证

首先验证筛选功能是否正常工作：

```http
# 查看已注册的模块
GET /api/test/filter/modules

# 查看销售订单模块支持的字段
GET /api/test/filter/fields/sales-order

# 检查特定字段是否支持
GET /api/test/filter/supported/sales-order/customerName

# 测试获取筛选选项
GET /api/test/filter/options/sales-order/customerName
```

### 2. 使用通用筛选API

```http
# 通用筛选接口
GET /api/common/filter/options?moduleCode=sales-order&fieldName=customerName&searchText=华为

# 批量获取筛选选项
GET /api/common/filter/batch-options?moduleCode=sales-order&fieldNames=customerName,productName,salesPerson

# 检查字段支持
GET /api/common/filter/supported?moduleCode=sales-order&fieldName=customerName

# 获取模块支持的所有字段
GET /api/common/filter/fields?moduleCode=sales-order
```

### 3. 销售订单接口（继续使用原有架构）

```http
# 销售订单继续使用原有接口（稳定架构）
GET /api/sales/orders/query/filter-options?fieldName=customerName&searchText=华为

# 注意：销售订单不使用新的通用架构，保持业务稳定性
```

## 📊 销售订单模块筛选字段（仅作为新架构的参考示例）

**注意：以下字段配置仅作为新架构的参考示例，销售订单实际使用原有架构**

### 主表字段（3个）
- `customerName` - 客户名称
- `salesPerson` - 销售员  
- `orderNo` - 销售单号

### 明细表字段（12个）
- `productName` - 产品名称
- `productionOrderNo` - 生产单号
- `customerOrderNo` - 客户订单号
- `customerProductCode` - 客户产品编码
- `paperType` - 纸质
- `productionPaperType` - 生产纸质
- `processRequirements` - 工艺要求
- `boxType` - 箱型
- `corrugationType` - 瓦楞类型
- `connectionMethod` - 连接方式
- `staplePosition` - 钉位
- `unit` - 单位
- `currency` - 币种

### 子查询字段（6个）
- `supplierName` - 采购供应商
- `paperTypeName` - 纸板类别
- `purchaseOrderNo` - 采购单号
- `bindingSpecification` - 合订规格
- `pressSizeWidth` - 压线尺寸
- `processes` - 工序

### 动态字段（2个）
- `specification` - 规格（长×宽×高 单位）
- `productionSpecification` - 生产规格（带回退机制）

## 🔧 为新模块集成筛选功能

### 步骤1：创建筛选配置类

```java
@Configuration
@RequiredArgsConstructor
@Slf4j
public class YourModuleFilterConfig {
    
    private final FilterFieldRegistry filterFieldRegistry;
    
    public static final String MODULE_CODE = "your-module";
    
    @PostConstruct
    public void registerFields() {
        List<FilterFieldMetadata> fieldConfigs = createFilterFields();
        filterFieldRegistry.registerModuleFields(MODULE_CODE, fieldConfigs);
        log.info("Registered {} filter fields for your module", fieldConfigs.size());
    }
    
    private List<FilterFieldMetadata> createFilterFields() {
        return List.of(
            FilterFieldMetadata.builder()
                .fieldName("fieldName")
                .entityFieldName("fieldName")
                .entityClass(YourEntity.class)
                .fieldPath("fieldName")
                .joinSource(FilterFieldMetadata.JoinSource.ROOT)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("字段描述")
                .enableSearch(true)
                .maxOptions(50)
                .build()
        );
    }
}
```

### 步骤2：创建筛选适配器

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class YourModuleFilterAdapter {
    
    private final ColumnFilterService columnFilterService;
    
    public List<YourFilterOptionDTO> getFilterOptions(
            String fieldName, String searchText, YourQueryParamDTO currentFilters) {
        
        Map<String, Object> filterMap = convertToFilterMap(currentFilters);
        
        List<FilterOptionDTO> commonOptions = 
                columnFilterService.getFilterOptions(
                        YourModuleFilterConfig.MODULE_CODE, 
                        fieldName, 
                        searchText, 
                        filterMap);
        
        return convertToYourFilterOptions(commonOptions);
    }
}
```

### 步骤3：创建筛选控制器

```java
@RestController
@RequestMapping("/your-module/filter")
@RequiredArgsConstructor
@Slf4j
public class YourModuleFilterController {
    
    private final YourModuleFilterAdapter filterAdapter;
    
    @GetMapping("/options")
    public ApiResponse<List<YourFilterOptionDTO>> getFilterOptions(
            @RequestParam String fieldName,
            @RequestParam(required = false) String searchText,
            YourQueryParamDTO currentFilters) {
        
        List<YourFilterOptionDTO> options = filterAdapter.getFilterOptions(
                fieldName, searchText, currentFilters);
        
        return ApiResponse.success(options);
    }
}
```

## 🎯 自定义筛选处理器

对于复杂的筛选逻辑，可以创建自定义处理器：

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class YourCustomFilterHandler implements FilterHandler {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    public List<FilterOptionDTO> getFilterOptions(FilterRequest request, FilterFieldMetadata metadata) {
        // 实现自定义筛选逻辑
        return switch (request.getFieldName()) {
            case "complexField" -> getComplexFieldOptions(request);
            default -> Collections.emptyList();
        };
    }
    
    @Override
    public boolean supports(String fieldName) {
        return "complexField".equals(fieldName);
    }
}
```

## 📝 API响应格式

### 筛选选项响应

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "label": "华为技术有限公司",
      "value": "华为技术有限公司"
    },
    {
      "label": "小米科技有限公司", 
      "value": "小米科技有限公司"
    }
  ]
}
```

### 批量筛选选项响应

```json
{
  "success": true,
  "code": "200", 
  "message": "操作成功",
  "data": {
    "customerName": [
      {"label": "华为技术有限公司", "value": "华为技术有限公司"}
    ],
    "productName": [
      {"label": "手机包装盒", "value": "手机包装盒"}
    ]
  }
}
```

## ⚡ 性能优化建议

1. **合理设置maxOptions** - 限制返回的选项数量
2. **启用搜索功能** - 对于大数据量字段启用搜索
3. **使用级联筛选** - 减少不必要的选项加载
4. **缓存筛选结果** - 对于静态数据启用缓存

## 🔍 故障排查

### 常见问题

1. **字段不支持筛选**
   - 检查字段是否已在配置中注册
   - 验证模块编码是否正确

2. **筛选选项为空**
   - 检查数据库中是否有对应数据
   - 验证查询条件是否正确

3. **自定义处理器不生效**
   - 确认处理器已注册为Spring Bean
   - 检查supports方法返回值

### 调试接口

```http
# 查看所有注册的模块
GET /api/test/filter/modules

# 查看模块支持的字段
GET /api/test/filter/fields/{moduleCode}

# 测试字段支持情况
GET /api/test/filter/supported/{moduleCode}/{fieldName}
```

## 📚 相关文档

- [销售订单筛选功能详细说明](./销售订单筛选选项接口使用说明.md)
- [数据库设计规范](./AVOID_DUPLICATE_COLUMNS.md)
- [后端开发规则](../后端开发规则.txt)

## 💡 高级用法

### 级联筛选

级联筛选允许根据当前筛选条件动态更新其他字段的选项：

```javascript
// 前端示例：当客户名称改变时，更新产品名称选项
const handleCustomerChange = async (customerName) => {
  const productOptions = await getFilterOptions('productName', '', {
    customerName: [customerName]
  });
  updateProductOptions(productOptions);
};

// API调用
const getFilterOptions = async (fieldName, searchText, currentFilters) => {
  const params = new URLSearchParams({
    fieldName,
    searchText: searchText || ''
  });

  // 添加当前筛选条件
  Object.entries(currentFilters).forEach(([key, values]) => {
    if (Array.isArray(values)) {
      values.forEach(value => params.append(key, value));
    } else if (values) {
      params.append(key, values);
    }
  });

  const response = await fetch(`/api/sales/orders/query/filter-options?${params}`);
  return response.json();
};
```

### 搜索功能

对于大数据量字段，支持搜索功能：

```javascript
// 带搜索的筛选选项获取
const searchCustomers = async (searchText) => {
  const response = await fetch(
    `/api/common/filter/options?moduleCode=sales-order&fieldName=customerName&searchText=${encodeURIComponent(searchText)}`
  );
  return response.json();
};

// 防抖搜索实现
const debouncedSearch = debounce(searchCustomers, 300);
```

### 批量获取筛选选项

一次性获取多个字段的筛选选项：

```javascript
const getBatchFilterOptions = async (fieldNames, currentFilters = {}) => {
  const params = new URLSearchParams({
    moduleCode: 'sales-order',
    fieldNames: fieldNames.join(',')
  });

  // 添加当前筛选条件
  Object.entries(currentFilters).forEach(([key, values]) => {
    if (Array.isArray(values)) {
      values.forEach(value => params.append(key, value));
    }
  });

  const response = await fetch(`/api/common/filter/batch-options?${params}`);
  return response.json();
};

// 使用示例
const options = await getBatchFilterOptions(
  ['customerName', 'productName', 'salesPerson'],
  { customerName: ['华为技术有限公司'] }
);
```

## 🛠️ 开发最佳实践

### 1. 字段配置最佳实践

```java
// 推荐的字段配置方式
FilterFieldMetadata.builder()
    .fieldName("customerName")           // 前端使用的字段名
    .entityFieldName("customerName")     // 实体类字段名
    .entityClass(SalesOrder.class)       // 实体类
    .fieldPath("customerName")           // 查询路径
    .joinSource(FilterFieldMetadata.JoinSource.ROOT)  // 连接源
    .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)  // 筛选类型
    .description("客户名称")              // 字段描述
    .enableSearch(true)                  // 启用搜索
    .maxOptions(50)                      // 最大选项数
    .build()
```

### 2. 性能优化配置

```java
// 对于大数据量字段
FilterFieldMetadata.builder()
    .fieldName("productName")
    .enableSearch(true)        // 必须启用搜索
    .maxOptions(20)           // 限制选项数量
    .searchMinLength(2)       // 最小搜索长度
    .build();

// 对于小数据量字段
FilterFieldMetadata.builder()
    .fieldName("currency")
    .filterType(FilterFieldMetadata.FilterType.STATIC_DICTIONARY)
    .dictionaryCode("CURRENCY")  // 使用字典
    .enableSearch(false)         // 无需搜索
    .build();
```

### 3. 自定义处理器最佳实践

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class OptimizedCustomFilterHandler implements FilterHandler {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<FilterOptionDTO> getFilterOptions(FilterRequest request, FilterFieldMetadata metadata) {
        // 使用缓存提升性能
        String cacheKey = generateCacheKey(request);

        // 实现分页查询
        return getPagedOptions(request, metadata);
    }

    private List<FilterOptionDTO> getPagedOptions(FilterRequest request, FilterFieldMetadata metadata) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<?> root = query.from(metadata.getEntityClass());

        // 添加搜索条件
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.hasText(request.getSearchText())) {
            predicates.add(cb.like(
                cb.lower(root.get(metadata.getEntityFieldName())),
                "%" + request.getSearchText().toLowerCase() + "%"
            ));
        }

        // 添加级联筛选条件
        addCascadeFilters(cb, root, predicates, request.getCurrentFilters());

        query.select(root.get(metadata.getEntityFieldName()))
             .where(predicates.toArray(new Predicate[0]))
             .distinct(true)
             .orderBy(cb.asc(root.get(metadata.getEntityFieldName())));

        return entityManager.createQuery(query)
                .setMaxResults(metadata.getMaxOptions())
                .getResultList()
                .stream()
                .map(value -> FilterOptionDTO.builder()
                    .label(value)
                    .value(value)
                    .build())
                .collect(Collectors.toList());
    }
}
```

## 🔧 故障排查指南

### 启动时常见错误

1. **Bean名称冲突**
   ```
   Ambiguous mapping. Cannot map 'controller1' method to {GET [/path]}:
   There is already 'controller2' bean method mapped.
   ```
   **解决方案：** 检查控制器路径映射，确保没有重复的API路径

2. **包名错误**
   ```
   java: 程序包javax.persistence不存在
   ```
   **解决方案：** 将`javax.persistence`改为`jakarta.persistence`

3. **依赖注入失败**
   ```
   No qualifying bean of type 'FilterHandler' available
   ```
   **解决方案：** 确保自定义处理器添加了`@Component`注解

### 运行时常见问题

1. **筛选选项为空**
   - 检查数据库中是否有对应数据
   - 验证字段配置是否正确
   - 确认查询条件没有过度限制

2. **级联筛选不生效**
   - 检查当前筛选条件是否正确传递
   - 验证字段间的关联关系配置
   - 确认筛选条件格式正确

3. **性能问题**
   - 启用搜索功能减少数据量
   - 设置合理的maxOptions限制
   - 考虑添加数据库索引

### 调试技巧

```java
// 在自定义处理器中添加调试日志
@Override
public List<FilterOptionDTO> getFilterOptions(FilterRequest request, FilterFieldMetadata metadata) {
    log.debug("Processing filter request: field={}, searchText={}, filters={}",
             request.getFieldName(), request.getSearchText(), request.getCurrentFilters());

    List<FilterOptionDTO> result = processRequest(request, metadata);

    log.debug("Filter options result: field={}, count={}",
             request.getFieldName(), result.size());

    return result;
}
```

---

**注意：** 该筛选功能完全向后兼容，不会影响现有业务逻辑。新模块可以直接使用通用接口，现有模块可以逐步迁移。

## 📞 技术支持

如有问题，请参考：
- 测试接口：`/api/test/filter/*`
- 日志级别：设置`com.czerp.erpbackend.common.filter`为DEBUG
- 相关文档：查看`docs`目录下的相关文档
