-- 检查数据库字符集状态
-- 执行此脚本来了解当前字符集情况

-- ========================================
-- 1. 检查数据库默认字符集
-- ========================================
SELECT 
    SCHEMA_NAME as '数据库名',
    DEFAULT_CHARACTER_SET_NAME as '默认字符集',
    DEFAULT_COLLATION_NAME as '默认排序规则'
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'czerp_web';

-- ========================================
-- 2. 检查所有表的字符集
-- ========================================
SELECT 
    TABLE_NAME as '表名', 
    TABLE_COLLATION as '表字符集',
    CASE 
        WHEN TABLE_COLLATION = 'utf8mb4_0900_ai_ci' THEN '✓ 目标字符集'
        WHEN TABLE_COLLATION = 'utf8mb4_unicode_ci' THEN '⚠ 需要修复'
        ELSE '? 其他字符集'
    END AS '状态'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'czerp_web' 
ORDER BY TABLE_COLLATION, TABLE_NAME;

-- ========================================
-- 3. 统计字符集分布
-- ========================================
SELECT 
    TABLE_COLLATION as '字符集',
    COUNT(*) as '表数量',
    GROUP_CONCAT(TABLE_NAME ORDER BY TABLE_NAME SEPARATOR ', ') as '表列表'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'czerp_web' 
GROUP BY TABLE_COLLATION
ORDER BY COUNT(*) DESC;

-- ========================================
-- 4. 检查关键字段的字符集（可能导致冲突的字段）
-- ========================================
SELECT 
    TABLE_NAME as '表名',
    COLUMN_NAME as '字段名',
    COLLATION_NAME as '字段字符集',
    CASE 
        WHEN COLLATION_NAME = 'utf8mb4_0900_ai_ci' THEN '✓'
        WHEN COLLATION_NAME = 'utf8mb4_unicode_ci' THEN '⚠'
        ELSE '?'
    END AS '状态'
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'czerp_web' 
    AND TABLE_NAME IN ('sales_order_item', 'purchase_order_item', 'purchase_order')
    AND COLUMN_NAME IN ('id', 'source_sales_order_item_id', 'purchase_order_no')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- ========================================
-- 5. 检查可能存在的外键约束
-- ========================================
SELECT 
    CONSTRAINT_NAME as '约束名',
    TABLE_NAME as '表名',
    COLUMN_NAME as '字段名',
    REFERENCED_TABLE_NAME as '引用表',
    REFERENCED_COLUMN_NAME as '引用字段'
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'czerp_web' 
    AND REFERENCED_TABLE_NAME IS NOT NULL
    AND (TABLE_NAME IN ('purchase_order', 'purchase_order_item', 'sales_order_item') 
         OR REFERENCED_TABLE_NAME IN ('purchase_order', 'purchase_order_item', 'sales_order_item'))
ORDER BY TABLE_NAME;

-- ========================================
-- 6. 模拟问题查询（测试是否存在字符集冲突）
-- ========================================
-- 注意：这个查询可能会失败，这正是我们要修复的问题
/*
SELECT COUNT(*) as '测试查询结果'
FROM sales_order_item soi
WHERE soi.id IN (
    SELECT poi.source_sales_order_item_id 
    FROM purchase_order_item poi 
    JOIN purchase_order po ON po.id = poi.purchase_order_id 
    WHERE po.purchase_order_no = 'CG2025051900003'
);
*/

-- ========================================
-- 执行结果说明
-- ========================================
/*
执行此脚本后，请检查：

1. 数据库默认字符集：应该是 utf8mb4_0900_ai_ci
2. 表字符集分布：
   - ✓ 目标字符集：已经是正确的字符集
   - ⚠ 需要修复：使用 utf8mb4_unicode_ci 的表
   - ? 其他字符集：需要特别关注

3. 关键字段检查：
   - sales_order_item.id 应该是 utf8mb4_0900_ai_ci
   - purchase_order_item.source_sales_order_item_id 应该是 utf8mb4_unicode_ci（需要修复）
   - purchase_order.purchase_order_no 应该是 utf8mb4_unicode_ci（需要修复）

4. 外键约束：记录所有相关的外键，修复时需要注意

如果看到字符集不一致，请执行 fix_collation_conflicts.sql 脚本进行修复。
*/
