package com.czerp.erpbackend.sales.config;

import com.czerp.erpbackend.common.filter.config.FilterFieldRegistry;
import com.czerp.erpbackend.common.filter.metadata.FilterFieldMetadata;
import com.czerp.erpbackend.sales.entity.SalesOrder;
import com.czerp.erpbackend.sales.entity.SalesOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 销售订单模块筛选配置
 */
@Configuration
@RequiredArgsConstructor
@Slf4j
public class SalesOrderFilterConfig {
    
    private final FilterFieldRegistry filterFieldRegistry;
    
    /**
     * 模块编码
     */
    public static final String MODULE_CODE = "sales-order";
    
    @PostConstruct
    public void registerFields() {
        List<FilterFieldMetadata> fieldConfigs = createSalesOrderFilterFields();
        filterFieldRegistry.registerModuleFields(MODULE_CODE, fieldConfigs);
        log.info("Registered {} filter fields for sales order module", fieldConfigs.size());
    }
    
    /**
     * 创建销售订单筛选字段配置
     */
    private List<FilterFieldMetadata> createSalesOrderFilterFields() {
        return List.of(
            // ========== 主表字段（sales_order） ==========
            
            FilterFieldMetadata.builder()
                .fieldName("customerName")
                .entityFieldName("customerName")
                .entityClass(SalesOrder.class)
                .fieldPath("customerName")
                .joinSource(FilterFieldMetadata.JoinSource.ROOT)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("客户名称")
                .enableSearch(true)
                .maxOptions(50)
                .build(),
                
            FilterFieldMetadata.builder()
                .fieldName("salesPerson")
                .entityFieldName("salesPerson")
                .entityClass(SalesOrder.class)
                .fieldPath("salesPerson")
                .joinSource(FilterFieldMetadata.JoinSource.ROOT)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("销售员")
                .enableSearch(true)
                .maxOptions(50)
                .build(),
                
            FilterFieldMetadata.builder()
                .fieldName("orderNo")
                .entityFieldName("orderNo")
                .entityClass(SalesOrder.class)
                .fieldPath("orderNo")
                .joinSource(FilterFieldMetadata.JoinSource.ROOT)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("销售单号")
                .enableSearch(true)
                .maxOptions(50)
                .build(),
            
            // ========== 明细表字段（sales_order_item） ==========
            
            FilterFieldMetadata.builder()
                .fieldName("productName")
                .entityFieldName("productName")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.productName")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("产品名称")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("productionOrderNo")
                .entityFieldName("productionOrderNo")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.productionOrderNo")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("生产单号")
                .enableSearch(true)
                .maxOptions(50)
                .build(),
                
            FilterFieldMetadata.builder()
                .fieldName("customerOrderNo")
                .entityFieldName("customerOrderNo")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.customerOrderNo")  // 🔥 修复：从SalesOrder.items访问customerOrderNo
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("客户订单号")
                .enableSearch(true)
                .maxOptions(50)
                .build(),
                
            FilterFieldMetadata.builder()
                .fieldName("customerProductCode")
                .entityFieldName("customerProductCode")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.customerProductCode")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("客户产品编码")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("paperType")
                .entityFieldName("paperType")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.paperType")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("纸质")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("productionPaperType")
                .entityFieldName("productionPaperType")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.productionPaperType")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("生产纸质")
                .enableSearch(true)
                .maxOptions(50)
                .build(),
                
            FilterFieldMetadata.builder()
                .fieldName("processRequirements")
                .entityFieldName("processRequirements")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.processRequirements")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("工艺要求")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("boxType")
                .entityFieldName("boxType")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.boxType")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("箱型")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("corrugationType")
                .entityFieldName("corrugationType")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.corrugationType")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("瓦楞类型")
                .enableSearch(true)
                .maxOptions(50)
                .build(),
                
            FilterFieldMetadata.builder()
                .fieldName("connectionMethod")
                .entityFieldName("connectionMethod")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.connectionMethod")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("连接方式")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("staplePosition")
                .entityFieldName("staplePosition")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.staplePosition")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("钉位")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("unit")
                .entityFieldName("unit")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.unit")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("单位")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("currency")
                .entityFieldName("currency")
                .entityClass(SalesOrder.class)  // 🔥 修复：使用SalesOrder作为根实体
                .fieldPath("items.currency")
                .joinSource(FilterFieldMetadata.JoinSource.JOIN)
                .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
                .description("币种")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            // ========== 子查询字段（需要自定义处理器） ==========

            FilterFieldMetadata.builder()
                .fieldName("supplierName")
                .entityFieldName("supplierName")
                .entityClass(SalesOrder.class)
                .joinSource(FilterFieldMetadata.JoinSource.SUBQUERY)
                .filterType(FilterFieldMetadata.FilterType.CUSTOM_HANDLER)
                .customHandlerBean("salesOrderSubqueryFilterHandler")
                .description("采购供应商")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("paperTypeName")
                .entityFieldName("paperBoardCategory")
                .entityClass(SalesOrder.class)
                .joinSource(FilterFieldMetadata.JoinSource.SUBQUERY)
                .filterType(FilterFieldMetadata.FilterType.CUSTOM_HANDLER)
                .customHandlerBean("salesOrderSubqueryFilterHandler")
                .description("纸板类别")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("purchaseOrderNo")
                .entityFieldName("purchaseOrderNo")
                .entityClass(SalesOrder.class)
                .joinSource(FilterFieldMetadata.JoinSource.SUBQUERY)
                .filterType(FilterFieldMetadata.FilterType.CUSTOM_HANDLER)
                .customHandlerBean("salesOrderSubqueryFilterHandler")
                .description("采购单号")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("bindingSpecification")
                .entityFieldName("bindingSpecification")
                .entityClass(SalesOrder.class)
                .joinSource(FilterFieldMetadata.JoinSource.SUBQUERY)
                .filterType(FilterFieldMetadata.FilterType.CUSTOM_HANDLER)
                .customHandlerBean("salesOrderSubqueryFilterHandler")
                .description("合订规格")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("pressSizeWidth")
                .entityFieldName("pressSizeWidth")
                .entityClass(SalesOrder.class)
                .joinSource(FilterFieldMetadata.JoinSource.SUBQUERY)
                .filterType(FilterFieldMetadata.FilterType.CUSTOM_HANDLER)
                .customHandlerBean("salesOrderMaterialFilterHandler")
                .description("压线尺寸")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("processes")
                .entityFieldName("processName")
                .entityClass(SalesOrder.class)
                .joinSource(FilterFieldMetadata.JoinSource.SUBQUERY)
                .filterType(FilterFieldMetadata.FilterType.CUSTOM_HANDLER)
                .customHandlerBean("salesOrderProcessFilterHandler")
                .description("工序")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            // ========== 动态字段（需要自定义处理器） ==========

            FilterFieldMetadata.builder()
                .fieldName("specification")
                .entityFieldName("specification")
                .entityClass(SalesOrder.class)
                .joinSource(FilterFieldMetadata.JoinSource.DYNAMIC)
                .filterType(FilterFieldMetadata.FilterType.CUSTOM_HANDLER)
                .customHandlerBean("salesOrderDynamicFieldHandler")
                .description("规格（长×宽×高 单位）")
                .enableSearch(true)
                .maxOptions(50)
                .build(),

            FilterFieldMetadata.builder()
                .fieldName("productionSpecification")
                .entityFieldName("productionSpecification")
                .entityClass(SalesOrder.class)
                .joinSource(FilterFieldMetadata.JoinSource.DYNAMIC)
                .filterType(FilterFieldMetadata.FilterType.CUSTOM_HANDLER)
                .customHandlerBean("salesOrderDynamicFieldHandler")
                .description("生产规格（生产长×生产宽×生产高 单位）")
                .enableSearch(true)
                .maxOptions(50)
                .build()
        );
    }
}
