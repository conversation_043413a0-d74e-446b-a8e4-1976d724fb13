package com.czerp.erpbackend.customer.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.customer.dto.CreateCustomerCategoryRequest;
import com.czerp.erpbackend.customer.dto.CustomerCategoryDTO;
import com.czerp.erpbackend.customer.dto.CustomerCategoryQueryRequest;
import com.czerp.erpbackend.customer.dto.UpdateCustomerCategoryRequest;

import java.util.List;

/**
 * 客户分类服务接口
 */
public interface CustomerCategoryService {
    
    /**
     * 分页查询客户分类列表
     * @param request 查询请求
     * @return 客户分类分页列表
     */
    PageResponse<CustomerCategoryDTO> findCategories(CustomerCategoryQueryRequest request);
    
    /**
     * 查询所有客户分类
     * @return 客户分类列表
     */
    List<CustomerCategoryDTO> findAllCategories();
    
    /**
     * 根据ID查询客户分类
     * @param id 客户分类ID
     * @return 客户分类信息
     */
    CustomerCategoryDTO findCategoryById(String id);
    
    /**
     * 根据分类编码查询客户分类
     * @param categoryCode 分类编码
     * @return 客户分类信息
     */
    CustomerCategoryDTO findCategoryByCode(String categoryCode);
    
    /**
     * 创建客户分类
     * @param request 创建请求
     * @return 客户分类信息
     */
    CustomerCategoryDTO createCategory(CreateCustomerCategoryRequest request);
    
    /**
     * 更新客户分类
     * @param id 客户分类ID
     * @param request 更新请求
     * @return 客户分类信息
     */
    CustomerCategoryDTO updateCategory(String id, UpdateCustomerCategoryRequest request);
    
    /**
     * 删除客户分类
     * @param id 客户分类ID
     */
    void deleteCategory(String id);
    
    /**
     * 批量删除客户分类
     * @param ids 客户分类ID列表
     */
    void batchDeleteCategories(List<String> ids);
    
    /**
     * 获取默认客户分类
     * @return 默认客户分类
     */
    CustomerCategoryDTO getDefaultCategory();
}
