package com.czerp.erpbackend.material.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 创建纸质类别请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreatePaperTypeRequest {

    /**
     * 纸板类别名称
     */
    @NotBlank(message = "纸板类别名称不能为空")
    @Size(max = 50, message = "纸板类别名称不能超过50个字符")
    private String paperTypeName;

    /**
     * 打钉钉口(inch)
     */
    private BigDecimal staplingFlapInch;

    /**
     * 打钉钉口(cm)
     */
    private BigDecimal staplingFlapCm;

    /**
     * 粘箱钉口(inch)
     */
    private BigDecimal gluingFlapInch;

    /**
     * 粘箱钉口(cm)
     */
    private BigDecimal gluingFlapCm;

    /**
     * 加分(mm)
     */
    private BigDecimal addMarginMm;

    /**
     * 缩分(mm)
     */
    private BigDecimal reduceMarginMm;

    /**
     * 厚度(mm)
     */
    private BigDecimal thicknessMm;

    /**
     * 层数
     */
    private Integer layerCount;

    /**
     * 每板数
     */
    private Integer sheetsPerBoard;
}
