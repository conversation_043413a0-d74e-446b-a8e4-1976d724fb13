销售订单管理字段名称对照表
销售订单基本信息 (SalesOrderDTO)
字段名称	类型	描述
id	String	订单ID
orderNo	String	销售单号
productionOrderNo	String	生产单号
customerId	String	客户ID
customerName	String	客户名称
customerOrderNo	String	客户订单号
orderDate	Date	订单日期
deliveryDate	Date	交期
currency	String	币别
salesPerson	String	销售员
totalAmount	BigDecimal	金额总计
totalWeight	BigDecimal	总重(KG)
totalArea	BigDecimal	总面积(平米)
totalVolume	BigDecimal	总体积(立方米)
taxRate	BigDecimal	税率
isTaxed	Boolean	是否含税
orderStatus	String	订单状态
remark	String	备注
isTerminated	Boolean	是否中止
createdBy	String	创建人
createdByName	String	创建人姓名
createdTime	DateTime	创建时间
updatedBy	String	更新人
updatedByName	String	更新人姓名
updatedTime	DateTime	更新时间
lastPrintTime	DateTime	最近打印时间
销售订单明细 (SalesOrderItemDTO)
字段名称	类型	描述
id	String	明细ID
orderId	String	订单ID
productId	String	产品ID
customerProductCode	String	客方货号
productName	String	品名
processRequirements	String	工艺要求
boxType	String	盒式
paperType	String	纸质
productionPaperType	String	生产纸质
specification	String	规格
productionSpecification	String	生产规格
quantity	Integer	数量
spareQuantity	Integer	备品数量
unit	String	单位
price	BigDecimal	单价
amount	BigDecimal	金额
isSpecialPrice	Boolean	是否特价
spareRatio	BigDecimal	成套比例
useInventory	Integer	使用库存
process	String	工序
connectionMethod	String	连接方式
boardType	String	纸板类别
lineSizeWidth	BigDecimal	压线尺寸(纸度)
lineSizeLength	BigDecimal	压线尺寸(纸长)
dieCuttingCount	Integer	模开数
useBoardInventory	Integer	使用纸板库存数
unitWeight	BigDecimal	单重
productArea	BigDecimal	产品面积
remark	String	备注
createdBy	String	创建人
createdByName	String	创建人姓名
createdTime	DateTime	创建时间
updatedBy	String	更新人
updatedByName	String	更新人姓名
updatedTime	DateTime	更新时间
订单状态枚举值 (OrderStatus)
状态码	描述
draft	草稿
pending	待审核
approved	已审核
processing	处理中
completed	已完成
canceled	已取消
rejected	已拒绝