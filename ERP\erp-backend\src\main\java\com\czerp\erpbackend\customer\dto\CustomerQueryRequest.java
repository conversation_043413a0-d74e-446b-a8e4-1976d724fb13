package com.czerp.erpbackend.customer.dto;

import com.czerp.erpbackend.common.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerQueryRequest extends PageRequest {
    
    /**
     * 客户编码
     */
    private String customerCode;
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 客户分类ID
     */
    private String categoryId;
    
    /**
     * 联系人
     */
    private String contactPerson;
    
    /**
     * 手机
     */
    private String mobile;
    
    /**
     * 地区
     */
    private String region;
    
    /**
     * 销售员
     */
    private String salesPerson;
    
    /**
     * 行业
     */
    private String industry;
    
    /**
     * 停用
     */
    private Boolean disabled;
}
