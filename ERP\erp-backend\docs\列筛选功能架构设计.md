# 列筛选功能架构设计

## 📋 重构背景

原有的列筛选功能紧密耦合在销售订单模块中，导致：
- 代码复用性差，其他模块无法使用
- 维护成本高，筛选逻辑分散
- 扩展性差，新增字段需要修改多处代码

本次重构将筛选功能拆分为通用的、可复用的服务框架。

## 🏗️ 架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    前端应用层                                │
├─────────────────────────────────────────────────────────────┤
│                    控制器层                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  通用筛选控制器  │  │ 销售订单筛选控制器│  │  其他模块控制器  ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│                    适配器层                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  销售订单适配器  │  │  采购订单适配器  │  │  其他模块适配器  ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│                    核心服务层                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              通用列筛选服务                              │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    配置管理层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  字段注册中心    │  │  筛选配置管理    │  │  元数据管理      ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│                    查询构建层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  查询构建器      │  │  级联筛选构建器  │  │  自定义处理器    ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                JPA/Hibernate                           │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. 通用列筛选服务 (`ColumnFilterService`)
- **职责**: 提供统一的筛选选项获取接口
- **特点**: 模块无关，支持多种筛选类型
- **接口**:
  ```java
  List<FilterOptionDTO> getFilterOptions(String moduleCode, String fieldName, String searchText, Map<String, Object> currentFilters);
  Map<String, List<FilterOptionDTO>> getBatchFilterOptions(String moduleCode, List<String> fieldNames, Map<String, Object> currentFilters);
  ```

#### 2. 筛选字段注册中心 (`FilterFieldRegistry`)
- **职责**: 管理各模块的筛选字段配置
- **特点**: 支持动态注册，元数据驱动
- **功能**:
  - 字段注册和查询
  - 模块管理
  - 配置验证

#### 3. 查询构建器 (`FilterQueryBuilder`)
- **职责**: 根据字段配置构建JPA查询
- **特点**: 支持多种连接类型，自动优化查询
- **支持的连接类型**:
  - ROOT: 主表字段
  - JOIN: 关联表字段
  - SUBQUERY: 子查询字段

#### 4. 级联筛选构建器 (`CascadeFilterBuilder`)
- **职责**: 处理级联筛选逻辑
- **特点**: 智能排除当前字段，支持复杂关联
- **功能**:
  - 筛选条件转换
  - 级联关系处理
  - 查询优化

#### 5. 自定义筛选处理器 (`FilterHandler`)
- **职责**: 处理复杂的业务筛选逻辑
- **特点**: 可扩展，支持自定义实现
- **应用场景**:
  - 复杂计算字段
  - 跨表关联查询
  - 特殊业务逻辑

## 🔧 技术实现

### 筛选类型支持

#### 1. 动态查询 (`DYNAMIC_QUERY`)
```java
FilterFieldMetadata.builder()
    .fieldName("customerName")
    .entityClass(SalesOrder.class)
    .filterType(FilterFieldMetadata.FilterType.DYNAMIC_QUERY)
    .enableSearch(true)
    .maxOptions(50)
    .build()
```

#### 2. 静态字典 (`STATIC_DICTIONARY`)
```java
FilterFieldMetadata.builder()
    .fieldName("currency")
    .filterType(FilterFieldMetadata.FilterType.STATIC_DICTIONARY)
    .dictionaryCode("CURRENCY")
    .build()
```

#### 3. 自定义处理器 (`CUSTOM_HANDLER`)
```java
FilterFieldMetadata.builder()
    .fieldName("processes")
    .filterType(FilterFieldMetadata.FilterType.CUSTOM_HANDLER)
    .customHandlerBean("salesOrderProcessFilterHandler")
    .build()
```

### 连接类型支持

#### 1. 主表字段 (`ROOT`)
```java
// 直接查询主表字段
.joinSource(FilterFieldMetadata.JoinSource.ROOT)
.fieldPath("customerName")
```

#### 2. 关联表字段 (`JOIN`)
```java
// 通过JOIN查询关联表字段
.joinSource(FilterFieldMetadata.JoinSource.JOIN)
.joinPath("salesOrderItems")
.fieldPath("productName")
```

#### 3. 子查询字段 (`SUBQUERY`)
```java
// 通过子查询获取字段
.joinSource(FilterFieldMetadata.JoinSource.SUBQUERY)
.customHandlerBean("salesOrderSubqueryFilterHandler")
```

### 级联筛选实现

```java
public List<FilterOptionDTO> getFilterOptionsWithCascade(
        FilterRequest request, FilterFieldMetadata metadata) {
    
    CriteriaBuilder cb = entityManager.getCriteriaBuilder();
    CriteriaQuery<String> query = cb.createQuery(String.class);
    Root<?> root = query.from(metadata.getEntityClass());
    
    List<Predicate> predicates = new ArrayList<>();
    
    // 添加搜索条件
    if (StringUtils.hasText(request.getSearchText())) {
        predicates.add(createSearchPredicate(cb, root, metadata, request.getSearchText()));
    }
    
    // 添加级联筛选条件（排除当前字段）
    Map<String, Object> cascadeFilters = excludeCurrentField(
            request.getCurrentFilters(), request.getFieldName());
    
    cascadeFilterBuilder.addCascadeFilters(cb, root, predicates, cascadeFilters);
    
    // 构建查询
    query.select(root.get(metadata.getEntityFieldName()))
         .where(predicates.toArray(new Predicate[0]))
         .distinct(true)
         .orderBy(cb.asc(root.get(metadata.getEntityFieldName())));
    
    return executeQuery(query, metadata.getMaxOptions());
}
```

## 🚀 扩展性设计

### 新模块集成步骤

1. **创建筛选配置类**
   - 定义模块编码
   - 注册筛选字段
   - 配置字段元数据

2. **创建筛选适配器**
   - 适配现有DTO格式
   - 转换筛选条件
   - 处理响应格式

3. **创建筛选控制器**
   - 提供模块专用API
   - 保持向后兼容
   - 添加权限控制

4. **创建自定义处理器（可选）**
   - 处理复杂筛选逻辑
   - 实现特殊业务需求
   - 优化查询性能

### 自定义处理器扩展

```java
@Component
public class CustomFilterHandler implements FilterHandler {
    
    @Override
    public List<FilterOptionDTO> getFilterOptions(
            FilterRequest request, FilterFieldMetadata metadata) {
        // 实现自定义筛选逻辑
        return processCustomLogic(request, metadata);
    }
    
    @Override
    public boolean supports(String fieldName) {
        return "customField".equals(fieldName);
    }
}
```

## 📊 性能优化

### 查询优化策略

1. **分页查询**: 限制返回结果数量
2. **索引优化**: 为筛选字段添加数据库索引
3. **查询缓存**: 对静态数据启用缓存
4. **延迟加载**: 按需加载筛选选项

### 内存优化

1. **结果限制**: 通过maxOptions限制内存使用
2. **搜索功能**: 减少大数据量字段的内存占用
3. **批量处理**: 优化批量筛选选项获取

## 🔒 安全考虑

### 权限控制

1. **接口权限**: 在控制器层添加权限注解
2. **数据权限**: 在查询构建时添加数据权限过滤
3. **字段权限**: 根据用户权限动态过滤可用字段

### 输入验证

1. **参数验证**: 验证字段名称和模块编码
2. **SQL注入防护**: 使用参数化查询
3. **XSS防护**: 对搜索文本进行转义

## 📈 监控和维护

### 性能监控

1. **查询时间监控**: 记录筛选查询执行时间
2. **内存使用监控**: 监控筛选选项内存占用
3. **错误率监控**: 统计筛选功能错误率

### 日志记录

```java
// 关键操作日志
log.info("Getting filter options for field: {}, module: {}, searchText: {}", 
         fieldName, moduleCode, searchText);

// 性能日志
log.debug("Filter options query completed: field={}, count={}, time={}ms", 
         fieldName, result.size(), executionTime);

// 错误日志
log.error("Failed to get filter options for field: {}, error: {}", 
         fieldName, e.getMessage(), e);
```

---

**总结**: 本架构设计实现了筛选功能的完全解耦，提供了高度可复用、可扩展的筛选服务框架，同时保持了完全的向后兼容性。
