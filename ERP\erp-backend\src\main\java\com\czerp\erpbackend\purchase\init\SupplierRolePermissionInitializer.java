package com.czerp.erpbackend.purchase.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 供应商权限角色分配初始化器
 * 负责将供应商相关权限分配给管理员角色
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(7) // 在供应商权限初始化之后执行
public class SupplierRolePermissionInitializer implements CommandLineRunner {
    
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final RolePermissionRepository rolePermissionRepository;
    
    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing supplier role permissions...");
        
        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping supplier role permissions initialization");
            return;
        }
        
        // 获取所有供应商相关权限
        List<String> supplierPermissionCodes = Arrays.asList(
                "supplier", "supplier:list", "supplier:read", "supplier:create", "supplier:update", "supplier:delete",
                "supplier-category", "supplier-category:list", "supplier-category:read", "supplier-category:create", "supplier-category:update", "supplier-category:delete"
        );
        
        List<Permission> supplierPermissions = permissionRepository.findByCodeIn(supplierPermissionCodes);
        
        // 为管理员角色分配供应商权限
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Permission permission : supplierPermissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString());
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }
        
        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            log.info("Assigned {} supplier permissions to admin role", rolePermissions.size());
        } else {
            log.info("All supplier permissions already assigned to admin role, skipping...");
        }
        
        log.info("Supplier role permissions initialized successfully");
    }
}
