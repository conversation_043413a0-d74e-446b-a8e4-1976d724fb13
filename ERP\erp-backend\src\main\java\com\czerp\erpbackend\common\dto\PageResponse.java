package com.czerp.erpbackend.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页响应
 * @param <T> 数据类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {

    /**
     * 数据列表
     */
    private List<T> content;

    /**
     * 总记录数
     */
    private long totalElements;

    /**
     * 总页数
     */
    private int totalPages;

    /**
     * 当前页码
     */
    private int page;

    /**
     * 每页大小
     */
    private int size;

    /**
     * 是否为第一页
     */
    private boolean first;

    /**
     * 是否为最后一页
     */
    private boolean last;

    /**
     * 是否为空
     */
    private boolean empty;

    /**
     * 构造函数
     * @param content 数据列表
     * @param totalElements 总记录数
     * @param totalPages 总页数
     * @param page 当前页码
     * @param size 每页大小
     */
    public PageResponse(List<T> content, long totalElements, int totalPages, int page, int size) {
        this.content = content;
        this.totalElements = totalElements;
        this.totalPages = totalPages;
        this.page = page;
        this.size = size;
        this.first = page == 1;
        this.last = page == totalPages || totalPages == 0;
        this.empty = content.isEmpty();
    }

    /**
     * 从Spring Data Page转换
     * @param page Spring Data Page
     * @param <T> 数据类型
     * @return PageResponse
     */
    public static <T> PageResponse<T> from(Page<T> page) {
        // 计算正确的页码（从0开始转为从1开始）
        int pageNumber = page.getNumber() + 1;

        // 计算正确的总页数（如果没有数据，总页数应该为0）
        int totalPages = page.getTotalElements() > 0 ? page.getTotalPages() : 0;

        return PageResponse.<T>builder()
                .content(page.getContent())
                .totalElements(page.getTotalElements())
                .totalPages(totalPages)
                .page(pageNumber)
                .size(page.getSize())
                .first(pageNumber == 1)
                .last(pageNumber == totalPages || totalPages == 0)
                .empty(page.isEmpty())
                .build();
    }
}
