package com.czerp.erpbackend.system.repository;

import com.czerp.erpbackend.system.entity.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户角色关联存储库
 */
@Repository
public interface UserRoleRepository extends JpaRepository<UserRole, String> {

    /**
     * 根据用户ID查询角色ID列表
     * @param userId 用户ID
     * @return 角色ID列表
     */
    @Query("SELECT ur.roleId FROM UserRole ur WHERE ur.userId = ?1")
    List<String> findRoleIdsByUserId(String userId);

    /**
     * 根据用户ID删除用户角色关联
     * @param userId 用户ID
     * @return 删除数量
     */
    @Modifying
    int deleteByUserId(String userId);

    /**
     * 根据角色ID查询用户数量
     * @param roleId 角色ID
     * @return 用户数量
     */
    long countByRoleId(String roleId);

    /**
     * 根据角色ID查询用户ID列表
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    @Query("SELECT ur.userId FROM UserRole ur WHERE ur.roleId = ?1")
    List<String> findUserIdsByRoleId(String roleId);

    /**
     * 根据用户ID查询角色编码列表
     * @param userId 用户ID
     * @return 角色编码列表
     */
    @Query("SELECT r.code FROM Role r JOIN UserRole ur ON r.id = ur.roleId WHERE ur.userId = :userId")
    List<String> findRoleCodesByUserId(@Param("userId") String userId);

    /**
     * 检查用户是否已分配角色
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否存在
     */
    boolean existsByUserIdAndRoleId(String userId, String roleId);
}