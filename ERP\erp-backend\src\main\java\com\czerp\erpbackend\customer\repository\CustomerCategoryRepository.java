package com.czerp.erpbackend.customer.repository;

import com.czerp.erpbackend.customer.entity.CustomerCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 客户分类存储库
 */
@Repository
public interface CustomerCategoryRepository extends JpaRepository<CustomerCategory, String>, JpaSpecificationExecutor<CustomerCategory> {

    /**
     * 根据分类编码查找分类
     * @param categoryCode 分类编码
     * @return 分类
     */
    Optional<CustomerCategory> findByCategoryCode(String categoryCode);

    /**
     * 判断分类编码是否存在
     * @param categoryCode 分类编码
     * @return 是否存在
     */
    boolean existsByCategoryCode(String categoryCode);

    /**
     * 根据分类名称模糊查询分类列表
     * @param categoryName 分类名称
     * @param pageable 分页参数
     * @return 分类列表
     */
    Page<CustomerCategory> findByCategoryNameContaining(String categoryName, Pageable pageable);

    /**
     * 根据分类编码模糊查询分类列表
     * @param categoryCode 分类编码
     * @param pageable 分页参数
     * @return 分类列表
     */
    Page<CustomerCategory> findByCategoryCodeContaining(String categoryCode, Pageable pageable);

    /**
     * 根据状态查询分类列表
     * @param status 状态
     * @param pageable 分页参数
     * @return 分类列表
     */
    Page<CustomerCategory> findByStatus(String status, Pageable pageable);

    /**
     * 根据是否默认查询分类列表
     * @param isDefault 是否默认
     * @param pageable 分页参数
     * @return 分类列表
     */
    Page<CustomerCategory> findByIsDefault(Boolean isDefault, Pageable pageable);

    /**
     * 查询所有默认分类
     * @return 默认分类列表
     */
    List<CustomerCategory> findByIsDefaultTrue();

    /**
     * 根据排序查询所有分类
     * @return 分类列表
     */
    List<CustomerCategory> findAllByOrderBySortOrderAsc();

    /**
     * 根据排序查询所有未删除的分类
     * @return 未删除的分类列表
     */
    List<CustomerCategory> findByIsDeletedFalseOrderBySortOrderAsc();
}
