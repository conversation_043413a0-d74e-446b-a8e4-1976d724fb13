# CZERP系统 - 用户管理模块后端开发文档

## 一、概述

本文档描述了CZERP系统用户管理模块的后端开发需求和接口规范，作为前后端对接的契约基础。

## 二、数据模型

### 1. 用户(User)

```sql
CREATE TABLE `sys_user` (
  `id` varchar(36) NOT NULL COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码(加密存储)',
  `nickname` varchar(50) NOT NULL COMMENT '姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态(active-正常,inactive-停用,locked-锁定)',
  `department_id` varchar(36) DEFAULT NULL COMMENT '部门ID',
  `position` varchar(50) DEFAULT NULL COMMENT '职位',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  `created_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(36) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_email` (`email`),
  KEY `idx_department` (`department_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';
```

### 2. 角色(Role)

```sql
CREATE TABLE `sys_role` (
  `id` varchar(36) NOT NULL COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态(active-启用,inactive-停用)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(36) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统角色表';
```

### 3. 用户角色关联(User_Role)

```sql
CREATE TABLE `sys_user_role` (
  `id` varchar(36) NOT NULL COMMENT '关联ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `role_id` varchar(36) NOT NULL COMMENT '角色ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_role` (`user_id`,`role_id`),
  KEY `idx_role` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
```

### 4. 权限(Permission)

```sql
CREATE TABLE `sys_permission` (
  `id` varchar(36) NOT NULL COMMENT '权限ID',
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `code` varchar(100) NOT NULL COMMENT '权限编码',
  `type` varchar(20) NOT NULL COMMENT '类型(menu-菜单,button-按钮,api-接口)',
  `parent_id` varchar(36) DEFAULT NULL COMMENT '父权限ID',
  `path` varchar(255) DEFAULT NULL COMMENT '路径(菜单路径或API路径)',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径(前端组件)',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态(active-启用,inactive-停用)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(36) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_parent` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统权限表';
```

### 5. 角色权限关联(Role_Permission)

```sql
CREATE TABLE `sys_role_permission` (
  `id` varchar(36) NOT NULL COMMENT '关联ID',
  `role_id` varchar(36) NOT NULL COMMENT '角色ID',
  `permission_id` varchar(36) NOT NULL COMMENT '权限ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_role_permission` (`role_id`,`permission_id`),
  KEY `idx_permission` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';
```

### 6. 部门(Department)

```sql
CREATE TABLE `sys_department` (
  `id` varchar(36) NOT NULL COMMENT '部门ID',
  `name` varchar(50) NOT NULL COMMENT '部门名称',
  `code` varchar(50) NOT NULL COMMENT '部门编码',
  `parent_id` varchar(36) DEFAULT NULL COMMENT '父部门ID',
  `level` int(11) NOT NULL DEFAULT '1' COMMENT '层级',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `manager_id` varchar(36) DEFAULT NULL COMMENT '部门负责人ID',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态(active-启用,inactive-停用)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_by` varchar(36) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(36) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_parent` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统部门表';
```

## 三、API接口规范

### 1. 通用响应格式

```typescript
// 通用响应格式
interface ApiResponse<T> {
  code: number;       // 状态码：200-成功，其他-失败
  message: string;    // 响应消息
  data: T;            // 响应数据
}

// 分页结果
interface PaginationResult<T> {
  total: number;      // 总记录数
  items: T[];         // 当前页数据
}
```

### 2. 用户管理接口

#### 2.1 获取用户列表

- **URL**: `/api/users`
- **Method**: `GET`
- **权限**: `user:list`
- **请求参数**:

```typescript
interface UserQueryParams {
  keyword?: string;                   // 关键词(用户名/姓名/邮箱)
  status?: 'active' | 'inactive' | 'locked';  // 状态
  department?: string;                // 部门ID
  page: number;                       // 页码
  pageSize: number;                   // 每页条数
  sortField?: string;                 // 排序字段
  sortOrder?: 'ascend' | 'descend';   // 排序方向
}
```

- **响应数据**:

```typescript
interface UserListResponse {
  total: number;      // 总记录数
  items: User[];      // 用户列表
}

interface User {
  id: string;
  username: string;
  nickname: string;
  avatar?: string;
  email: string;
  phone?: string;
  status: 'active' | 'inactive' | 'locked';
  department?: string;
  departmentName?: string;
  position?: string;
  roles: string[];    // 角色编码列表
  permissions: string[];  // 权限编码列表
  createTime: string;
  updateTime: string;
  lastLoginTime?: string;
}
```

#### 2.2 获取用户详情

- **URL**: `/api/users/{id}`
- **Method**: `GET`
- **权限**: `user:view`
- **响应数据**: `User`

#### 2.3 创建用户

- **URL**: `/api/users`
- **Method**: `POST`
- **权限**: `user:create`
- **请求参数**:

```typescript
interface CreateUserRequest {
  username: string;
  nickname: string;
  password: string;
  email: string;
  phone?: string;
  department?: string;
  position?: string;
  roles: string[];    // 角色ID列表
  status?: 'active' | 'inactive' | 'locked';
}
```

- **响应数据**: `User`

#### 2.4 更新用户

- **URL**: `/api/users/{id}`
- **Method**: `PUT`
- **权限**: `user:update`
- **请求参数**:

```typescript
interface UpdateUserRequest {
  nickname?: string;
  email?: string;
  phone?: string;
  department?: string;
  position?: string;
  roles?: string[];   // 角色ID列表
  status?: 'active' | 'inactive' | 'locked';
}
```

- **响应数据**: `User`

#### 2.5 删除用户

- **URL**: `/api/users/{id}`
- **Method**: `DELETE`
- **权限**: `user:delete`
- **响应数据**: `void`

#### 2.6 重置用户密码

- **URL**: `/api/users/{id}/reset-password`
- **Method**: `POST`
- **权限**: `user:reset-password`
- **响应数据**: `string` (新密码)

#### 2.7 修改用户状态

- **URL**: `/api/users/{id}/status`
- **Method**: `PUT`
- **权限**: `user:update-status`
- **请求参数**:

```typescript
interface ChangeStatusRequest {
  status: 'active' | 'inactive' | 'locked';
}
```

- **响应数据**: `void`

### 3. 角色管理接口

#### 3.1 获取角色列表

- **URL**: `/api/roles`
- **Method**: `GET`
- **权限**: `role:list`
- **请求参数**:

```typescript
interface RoleQueryParams {
  keyword?: string;                   // 关键词(名称/编码)
  status?: 'active' | 'inactive';     // 状态
  page?: number;                      // 页码
  pageSize?: number;                  // 每页条数
}
```

- **响应数据**:

```typescript
interface RoleListResponse {
  total: number;      // 总记录数
  items: Role[];      // 角色列表
}

interface Role {
  id: string;
  name: string;
  code: string;
  description?: string;
  status: 'active' | 'inactive';
  createTime: string;
  updateTime: string;
}
```

#### 3.2 获取角色详情

- **URL**: `/api/roles/{id}`
- **Method**: `GET`
- **权限**: `role:view`
- **响应数据**:

```typescript
interface RoleDetail extends Role {
  permissions: string[];  // 权限ID列表
}
```

#### 3.3 创建角色

- **URL**: `/api/roles`
- **Method**: `POST`
- **权限**: `role:create`
- **请求参数**:

```typescript
interface CreateRoleRequest {
  name: string;
  code: string;
  description?: string;
  permissions: string[];  // 权限ID列表
  status?: 'active' | 'inactive';
}
```

- **响应数据**: `Role`

#### 3.4 更新角色

- **URL**: `/api/roles/{id}`
- **Method**: `PUT`
- **权限**: `role:update`
- **请求参数**:

```typescript
interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: string[];  // 权限ID列表
  status?: 'active' | 'inactive';
}
```

- **响应数据**: `Role`

#### 3.5 删除角色

- **URL**: `/api/roles/{id}`
- **Method**: `DELETE`
- **权限**: `role:delete`
- **响应数据**: `void`

### 4. 部门管理接口

#### 4.1 获取部门树

- **URL**: `/api/departments/tree`
- **Method**: `GET`
- **权限**: `department:list`
- **响应数据**:

```typescript
interface DepartmentTree {
  id: string;
  name: string;
  code: string;
  parentId?: string;
  level: number;
  managerId?: string;
  managerName?: string;
  status: 'active' | 'inactive';
  children?: DepartmentTree[];
}
```

#### 4.2 获取部门详情

- **URL**: `/api/departments/{id}`
- **Method**: `GET`
- **权限**: `department:view`
- **响应数据**:

```typescript
interface Department {
  id: string;
  name: string;
  code: string;
  parentId?: string;
  parentName?: string;
  level: number;
  managerId?: string;
  managerName?: string;
  status: 'active' | 'inactive';
  createTime: string;
  updateTime: string;
}
```

#### 4.3 创建部门

- **URL**: `/api/departments`
- **Method**: `POST`
- **权限**: `department:create`
- **请求参数**:

```typescript
interface CreateDepartmentRequest {
  name: string;
  code: string;
  parentId?: string;
  managerId?: string;
  status?: 'active' | 'inactive';
}
```

- **响应数据**: `Department`

#### 4.4 更新部门

- **URL**: `/api/departments/{id}`
- **Method**: `PUT`
- **权限**: `department:update`
- **请求参数**:

```typescript
interface UpdateDepartmentRequest {
  name?: string;
  parentId?: string;
  managerId?: string;
  status?: 'active' | 'inactive';
}
```

- **响应数据**: `Department`

#### 4.5 删除部门

- **URL**: `/api/departments/{id}`
- **Method**: `DELETE`
- **权限**: `department:delete`
- **响应数据**: `void`

#### 4.6 获取部门成员

- **URL**: `/api/departments/{id}/members`
- **Method**: `GET`
- **权限**: `department:view`
- **请求参数**:

```typescript
interface MemberQueryParams {
  page?: number;      // 页码
  pageSize?: number;  // 每页条数
}
```

- **响应数据**:

```typescript
interface MemberListResponse {
  total: number;      // 总记录数
  items: User[];      // 用户列表
}
```

### 5. 权限管理接口

#### 5.1 获取权限树

- **URL**: `/api/permissions/tree`
- **Method**: `GET`
- **权限**: `permission:list`
- **响应数据**:

```typescript
interface PermissionTree {
  id: string;
  name: string;
  code: string;
  type: 'menu' | 'button' | 'api';
  parentId?: string;
  path?: string;
  component?: string;
  icon?: string;
  sort: number;
  status: 'active' | 'inactive';
  children?: PermissionTree[];
}
```

## 四、认证与授权

### 1. 登录认证

#### 1.1 用户登录

- **URL**: `/api/auth/login`
- **Method**: `POST`
- **请求参数**:

```typescript
interface LoginRequest {
  username: string;
  password: string;
  rememberMe?: boolean;
}
```

- **响应数据**:

```typescript
interface LoginResponse {
  token: string;          // JWT令牌
  refreshToken: string;   // 刷新令牌
  user: User;             // 用户信息
  expiresIn: number;      // 过期时间(秒)
}
```

#### 1.2 退出登录

- **URL**: `/api/auth/logout`
- **Method**: `POST`
- **请求头**: `Authorization: Bearer {token}`
- **响应数据**: `void`

#### 1.3 刷新令牌

- **URL**: `/api/auth/refresh-token`
- **Method**: `POST`
- **请求参数**:

```typescript
interface RefreshTokenRequest {
  refreshToken: string;
}
```

- **响应数据**:

```typescript
interface RefreshTokenResponse {
  token: string;          // 新JWT令牌
  refreshToken: string;   // 新刷新令牌
  expiresIn: number;      // 过期时间(秒)
}
```

#### 1.4 获取当前用户信息

- **URL**: `/api/user/current`
- **Method**: `GET`
- **请求头**: `Authorization: Bearer {token}`
- **响应数据**: `User`

#### 1.5 修改密码

- **URL**: `/api/user/change-password`
- **Method**: `POST`
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

```typescript
interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}
```

- **响应数据**: `void`

### 2. 权限控制

系统采用基于RBAC(Role-Based Access Control)的权限控制模型：

1. 用户关联角色，角色关联权限
2. 权限分为菜单权限、按钮权限和API权限
3. 前端根据用户拥有的权限动态渲染菜单和按钮
4. 后端通过拦截器验证用户是否有权限访问API

## 五、安全措施

1. 密码加密：使用BCrypt算法加密存储用户密码
2. JWT认证：使用JWT进行无状态认证
3. 请求验证：所有请求参数进行合法性验证
4. 防XSS攻击：对输入输出进行HTML转义
5. 防CSRF攻击：使用CSRF Token验证
6. 操作日志：记录用户的关键操作
7. 登录日志：记录用户的登录信息
8. 密码策略：强制密码复杂度要求
9. 账户锁定：多次登录失败后锁定账户

## 六、开发注意事项

1. 所有接口必须进行权限验证，除了登录和刷新令牌接口
2. 用户名和邮箱必须唯一
3. 不能删除系统内置的管理员用户和角色
4. 角色编码必须唯一，且不能修改
5. 部门编码必须唯一，且不能修改
6. 删除部门前必须确保部门下没有用户和子部门
7. 删除角色前必须确保没有用户关联该角色
8. 密码必须符合复杂度要求（至少8位，包含大小写字母、数字和特殊字符）
9. 所有操作必须记录操作日志
10. 所有查询接口必须支持分页和排序

## 七、数据初始化

系统初始化时需要创建以下基础数据：

1. 超级管理员用户：admin/admin123
2. 基础角色：超级管理员、系统管理员、普通用户
3. 基础权限：系统所有功能的权限
4. 基础部门：总公司

## 八、测试用例

1. 用户登录测试
   - 正确的用户名和密码
   - 错误的用户名或密码
   - 账户被锁定
   - 账户被停用

2. 用户管理测试
   - 创建用户
   - 修改用户信息
   - 重置密码
   - 修改用户状态
   - 删除用户

3. 角色管理测试
   - 创建角色
   - 修改角色信息
   - 分配权限
   - 删除角色

4. 部门管理测试
   - 创建部门
   - 修改部门信息
   - 删除部门

5. 权限控制测试
   - 不同角色访问不同权限的接口
   - 无权限访问接口

## 九、接口错误码

| 错误码 | 描述 |
| ------ | ---- |
| 200    | 成功 |
| 400    | 请求参数错误 |
| 401    | 未认证或认证失败 |
| 403    | 无权限访问 |
| 404    | 资源不存在 |
| 409    | 资源冲突 |
| 500    | 服务器内部错误 |

## 十、接口调用示例

### 登录接口

**请求**:
```http
POST /api/auth/login HTTP/1.1
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123",
  "rememberMe": true
}
```

**响应**:
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "1",
      "username": "admin",
      "nickname": "系统管理员",
      "email": "<EMAIL>",
      "status": "active",
      "roles": ["admin"],
      "permissions": ["*"],
      "createTime": "2023-01-01T00:00:00Z",
      "updateTime": "2023-01-01T00:00:00Z",
      "lastLoginTime": "2023-04-17T10:30:00Z"
    },
    "expiresIn": 86400
  }
}
```

### 获取用户列表

**请求**:
```http
GET /api/users?page=1&pageSize=10&keyword=admin HTTP/1.1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应**:
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 1,
    "items": [
      {
        "id": "1",
        "username": "admin",
        "nickname": "系统管理员",
        "email": "<EMAIL>",
        "status": "active",
        "roles": ["admin"],
        "createTime": "2023-01-01T00:00:00Z",
        "updateTime": "2023-01-01T00:00:00Z",
        "lastLoginTime": "2023-04-17T10:30:00Z"
      }
    ]
  }
}
```
