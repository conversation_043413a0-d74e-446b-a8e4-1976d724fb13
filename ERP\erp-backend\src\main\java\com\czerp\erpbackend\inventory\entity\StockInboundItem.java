package com.czerp.erpbackend.inventory.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import com.czerp.erpbackend.purchase.entity.PurchaseOrderItem;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 入库单明细实体
 */
@Entity
@Table(name = "stock_inbound_item")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class StockInboundItem extends BaseEntity {

    /**
     * 入库单明细ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 入库单
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inbound_id", nullable = false)
    private StockInbound stockInbound;

    /**
     * 来源采购订单明细
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "purchase_order_item_id")
    private PurchaseOrderItem purchaseOrderItem;

    /**
     * 数量
     */
    @Column(name = "quantity")
    private Integer quantity;

    /**
     * 收备品数
     */
    @Column(name = "spare_quantity")
    private Integer spareQuantity;

    /**
     * 价格
     */
    @Column(name = "price", precision = 18, scale = 4)
    private BigDecimal price;

    /**
     * 面积(平米)
     */
    @Column(name = "area_square_meters", precision = 18, scale = 4)
    private BigDecimal areaSquareMeters;

    /**
     * 单重
     */
    @Column(name = "unit_weight", precision = 18, scale = 4)
    private BigDecimal unitWeight;

    /**
     * 重量(KG)
     */
    @Column(name = "weight_kg", precision = 18, scale = 2)
    private BigDecimal weightKg;

    /**
     * 每板数
     */
    @Column(name = "quantity_per_board")
    private Integer quantityPerBoard;

    /**
     * 税率%
     */
    @Column(name = "tax_rate", precision = 5, scale = 2)
    private BigDecimal taxRate;

    /**
     * 币别
     */
    @Column(name = "currency", length = 20)
    private String currency;

    /**
     * 供应商送货单号
     */
    @Column(name = "supplier_delivery_no", length = 100)
    private String supplierDeliveryNo;

    /**
     * 折度规格
     */
    @Column(name = "folding_specification", length = 100)
    private String foldingSpecification;

    /**
     * 折算数量
     */
    @Column(name = "conversion_quantity", precision = 18, scale = 4)
    private BigDecimal conversionQuantity;

    /**
     * 折算单价
     */
    @Column(name = "conversion_price", precision = 18, scale = 4)
    private BigDecimal conversionPrice;

    /**
     * 折算金额
     */
    @Column(name = "conversion_amount", precision = 18, scale = 2)
    private BigDecimal conversionAmount;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;
}
