package com.czerp.erpbackend.system.service.impl;

import com.czerp.erpbackend.common.constant.CommonStatus;
import com.czerp.erpbackend.common.constant.UserStatus;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.system.dto.CreateUserRequest;
import com.czerp.erpbackend.system.dto.UpdateUserRequest;
import com.czerp.erpbackend.system.dto.UserDTO;
import com.czerp.erpbackend.system.dto.UserQueryRequest;
import com.czerp.erpbackend.system.entity.Department;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.User;
import com.czerp.erpbackend.system.entity.UserRole;
import com.czerp.erpbackend.system.mapper.UserMapper;
import com.czerp.erpbackend.system.repository.DepartmentRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import com.czerp.erpbackend.system.repository.UserRepository;
import com.czerp.erpbackend.system.repository.UserRoleRepository;
import com.czerp.erpbackend.system.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final DepartmentRepository departmentRepository;
    private final UserRoleRepository userRoleRepository;
    private final RolePermissionRepository rolePermissionRepository;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    /**
     * 分页查询用户列表
     * @param request 查询请求
     * @return 用户分页列表
     */
    @Override
    public PageResponse<UserDTO> findUsers(UserQueryRequest request) {
        int page = request.getPage() > 0 ? request.getPage() - 1 : 0;
        Pageable pageable = PageRequest.of(page, request.getSize());
        Page<User> userPage = userRepository.search(
                request.getKeyword(),
                request.getStatus(),
                request.getDepartment(),
                pageable
        );

        List<UserDTO> userDTOs = userPage.getContent().stream()
                .map(user -> {
                    UserDTO userDTO = userMapper.toDto(user);
                    fillUserRolesAndPermissions(userDTO, user.getId());
                    return userDTO;
                })
                .collect(Collectors.toList());

        return new PageResponse<UserDTO>(
                userDTOs,
                userPage.getTotalElements(),
                userPage.getTotalPages(),
                userPage.getNumber() + 1,
                userPage.getSize()
        );
    }

    /**
     * 根据ID查询用户
     * @param id 用户ID
     * @return 用户信息
     */
    @Override
    public UserDTO findUserById(String id) {
        User user = getUserById(id);
        UserDTO userDTO = userMapper.toDto(user);
        fillUserRolesAndPermissions(userDTO, id);
        return userDTO;
    }

    /**
     * 填充用户DTO的角色和权限
     * @param userDTO 用户DTO
     * @param userId 用户ID
     */
    private void fillUserRolesAndPermissions(UserDTO userDTO, String userId) {
        // 获取用户角色
        List<String> roleCodes = userRoleRepository.findRoleCodesByUserId(userId);
        userDTO.setRoles(roleCodes);

        // 获取用户权限
        if (roleCodes != null && !roleCodes.isEmpty()) {
            List<String> permissionCodes = rolePermissionRepository.findPermissionCodesByRoleCodesIn(roleCodes);
            userDTO.setPermissions(permissionCodes);
        }
    }

    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户信息
     */
    @Override
    public UserDTO findUserByUsername(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new BusinessException("用户不存在"));
        UserDTO userDTO = userMapper.toDto(user);
        fillUserRolesAndPermissions(userDTO, user.getId());
        return userDTO;
    }

    /**
     * 创建用户角色关系
     * @param user 用户
     * @param roleIds 角色ID列表
     * @return 用户角色关联列表
     */
    private List<UserRole> createUserRoles(User user, List<String> roleIds) {
        // 验证角色存在
        List<Role> roles = roleRepository.findAllById(roleIds);
        if (roles.size() != roleIds.size()) {
            throw new BusinessException("角色不存在");
        }

        // 创建用户角色关联
        List<UserRole> userRoles = new ArrayList<>();
        for (Role role : roles) {
            UserRole userRole = new UserRole();
            userRole.setId(UUID.randomUUID().toString());
            userRole.setUserId(user.getId());
            userRole.setRoleId(role.getId());
            userRole.setUser(user);
            userRole.setRole(role);
            userRoles.add(userRole);
        }

        return userRoleRepository.saveAll(userRoles);
    }

    /**
     * 处理用户部门关系
     * @param user 用户
     * @param departmentId 部门ID
     */
    private void processUserDepartment(User user, String departmentId) {
        if (StringUtils.hasText(departmentId)) {
            Department department = departmentRepository.findById(departmentId)
                    .orElseThrow(() -> new BusinessException("部门不存在"));
            user.setDepartmentId(departmentId);
            user.setDepartment(department);
        } else {
            user.setDepartmentId(null);
            user.setDepartment(null);
        }
    }

    /**
     * 创建用户
     * @param request 创建用户请求
     * @return 用户信息
     */
    @Override
    @Transactional
    public UserDTO createUser(CreateUserRequest request) {
        // 校验用户基本信息
        validateNewUser(request.getUsername(), request.getEmail());

        // 创建用户基本信息
        User user = userMapper.toEntity(request);
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setStatus(UserStatus.ENABLED.getValue());
        user.setIsDeleted(false);

        // 处理部门关系
        processUserDepartment(user, request.getDepartment());

        // 保存用户
        user = userRepository.save(user);

        // 处理角色关系
        if (request.getRoles() != null && !request.getRoles().isEmpty()) {
            createUserRoles(user, request.getRoles());
        }

        // 转换为DTO并填充角色和权限
        UserDTO userDTO = userMapper.toDto(user);
        fillUserRolesAndPermissions(userDTO, user.getId());
        return userDTO;
    }

    /**
     * 校验新用户信息
     * @param username 用户名
     * @param email 邮箱
     */
    private void validateNewUser(String username, String email) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(username)) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (StringUtils.hasText(email) && userRepository.existsByEmail(email)) {
            throw new BusinessException("邮箱已存在");
        }
    }

    /**
     * 校验更新的邮箱是否有冲突
     * @param userId 用户ID
     * @param newEmail 新邮箱
     * @param oldEmail 旧邮箱
     */
    private void validateUpdatedEmail(String userId, String newEmail, String oldEmail) {
        if (StringUtils.hasText(newEmail) && !newEmail.equals(oldEmail)) {
            Optional<User> existingUser = userRepository.findByEmail(newEmail);
            if (existingUser.isPresent() && !existingUser.get().getId().equals(userId)) {
                throw new BusinessException("邮箱已存在");
            }
        }
    }

    /**
     * 更新用户
     * @param id 用户ID
     * @param request 更新用户请求
     * @return 用户信息
     */
    @Override
    @Transactional
    public UserDTO updateUser(String id, UpdateUserRequest request) {
        // 获取当前用户
        User user = getUserById(id);

        // 检查邮箱是否已被其他用户使用
        validateUpdatedEmail(id, request.getEmail(), user.getEmail());

        // 更新基本信息
        userMapper.updateEntity(request, user);

        // 处理部门关系
        processUserDepartment(user, request.getDepartment());

        // 保存用户
        user = userRepository.save(user);

        // 处理角色关系
        if (request.getRoles() != null) {
            // 删除原有角色
            userRoleRepository.deleteByUserId(id);

            // 分配新角色
            if (!request.getRoles().isEmpty()) {
                createUserRoles(user, request.getRoles());
            }
        }

        // 转换为DTO并填充角色和权限
        UserDTO userDTO = userMapper.toDto(user);
        fillUserRolesAndPermissions(userDTO, user.getId());
        return userDTO;
    }

    /**
     * 删除用户
     * @param id 用户ID
     */
    @Override
    @Transactional
    public void deleteUser(String id) {
        User user = getUserById(id);

        // 逻辑删除
        user.setIsDeleted(true);
        userRepository.save(user);

        // 删除用户角色关联
        userRoleRepository.deleteByUserId(id);
    }

    /**
     * 更改用户状态
     * @param id 用户ID
     * @param status 状态
     * @return 用户信息
     */
    @Override
    public UserDTO changeStatus(String id, String status) {
        User user = getUserById(id);

        // 检查状态是否有效
        try {
            UserStatus.fromValue(status);
        } catch (IllegalArgumentException e) {
            throw new BusinessException("无效的状态");
        }

        user.setStatus(status);
        user = userRepository.save(user);

        UserDTO userDTO = userMapper.toDto(user);
        fillUserRolesAndPermissions(userDTO, user.getId());
        return userDTO;
    }

    /**
     * 修改密码
     * @param id 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 用户信息
     */
    @Override
    public UserDTO changePassword(String id, String oldPassword, String newPassword) {
        User user = getUserById(id);

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("旧密码不正确");
        }

        // 设置新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        user = userRepository.save(user);

        UserDTO userDTO = userMapper.toDto(user);
        fillUserRolesAndPermissions(userDTO, user.getId());
        return userDTO;
    }

    /**
     * 重置密码
     * @param id 用户ID
     * @param password 新密码
     * @return 用户信息
     */
    @Override
    public UserDTO resetPassword(String id, String password) {
        User user = getUserById(id);
        user.setPassword(passwordEncoder.encode(password));
        user = userRepository.save(user);
        UserDTO userDTO = userMapper.toDto(user);
        fillUserRolesAndPermissions(userDTO, user.getId());
        return userDTO;
    }

    /**
     * 重置密码
     * @param id 用户ID
     * @return 新密码
     */
    @Override
    public UserDTO resetPassword(String id) {
        // 生成随机密码
        String newPassword = generateRandomPassword();

        return resetPassword(id, newPassword);
    }

    /**
     * 生成随机密码
     * @return 随机密码
     */
    private String generateRandomPassword() {
        String upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String lowerChars = "abcdefghijklmnopqrstuvwxyz";
        String numbers = "0123456789";
        String specialChars = "!@#$%^&*()_-+=<>?";
        String allChars = upperChars + lowerChars + numbers + specialChars;

        Random random = new Random();
        StringBuilder password = new StringBuilder();

        // 确保密码包含至少一个大写字母
        password.append(upperChars.charAt(random.nextInt(upperChars.length())));

        // 确保密码包含至少一个小写字母
        password.append(lowerChars.charAt(random.nextInt(lowerChars.length())));

        // 确保密码包含至少一个数字
        password.append(numbers.charAt(random.nextInt(numbers.length())));

        // 确保密码包含至少一个特殊字符
        password.append(specialChars.charAt(random.nextInt(specialChars.length())));

        // 添加随机字符直到达到所需的密码长度
        for (int i = 4; i < 12; i++) {
            password.append(allChars.charAt(random.nextInt(allChars.length())));
        }

        // 打乱密码字符顺序
        char[] passwordArray = password.toString().toCharArray();
        for (int i = 0; i < passwordArray.length; i++) {
            int j = random.nextInt(passwordArray.length);
            char temp = passwordArray[i];
            passwordArray[i] = passwordArray[j];
            passwordArray[j] = temp;
        }

        return new String(passwordArray);
    }

    /**
     * 根据部门ID查询用户列表
     * @param departmentId 部门ID
     * @param page 页码
     * @param size 每页大小
     * @return 用户分页列表
     */
    @Override
    public PageResponse<UserDTO> findUsersByDepartmentId(String departmentId, int page, int size) {
        page = page > 0 ? page - 1 : 0;
        Pageable pageable = PageRequest.of(page, size);
        Page<User> userPage = userRepository.findByDepartmentIdAndIsDeletedFalse(departmentId, pageable);

        List<UserDTO> userDTOs = userPage.getContent().stream()
                .map(user -> {
                    UserDTO userDTO = userMapper.toDto(user);
                    fillUserRolesAndPermissions(userDTO, user.getId());
                    return userDTO;
                })
                .collect(Collectors.toList());

        return new PageResponse<UserDTO>(
                userDTOs,
                userPage.getTotalElements(),
                userPage.getTotalPages(),
                userPage.getNumber() + 1,
                userPage.getSize()
        );
    }

    /**
     * 根据角色ID查询用户列表
     * @param roleId 角色ID
     * @param page 页码
     * @param size 每页大小
     * @return 用户分页列表
     */
    @Override
    public PageResponse<UserDTO> findUsersByRoleId(String roleId, int page, int size) {
        // 获取拥有指定角色的用户ID列表
        List<String> userIds = userRoleRepository.findUserIdsByRoleId(roleId);

        if (userIds.isEmpty()) {
            return new PageResponse<UserDTO>(new ArrayList<>(), 0, 0, page, size);
        }

        page = page > 0 ? page - 1 : 0;
        Pageable pageable = PageRequest.of(page, size);

        // 查询用户列表
        Page<User> userPage = userRepository.findByIdInAndIsDeletedFalse(userIds, pageable);

        List<UserDTO> userDTOs = userPage.getContent().stream()
                .map(user -> {
                    UserDTO userDTO = userMapper.toDto(user);
                    fillUserRolesAndPermissions(userDTO, user.getId());
                    return userDTO;
                })
                .collect(Collectors.toList());

        return new PageResponse<UserDTO>(
                userDTOs,
                userPage.getTotalElements(),
                userPage.getTotalPages(),
                userPage.getNumber() + 1,
                userPage.getSize()
        );
    }

    /**
     * 根据ID获取用户实体
     * @param id 用户ID
     * @return 用户实体
     */
    private User getUserById(String id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));
    }
}