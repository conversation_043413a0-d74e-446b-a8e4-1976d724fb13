package com.czerp.erpbackend.production.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import com.czerp.erpbackend.common.entity.BaseEntity;

/**
 * 工序实体
 */
@Entity
@Table(name = "process_info")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Process extends BaseEntity {

    /**
     * 工序ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 工序名称
     */
    @Column(name = "process", length = 100, nullable = false)
    private String process;

    /**
     * 工艺要求
     */
    @Column(name = "process_requirements", length = 500)
    private String processRequirements;

    /**
     * 印版编号
     */
    @Column(name = "printing_plate_no", length = 100)
    private String printingPlateNo;

    /**
     * 水墨编号
     */
    @Column(name = "ink_no", length = 100)
    private String inkNo;

    /**
     * 水墨名称
     */
    @Column(name = "ink_name", length = 100)
    private String inkName;

    /**
     * 颜色数
     */
    @Column(name = "color_count")
    private Integer colorCount;

    /**
     * 创建人姓名
     */
    @Column(name = "created_by_name", length = 50)
    private String createdByName;

    /**
     * 更新人姓名
     */
    @Column(name = "updated_by_name", length = 50)
    private String updatedByName;
}
