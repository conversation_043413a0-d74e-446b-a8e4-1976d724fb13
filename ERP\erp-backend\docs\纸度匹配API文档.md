# 纸度匹配API文档

## 概述

纸度匹配API用于根据纸盒的宽度和高度，从预定义的标准纸度列表中找出最合适的纸度，使用混合算法策略进行匹配。

## API接口

### 1. 匹配最佳纸度（需要权限验证）

**请求URL**：`/system/paper-size-matching`

**请求方法**：`POST`

**权限要求**：`system:paper-size-matching:match`

**请求参数**：

```json
{
  "width": 20.5,   // 纸盒宽度，必填，大于0
  "height": 15.3,  // 纸盒高度，必填，大于0
  "unit": "CM"     // 长度单位，可选，默认为CM，可选值：CM/cm（厘米）、MM/mm（毫米）、INCH/inch（英寸），大小写不敏感
}
```

**响应结果**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "bestPaperSize": {
      "id": 1,
      "paperSizeInch": 29.00,
      "paperSizeCm": 73.66,
      "maxLossInch": 0.50,
      "maxLossCm": 1.27,
      "createdBy": "admin",
      "createdTime": "2023-01-01T12:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-01-01T12:00:00"
    },
    "maxBoxesProducible": 2,
    "wastePercentage": 0.05,
    "wastePercentageFormatted": "5%",
    "message": "成功找到最佳纸度"
  }
}
```

### 2. 匹配最佳纸度（公共接口，无需权限验证）

**请求URL**：`/public/system/paper-size-matching`

**请求方法**：`POST`

**请求参数**：

```json
{
  "width": 20.5,   // 纸盒宽度，必填，大于0
  "height": 15.3,  // 纸盒高度，必填，大于0
  "unit": "CM"     // 长度单位，可选，默认为CM，可选值：CM/cm（厘米）、MM/mm（毫米）、INCH/inch（英寸），大小写不敏感
}
```

**响应结果**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "bestPaperSize": {
      "id": 1,
      "paperSizeInch": 29.00,
      "paperSizeCm": 73.66,
      "maxLossInch": 0.50,
      "maxLossCm": 1.27,
      "createdBy": "admin",
      "createdTime": "2023-01-01T12:00:00",
      "updatedBy": "admin",
      "updatedTime": "2023-01-01T12:00:00"
    },
    "maxBoxesProducible": 2,
    "wastePercentage": 0.05,
    "wastePercentageFormatted": "5%",
    "message": "成功找到最佳纸度"
  }
}
```

## 错误情况

### 1. 无可用纸度数据

当系统中没有配置任何纸度数据时，返回：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "bestPaperSize": null,
    "maxBoxesProducible": null,
    "wastePercentage": null,
    "wastePercentageFormatted": null,
    "message": "无可用纸度数据"
  }
}
```

### 2. 计算值超出最大纸度范围

当计算值大于或等于列表中的最大纸度值时，返回：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "bestPaperSize": null,
    "maxBoxesProducible": null,
    "wastePercentage": null,
    "wastePercentageFormatted": null,
    "message": "计算值超出最大纸度范围"
  }
}
```

### 3. 所需尺寸过大

当所有可用纸度都无法生产至少一个物品时，返回：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "bestPaperSize": null,
    "maxBoxesProducible": null,
    "wastePercentage": null,
    "wastePercentageFormatted": null,
    "message": "未找到合适的纸度，所需尺寸过大"
  }
}
```

### 4. 输入参数无效

当输入参数无效（如宽度或高度小于等于0）时，返回：

```json
{
  "code": 400,
  "message": "纸盒宽度必须大于0",
  "data": null
}
```

或

```json
{
  "code": 400,
  "message": "纸盒高度必须大于0",
  "data": null
}
```

## 算法说明

该API使用"混合纸度推荐算法"，通过以下步骤找出最佳纸度：

1. 计算基准值：calculatedValue = (宽度 + 高度) / 2.54
2. 根据计算值与最小纸度的比较，分为两种场景：

### 场景一：calculatedValue < MinPaperSize (计算值小于最小纸度)

目标：寻找单位成本最优（或接近最优）且尺寸尽可能小的纸度，以提高材料利用率。

步骤：
1. 定义所需尺寸：RequiredDim = calculatedValue
2. 遍历所有可用纸度，计算每个纸度的可生产数量和单位成本
3. 查找绝对最低成本，并确定成本容差阈值
4. 筛选出成本在容差范围内的候选纸度
5. 在候选集中选择尺寸最小的纸度作为最终结果

### 场景二：calculatedValue >= MinPaperSize (计算值大于等于最小纸度)

目标：寻找刚好能满足需求的、下一个更大的标准纸度。

步骤：
1. 遍历已排序的可用纸度列表
2. 查找第一个严格大于calculatedValue的纸度
3. 如果找不到满足条件的纸度（即calculatedValue大于或等于最大纸度），则返回错误信息

## 注意事项

1. 输入的宽度和高度可以使用不同的单位（CM/cm、MM/mm、INCH/inch），通过unit参数指定，单位参数大小写不敏感（如"CM"、"cm"、"Cm"都会被识别为厘米）
2. 如果不指定unit参数，默认使用厘米(CM)作为单位
3. 系统内部会将所有单位统一转换为英寸进行计算
4. 浪费率以小数形式返回，如0.05表示5%
5. 同时提供了格式化后的百分比字符串，如"5%"
6. 成本容差率默认为3%，用于场景一，定义了多大范围内的成本可以被认为是"接近最优"
