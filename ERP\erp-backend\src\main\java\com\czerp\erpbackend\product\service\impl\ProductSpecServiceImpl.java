package com.czerp.erpbackend.product.service.impl;

import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.product.dto.CreateSpecRequest;
import com.czerp.erpbackend.product.dto.ProductSpecDTO;
import com.czerp.erpbackend.product.dto.UpdateSpecRequest;
import com.czerp.erpbackend.product.entity.ProductSpec;
import com.czerp.erpbackend.product.mapper.ProductSpecMapper;
import com.czerp.erpbackend.product.repository.ProductRepository;
import com.czerp.erpbackend.product.repository.ProductSpecRepository;
import com.czerp.erpbackend.product.service.ProductSpecService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 产品规格服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductSpecServiceImpl implements ProductSpecService {
    
    private final ProductSpecRepository specRepository;
    private final ProductRepository productRepository;
    private final ProductSpecMapper specMapper;
    
    /**
     * 查询所有规格
     * @return 规格列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<ProductSpecDTO> findAllSpecs() {
        log.debug("Finding all specs");
        
        List<ProductSpec> specs = specRepository.findAll();
        
        return specs.stream()
                .map(specMapper::toDto)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据ID查询规格
     * @param id 规格ID
     * @return 规格信息
     */
    @Override
    @Transactional(readOnly = true)
    public ProductSpecDTO findSpecById(String id) {
        log.debug("Finding spec by id: {}", id);
        
        ProductSpec spec = specRepository.findById(id)
                .orElseThrow(() -> new BusinessException("规格不存在"));
        
        return specMapper.toDto(spec);
    }
    
    /**
     * 创建规格
     * @param request 创建请求
     * @return 规格信息
     */
    @Override
    @Transactional
    public ProductSpecDTO createSpec(CreateSpecRequest request) {
        log.debug("Creating spec with request: {}", request);
        
        // 检查编码是否已存在
        if (specRepository.existsByCode(request.getCode())) {
            throw new BusinessException("规格编码已存在");
        }
        
        // 创建规格
        ProductSpec spec = specMapper.toEntity(request);
        spec.setId(UUID.randomUUID().toString());
        
        // 保存规格
        spec = specRepository.save(spec);
        
        return specMapper.toDto(spec);
    }
    
    /**
     * 更新规格
     * @param id 规格ID
     * @param request 更新请求
     * @return 规格信息
     */
    @Override
    @Transactional
    public ProductSpecDTO updateSpec(String id, UpdateSpecRequest request) {
        log.debug("Updating spec with id: {} and request: {}", id, request);
        
        // 查询规格
        ProductSpec spec = specRepository.findById(id)
                .orElseThrow(() -> new BusinessException("规格不存在"));
        
        // 更新规格
        specMapper.updateEntity(request, spec);
        
        // 保存规格
        spec = specRepository.save(spec);
        
        return specMapper.toDto(spec);
    }
    
    /**
     * 删除规格
     * @param id 规格ID
     */
    @Override
    @Transactional
    public void deleteSpec(String id) {
        log.debug("Deleting spec with id: {}", id);
        
        // 查询规格
        ProductSpec spec = specRepository.findById(id)
                .orElseThrow(() -> new BusinessException("规格不存在"));
        
        // 检查是否有关联的货品
        long productCount = productRepository.findBySpecId(id).size();
        if (productCount > 0) {
            throw new BusinessException("该规格下有货品，不能删除");
        }
        
        // 删除规格
        specRepository.delete(spec);
    }
}
