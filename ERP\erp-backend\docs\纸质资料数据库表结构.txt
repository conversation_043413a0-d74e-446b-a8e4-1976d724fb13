mysql> SHOW FULL COLUMNS FROM paper_material;
+------------------------+---------------+--------------------+------+-----+---------+----------------+---------------------------------+-----------------------+
| Field                  | Type          | Collation          | Null | Key | Default | Extra          | Privileges                      | Comment               |
+------------------------+---------------+--------------------+------+-----+---------+----------------+---------------------------------+-----------------------+
| id                     | bigint        | NULL               | NO   | PRI | NULL    | auto_increment | select,insert,update,references | 主键ID                |
| paper_code             | varchar(50)   | utf8mb4_0900_ai_ci | YES  | MUL | NULL    |                | select,insert,update,references | 纸质编码              |
| paper_name             | varchar(50)   | utf8mb4_0900_ai_ci | YES  | MUL | NULL    |                | select,insert,update,references | 纸质                  |
| paper_type             | varchar(50)   | utf8mb4_0900_ai_ci | YES  | MUL | NULL    |                | select,insert,update,references | 纸类                  |
| flute_type             | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 楞别                  |
| is_standard            | tinyint(1)    | NULL               | YES  | MUL | NULL    |                | select,insert,update,references | 标准纸质 (1-是, 0-否) |
| production_code        | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 生产代号              |
| face_paper             | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 面纸                  |
| core_paper1            | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 芯纸1                 |
| middle_partition1      | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 中隔1                 |
| core_paper2            | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 芯纸2                 |
| middle_partition2      | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 中隔2                 |
| core_paper3            | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 芯纸3                 |
| liner_paper            | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 里纸                  |
| layer_count            | int           | NULL               | YES  |     | NULL    |                | select,insert,update,references | 层数                  |
| weight_kg_per_ksi      | decimal(10,6) | NULL               | YES  |     | NULL    |                | select,insert,update,references | 重量(千克/千平方英寸) |
| weight_kg_per_sqm      | decimal(10,6) | NULL               | YES  |     | NULL    |                | select,insert,update,references | 重量(千克/平方米)     |
| edge_crush_strength    | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 边压强度              |
| bursting_strength      | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 纸板耐破度            |
| corresponding_standard | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 对应标准纸质          |
| default_supplier       | varchar(100)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 默认供应商名称        |
| remarks                | varchar(500)  | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 备注                  |
| is_disabled            | tinyint(1)    | NULL               | YES  | MUL | 0       |                | select,insert,update,references | 停用 (1-是, 0-否)     |
| created_by             | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 创建人                |
| created_time           | datetime      | NULL               | YES  |     | NULL    |                | select,insert,update,references | 创建时间              |
| updated_by             | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references | 更新人                |
| updated_time           | datetime      | NULL               | YES  |     | NULL    |                | select,insert,update,references | 更新时间              |
| is_deleted             | bit(1)        | NULL               | NO   |     | NULL    |                | select,insert,update,references |                       |
| version                | int           | NULL               | YES  |     | NULL    |                | select,insert,update,references |                       |
| CreatedBy              | varchar(50)   | utf8mb4_0900_ai_ci | NO   |     | NULL    |                | select,insert,update,references |                       |
| UpdatedBy              | varchar(50)   | utf8mb4_0900_ai_ci | YES  |     | NULL    |                | select,insert,update,references |                       |
+------------------------+---------------+--------------------+------+-----+---------+----------------+---------------------------------+-----------------------+
31 <USER> <GROUP> set (0.02 sec)