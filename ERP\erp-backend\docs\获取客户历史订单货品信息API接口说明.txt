客户历史订单明细查询 API 文档
接口概述
该接口用于查询指定客户的历史订单明细记录，返回包含客方货号、品名、工艺要求等信息的列表，可用于销售订单创建时快速选择历史产品信息。
接口详情
请求方法
GET /api/sales/orders/customers/{customerId}/historical-items
接口权限
需要 sales:order:read 权限
路径参数
参数名	类型	必填	描述
customerId	String	是	客户ID
查询参数
参数名	类型	必填	描述
keyword	String	否	搜索关键词，用于搜索客方货号或品名

响应格式
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "so12345item1",
      "orderId": "so12345",
      "customerOrderNo": "CUS-20230101",
      "customerProductCode": "CP001",
      "productName": "三层瓦楞纸箱",
      "processRequirements": "单面印刷，覆亚膜",
      "boxType": "普通箱",
      "paperType": "A楞",
      "corrugationType": "三层",
      "productionPaperType": "牛卡纸",
      "length": 200.00,
      "width": 150.00,
      "height": 100.00,
      "sizeUnit": "mm",
      "createdTime": "2023-06-01T10:30:00",
      "processes": [
        {
          "id": "proc1",
          "orderId": "so12345",
          "sequence": 1,
          "processName": "印刷",
          "processRequirements": "单面印刷，覆亚膜",
          "plateNumber": "P001",
          "inkNumber": "I001",
          "inkName": "黑色",
          "colorCount": 1
        }
      ],
      "materials": [
        {
          "id": "mat1",
          "orderItemId": "so12345item1",
          "serialNo": 1,
          "paperQuality": "A3A",
          "paperWidth": 35.00,
          "paperLength": 38.25,
          "widthOpen": 3.00,
          "lengthOpen": 1.00,
          "method": "竖坑",
          "pressSizeWidth": "96+100+96",
          "pressMethod": "",
          "actualMaterialWidth": 29.2000,
          "actualMaterialLength": 97.0000,
          "dieModel": "",
          "dieModelNo": "",
          "dieOpenCount": 0,
          "dieModelPosition": "",
          "dieModelChecked": false,
          "actualMaterialLengthConverted": 38.1890,
          "actualMaterialWidthConverted": 11.4961,
          "materialUsageCount": 1.00
        }
      ]
    },
    {
      "id": "so12346item1",
      "orderId": "so12346",
      "customerOrderNo": "CUS-20230215",
      "customerProductCode": "CP002",
      "productName": "五层瓦楞纸箱",
      "processRequirements": "双面印刷，防水处理",
      "boxType": "加强箱",
      "paperType": "B楞",
      "corrugationType": "五层",
      "productionPaperType": "高强瓦楞纸",
      "length": 300.00,
      "width": 250.00,
      "height": 200.00,
      "sizeUnit": "mm",
      "createdTime": "2023-06-15T14:20:00",
      "processes": [
        {
          "id": "proc2",
          "orderId": "so12346",
          "sequence": 1,
          "processName": "印刷",
          "processRequirements": "双面印刷",
          "plateNumber": "P002",
          "inkNumber": "I002",
          "inkName": "彩色",
          "colorCount": 4
        },
        {
          "id": "proc3",
          "orderId": "so12346",
          "sequence": 2,
          "processName": "表面处理",
          "processRequirements": "防水处理",
          "plateNumber": "",
          "inkNumber": "",
          "inkName": "",
          "colorCount": 0
        }
      ],
      "materials": [
        {
          "id": "mat2",
          "orderItemId": "so12346item1",
          "serialNo": 1,
          "paperQuality": "B5B",
          "paperWidth": 45.00,
          "paperLength": 48.25,
          "widthOpen": 4.00,
          "lengthOpen": 2.00,
          "method": "横坑",
          "pressSizeWidth": "120+150+120",
          "pressMethod": "压线",
          "actualMaterialWidth": 35.2000,
          "actualMaterialLength": 120.0000,
          "dieModel": "标准",
          "dieModelNo": "DM001",
          "dieOpenCount": 2,
          "dieModelPosition": "中间",
          "dieModelChecked": true,
          "actualMaterialLengthConverted": 47.2440,
          "actualMaterialWidthConverted": 13.8583,
          "materialUsageCount": 2.00
        }
      ]
    }
  ]
}

响应字段说明
字段名	类型	描述
id	String	订单明细ID
orderId	String	订单ID
customerOrderNo	String	客户订单号
customerProductCode	String	客方货号
productName	String	品名
processRequirements	String	工艺要求
boxType	String	盒式
paperType	String	纸质
corrugationType	String	楞别
productionPaperType	String	生产纸质
length	BigDecimal	长
width	BigDecimal	宽
height	BigDecimal	高
sizeUnit	String	尺寸单位
createdTime	LocalDateTime	创建日期时间
processes	Array	工序信息列表
processes[].id	String	工序ID
processes[].processName	String	工序名称
processes[].processRequirements	String	工序要求
processes[].sequence	Integer	工序顺序
processes[].plateNumber	String	版号
processes[].inkNumber	String	墨号
processes[].inkName	String	墨名
processes[].colorCount	Integer	色数
materials	Array	材料信息列表
materials[].id	String	材料ID
materials[].serialNo	Integer	序号
materials[].paperQuality	String	纸质
materials[].paperWidth	BigDecimal	纸度
materials[].paperLength	BigDecimal	纸长
materials[].widthOpen	BigDecimal	度开
materials[].lengthOpen	BigDecimal	长开
materials[].method	String	方式
materials[].pressSizeWidth	String	压线尺寸(纸度)
materials[].pressMethod	String	压线方式
materials[].actualMaterialWidth	BigDecimal	实际用料宽
materials[].actualMaterialLength	BigDecimal	实际用料长
materials[].dieModel	String	啤模
materials[].dieModelNo	String	啤模编号
materials[].dieOpenCount	Integer	模开数
materials[].dieModelPosition	String	啤模位置
materials[].dieModelChecked	Boolean	啤模选中状态
materials[].actualMaterialLengthConverted	BigDecimal	实际用料长(转换)
materials[].actualMaterialWidthConverted	BigDecimal	实际用料宽(转换)
materials[].materialUsageCount	BigDecimal	用量
业务规则
返回的历史订单明细按创建时间降序排序，最新的记录排在前面
对结果按客方货号去重，每个客方货号只返回最新的一条记录
如果提供了关键词参数，将在客方货号和品名字段中进行模糊匹配
只返回未删除订单的明细记录
错误码
错误码	描述	解决方案
400	请求参数错误	检查请求参数是否正确
401	未授权	检查用户是否已登录
403	权限不足	检查用户是否有 sales:order:read 权限
404	客户不存在	检查客户ID是否正确
500	服务器内部错误	联系系统管理员
前端实现建议
在销售订单明细表单中的客方货号输入框旁添加"···"按钮
点击按钮时，调用此API获取历史订单明细数据
在模态框中显示历史记录列表，并提供搜索功能
在模态框中添加选项卡或折叠面板，用于展示工序和材料信息
提供选择是否同时复制工序和材料信息的选项
用户选择一条历史记录后，自动填充以下字段：
客方货号
品名
工艺要求
盒式
纸质
楞别
生产纸质
长
宽
高
尺寸单位
根据用户选择，可以同时复制以下信息：
工序信息（包括工序名称、工序要求、版号、墨号等）
材料信息（包括纸质、纸度、纸长、度开、长开、方式等）
注意事项
调用API前需确保已选择客户，并获取到客户ID
如果客户未选择，应禁用"···"按钮或提示用户先选择客户
模态框中应显示记录的创建日期，方便用户了解记录的时间
建议在模态框中提供搜索功能，方便用户快速找到需要的记录
工序和材料信息可能较多，建议使用分页或虚拟滚动方式展示
考虑添加查询参数控制是否加载工序和材料信息，以提高性能
工序信息是按订单ID关联的，而材料信息是按订单明细ID关联的
复制工序和材料信息时，需要注意调整相关ID关联关系