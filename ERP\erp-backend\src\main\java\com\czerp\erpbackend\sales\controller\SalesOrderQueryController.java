package com.czerp.erpbackend.sales.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.sales.dto.FilterOptionDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryParamDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryResponseDTO;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryResultDTO;
import com.czerp.erpbackend.sales.service.SalesOrderQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 销售订单查询Controller
 */
@RestController
@RequestMapping("/sales/orders/query")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "销售订单查询", description = "销售订单查询相关接口")
public class SalesOrderQueryController {

    private final SalesOrderQueryService salesOrderQueryService;

    /**
     * 分页查询销售订单（传统方式，保持向后兼容）
     * @param queryParam 查询参数
     * @return 销售订单分页列表
     */
    @GetMapping
    @Operation(summary = "分页查询销售订单", description = "分页查询销售订单")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<PageResponse<SalesOrderQueryResultDTO>>> querySalesOrders(SalesOrderQueryParamDTO queryParam) {
        log.info("Querying sales orders with params: {}", queryParam);
        PageResponse<SalesOrderQueryResultDTO> orders = salesOrderQueryService.querySalesOrders(queryParam);
        return ResponseEntity.ok(ApiResponse.success(orders));
    }

    /**
     * 分页查询销售订单（增强版，支持筛选选项）
     * @param queryParam 查询参数
     * @return 销售订单查询响应（包含分页数据和筛选选项）
     */
    @GetMapping("/enhanced")
    @Operation(summary = "分页查询销售订单（增强版）", description = "分页查询销售订单，支持筛选选项返回")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<SalesOrderQueryResponseDTO>> querySalesOrdersEnhanced(SalesOrderQueryParamDTO queryParam) {
        log.info("Enhanced querying sales orders with params: {}", queryParam);
        SalesOrderQueryResponseDTO response = salesOrderQueryService.querySalesOrdersEnhanced(queryParam);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取指定字段的筛选选项
     * @param fieldName 字段名称
     * @param searchText 搜索文本（可选）
     * @param currentFilters 当前其他筛选条件（可选，用于级联筛选）
     * @return 筛选选项列表
     */
    @GetMapping("/filter-options")
    @Operation(summary = "获取筛选选项", description = "获取指定字段的筛选选项，支持级联筛选")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('sales:order:read')")
    public ResponseEntity<ApiResponse<List<FilterOptionDTO>>> getFilterOptions(
            @RequestParam String fieldName,
            @RequestParam(required = false) String searchText,
            SalesOrderQueryParamDTO currentFilters) {
        log.info("Getting filter options for field: {}, searchText: {}, with filters: {}",
                fieldName, searchText, currentFilters != null ? "provided" : "null");
        List<FilterOptionDTO> options = salesOrderQueryService.getFilterOptions(fieldName, searchText, currentFilters);
        return ResponseEntity.ok(ApiResponse.success(options));
    }
}
