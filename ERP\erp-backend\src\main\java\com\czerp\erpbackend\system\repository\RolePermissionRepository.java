package com.czerp.erpbackend.system.repository;

import com.czerp.erpbackend.system.entity.RolePermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色权限关联存储库
 */
@Repository
public interface RolePermissionRepository extends JpaRepository<RolePermission, String> {

    /**
     * 根据角色ID查询权限ID列表
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @Query("SELECT rp.permissionId FROM RolePermission rp WHERE rp.roleId = ?1")
    List<String> findPermissionIdsByRoleId(String roleId);

    /**
     * 根据角色ID删除角色权限关联
     * @param roleId 角色ID
     * @return 删除数量
     */
    @Modifying
    int deleteByRoleId(String roleId);

    /**
     * 根据权限ID查询角色ID列表
     * @param permissionId 权限ID
     * @return 角色ID列表
     */
    @Query("SELECT rp.roleId FROM RolePermission rp WHERE rp.permissionId = ?1")
    List<String> findRoleIdsByPermissionId(String permissionId);

    /**
     * 根据角色编码列表查询权限编码列表
     * @param roleCodes 角色编码列表
     * @return 权限编码列表
     */
    @Query("SELECT DISTINCT p.code FROM Permission p JOIN RolePermission rp ON p.id = rp.permissionId JOIN Role r ON rp.roleId = r.id WHERE r.code IN :roleCodes")
    List<String> findPermissionCodesByRoleCodesIn(@Param("roleCodes") List<String> roleCodes);

    /**
     * 检查角色权限关联是否存在
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 是否存在
     */
    boolean existsByRoleIdAndPermissionId(String roleId, String permissionId);

    /**
     * 根据角色ID和权限ID列表查询角色权限关联
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 角色权限关联列表
     */
    List<RolePermission> findByRoleIdAndPermissionIdIn(String roleId, List<String> permissionIds);
}