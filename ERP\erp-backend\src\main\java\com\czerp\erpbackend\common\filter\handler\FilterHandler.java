package com.czerp.erpbackend.common.filter.handler;

import com.czerp.erpbackend.common.filter.dto.FilterOptionDTO;
import com.czerp.erpbackend.common.filter.dto.FilterRequest;
import com.czerp.erpbackend.common.filter.metadata.FilterFieldMetadata;

import java.util.List;

/**
 * 自定义筛选处理器接口
 * 用于处理复杂的筛选逻辑
 */
public interface FilterHandler {
    
    /**
     * 获取筛选选项
     * @param request 筛选请求
     * @param metadata 字段元数据
     * @return 筛选选项列表
     */
    List<FilterOptionDTO> getFilterOptions(FilterRequest request, FilterFieldMetadata metadata);
    
    /**
     * 检查是否支持指定字段
     * @param fieldName 字段名称
     * @return 是否支持
     */
    default boolean supports(String fieldName) {
        return true;
    }
}
