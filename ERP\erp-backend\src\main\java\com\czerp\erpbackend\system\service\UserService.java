package com.czerp.erpbackend.system.service;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.system.dto.CreateUserRequest;
import com.czerp.erpbackend.system.dto.UpdateUserRequest;
import com.czerp.erpbackend.system.dto.UserDTO;
import com.czerp.erpbackend.system.dto.UserQueryRequest;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 分页查询用户列表
     * @param request 查询请求
     * @return 用户分页列表
     */
    PageResponse<UserDTO> findUsers(UserQueryRequest request);
    
    /**
     * 根据ID查询用户
     * @param id 用户ID
     * @return 用户信息
     */
    UserDTO findUserById(String id);
    
    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户信息
     */
    UserDTO findUserByUsername(String username);
    
    /**
     * 创建用户
     * @param request 创建用户请求
     * @return 用户信息
     */
    UserDTO createUser(CreateUserRequest request);
    
    /**
     * 更新用户
     * @param id 用户ID
     * @param request 更新用户请求
     * @return 用户信息
     */
    UserDTO updateUser(String id, UpdateUserRequest request);
    
    /**
     * 删除用户
     * @param id 用户ID
     */
    void deleteUser(String id);
    
    /**
     * 更改用户状态
     * @param id 用户ID
     * @param status 状态
     * @return 用户信息
     */
    UserDTO changeStatus(String id, String status);
    
    /**
     * 修改密码
     * @param id 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 用户信息
     */
    UserDTO changePassword(String id, String oldPassword, String newPassword);
    
    /**
     * 重置密码
     * @param id 用户ID
     * @return 用户信息
     */
    UserDTO resetPassword(String id);
    
    /**
     * 重置密码
     * @param id 用户ID
     * @param password 新密码
     * @return 用户信息
     */
    UserDTO resetPassword(String id, String password);
    
    /**
     * 根据部门ID查询用户列表
     * @param departmentId 部门ID
     * @param page 页码
     * @param size 每页大小
     * @return 用户分页列表
     */
    PageResponse<UserDTO> findUsersByDepartmentId(String departmentId, int page, int size);
    
    /**
     * 根据部门查询用户列表（保持兼容性）
     * @param departmentId 部门ID
     * @param page 页码
     * @param size 每页大小
     * @return 用户分页列表
     */
    default PageResponse<UserDTO> findUsersByDepartment(String departmentId, int page, int size) {
        return findUsersByDepartmentId(departmentId, page, size);
    }
    
    /**
     * 根据角色ID查询用户列表
     * @param roleId 角色ID
     * @param page 页码
     * @param size 每页大小
     * @return 用户分页列表
     */
    PageResponse<UserDTO> findUsersByRoleId(String roleId, int page, int size);
    
    /**
     * 根据角色查询用户列表（保持兼容性）
     * @param roleId 角色ID
     * @return 用户列表
     */
    default List<UserDTO> findUsersByRole(String roleId) {
        // 获取第一页，每页1000条记录
        return findUsersByRoleId(roleId, 1, 1000).getContent();
    }
} 