package com.czerp.erpbackend.material.controller;

import com.czerp.erpbackend.common.dto.ApiResponse;
import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.system.dto.PaperSizeSettingDTO;
import com.czerp.erpbackend.system.entity.PaperSizeSetting;
import com.czerp.erpbackend.system.mapper.PaperSizeSettingMapper;
import com.czerp.erpbackend.system.repository.PaperSizeSettingRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 纸度设置控制器
 * 这是一个适配控制器，将前端的 /api/material/paper-width 请求转发到 PaperSizeSetting 相关的服务
 */
@RestController
@RequestMapping("/material/paper-width")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "纸度设置", description = "纸度设置接口")
public class PaperWidthController {

    private final PaperSizeSettingRepository paperSizeSettingRepository;
    private final PaperSizeSettingMapper paperSizeSettingMapper;

    /**
     * 获取纸度设置列表
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 纸度设置分页列表
     */
    @GetMapping
    @Operation(summary = "获取纸度设置列表", description = "分页查询纸度设置列表，支持关键字搜索")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:paper-size-setting:list')")
    public ResponseEntity<ApiResponse<PageResponse<PaperSizeSettingDTO>>> getPaperWidths(
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        log.info("Getting paper widths with keyword: {}, page: {}, size: {}", keyword, page, size);

        Pageable pageable = PageRequest.of(
                page - 1,
                size,
                Sort.by(Sort.Direction.ASC, "paperSizeInch")
        );

        Page<PaperSizeSetting> pageResult = paperSizeSettingRepository.search(keyword, pageable);

        List<PaperSizeSettingDTO> content = pageResult.getContent().stream()
                .map(paperSizeSettingMapper::toDto)
                .collect(Collectors.toList());

        PageResponse<PaperSizeSettingDTO> response = new PageResponse<>(
                content,
                pageResult.getTotalElements(),
                pageResult.getTotalPages(),
                page,
                size
        );

        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取所有纸度设置
     * @return 纸度设置列表
     */
    @GetMapping("/all")
    @Operation(summary = "获取所有纸度设置", description = "获取所有未删除的纸度设置列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:paper-size-setting:list')")
    public ResponseEntity<ApiResponse<List<PaperSizeSettingDTO>>> getAllPaperWidths() {
        log.info("Getting all paper widths");

        List<PaperSizeSetting> settings = paperSizeSettingRepository.findByIsDeletedFalseOrderByPaperSizeInchAsc();
        List<PaperSizeSettingDTO> dtos = settings.stream()
                .map(paperSizeSettingMapper::toDto)
                .collect(Collectors.toList());

        return ResponseEntity.ok(ApiResponse.success(dtos));
    }

    /**
     * 获取所有启用的纸度设置
     * @return 纸度设置列表
     */
    @GetMapping("/active")
    @Operation(summary = "获取所有启用的纸度设置", description = "获取所有未删除且未停用的纸度设置列表")
    @SecurityRequirement(name = "Bearer Authentication")
    @PreAuthorize("hasAuthority('system:paper-size-setting:list')")
    public ResponseEntity<ApiResponse<List<PaperSizeSettingDTO>>> getActivePaperWidths() {
        log.info("Getting all active paper widths");

        // 由于 PaperSizeSetting 没有停用字段，这里只返回未删除的
        List<PaperSizeSetting> settings = paperSizeSettingRepository.findByIsDeletedFalseOrderByPaperSizeInchAsc();
        List<PaperSizeSettingDTO> dtos = settings.stream()
                .map(paperSizeSettingMapper::toDto)
                .collect(Collectors.toList());

        return ResponseEntity.ok(ApiResponse.success(dtos));
    }
}
