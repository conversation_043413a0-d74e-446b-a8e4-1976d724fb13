核心目标：在创建采购订单时，能方便地选择尚未（完全）采购的销售订单及其明细。

一、数据模型设计关键点：

为了追踪销售订单的采购状态，我们需要在销售订单明细 (sales_order_item) 和采购订单明细 (purchase_order_item) 之间建立关联。直接在销售订单 (sales_order) 层面加一个状态字段（如 is_purchased）是不够精确的，因为一个销售订单可能包含多个货品，有些可能已采购，有些则没有。

sales_order (销售订单主表)

id (PK)

order_number

customer_id

order_date

... 其他字段 ...

(可选，但推荐) overall_purchase_status: (枚举类型: NOT_PURCHASED, PARTIALLY_PURCHASED, FULLY_PURCHASED)

这个字段是冗余的，可以通过计算得到，但可以极大提升查询效率。

需要有机制在采购订单变更时更新此状态。

sales_order_item (销售订单明细表)

id (PK)

sales_order_id (FK to sales_order.id)

product_id

quantity (销售数量)

unit_price

... 其他字段 ...

(可选，但推荐) purchased_quantity: (DECIMAL/INTEGER, default 0) 已为此销售订单明细采购的数量。

(可选，但推荐) purchase_status: (枚举类型: NOT_PURCHASED, PARTIALLY_PURCHASED, FULLY_PURCHASED)

同样是冗余字段，用于快速筛选和显示。

purchased_quantity 和 purchase_status 都需要在关联的采购订单创建/修改/删除时更新。

purchase_order (采购订单主表)

id (PK)

order_number

supplier_id

order_date

... 其他字段 ...

purchase_order_item (采购订单明细表)

id (PK)

purchase_order_id (FK to purchase_order.id)

product_id

quantity (采购数量)

unit_price

source_sales_order_item_id (FK to sales_order_item.id, NULLABLE):

这是核心关联字段！它指明了这个采购明细是为哪个销售订单明细采购的。

允许为 NULL，因为有些采购可能不是直接由销售订单驱动的（例如，库存采购）。

... 其他字段 ...

二、核心逻辑与流程设计：

A. 更新销售订单明细的采购状态和数量：

当一个 purchase_order_item 被创建、修改（数量变化）或删除时，如果其 source_sales_order_item_id 不为空，则需要触发以下逻辑：

找到关联的 sales_order_item。

重新计算该 sales_order_item 的 purchased_quantity:
new_purchased_quantity = SUM(poi.quantity) WHERE poi.source_sales_order_item_id = current_soi.id (聚合所有关联的采购订单明细的数量)。

更新 sales_order_item.purchased_quantity 为 new_purchased_quantity。

更新 sales_order_item.purchase_status:

IF new_purchased_quantity == 0 THEN NOT_PURCHASED

IF new_purchased_quantity > 0 AND new_purchased_quantity < sales_order_item.quantity THEN PARTIALLY_PURCHASED

IF new_purchased_quantity >= sales_order_item.quantity THEN FULLY_PURCHASED (注意：通常不应允许超额采购，但如果允许，状态仍为 FULLY_PURCHASED)

更新 sales_order.overall_purchase_status:

获取该销售订单下的所有 sales_order_item 的 purchase_status。

IF 所有明细都是 FULLY_PURCHASED THEN sales_order.overall_purchase_status = FULLY_PURCHASED

IF 所有明细都是 NOT_PURCHASED THEN sales_order.overall_purchase_status = NOT_PURCHASED

ELSE sales_order.overall_purchase_status = PARTIALLY_PURCHASED

这个更新逻辑可以在应用层代码中实现，或者（对于更高级的数据库）通过触发器实现。应用层实现通常更灵活。

B. 显示“未采购的销售订单列表”的流程：

用户触发： 用户在创建采购订单的界面，点击“从销售订单选择”或类似按钮。

后端查询：

如果使用了冗余状态字段 (overall_purchase_status):

SELECT so.*
FROM sales_order so
WHERE so.overall_purchase_status IN ('NOT_PURCHASED', 'PARTIALLY_PURCHASED')
ORDER BY so.order_date DESC; -- 或其他排序


这是最高效的方式。

如果不使用销售订单主表的冗余状态字段，但使用明细的冗余状态 (purchase_status):

SELECT DISTINCT so.*
FROM sales_order so
JOIN sales_order_item soi ON so.id = soi.sales_order_id
WHERE soi.purchase_status IN ('NOT_PURCHASED', 'PARTIALLY_PURCHASED')
ORDER BY so.order_date DESC;
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
SQL
IGNORE_WHEN_COPYING_END

如果完全不使用冗余字段（实时计算，性能最低，但数据绝对一致）：

SELECT DISTINCT so.*
FROM sales_order so
JOIN sales_order_item soi ON so.id = soi.sales_order_id
LEFT JOIN (
    SELECT source_sales_order_item_id, SUM(quantity) as total_purchased
    FROM purchase_order_item
    WHERE source_sales_order_item_id IS NOT NULL
    GROUP BY source_sales_order_item_id
) AS po_summary ON soi.id = po_summary.source_sales_order_item_id
WHERE soi.quantity > COALESCE(po_summary.total_purchased, 0) -- 销售数量 > 已采购数量
ORDER BY so.order_date DESC;
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
SQL
IGNORE_WHEN_COPYING_END

这里的 COALESCE(po_summary.total_purchased, 0) 很重要，用于处理从未被采购过的销售订单明细。

前端展示：

以列表形式展示符合条件的销售订单（订单号、客户、日期等）。

允许用户选择一个或多个销售订单。

C. 从选定的销售订单生成采购订单明细的流程：

用户选择销售订单后： 用户在列表中选择了一个或多个销售订单，点击“下一步”或“加载明细”。

后端加载明细：

对于用户选择的每个销售订单，查询其下所有 sales_order_item。

对于每个 sales_order_item，需要计算其“待采购数量”：
remaining_to_purchase = soi.quantity - COALESCE(soi.purchased_quantity, 0) (如果使用冗余字段 purchased_quantity)
或者
remaining_to_purchase = soi.quantity - COALESCE(SUM(poi.quantity FOR THIS soi.id), 0) (如果实时计算)

只显示那些 remaining_to_purchase > 0 的销售订单明细。

前端展示待采购明细：

以表格形式展示可选的销售订单明细：

产品名称/编码

原销售数量 (soi.quantity)

已采购数量 (soi.purchased_quantity 或计算值)

待采购数量 (remaining_to_purchase)

本次采购数量 (输入框，默认为 remaining_to_purchase，用户可修改)

(可选) 建议供应商 (如果产品有首选供应商)

允许用户勾选需要采购的明细，并确认“本次采购数量”。

用户提交创建采购订单：

前端将选中的明细及“本次采购数量”提交给后端。

后端：
a. 创建一条新的 purchase_order 记录。
b. 对于前端提交的每一条要采购的明细：
i. 创建一条 purchase_order_item 记录。
ii. 设置 product_id。
iii.设置 quantity 为用户输入的“本次采购数量”。
iv. 设置 source_sales_order_item_id 为对应的原始 sales_order_item.id。
v. (可选) 根据产品或用户选择填充供应商、单价等。
c. 触发更新逻辑（如A部分所述）：更新所有相关的 sales_order_item 的 purchased_quantity 和 purchase_status，以及对应 sales_order 的 overall_purchase_status。
d. 所有数据库操作应在一个事务中完成。

三、方案对比与建议：

不使用任何冗余状态字段（完全实时计算）：

优点： 数据绝对一致，不需要维护额外的更新逻辑。

缺点： 查询“未采购销售订单列表”时性能可能较低，尤其当数据量大时，因为每次都需要连接多个表并进行聚合计算。

在 sales_order_item 层面使用冗余字段 (purchased_quantity, purchase_status)：

优点：

查询销售订单明细的采购状态非常快。

计算“待采购数量”非常快。

查询“未采购销售订单列表”时，需要 JOIN sales_order_item 并根据其状态筛选，性能优于完全实时计算。

缺点： 需要在采购订单明细创建/修改/删除时，维护这些冗余字段的更新逻辑。如果逻辑出错或遗漏，可能导致数据不一致。

在 sales_order 和 sales_order_item 层面都使用冗余字段：

优点：

查询“未采购销售订单列表”的性能最高 (直接查 sales_order.overall_purchase_status)。

其他优点同方案2。

缺点： 维护成本最高，因为需要更新两层冗余字段。出错风险也相应增加。

建议：

强烈建议在 purchase_order_item 表中加入 source_sales_order_item_id 字段。 这是实现追溯的核心。

折中且推荐的方案是采用方案2：在 sales_order_item 层面维护 purchased_quantity 和 purchase_status。

这在查询效率和维护复杂度之间取得了较好的平衡。

显示销售订单列表时，可以通过 JOIN sales_order_item 并基于其 purchase_status 进行筛选，性能通常是可以接受的。

更新逻辑相对集中在 sales_order_item 层面。

如果系统对列表查询性能要求极高，且销售订单数量非常庞大，可以考虑方案3（在 sales_order 主表也加状态）。但要非常小心地设计和测试更新逻辑。

额外考虑：

事务管理： 创建采购订单及其明细，并更新销售订单相关状态，必须在一个数据库事务中完成，确保数据一致性。

并发控制： 如果多个用户可能同时基于同一个销售订单创建采购，需要考虑并发问题（例如，超额采购）。乐观锁或悲观锁机制可能需要引入，或者在汇总已采购数量时使用数据库的原子操作。

用户体验： 清晰地向用户展示哪些已采购、哪些未采购、本次建议采购多少，允许用户灵活调整。

物料合并： 如果多个销售订单明细包含相同的产品，是否允许在生成采购订单时将这些产品的需求合并到一条采购订单明细中？如果允许，source_sales_order_item_id 的设计就需要调整（可能需要一个中间关联表 purchase_order_item_sales_order_item_link 来实现多对多）。但你描述的场景听起来更像是直接从一个SO Item生成一个或多个PO Item。

这个设计应该能满足你的需求，并提供了一个相对健壮和可扩展的解决方案。关键在于准确追踪和更新已采购数量。