package com.czerp.erpbackend.purchase.service.impl;

import com.czerp.erpbackend.common.dto.PageResponse;
import com.czerp.erpbackend.common.exception.BusinessException;
import com.czerp.erpbackend.purchase.dto.CreateSupplierRequest;
import com.czerp.erpbackend.purchase.dto.SupplierDTO;
import com.czerp.erpbackend.purchase.dto.SupplierQueryRequest;
import com.czerp.erpbackend.purchase.dto.UpdateSupplierRequest;
import com.czerp.erpbackend.purchase.entity.Supplier;
import com.czerp.erpbackend.purchase.mapper.SupplierMapper;
import com.czerp.erpbackend.purchase.repository.SupplierCategoryRepository;
import com.czerp.erpbackend.purchase.repository.SupplierRepository;
import com.czerp.erpbackend.purchase.service.SupplierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SupplierServiceImpl implements SupplierService {

    private final SupplierRepository supplierRepository;
    private final SupplierCategoryRepository categoryRepository;
    private final SupplierMapper supplierMapper;

    /**
     * 分页查询供应商列表
     * @param request 查询请求
     * @return 供应商分页列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageResponse<SupplierDTO> findSuppliers(SupplierQueryRequest request) {
        log.info("Finding suppliers with request: {}", request);

        try {
            // 构建分页参数
            int page = request.getPage() != null ? request.getPage() - 1 : 0;
            int size = request.getSize() != null ? request.getSize() : 10;

            // 构建排序参数
            Sort sort = Sort.by(Sort.Direction.ASC, "supplierCode");
            if (StringUtils.hasText(request.getSortField())) {
                Sort.Direction direction = "desc".equalsIgnoreCase(request.getSortDirection())
                        ? Sort.Direction.DESC : Sort.Direction.ASC;

                // 处理特殊字段
                String sortField = request.getSortField();
                if ("createTime".equals(sortField)) {
                    sortField = "createdTime";
                } else if ("updateTime".equals(sortField)) {
                    sortField = "updatedTime";
                }

                sort = Sort.by(direction, sortField);
            }

            Pageable pageable = PageRequest.of(page, size, sort);
            log.info("Executing search with pageable: {}", pageable);

            // 执行查询
            Page<Supplier> supplierPage = supplierRepository.search(
                    request.getKeyword(),
                    request.getCategoryId(),
                    request.getStatus(),
                    request.getIndustry(),
                    request.getRegion(),
                    request.getLastOrderStartDate(),
                    request.getLastOrderEndDate(),
                    request.getLastReceiveStartDate(),
                    request.getLastReceiveEndDate(),
                    pageable
            );

            log.info("Search completed, found {} suppliers", supplierPage.getTotalElements());

            // 转换为DTO
            List<SupplierDTO> supplierDTOs = supplierPage.getContent().stream()
                    .map(supplierMapper::toDto)
                    .collect(Collectors.toList());

            // 构建分页响应
            int pageNumber = request.getPage() != null ? request.getPage() : 1;

            PageResponse<SupplierDTO> response = new PageResponse<>(
                    supplierDTOs,
                    supplierPage.getTotalElements(),
                    supplierPage.getTotalPages(),
                    pageNumber,
                    size
            );

            log.info("Returning page {} of {}, with {} suppliers",
                    response.getPage(), response.getTotalPages(), response.getContent().size());

            return response;
        } catch (Exception e) {
            log.error("Error finding suppliers: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 查询所有供应商
     * @return 供应商列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<SupplierDTO> findAllSuppliers() {
        log.debug("Finding all suppliers");

        List<Supplier> suppliers = supplierRepository.findAll(Sort.by(Sort.Direction.ASC, "supplierCode"));

        return suppliers.stream()
                .map(supplierMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID查询供应商
     * @param id 供应商ID
     * @return 供应商信息
     */
    @Override
    @Transactional(readOnly = true)
    public SupplierDTO findSupplierById(Long id) {
        log.debug("Finding supplier by id: {}", id);

        Supplier supplier = supplierRepository.findById(id)
                .orElseThrow(() -> new BusinessException("供应商不存在"));

        return supplierMapper.toDto(supplier);
    }

    /**
     * 创建供应商
     * @param request 创建请求
     * @return 供应商信息
     */
    @Override
    @Transactional
    public SupplierDTO createSupplier(CreateSupplierRequest request) {
        log.debug("Creating supplier with request: {}", request);

        // 检查供应商编码是否已存在
        if (supplierRepository.existsBySupplierCode(request.getSupplierCode())) {
            throw new BusinessException("供应商编码已存在");
        }

        // 检查分类是否存在
        if (StringUtils.hasText(request.getCategoryId())) {
            categoryRepository.findById(request.getCategoryId())
                    .orElseThrow(() -> new BusinessException("供应商分类不存在"));
        }

        // 创建供应商
        Supplier supplier = supplierMapper.toEntity(request);

        // 设置默认值
        if (supplier.getPriceDecimalPlaces() == null) {
            supplier.setPriceDecimalPlaces(2);
        }
        if (supplier.getAmountDecimalPlaces() == null) {
            supplier.setAmountDecimalPlaces(2);
        }
        if (supplier.getUnitWeightDecimalPlaces() == null) {
            supplier.setUnitWeightDecimalPlaces(3);
        }
        if (supplier.getTotalWeightDecimalPlaces() == null) {
            supplier.setTotalWeightDecimalPlaces(3);
        }
        if (!StringUtils.hasText(supplier.getStatus())) {
            supplier.setStatus("active");
        }

        // 保存供应商
        supplier = supplierRepository.save(supplier);

        return supplierMapper.toDto(supplier);
    }

    /**
     * 更新供应商
     * @param id 供应商ID
     * @param request 更新请求
     * @return 供应商信息
     */
    @Override
    @Transactional
    public SupplierDTO updateSupplier(Long id, UpdateSupplierRequest request) {
        log.debug("Updating supplier with id: {} and request: {}", id, request);

        // 查询供应商
        Supplier supplier = supplierRepository.findById(id)
                .orElseThrow(() -> new BusinessException("供应商不存在"));

        // 检查分类是否存在
        if (StringUtils.hasText(request.getCategoryId()) && !request.getCategoryId().equals(supplier.getCategoryId())) {
            categoryRepository.findById(request.getCategoryId())
                    .orElseThrow(() -> new BusinessException("供应商分类不存在"));
        }

        // 更新供应商
        supplierMapper.updateEntity(request, supplier);

        // 保存供应商
        supplier = supplierRepository.save(supplier);

        return supplierMapper.toDto(supplier);
    }

    /**
     * 删除供应商
     * @param id 供应商ID
     */
    @Override
    @Transactional
    public void deleteSupplier(Long id) {
        log.debug("Deleting supplier with id: {}", id);

        // 查询供应商
        Supplier supplier = supplierRepository.findById(id)
                .orElseThrow(() -> new BusinessException("供应商不存在"));

        // 删除供应商
        supplierRepository.delete(supplier);
    }

    /**
     * 批量删除供应商
     * @param ids 供应商ID列表
     */
    @Override
    @Transactional
    public void batchDeleteSuppliers(List<Long> ids) {
        log.debug("Batch deleting suppliers with ids: {}", ids);

        // 查询供应商列表
        List<Supplier> suppliers = supplierRepository.findAllById(ids);

        if (suppliers.isEmpty()) {
            return;
        }

        // 删除供应商
        supplierRepository.deleteAll(suppliers);
    }

    /**
     * 切换供应商状态
     * @param id 供应商ID
     * @param status 状态
     * @return 供应商信息
     */
    @Override
    @Transactional
    public SupplierDTO toggleSupplierStatus(Long id, String status) {
        log.debug("Toggling supplier status with id: {} and status: {}", id, status);

        // 查询供应商
        Supplier supplier = supplierRepository.findById(id)
                .orElseThrow(() -> new BusinessException("供应商不存在"));

        // 更新状态
        supplier.setStatus(status);
        supplier = supplierRepository.save(supplier);

        return supplierMapper.toDto(supplier);
    }
}
