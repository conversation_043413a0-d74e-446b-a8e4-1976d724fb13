package com.czerp.erpbackend.material.init;

import com.czerp.erpbackend.system.entity.Permission;
import com.czerp.erpbackend.system.entity.Role;
import com.czerp.erpbackend.system.entity.RolePermission;
import com.czerp.erpbackend.system.repository.PermissionRepository;
import com.czerp.erpbackend.system.repository.RolePermissionRepository;
import com.czerp.erpbackend.system.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 纸质资料角色权限初始化器
 * 负责为管理员角色分配纸质资料模块的权限
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(16) // 在纸质资料权限初始化之后执行
public class PaperMaterialRolePermissionInitializer implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Initializing paper material role permissions...");

        // 为管理员角色分配纸质资料模块权限
        assignPermissionsToAdminRole();

        log.info("Paper material role permissions initialized successfully");
    }

    /**
     * 为管理员角色分配纸质资料模块权限
     */
    private void assignPermissionsToAdminRole() {
        log.info("Assigning paper material permissions to admin role...");

        // 获取管理员角色
        Optional<Role> adminRole = roleRepository.findByCode("admin");
        if (adminRole.isEmpty()) {
            log.warn("Admin role not found, skipping permission assignment");
            return;
        }

        // 获取所有纸质资料相关权限
        List<String> permissionCodes = Arrays.asList(
                "material",
                "material:paper-material",
                "material:paper-material:list",
                "material:paper-material:read",
                "material:paper-material:create",
                "material:paper-material:update",
                "material:paper-material:delete",
                "material:paper-material:import"
        );

        List<Permission> permissions = permissionRepository.findByCodeIn(permissionCodes);
        if (permissions.isEmpty()) {
            log.warn("No paper material permissions found, skipping permission assignment");
            return;
        }

        // 创建新的角色权限关联
        List<RolePermission> newRolePermissions = new ArrayList<>();
        for (Permission permission : permissions) {
            // 检查权限是否已分配
            if (!rolePermissionRepository.existsByRoleIdAndPermissionId(adminRole.get().getId(), permission.getId())) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setId(UUID.randomUUID().toString()); // 添加ID字段
                rolePermission.setRoleId(adminRole.get().getId());
                rolePermission.setPermissionId(permission.getId());
                rolePermission.setCreateBy("system");
                rolePermission.setCreateTime(LocalDateTime.now());
                newRolePermissions.add(rolePermission);
                log.info("Assigning permission {} to admin role", permission.getCode());
            }
        }

        // 保存新的角色权限关联
        if (!newRolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(newRolePermissions);
            log.info("Assigned {} paper material permissions to admin role", newRolePermissions.size());
        } else {
            log.info("Admin role already has all paper material permissions");
        }
    }
}
