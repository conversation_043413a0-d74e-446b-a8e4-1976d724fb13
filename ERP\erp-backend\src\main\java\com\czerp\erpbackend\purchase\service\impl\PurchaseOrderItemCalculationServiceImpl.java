package com.czerp.erpbackend.purchase.service.impl;

import com.czerp.erpbackend.purchase.repository.PurchaseOrderItemRepository;
import com.czerp.erpbackend.purchase.service.PurchaseOrderItemCalculationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采购订单明细计算服务实现
 * 提供动态计算已入库数量等相关功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class PurchaseOrderItemCalculationServiceImpl implements PurchaseOrderItemCalculationService {

    private final PurchaseOrderItemRepository purchaseOrderItemRepository;

    @Override
    @Cacheable(value = "receivedQuantity", key = "#purchaseOrderItemId", unless = "#result == null")
    public Integer calculateReceivedQuantity(Long purchaseOrderItemId) {
        log.debug("Calculating received quantity for purchase order item: {}", purchaseOrderItemId);
        
        if (purchaseOrderItemId == null) {
            return 0;
        }
        
        try {
            Integer receivedQuantity = purchaseOrderItemRepository.calculateReceivedQuantity(purchaseOrderItemId);
            log.debug("Calculated received quantity for item {}: {}", purchaseOrderItemId, receivedQuantity);
            return receivedQuantity != null ? receivedQuantity : 0;
        } catch (Exception e) {
            log.error("Error calculating received quantity for purchase order item: {}", purchaseOrderItemId, e);
            return 0;
        }
    }

    @Override
    public Map<Long, Integer> calculateReceivedQuantitiesBatch(List<Long> purchaseOrderItemIds) {
        log.debug("Calculating received quantities for {} purchase order items", 
                purchaseOrderItemIds != null ? purchaseOrderItemIds.size() : 0);
        
        if (purchaseOrderItemIds == null || purchaseOrderItemIds.isEmpty()) {
            return new HashMap<>();
        }
        
        try {
            List<Object[]> results = purchaseOrderItemRepository.calculateReceivedQuantitiesBatch(purchaseOrderItemIds);
            
            Map<Long, Integer> receivedQuantityMap = results.stream()
                    .collect(Collectors.toMap(
                            result -> (Long) result[0],
                            result -> result[1] != null ? ((Number) result[1]).intValue() : 0
                    ));
            
            // 确保所有请求的ID都有对应的值（没有入库记录的设为0）
            for (Long itemId : purchaseOrderItemIds) {
                receivedQuantityMap.putIfAbsent(itemId, 0);
            }
            
            log.debug("Calculated received quantities for {} items", receivedQuantityMap.size());
            return receivedQuantityMap;
        } catch (Exception e) {
            log.error("Error calculating received quantities batch", e);
            // 返回默认值映射
            return purchaseOrderItemIds.stream()
                    .collect(Collectors.toMap(id -> id, id -> 0));
        }
    }

    @Override
    public Integer calculateUnreceivedQuantity(Long purchaseOrderItemId, Integer quantity, Integer returnedQuantity) {
        if (quantity == null || quantity <= 0) {
            return 0;
        }
        
        Integer receivedQty = calculateReceivedQuantity(purchaseOrderItemId);
        int returned = returnedQuantity != null ? returnedQuantity : 0;
        
        int unreceived = quantity - receivedQty + returned;
        return Math.max(0, unreceived);
    }

    @Override
    public boolean isAvailableForInbound(Long purchaseOrderItemId, Integer quantity) {
        if (quantity == null || quantity <= 0) {
            return false;
        }
        
        Integer receivedQty = calculateReceivedQuantity(purchaseOrderItemId);
        boolean available = receivedQty < quantity;
        
        log.debug("Purchase order item {} availability check: quantity={}, received={}, available={}", 
                purchaseOrderItemId, quantity, receivedQty, available);
        
        return available;
    }

    @Override
    public Map<Long, Boolean> isAvailableForInboundBatch(List<Long> purchaseOrderItemIds, Map<Long, Integer> quantityMap) {
        if (purchaseOrderItemIds == null || purchaseOrderItemIds.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<Long, Integer> receivedQuantityMap = calculateReceivedQuantitiesBatch(purchaseOrderItemIds);
        
        return purchaseOrderItemIds.stream()
                .collect(Collectors.toMap(
                        itemId -> itemId,
                        itemId -> {
                            Integer quantity = quantityMap.get(itemId);
                            Integer receivedQty = receivedQuantityMap.get(itemId);
                            
                            if (quantity == null || quantity <= 0) {
                                return false;
                            }
                            
                            return receivedQty == null || receivedQty < quantity;
                        }
                ));
    }
}
