package com.czerp.erpbackend.material.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 纸质资料DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaperMaterialDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 纸质编码
     */
    private String paperCode;

    /**
     * 纸质
     */
    private String paperName;

    /**
     * 纸类
     */
    private PaperTypeDTO paperType;

    /**
     * 纸类名称（用于兼容前端）
     */
    private String paperTypeName;

    /**
     * 楞别
     */
    private String fluteType;

    /**
     * 标准纸质
     */
    private Boolean isStandard;

    /**
     * 生产代号
     */
    private String productionCode;

    /**
     * 面纸
     */
    private String facePaper;

    /**
     * 芯纸1
     */
    private String corePaper1;

    /**
     * 中隔1
     */
    private String middlePartition1;

    /**
     * 芯纸2
     */
    private String corePaper2;

    /**
     * 中隔2
     */
    private String middlePartition2;

    /**
     * 芯纸3
     */
    private String corePaper3;

    /**
     * 里纸
     */
    private String linerPaper;

    /**
     * 层数
     */
    private Integer layerCount;

    /**
     * 重量(千克/千平方英寸)
     */
    private BigDecimal weightKgPerKsi;

    /**
     * 重量(千克/平方米)
     */
    private BigDecimal weightKgPerSqm;

    /**
     * 边压强度
     */
    private String edgeCrushStrength;

    /**
     * 纸板耐破度
     */
    private String burstingStrength;

    /**
     * 对应标准纸质
     */
    private String correspondingStandard;

    /**
     * 默认供应商名称
     */
    private String defaultSupplier;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 停用
     */
    private Boolean isDisabled;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
