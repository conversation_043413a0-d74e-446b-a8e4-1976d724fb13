package com.czerp.erpbackend.inventory.event;

import org.springframework.context.ApplicationEvent;

import java.util.Set;

/**
 * 入库单缓存失效事件
 * 用于在入库单操作后触发相关缓存清除
 */
public class StockInboundCacheEvictionEvent extends ApplicationEvent {

    /**
     * 事件类型
     */
    public enum EventType {
        CREATED,    // 创建入库单
        UPDATED,    // 更新入库单
        DELETED     // 删除入库单
    }

    private final EventType eventType;
    private final Long stockInboundId;
    private final Set<Long> affectedPurchaseOrderItemIds;

    /**
     * 构造函数
     * 
     * @param source 事件源
     * @param eventType 事件类型
     * @param stockInboundId 入库单ID
     * @param affectedPurchaseOrderItemIds 受影响的采购订单明细ID集合
     */
    public StockInboundCacheEvictionEvent(Object source, EventType eventType, Long stockInboundId, 
                                         Set<Long> affectedPurchaseOrderItemIds) {
        super(source);
        this.eventType = eventType;
        this.stockInboundId = stockInboundId;
        this.affectedPurchaseOrderItemIds = affectedPurchaseOrderItemIds;
    }

    public EventType getEventType() {
        return eventType;
    }

    public Long getStockInboundId() {
        return stockInboundId;
    }

    public Set<Long> getAffectedPurchaseOrderItemIds() {
        return affectedPurchaseOrderItemIds;
    }

    @Override
    public String toString() {
        return "StockInboundCacheEvictionEvent{" +
                "eventType=" + eventType +
                ", stockInboundId=" + stockInboundId +
                ", affectedPurchaseOrderItemIds=" + affectedPurchaseOrderItemIds +
                '}';
    }
}
