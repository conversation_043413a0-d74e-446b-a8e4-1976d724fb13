package com.czerp.erpbackend.production.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 生产排程单头表实体
 */
@Entity
@Table(name = "production_schedule")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProductionSchedule extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 排程单号
     */
    @Column(name = "schedule_no", length = 50, nullable = false, unique = true)
    private String scheduleNo;

    /**
     * 排程日期
     */
    @Column(name = "schedule_date")
    private LocalDate scheduleDate;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;

    /**
     * 生产排程明细列表
     */
    @OneToMany(mappedBy = "schedule", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ProductionScheduleItem> items = new ArrayList<>();
}
