# 销售订单后端筛选功能需求分析

基于前端代码实现的深入分析，本文档详细列出后端还需要实现的功能，确保前后端完美对接。

## 1. API接口缺失

### 1.1 增强版查询接口
**接口路径**: `GET /api/sales/orders/query/enhanced`

**用途**: 支持后端筛选的销售订单查询接口，替代现有的 `/api/sales/orders/query` 接口

**重要性**: ⭐⭐⭐⭐⭐ (核心功能)

**前端调用代码**:
```typescript
// 前端服务层调用
export async function getSalesOrdersEnhanced(params: SalesOrderQueryParams): Promise<ApiResponse<EnhancedSalesOrderQueryResult>> {
  return get<EnhancedSalesOrderQueryResult>(`${BASE_URL}/query/enhanced`, params);
}
```

### 1.2 筛选选项获取接口
**接口路径**: `GET /api/sales/orders/query/filter-options`

**用途**: 动态获取指定字段的筛选选项（如客户名称列表、产品名称列表等）

**重要性**: ⭐⭐⭐⭐ (用户体验优化)

**前端调用代码**:
```typescript
export async function getFilterOptions(fieldName: string, searchText?: string): Promise<ApiResponse<FilterOption[]>> {
  const params: any = { fieldName };
  if (searchText) {
    params.searchText = searchText;
  }
  return get<FilterOption[]>(`${BASE_URL}/query/filter-options`, params);
}
```

## 2. 筛选功能支持

### 2.1 文本筛选参数（模糊匹配）
前端通过 `mapFiltersToBackendParams` 函数映射的参数：

| 前端字段 | 后端参数 | 筛选类型 | 说明 |
|---------|---------|---------|------|
| `productionOrderNo` | `filterProductionOrderNo` | String | 生产单号模糊搜索 |
| `orderNo` | `filterOrderNo` | String | 销售单号模糊搜索 |
| `customerOrderNo` | `filterCustomerOrderNo` | String | 客户订单号模糊搜索 |
| `customerProductCode` | `filterCustomerProductCode` | String | 客方货号模糊搜索 |

**实现要求**:
- 使用 `LIKE '%keyword%'` 进行模糊匹配
- 忽略大小写
- 支持空值和null值的处理

### 2.2 选择筛选参数（多选精确匹配）
| 前端字段 | 后端参数 | 筛选类型 | 说明 |
|---------|---------|---------|------|
| `customerName` | `filterCustomerNames` | String[] | 客户名称多选筛选 |
| `productName` | `filterProductNames` | String[] | 产品名称多选筛选 |
| `salesPerson` | `filterSalesPersons` | String[] | 销售员多选筛选 |

**实现要求**:
- 使用 `IN (value1, value2, ...)` 进行精确匹配
- 支持数组参数
- 空数组时不应用筛选条件

### 2.3 数字范围筛选参数
| 前端字段 | 后端参数 | 筛选类型 | 说明 |
|---------|---------|---------|------|
| `quantity` | `filterQuantityMin`, `filterQuantityMax` | Number | 数量范围筛选 |
| `totalAmount` | `filterTotalAmountMin`, `filterTotalAmountMax` | Number | 金额范围筛选 |

**前端映射逻辑**:
```typescript
case 'quantity':
  if (Array.isArray(filterValue) && filterValue.length === 2) {
    const [min, max] = filterValue;
    if (min !== undefined && min !== null && min !== '') {
      backendParams.filterQuantityMin = Number(min);
    }
    if (max !== undefined && max !== null && max !== '') {
      backendParams.filterQuantityMax = Number(max);
    }
  }
  break;
```

**实现要求**:
- 支持最小值、最大值独立设置
- 使用 `>= min AND <= max` 进行范围查询
- 处理边界值（只有最小值或只有最大值的情况）

### 2.4 日期范围筛选参数
| 前端字段 | 后端参数 | 筛选类型 | 说明 |
|---------|---------|---------|------|
| `orderDate` | `filterOrderDateStart`, `filterOrderDateEnd` | String (YYYY-MM-DD) | 订单日期范围 |
| `deliveryDate` | `filterDeliveryDateStart`, `filterDeliveryDateEnd` | String (YYYY-MM-DD) | 交期范围 |

**前端映射逻辑**:
```typescript
case 'orderDate':
  if (Array.isArray(filterValue) && filterValue.length === 2) {
    const [startDate, endDate] = filterValue;
    if (startDate) {
      backendParams.filterOrderDateStart = startDate.format ? startDate.format('YYYY-MM-DD') : startDate;
    }
    if (endDate) {
      backendParams.filterOrderDateEnd = endDate.format ? endDate.format('YYYY-MM-DD') : endDate;
    }
  }
  break;
```

**实现要求**:
- 日期格式: `YYYY-MM-DD`
- 使用 `>= startDate AND <= endDate` 进行范围查询
- 处理时区问题（建议使用UTC或统一时区）

### 2.5 筛选选项控制参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| `includeFilterOptions` | Boolean | 是否在响应中包含筛选选项 |
| `filterOptionFields` | String[] | 需要返回筛选选项的字段列表 |

**前端使用示例**:
```typescript
const enhancedParams: SalesOrderQueryParams = {
  ...baseParams,
  ...getBackendParams(),
  includeFilterOptions: true,
  filterOptionFields: ['customerName', 'productName', 'salesPerson']
};
```

## 3. 数据结构适配

### 3.1 增强版响应格式
前端期望的响应结构：

```typescript
interface EnhancedSalesOrderQueryResult {
  pageData: SalesOrderQueryResult;  // 原有的分页数据
  filterOptions?: FilterOptions;    // 筛选选项
  filterStats?: FilterStats;        // 筛选统计
}
```

### 3.2 筛选选项结构
```typescript
interface FilterOption {
  label: string;  // 显示文本
  value: string;  // 实际值
  count: number;  // 该选项对应的记录数量
}

interface FilterOptions {
  customerName?: FilterOption[];
  productName?: FilterOption[];
  salesPerson?: FilterOption[];
  boxType?: FilterOption[];
  paperType?: FilterOption[];
  orderStatus?: FilterOption[];
  [key: string]: FilterOption[] | undefined;
}
```

### 3.3 筛选统计结构
```typescript
interface FilterStats {
  totalRecords: number;      // 总记录数（未筛选）
  filteredRecords: number;   // 筛选后记录数
  activeFilterCount: number; // 激活的筛选条件数量
}
```

### 3.4 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "pageData": {
      "content": [/* 销售订单数据 */],
      "totalElements": 100,
      "totalPages": 10,
      "page": 1,
      "size": 10
    },
    "filterOptions": {
      "customerName": [
        {
          "label": "客户A",
          "value": "客户A",
          "count": 25
        },
        {
          "label": "客户B",
          "value": "客户B",
          "count": 15
        }
      ],
      "productName": [/* 产品选项 */],
      "salesPerson": [/* 销售员选项 */]
    },
    "filterStats": {
      "totalRecords": 1000,
      "filteredRecords": 100,
      "activeFilterCount": 2
    }
  }
}
```

## 4. 性能优化需求

### 4.1 数据库索引建议
基于前端筛选字段的使用频率，建议创建以下索引：【数据库表名称为示例需根据实际情况调整】

**高频筛选字段（建议复合索引）**:
```sql
-- 销售订单主表索引
CREATE INDEX idx_sales_order_filter ON sales_orders (customer_name, order_date, sales_person);
CREATE INDEX idx_sales_order_date_range ON sales_orders (order_date, delivery_date);

-- 销售订单明细表索引
CREATE INDEX idx_sales_order_item_filter ON sales_order_items (product_name, quantity, total_amount);
CREATE INDEX idx_sales_order_item_codes ON sales_order_items (production_order_no, customer_order_no, customer_product_code);
```

**单字段索引**:
```sql
CREATE INDEX idx_sales_order_no ON sales_orders (order_no);
CREATE INDEX idx_production_order_no ON sales_order_items (production_order_no);
CREATE INDEX idx_customer_name ON sales_orders (customer_name);
CREATE INDEX idx_product_name ON sales_order_items (product_name);
```

### 4.2 查询优化策略

**分页优化**:
- 使用 `LIMIT OFFSET` 进行分页
- 对于大偏移量，考虑使用游标分页
- 缓存总记录数，避免重复COUNT查询

**筛选选项优化**:
- 使用 `GROUP BY` 和 `COUNT` 生成筛选选项
- 限制选项数量（建议最多50个选项）
- 考虑缓存常用筛选选项

**查询执行计划优化**:
- 使用 `EXPLAIN` 分析查询计划
- 避免全表扫描
- 合理使用子查询和JOIN

### 4.3 缓存策略
- **筛选选项缓存**: 客户名称、产品名称等相对稳定的选项可以缓存
- **查询结果缓存**: 对于相同筛选条件的查询结果进行短期缓存
- **统计信息缓存**: 总记录数等统计信息可以定期更新缓存

## 5. 优先级排序

### 5.1 P0 - 核心功能（必须实现）
1. **增强版查询接口** (`/query/enhanced`)
   - 实现难度: 中等
   - 预估工期: 3-5天
   - 依赖: 数据库索引优化

2. **基础筛选参数支持**
   - 文本筛选（模糊匹配）
   - 选择筛选（多选精确匹配）
   - 实现难度: 简单
   - 预估工期: 2-3天

### 5.2 P1 - 重要功能（优先实现）
3. **数字和日期范围筛选**
   - 实现难度: 简单
   - 预估工期: 1-2天

4. **基础筛选选项接口**
   - 实现难度: 中等
   - 预估工期: 2-3天

### 5.3 P2 - 优化功能（后续实现）
5. **筛选统计信息**
   - 实现难度: 简单
   - 预估工期: 1天

6. **性能优化和缓存**
   - 实现难度: 中等
   - 预估工期: 2-3天

7. **动态筛选选项接口** (`/filter-options`)
   - 实现难度: 中等
   - 预估工期: 2天

### 5.4 建议实施顺序
1. **第一阶段**: 实现增强版查询接口的基础功能（P0.1 + P0.2）
2. **第二阶段**: 完善筛选功能（P1.3 + P1.4）
3. **第三阶段**: 添加统计和优化功能（P2.5 + P2.6）
4. **第四阶段**: 实现动态筛选选项（P2.7）

## 6. 技术实现建议

### 6.1 后端架构建议
- 使用 Spring Data JPA 的 Specification 进行动态查询构建
- 创建专门的 FilterSpecification 类处理筛选逻辑
- 使用 DTO 模式分离查询参数和响应数据

### 6.2 代码结构建议
```
├── controller/
│   └── SalesOrderController.java (新增 /query/enhanced 接口)
├── service/
│   ├── SalesOrderService.java (新增筛选查询方法)
│   └── SalesOrderFilterService.java (专门处理筛选逻辑)
├── repository/
│   └── SalesOrderRepository.java (新增 Specification 查询方法)
├── specification/
│   └── SalesOrderSpecification.java (动态查询条件构建)
└── dto/
    ├── SalesOrderFilterParams.java (筛选参数DTO)
    ├── EnhancedQueryResult.java (增强版响应DTO)
    ├── FilterOption.java (筛选选项DTO)
    └── FilterStats.java (筛选统计DTO)
```

### 6.3 关键实现点
1. **参数验证**: 对筛选参数进行严格验证，防止SQL注入
2. **异常处理**: 提供友好的错误信息和降级机制
3. **日志记录**: 记录筛选查询的性能指标
4. **API文档**: 更新API文档，包含新的筛选参数说明

## 7. 前端降级处理机制

### 7.1 自动降级逻辑
前端已实现完善的降级处理机制，当后端筛选API不可用时会自动切换：

```typescript
try {
  if (backendFilterEnabled.value) {
    // 尝试使用增强版API
    const response = await getSalesOrdersEnhanced(enhancedParams);
    data = response.data;
    isEnhancedResponse = true;
  } else {
    // 使用原有API
    const response = await getSalesOrders(baseParams);
    data = response.data;
  }
} catch (error: any) {
  if (backendFilterEnabled.value && (error.code === 'ERR_NETWORK' || error.response?.status === 404)) {
    // 后端筛选API不可用，降级到原有API
    console.warn('后端筛选API不可用，降级到原有API:', error.message);
    message.warning('后端筛选功能暂不可用，已切换到前端筛选模式');

    // 自动切换到前端筛选模式
    toggleFilterMode(false);

    // 使用原有API重新请求
    const response = await getSalesOrders(baseParams);
    data = response.data;
    isEnhancedResponse = false;
  }
}
```

### 7.2 后端实现建议
- 返回标准的HTTP状态码（404表示接口不存在）
- 提供详细的错误信息
- 确保原有API的稳定性和兼容性

## 8. 测试用例建议

### 8.1 功能测试用例

**基础筛选测试**:
```
1. 文本筛选测试
   - 输入: filterOrderNo = "SO2024"
   - 期望: 返回包含"SO2024"的销售单号记录

2. 多选筛选测试
   - 输入: filterCustomerNames = ["客户A", "客户B"]
   - 期望: 返回客户名称为"客户A"或"客户B"的记录

3. 范围筛选测试
   - 输入: filterQuantityMin = 100, filterQuantityMax = 500
   - 期望: 返回数量在100-500之间的记录

4. 日期范围测试
   - 输入: filterOrderDateStart = "2024-01-01", filterOrderDateEnd = "2024-01-31"
   - 期望: 返回2024年1月的订单记录
```

**组合筛选测试**:
```
5. 多条件组合测试
   - 输入: filterCustomerNames = ["客户A"], filterQuantityMin = 100
   - 期望: 返回客户A且数量>=100的记录

6. 空值处理测试
   - 输入: filterCustomerNames = []
   - 期望: 忽略该筛选条件，不影响查询结果
```

**筛选选项测试**:
```
7. 筛选选项获取测试
   - 输入: includeFilterOptions = true, filterOptionFields = ["customerName"]
   - 期望: 响应中包含customerName的所有可选值及其数量
```

### 8.2 性能测试用例

**大数据量测试**:
```
1. 10万条记录的筛选性能测试
2. 复杂多条件筛选的响应时间测试
3. 并发筛选请求的性能测试
```

**边界测试**:
```
1. 最大分页大小测试
2. 最大筛选条件数量测试
3. 超长文本筛选测试
```

## 9. 监控和日志

### 9.1 关键指标监控
- API响应时间
- 筛选查询的数据库执行时间
- 筛选条件的使用频率统计
- 错误率和降级触发频率

### 9.2 日志记录建议
```java
// 筛选查询日志示例
log.info("Enhanced query executed: filters={}, resultCount={}, executionTime={}ms",
         filterParams, resultCount, executionTime);

// 性能警告日志
if (executionTime > 5000) {
    log.warn("Slow query detected: filters={}, executionTime={}ms",
             filterParams, executionTime);
}
```

## 10. 部署和配置

### 10.1 配置参数建议
```yaml
# application.yml
sales-order:
  filter:
    max-filter-options: 50          # 最大筛选选项数量
    query-timeout: 30               # 查询超时时间（秒）
    cache-enabled: true             # 是否启用缓存
    cache-ttl: 300                  # 缓存过期时间（秒）
    max-page-size: 1000            # 最大分页大小
```

### 10.2 数据库配置优化
```sql
-- 连接池配置建议
SET max_connections = 200;
SET innodb_buffer_pool_size = '2G';
SET query_cache_size = '256M';

-- 查询优化配置
SET optimizer_search_depth = 62;
SET optimizer_prune_level = 1;
```

## 总结

通过以上详细分析，后端开发团队可以：

1. **明确实现目标**: 了解前端期望的API接口和数据格式
2. **按优先级实施**: 从核心功能开始，逐步完善筛选功能
3. **确保性能**: 通过索引优化和缓存策略提供高性能查询
4. **保证稳定性**: 实现完善的错误处理和降级机制
5. **便于维护**: 建立监控和日志体系，便于问题排查和性能优化

前端已经实现了完整的筛选UI和降级处理机制，后端只需要按照本文档的规范实现相应的API接口，即可实现前后端的完美对接，为用户提供高效的数据筛选体验。

## 附录A: 后端实现代码示例

### A.1 Controller层示例

```java
@RestController
@RequestMapping("/api/sales/orders")
public class SalesOrderController {

    @Autowired
    private SalesOrderFilterService salesOrderFilterService;

    /**
     * 增强版查询接口（支持后端筛选）
     */
    @GetMapping("/query/enhanced")
    public ApiResponse<EnhancedQueryResult> queryEnhanced(
            @ModelAttribute SalesOrderFilterParams params) {

        try {
            EnhancedQueryResult result = salesOrderFilterService.queryWithFilters(params);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("Enhanced query failed", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取筛选选项
     */
    @GetMapping("/query/filter-options")
    public ApiResponse<List<FilterOption>> getFilterOptions(
            @RequestParam String fieldName,
            @RequestParam(required = false) String searchText) {

        try {
            List<FilterOption> options = salesOrderFilterService.getFilterOptions(fieldName, searchText);
            return ApiResponse.success(options);
        } catch (Exception e) {
            log.error("Get filter options failed", e);
            return ApiResponse.error("获取筛选选项失败: " + e.getMessage());
        }
    }
}
```

### A.2 Service层示例

```java
@Service
@Transactional(readOnly = true)
public class SalesOrderFilterService {

    @Autowired
    private SalesOrderRepository salesOrderRepository;

    public EnhancedQueryResult queryWithFilters(SalesOrderFilterParams params) {
        // 构建查询条件
        Specification<SalesOrderQueryView> spec = buildSpecification(params);

        // 分页查询
        Pageable pageable = PageRequest.of(params.getPage() - 1, params.getPageSize());
        Page<SalesOrderQueryView> page = salesOrderRepository.findAll(spec, pageable);

        // 构建响应
        EnhancedQueryResult result = new EnhancedQueryResult();
        result.setPageData(convertToQueryResult(page));

        // 如果需要筛选选项
        if (params.isIncludeFilterOptions()) {
            result.setFilterOptions(buildFilterOptions(params.getFilterOptionFields(), spec));
        }

        // 构建筛选统计
        result.setFilterStats(buildFilterStats(spec, page.getTotalElements()));

        return result;
    }

    private Specification<SalesOrderQueryView> buildSpecification(SalesOrderFilterParams params) {
        return Specification.where(null)
            // 文本筛选
            .and(textFilter("productionOrderNo", params.getFilterProductionOrderNo()))
            .and(textFilter("orderNo", params.getFilterOrderNo()))
            .and(textFilter("customerOrderNo", params.getFilterCustomerOrderNo()))
            .and(textFilter("customerProductCode", params.getFilterCustomerProductCode()))

            // 多选筛选
            .and(multiSelectFilter("customerName", params.getFilterCustomerNames()))
            .and(multiSelectFilter("productName", params.getFilterProductNames()))
            .and(multiSelectFilter("salesPerson", params.getFilterSalesPersons()))

            // 数字范围筛选
            .and(numberRangeFilter("quantity", params.getFilterQuantityMin(), params.getFilterQuantityMax()))
            .and(numberRangeFilter("totalAmount", params.getFilterTotalAmountMin(), params.getFilterTotalAmountMax()))

            // 日期范围筛选
            .and(dateRangeFilter("orderDate", params.getFilterOrderDateStart(), params.getFilterOrderDateEnd()))
            .and(dateRangeFilter("deliveryDate", params.getFilterDeliveryDateStart(), params.getFilterDeliveryDateEnd()));
    }

    // 文本筛选条件构建
    private Specification<SalesOrderQueryView> textFilter(String field, String value) {
        return (root, query, cb) -> {
            if (StringUtils.hasText(value)) {
                return cb.like(cb.lower(root.get(field)), "%" + value.toLowerCase() + "%");
            }
            return null;
        };
    }

    // 多选筛选条件构建
    private Specification<SalesOrderQueryView> multiSelectFilter(String field, List<String> values) {
        return (root, query, cb) -> {
            if (values != null && !values.isEmpty()) {
                return root.get(field).in(values);
            }
            return null;
        };
    }

    // 数字范围筛选条件构建
    private Specification<SalesOrderQueryView> numberRangeFilter(String field, Number min, Number max) {
        return (root, query, cb) -> {
            Predicate predicate = null;
            if (min != null) {
                predicate = cb.greaterThanOrEqualTo(root.get(field), min);
            }
            if (max != null) {
                Predicate maxPredicate = cb.lessThanOrEqualTo(root.get(field), max);
                predicate = predicate == null ? maxPredicate : cb.and(predicate, maxPredicate);
            }
            return predicate;
        };
    }

    // 日期范围筛选条件构建
    private Specification<SalesOrderQueryView> dateRangeFilter(String field, String startDate, String endDate) {
        return (root, query, cb) -> {
            Predicate predicate = null;
            if (StringUtils.hasText(startDate)) {
                LocalDate start = LocalDate.parse(startDate);
                predicate = cb.greaterThanOrEqualTo(root.get(field), start);
            }
            if (StringUtils.hasText(endDate)) {
                LocalDate end = LocalDate.parse(endDate);
                Predicate endPredicate = cb.lessThanOrEqualTo(root.get(field), end);
                predicate = predicate == null ? endPredicate : cb.and(predicate, endPredicate);
            }
            return predicate;
        };
    }
}
```

### A.3 DTO类示例

```java
// 筛选参数DTO
@Data
public class SalesOrderFilterParams {
    // 分页参数
    private Integer page = 1;
    private Integer pageSize = 10;

    // 基础查询参数
    private String keyword;
    private String orderNo;
    private String productionOrderNo;
    // ... 其他基础参数

    // 文本筛选参数
    private String filterProductionOrderNo;
    private String filterOrderNo;
    private String filterCustomerOrderNo;
    private String filterCustomerProductCode;

    // 多选筛选参数
    private List<String> filterCustomerNames;
    private List<String> filterProductNames;
    private List<String> filterSalesPersons;

    // 数字范围筛选参数
    private BigDecimal filterQuantityMin;
    private BigDecimal filterQuantityMax;
    private BigDecimal filterTotalAmountMin;
    private BigDecimal filterTotalAmountMax;

    // 日期范围筛选参数
    private String filterOrderDateStart;
    private String filterOrderDateEnd;
    private String filterDeliveryDateStart;
    private String filterDeliveryDateEnd;

    // 筛选选项控制参数
    private boolean includeFilterOptions = false;
    private List<String> filterOptionFields;
}

// 增强版查询结果DTO
@Data
public class EnhancedQueryResult {
    private QueryResult<SalesOrderQueryResultItem> pageData;
    private Map<String, List<FilterOption>> filterOptions;
    private FilterStats filterStats;
}

// 筛选选项DTO
@Data
public class FilterOption {
    private String label;
    private String value;
    private Long count;
}

// 筛选统计DTO
@Data
public class FilterStats {
    private Long totalRecords;
    private Long filteredRecords;
    private Integer activeFilterCount;
}
```

### A.4 Repository层示例

```java
@Repository
public interface SalesOrderRepository extends JpaRepository<SalesOrderQueryView, String>, JpaSpecificationExecutor<SalesOrderQueryView> {

    /**
     * 获取指定字段的筛选选项
     */
    @Query("SELECT DISTINCT s.customerName as value, s.customerName as label, COUNT(*) as count " +
           "FROM SalesOrderQueryView s " +
           "WHERE (:searchText IS NULL OR LOWER(s.customerName) LIKE LOWER(CONCAT('%', :searchText, '%'))) " +
           "GROUP BY s.customerName " +
           "ORDER BY count DESC, s.customerName ASC")
    List<FilterOptionProjection> findCustomerNameOptions(@Param("searchText") String searchText);

    @Query("SELECT DISTINCT s.productName as value, s.productName as label, COUNT(*) as count " +
           "FROM SalesOrderQueryView s " +
           "WHERE (:searchText IS NULL OR LOWER(s.productName) LIKE LOWER(CONCAT('%', :searchText, '%'))) " +
           "GROUP BY s.productName " +
           "ORDER BY count DESC, s.productName ASC")
    List<FilterOptionProjection> findProductNameOptions(@Param("searchText") String searchText);

    // 获取总记录数（用于统计）
    @Query("SELECT COUNT(*) FROM SalesOrderQueryView")
    Long countTotalRecords();
}

// 筛选选项投影接口
public interface FilterOptionProjection {
    String getValue();
    String getLabel();
    Long getCount();
}
```

## 附录B: 数据库视图建议

为了优化查询性能，建议创建专门的查询视图：

```sql
-- 销售订单查询视图
CREATE VIEW sales_order_query_view AS
SELECT
    so.id,
    so.order_no,
    so.order_date,
    so.customer_name,
    so.sales_person,
    so.delivery_date,
    soi.id as item_id,
    soi.production_order_no,
    soi.customer_order_no,
    soi.customer_product_code,
    soi.product_name,
    soi.quantity,
    soi.amount as total_amount,
    soi.box_type,
    soi.paper_type,
    -- 其他需要的字段
FROM sales_orders so
LEFT JOIN sales_order_items soi ON so.id = soi.order_id
WHERE so.deleted = 0 AND soi.deleted = 0;

-- 为视图创建索引
CREATE INDEX idx_soqv_customer_name ON sales_order_query_view (customer_name);
CREATE INDEX idx_soqv_product_name ON sales_order_query_view (product_name);
CREATE INDEX idx_soqv_order_date ON sales_order_query_view (order_date);
CREATE INDEX idx_soqv_quantity ON sales_order_query_view (quantity);
CREATE INDEX idx_soqv_total_amount ON sales_order_query_view (total_amount);
```

通过以上详细的代码示例和数据库设计建议，后端开发团队可以快速实现高质量的筛选功能，确保与前端代码的完美对接。
