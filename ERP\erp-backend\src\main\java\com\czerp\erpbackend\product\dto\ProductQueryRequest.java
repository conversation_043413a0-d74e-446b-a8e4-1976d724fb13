package com.czerp.erpbackend.product.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 货品查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductQueryRequest {
    
    /**
     * 关键词（货品名称、编码）
     */
    private String keyword;
    
    /**
     * 分类ID
     */
    private String categoryId;
    
    /**
     * 状态（是否禁用）
     */
    private Boolean disabled;
    
    /**
     * 创建开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    
    /**
     * 创建结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方向
     */
    private String sortDirection;
    
    /**
     * 页码（从1开始）
     */
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
}
