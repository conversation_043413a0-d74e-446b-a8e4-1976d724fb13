-- 修复纸度设置表的列名
-- 检查表是否存在这些列
SET @table_name = 'paper_size_setting';
SET @column_exists = 0;

-- 检查是否存在 deleted 列
SELECT COUNT(*) INTO @column_exists 
FROM information_schema.columns 
WHERE table_schema = 'czerp_web' 
AND table_name = @table_name 
AND column_name = 'deleted';

-- 如果存在 deleted 列，将数据迁移到 is_deleted 列，然后删除 deleted 列
SET @sql = IF(@column_exists > 0, 
    CONCAT('UPDATE ', @table_name, ' SET is_deleted = deleted;'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@column_exists > 0, 
    CONCAT('ALTER TABLE ', @table_name, ' DROP COLUMN deleted;'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查是否存在大写的审计字段
SELECT COUNT(*) INTO @column_exists 
FROM information_schema.columns 
WHERE table_schema = 'czerp_web' 
AND table_name = @table_name 
AND column_name = 'CreatedBy';

-- 如果存在大写的审计字段，将数据迁移到小写字段，然后删除大写字段
SET @sql = IF(@column_exists > 0, 
    CONCAT('UPDATE ', @table_name, ' SET created_by = COALESCE(created_by, CreatedBy), updated_by = COALESCE(updated_by, UpdatedBy), is_deleted = COALESCE(is_deleted, IsDeleted);'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@column_exists > 0, 
    CONCAT('ALTER TABLE ', @table_name, ' DROP COLUMN CreatedBy, DROP COLUMN UpdatedBy, DROP COLUMN IsDeleted, DROP COLUMN UpdatedTime;'),
    'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
