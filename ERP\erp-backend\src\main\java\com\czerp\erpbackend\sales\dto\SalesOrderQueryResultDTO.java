package com.czerp.erpbackend.sales.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销售订单查询结果DTO
 * 用于销售订单查询页面展示
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderQueryResultDTO {
    // 销售订单头信息
    private String id;
    private String orderNo; // 销售单号
    private LocalDate orderDate; // 日期
    private String customerName; // 客户名称
    private String salesPerson; // 销售员
    private String receivingUnit; // 收货单位
    private String receiver; // 收货人
    private String receiverPhone; // 收货人电话
    private String receivingAddress; // 收货地址
    private String createdBy; // 创建人
    private String createdByName; // 创建人姓名
    private LocalDateTime createdTime; // 创建时间

    // 销售订单行信息
    private String itemId; // 明细ID
    private String productionOrderNo; // 生产单号
    private String customerOrderNo; // 客户订单号
    private String customerProductCode; // 客方货号
    private String productName; // 品名
    private String processRequirements; // 工艺要求
    private String boxType; // 盒式
    private String paperType; // 纸质
    private String paperTypeName; // 纸质类别，从paper_material表关联paper_type表获取
    private String productionPaperType; // 生产纸质
    private String specification; // 规格
    private String productionSpecification; // 生产规格
    private Integer quantity; // 数量
    private Integer spareQuantity; // 备品数
    private BigDecimal price; // 单价
    private BigDecimal amount; // 金额
    private Boolean isSpecialPrice; // 特价
    private String productionRemark; // 生产备注
    private String remark; // 备注
    private String connectionMethod; // 连接方式
    private String unit; // 单位
    private BigDecimal paperQuotation; // 纸质报价
    private String currency; // 币种
    private BigDecimal unitWeight; // 单重
    private BigDecimal totalWeight; // 总重(KG)
    private BigDecimal productArea; // 产品面积
    private BigDecimal totalArea; // 总面积(平米)
    private BigDecimal productVolume; // 产品体积
    private BigDecimal totalVolume; // 总体积(立方米)
    private BigDecimal taxRate; // 税率
    private Boolean isTaxed; // 是否含税
    private String corrugationType; // 楞别
    private LocalDate deliveryDate; // 交期（送货日期）

    // 采购相关信息
    private String purchaseOrderNo; // 采购单号
    private String supplierName; // 采购供应商名称
    private Integer purchasedQuantity; // 已采购数量
    private String bindingSpecification; // 合订规格

    // 工序相关信息
    private String processes; // 工序信息，格式：分纸→开槽→粘箱→包装

    // 用料相关信息
    private String pressSizeWidth; // 压线尺寸(纸度)，从sales_order_material表获取
}
