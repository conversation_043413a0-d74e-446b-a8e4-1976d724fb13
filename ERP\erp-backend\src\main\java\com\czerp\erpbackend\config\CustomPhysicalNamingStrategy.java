package com.czerp.erpbackend.config;

import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.boot.model.naming.PhysicalNamingStrategy;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;

import java.util.Locale;
import java.util.regex.Pattern;

/**
 * 自定义物理命名策略
 * 将驼峰命名转换为小写下划线命名
 */
public class CustomPhysicalNamingStrategy implements PhysicalNamingStrategy {

    private static final Pattern PATTERN_UNDERSCORE = Pattern.compile("([a-z])([A-Z])");
    private static final String REPLACE_UNDERSCORE = "$1_$2";

    @Override
    public Identifier toPhysicalCatalogName(Identifier name, JdbcEnvironment jdbcEnvironment) {
        return apply(name);
    }

    @Override
    public Identifier toPhysicalSchemaName(Identifier name, JdbcEnvironment jdbcEnvironment) {
        return apply(name);
    }

    @Override
    public Identifier toPhysicalTableName(Identifier name, JdbcEnvironment jdbcEnvironment) {
        return apply(name);
    }

    @Override
    public Identifier toPhysicalSequenceName(Identifier name, JdbcEnvironment jdbcEnvironment) {
        return apply(name);
    }

    @Override
    public Identifier toPhysicalColumnName(Identifier name, JdbcEnvironment jdbcEnvironment) {
        return apply(name);
    }

    /**
     * 应用命名转换规则
     * @param name 原始标识符
     * @return 转换后的标识符
     */
    private Identifier apply(Identifier name) {
        if (name == null) {
            return null;
        }

        // 获取原始名称
        String text = name.getText();

        // 如果已经是小写下划线命名，直接返回
        if (text.equals(text.toLowerCase(Locale.ROOT)) && text.contains("_")) {
            return name;
        }

        // 将驼峰命名转换为下划线命名
        String newName = PATTERN_UNDERSCORE.matcher(text).replaceAll(REPLACE_UNDERSCORE).toLowerCase(Locale.ROOT);

        // 如果名称没有变化，直接返回原始标识符
        if (text.equals(newName)) {
            return name;
        }

        // 创建新的标识符
        return Identifier.toIdentifier(newName, name.isQuoted());
    }
}
