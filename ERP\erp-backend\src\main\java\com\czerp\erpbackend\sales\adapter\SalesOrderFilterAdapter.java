package com.czerp.erpbackend.sales.adapter;

import com.czerp.erpbackend.common.filter.service.ColumnFilterService;
import com.czerp.erpbackend.sales.config.SalesOrderFilterConfig;
import com.czerp.erpbackend.sales.dto.SalesOrderQueryParamDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售订单筛选适配器
 * 将销售订单查询参数转换为通用筛选服务的参数格式
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SalesOrderFilterAdapter {
    
    private final ColumnFilterService columnFilterService;
    
    /**
     * 获取筛选选项（适配原有接口）
     * @param fieldName 字段名称
     * @param searchText 搜索文本
     * @param currentFilters 当前筛选条件
     * @return 筛选选项列表
     */
    public List<com.czerp.erpbackend.sales.dto.FilterOptionDTO> getFilterOptions(
            String fieldName, String searchText, SalesOrderQueryParamDTO currentFilters) {
        
        // 转换筛选条件格式
        Map<String, Object> filterMap = convertToFilterMap(currentFilters);
        
        // 调用通用筛选服务
        List<com.czerp.erpbackend.common.filter.dto.FilterOptionDTO> commonOptions = 
                columnFilterService.getFilterOptions(
                        SalesOrderFilterConfig.MODULE_CODE, 
                        fieldName, 
                        searchText, 
                        filterMap);
        
        // 转换返回结果格式
        return convertToSalesOrderFilterOptions(commonOptions);
    }
    
    /**
     * 将销售订单查询参数转换为通用筛选条件映射
     */
    private Map<String, Object> convertToFilterMap(SalesOrderQueryParamDTO queryParam) {
        if (queryParam == null) {
            return new HashMap<>();
        }
        
        Map<String, Object> filterMap = new HashMap<>();
        
        // 主表字段
        addFilterIfNotEmpty(filterMap, "customerName", queryParam.getFilterCustomerNames());
        addFilterIfNotEmpty(filterMap, "salesPerson", queryParam.getFilterSalesPersons());
        addFilterIfNotEmpty(filterMap, "orderNo", queryParam.getFilterOrderNos());
        
        // 明细表字段
        addFilterIfNotEmpty(filterMap, "productName", queryParam.getFilterProductNames());
        addFilterIfNotEmpty(filterMap, "productionOrderNo", queryParam.getFilterProductionOrderNos());
        addFilterIfNotEmpty(filterMap, "customerOrderNo", queryParam.getFilterCustomerOrderNos());
        addFilterIfNotEmpty(filterMap, "customerProductCode", queryParam.getFilterCustomerProductCodes());
        addFilterIfNotEmpty(filterMap, "paperType", queryParam.getFilterPaperTypes());
        addFilterIfNotEmpty(filterMap, "productionPaperType", queryParam.getFilterProductionPaperTypes());
        
        // 子查询字段
        addFilterIfNotEmpty(filterMap, "supplierName", queryParam.getFilterSupplierNames());
        addFilterIfNotEmpty(filterMap, "paperTypeName", queryParam.getFilterPaperTypeNames());
        addFilterIfNotEmpty(filterMap, "purchaseOrderNo", queryParam.getFilterPurchaseOrderNos());
        addFilterIfNotEmpty(filterMap, "bindingSpecification", queryParam.getFilterBindingSpecifications());
        addFilterIfNotEmpty(filterMap, "pressSizeWidth", queryParam.getFilterPressSizeWidths());
        addFilterIfNotEmpty(filterMap, "processes", queryParam.getFilterProcesses());
        
        // 动态字段
        addFilterIfNotEmpty(filterMap, "specification", queryParam.getFilterSpecifications());
        addFilterIfNotEmpty(filterMap, "productionSpecification", queryParam.getFilterProductionSpecifications());
        
        return filterMap;
    }
    
    /**
     * 添加非空筛选条件
     */
    private void addFilterIfNotEmpty(Map<String, Object> filterMap, String key, List<String> values) {
        if (values != null && !values.isEmpty()) {
            filterMap.put(key, values);
        }
    }
    
    /**
     * 将通用筛选选项转换为销售订单筛选选项格式
     */
    private List<com.czerp.erpbackend.sales.dto.FilterOptionDTO> convertToSalesOrderFilterOptions(
            List<com.czerp.erpbackend.common.filter.dto.FilterOptionDTO> commonOptions) {
        
        return commonOptions.stream()
                .map(option -> com.czerp.erpbackend.sales.dto.FilterOptionDTO.builder()
                        .label(option.getLabel())
                        .value(option.getValue())
                        .count(option.getCount())
                        .build())
                .collect(java.util.stream.Collectors.toList());
    }
}
