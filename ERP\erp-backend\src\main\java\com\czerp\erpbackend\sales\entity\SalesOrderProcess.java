package com.czerp.erpbackend.sales.entity;

import com.czerp.erpbackend.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 销售订单工序实体
 */
@Entity
@Table(name = "sales_order_item_process")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderProcess extends BaseEntity {

    /**
     * 工序ID
     */
    @Id
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 订单ID
     */
    @Column(name = "order_id", length = 36, nullable = false)
    private String orderId;

    /**
     * 订单明细项ID
     */
    @Column(name = "order_item_id", length = 36)
    private String orderItemId;

    /**
     * 工序顺序
     */
    @Column(name = "sequence", nullable = false)
    private Integer sequence;

    /**
     * 工序名称
     */
    @Column(name = "process_name", length = 50, nullable = false)
    private String processName;

    /**
     * 工艺要求
     */
    @Column(name = "process_requirements", length = 500)
    private String processRequirements;

    /**
     * 印版编号
     */
    @Column(name = "plate_number", length = 50)
    private String plateNumber;

    /**
     * 水墨编号
     */
    @Column(name = "ink_number", length = 50)
    private String inkNumber;

    /**
     * 水墨名称
     */
    @Column(name = "ink_name", length = 100)
    private String inkName;

    /**
     * 颜色数
     */
    @Column(name = "color_count")
    private Integer colorCount;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;

    /**
     * 版本号
     */
    @Column(name = "version")
    private Integer version = 0;
}
